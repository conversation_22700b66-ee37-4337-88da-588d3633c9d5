import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintFiliacaoPartidariaProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      sigla?: ValueWithSource;
      vinculos?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      detalhes?: Record<string, ValueWithSource>;
      [key: string]: any;
    }>;
  };
}

export const renderFiliacaoPartidaria = ({ section }: RenderPrintFiliacaoPartidariaProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((filiacao) => {
    if (filiacao.sigla && !filiacao.sigla.is_deleted) {
      children.push(new Paragraph({
        children: [
          new TextRun({ text: `${(filiacao.sigla.label || "Sigla").toUpperCase()}: `, bold: true, color: "FE473C" }),
          new TextRun(String(filiacao.sigla.value)),
        ],
        spacing: { after: 200 }
      }));
    }

    if (filiacao.detalhes) {
      const tableRows = Object.entries(filiacao.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => {
          return new TableRow({
            children: [
              new TableCell({ children: [new Paragraph(translatePropToLabel(field.label || key).toUpperCase())] }),
              new TableCell({ children: [new Paragraph(String(field.value))] }),
            ],
          });
        });
      if (tableRows.length > 0) {
        children.push(new Table({ rows: tableRows, width: { size: 100, type: WidthType.PERCENTAGE } }));
      }
    }

    if (filiacao.vinculos && filiacao.vinculos.length > 0) {
        children.push(new Paragraph({text: "VÍNCULOS", style: "subtitle"}));
        filiacao.vinculos.filter(v => !v.is_deleted).forEach((vinculo, index) => {
            children.push(new Paragraph({text: `${translatePropToLabel(vinculo.label || "VÍNCULO").toUpperCase()} ${index + 1}`}));
            const vinculoRows = Object.entries(vinculo.value).filter(([_, f]) => !f.is_deleted).map(([key, field]) => new TableRow({children: [new TableCell({children: [new Paragraph(translatePropToLabel(field.label || key))]}), new TableCell({children: [new Paragraph(String(field.value))]})]}))
            if(vinculoRows.length > 0) {
                children.push(new Table({rows: vinculoRows, width: {size: 100, type: WidthType.PERCENTAGE}}));
            }
        });
    }

  });

  return { children };
};
