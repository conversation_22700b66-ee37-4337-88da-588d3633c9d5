import { Button, Input, Select, Text } from "@snap/design-system";
import { Check } from "lucide-react";
import { useEffect, useCallback, useMemo } from "react";
import { useParams } from "react-router";
import { ReportInput } from "~/components/ReportInput";
import { useUserData } from "~/store/userStore";
import {
  useNewReportActions,
  useNewReportInputValue,
  useNewReportSelectedType,
  useDocument1Type,
  useDocument1Value,
  useDocument2Type,
  useDocument2Value,
} from "~/store/newReportStore";
import {
  useActiveTab,
  useFolderName,
  useSelectedReports,
  useCreateFolderActions,
  useCanCreateFolder,
  /* useFolderError, */
} from "~/store/createFolderStore";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { useFolderCRUD } from "~/hooks/useFolderCRUD";
import { toast } from "sonner";
import { useEncryption, base64ToUint8Array } from "~/hooks/useEncryption";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
// import { Column } from "~/components/Table";
import { REPORT_CONSTANTS } from "~/helpers/constants";
// import { NewReportResponse } from "~/types/global";
import { useDialogActions } from "~/store/dialogStore";
import { usePassword } from "~/store/credentials";
import { translatePropToLabel } from "~/helpers";

export function CreateReportDialogContent() {
  const userData = useUserData();
  // const { folderId } = useParams<{ folderId?: string }>();
  const selectedReportType = useNewReportSelectedType();
  const reportInputValue = useNewReportInputValue();
  const document1Type = useDocument1Type();
  const document1Value = useDocument1Value();
  const document2Type = useDocument2Type();
  const document2Value = useDocument2Value();
  const {
    setSelectedReportType,
    setReportInputValue,
    setDocument1Type,
    setDocument1Value,
    setDocument2Type,
    setDocument2Value
  } = useNewReportActions();
  /* STORE */
  const activeTab = useActiveTab();
  const folderName = useFolderName();
  // const selectedReports = useSelectedReports();
  // const folderError = useFolderError();
  const { setActiveTab, setFolderName } = useCreateFolderActions();
  const reportTypes = userData?.report_types;
  const reportTypeOptions = useMemo(() =>
    reportTypes?.filter((type) => type !== "combinado")
      .map((type) => ({
        value: type,
        label: translatePropToLabel(type).toUpperCase(),
      })) || [],
    [reportTypes]
  );

  const documentTypeOptions = useMemo(() => [
    { value: "cpf", label: "CPF" },
    { value: "cnpj", label: "CNPJ" }
  ], []);
  // const { reportListQuery } = useReportCRUD(folderId);
  // const { data: reportDataResponse, isFetching } = reportListQuery;

  useEffect(() => {
    const firstType = reportTypes?.filter(type => type !== "combinado")[0] || "";
    setSelectedReportType(firstType);
  }, [userData?.report_types, setSelectedReportType]);

  const handleReportTabClick = useCallback(() => {
    setActiveTab("report");
  }, [setActiveTab]);

  const handleFolderTabClick = useCallback(() => {
    setActiveTab("folder");
  }, [setActiveTab]);

  const handleFolderNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFolderName(e.target.value);
  }, [setFolderName]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (activeTab === 'report') {
        const isValid = selectedReportType === "relacoes"
          ? document1Type && document1Value && document2Type && document2Value
          : selectedReportType && reportInputValue;

        if (isValid) {
          const createButton = document.querySelector('[data-testid="button-confirm-create-report"]') as HTMLButtonElement;
          createButton?.click();
        }
      } else if (activeTab === 'folder') {
        if (folderName.trim()) {
          const createButton = document.querySelector('[data-testid="button-confirm-create-folder"]') as HTMLButtonElement;
          createButton?.click();
        }
      }
    }
  }, [activeTab, selectedReportType, reportInputValue, document1Type, document1Value, document2Type, document2Value, folderName]);

  const columnProps = useMemo(() => ({
    report_name: REPORT_CONSTANTS.new_report.report_name,
    modified_at: REPORT_CONSTANTS.new_report.modified_at,
  }), []);

  // const userColumns: Column[] = useMemo(() => [
  //   {
  //     key: columnProps.report_name,
  //     header: "Selecione os relatórios",
  //     widthClass: "w-2/5 min-w-[200px]",
  //     className: "overflow-hidden",
  //     render: (_, row: NewReportResponse) => (
  //       <div className="flex items-center gap-4">
  //         <div className="truncate">
  //           <Text variant="body-md" className="font-semibold">{row?.report_name as string}</Text>
  //         </div>
  //       </div>
  //     ),
  //   },
  //   {
  //     key: columnProps.modified_at,
  //     header: "Modificado Em",
  //     widthClass: "w-1/5 min-w-[120px]",
  //     render: (_, row: NewReportResponse) => (
  //       <div className="flex items-center gap-4">
  //         <Text variant="body-md" className="font-semibold">{row.modified_at as string}</Text>
  //       </div>
  //     ),
  //   },
  // ], [columnProps]);

  return (
    <div className="space-y-4 min-h-[200px]">
      <p className="text-md mb-4 font-semibold">
        Que tipo de item você deseja criar?
      </p>
      <div className="flex items-center">
        <Button
          className={`uppercase w-full !rounded-none ${activeTab === "report" && "!bg-foreground !text-background"}`}
          onClick={handleReportTabClick}
          data-testid="button-create-report"
        >
          Criar Relatório
        </Button>
        <Button
          className={`uppercase w-full !rounded-none ${activeTab === "folder" && "!bg-foreground !text-background"}`}
          onClick={handleFolderTabClick}
          data-testid="button-create-folder"
        >
          Criar Pasta
        </Button>
      </div>
      <div className="mb-12">
        {activeTab === "report" && (
          <div className="grid grid-cols-2 gap-4 mb-2">
            <div>
              <Text className="block mb-1">Tipo de entrada:</Text>
              {userData?.report_types?.length ? (
                <Select
                  options={reportTypeOptions}
                  value={selectedReportType}
                  onChange={setSelectedReportType}
                  placeholder="Selecionar tipo"
                  data-testid="select-report-type"
                  className="w-full"
                />
              ) : (
                <p className="text-sm text-accent">Nenhum tipo de relatório disponível</p>
              )}
            </div>
            <div>
              {selectedReportType !== "relacoes" && <div>
                <Text className="block mb-1">Preencha o campo:</Text>
                <div className="pt-1.5">
                  <ReportInput
                    inputValue={reportInputValue}
                    setInputValue={setReportInputValue}
                    reportType={selectedReportType}
                    onKeyDown={handleKeyDown}
                  />
                </div>
              </div>
              }
            </div>
            {selectedReportType === "relacoes" && (
              <div className="col-span-2 grid grid-cols-2 gap-4">
                <div className="space-y-4">
                  <Text className="block mb-1 font-semibold">Documento 1:</Text>
                  <div>
                    <Text className="block mb-1">Tipo do documento:</Text>
                    <Select
                      options={documentTypeOptions}
                      value={document1Type}
                      onChange={setDocument1Type}
                      placeholder="Selecionar tipo"
                      data-testid="select-document1-type"
                      className="w-full"
                    />
                  </div>
                  <div>
                    <Text className="block mb-1">Valor do documento:</Text>
                    <ReportInput
                      inputValue={document1Value}
                      setInputValue={setDocument1Value}
                      reportType={selectedReportType}
                      documentType={document1Type}
                      onKeyDown={handleKeyDown}
                    />
                  </div>
                </div>
                <div className="space-y-4">
                  <Text className="block mb-1 font-semibold">Documento 2:</Text>
                  <div>
                    <Text className="block mb-1">Tipo do documento:</Text>
                    <Select
                      options={documentTypeOptions}
                      value={document2Type}
                      onChange={setDocument2Type}
                      placeholder="Selecionar tipo"
                      data-testid="select-document2-type"
                      className="w-full"
                    />
                  </div>
                  <div>
                    <Text className="block mb-1">Valor do documento:</Text>
                    <ReportInput
                      inputValue={document2Value}
                      setInputValue={setDocument2Value}
                      reportType={selectedReportType}
                      documentType={document2Type}
                      onKeyDown={handleKeyDown}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === "folder" && (
          <div className="flex flex-col gap-4">
            <div>
              <Text className="block mb-1">Digite o nome da pasta que deseja criar:</Text>
              <Input
                type="text"
                variant="outlined"
                value={folderName}
                placeholder="Nome da pasta"
                onChange={handleFolderNameChange}
                onKeyDown={handleKeyDown}
                className={`rounded-none border-0 w-full border-b-1 border-dashed pl-0 text-[16px]`}
                maxLength={100}
              />
              <div className="flex justify-end mt-1">
                <Text variant="body-sm" className="text-muted-foreground">
                  {folderName.length}/100 caracteres
                </Text>
              </div>
              {/* TODO: implementar validação de nome de pasta */}
              {/* {folderError && (
                <Text variant="body-sm" className="text-accent mt-1">
                  {folderError}
                </Text>
              )} */}
            </div>
            {/* TODO: descomentar quando implementar a seleção de relatórios
            <DataTable
              columns={userColumns}
              data={reportDataResponse?.data || []}
              keyField={REPORT_CONSTANTS.new_report.report_id as keyof NewReportResponse}
              enableSelection
            /> */}
          </div>
        )}
      </div>
    </div>
  );
}

export function CreateReportDialogFooter() {
  const { folderId } = useParams<{ folderId?: string }>();
  const { newReportMutation } = useReportCRUD(folderId);
  const { createFolderMutation } = useFolderCRUD();
  const { _encryptData } = useEncryption();
  const selectedReportType = useNewReportSelectedType();
  const reportInputValue = useNewReportInputValue();
  const document1Type = useDocument1Type();
  const document1Value = useDocument1Value();
  const document2Type = useDocument2Type();
  const document2Value = useDocument2Value();
  const { closeDialog } = useDialogActions();
  /* STORE */
  const activeTab = useActiveTab();
  const folderName = useFolderName();
  const selectedReports = useSelectedReports();
  const canCreateFolder = useCanCreateFolder();
  const { resetStore } = useCreateFolderActions();
  const { clearNewReportValues } = useNewReportActions();
  const secretKey = usePassword()

  const handleCreateNewReport = async () => {

    if (selectedReportType === "relacoes") {
      if (!document1Type || !document1Value || !document2Type || !document2Value) {
        toast("Preencha todos os campos", {
          description: "Necessário selecionar os tipos e preencher os valores dos dois documentos",
          action: {
            label: "Fechar",
            onClick: () => { },
          },
        });
        return;
      }

      const document1Key = `${document1Type}_1`;
      const document2Key = `${document2Type}_2`;

      const searchArgsFormatInput = {
        [document1Key]: document1Value,
        [document2Key]: document2Value,
      };

      const derivedKey = base64ToUint8Array(secretKey || "");
      const encryptedValue = await _encryptData(searchArgsFormatInput, derivedKey.buffer);

      if (!encryptedValue.data) {
        toast("Erro", {
          description: "Erro ao criptografar dados",
          action: {
            label: "Fechar",
            onClick: () => { },
          },
        });
        return;
      }

      newReportMutation.mutateAsync({
        report_type: selectedReportType,
        report_input_value: searchArgsFormatInput,
        report_search_args: searchArgsFormatInput,
        report_input_encrypted: encryptedValue.data,
        parent_folder_id: folderId || null,
      });
    } else {
      if (!selectedReportType || !reportInputValue) {
        toast("Preencha todos os campos", {
          description: "Necessário tipo de entrada e seu valor",
          action: {
            label: "Fechar",
            onClick: () => { },
          },
        });
        return;
      }

      if (selectedReportType === REPORT_CONSTANTS.types.email && !reportInputValue.includes("@")) {
        toast("Erro", {
          description: "Email inválido",
          action: {
            label: "Fechar",
            onClick: () => { },
          },
        });
        return;
      }

      const searchArgsFormatInput = {
        [selectedReportType]: [reportInputValue],
      };
      const derivedKey = base64ToUint8Array(secretKey || "");
      const encryptedValue = await _encryptData(searchArgsFormatInput, derivedKey.buffer);

      if (!encryptedValue.data) {
        toast("Erro", {
          description: "Erro ao criptografar dados",
          action: {
            label: "Fechar",
            onClick: () => { },
          },
        });
        return;
      }

      newReportMutation.mutateAsync({
        report_type: selectedReportType,
        report_input_value: reportInputValue,
        report_search_args: searchArgsFormatInput,
        report_input_encrypted: encryptedValue.data,
        parent_folder_id: folderId || null,
      });
    }
  };

  const handleCreateFolder = () => {
    const trimmedFolderName = folderName.trim();

    if (!trimmedFolderName) {
      toast("Erro", {
        description: "Nome da pasta é obrigatório",
        action: {
          label: "Fechar",
          onClick: () => { },
        },
      });
      return;
    }

    // Ensure folder name doesn't exceed 100 characters
    const limitedFolderName = trimmedFolderName.substring(0, 100);
    console.log("Folder name before mutation:", limitedFolderName, "Length:", limitedFolderName.length);

    createFolderMutation.mutate({
      folderName: limitedFolderName,
      selectedReports,
    });
  };

  const handleCancel = () => {
    clearNewReportValues();
    resetStore();
    closeDialog();
  };

  const isReportValid = useMemo(() => {
    if (selectedReportType === "relacoes") {
      return document1Type && document1Value && document2Type && document2Value;
    }
    return selectedReportType && reportInputValue;
  }, [selectedReportType, reportInputValue, document1Type, document1Value, document2Type, document2Value]);

  return (
    <div className="flex gap-4">
      {activeTab === "report" && (
        <Button
          className="uppercase"
          onClick={handleCreateNewReport}
          data-testid="button-confirm-create-report"
          disabled={newReportMutation.isPending || !isReportValid}
          iconPosition="right"
          icon={newReportMutation.isPending ? <AiOutlineLoading3Quarters size={20} className="animate-spin" /> : <Check />}
        >
          Criar Relatório
        </Button>
      )}

      {activeTab === "folder" && (
        <Button
          className="uppercase"
          onClick={handleCreateFolder}
          data-testid="button-confirm-create-folder"
          disabled={!canCreateFolder || createFolderMutation.isPending}
          iconPosition="right"
          icon={createFolderMutation.isPending ? <AiOutlineLoading3Quarters size={20} className="animate-spin" /> : <Check />}
        >
          {createFolderMutation.isPending ? "Criando..." : "Criar Pasta"}
        </Button>
      )}

      <Button
        onClick={handleCancel}
        data-testid="button-cancel-create-report"
      >
        Cancelar
      </Button>
    </div>
  );
}

// Export as static properties so you can use the composition pattern
export const CreateReportDialog = {
  Content: CreateReportDialogContent,
  Footer: CreateReportDialogFooter,
};
