import { Client } from 'minio';
import * as logger from '../utils/logger';

// MinIO configuration with validation
const MINIO_ENDPOINT = process.env.MINIO_CONTAINER_NAME;
const MINIO_PORT_STR = process.env.MINIO_S3_INTERNAL_PORT;
const MINIO_ACCESS_KEY = process.env.MINIO_ROOT_USER;
const MINIO_SECRET_KEY = process.env.MINIO_ROOT_PASSWORD;

// Validate required environment variables
if (!MINIO_ENDPOINT) {
  throw new Error('MINIO_CONTAINER_NAME environment variable is required');
}
if (!MINIO_PORT_STR) {
  throw new Error('MINIO_S3_INTERNAL_PORT environment variable is required');
}
if (!MINIO_ACCESS_KEY) {
  throw new Error('MINIO_ROOT_USER environment variable is required');
}
if (!MINIO_SECRET_KEY) {
  throw new Error('MINIO_ROOT_PASSWORD environment variable is required');
}

const MINIO_PORT = parseInt(MINIO_PORT_STR);
if (isNaN(MINIO_PORT)) {
  throw new Error('MINIO_S3_INTERNAL_PORT must be a valid number');
}

// Initialize MinIO client
export const minioClient = new Client({
  endPoint: MINIO_ENDPOINT,
  port: MINIO_PORT,
  useSSL: false,
  accessKey: MINIO_ACCESS_KEY,
  secretKey: MINIO_SECRET_KEY,
});

// Storage constants
export const TEMP_PDF_BUCKET = 'temp-pdf-data';
export const GENERATED_PDF_BUCKET = 'generated-pdfs';

// MinIO service functions for PDF service
export async function loadTempPdfData(dataReference: string): Promise<any | null> {
  logger.info(`[loadTempPdfData] Loading temporary PDF data with reference: ${dataReference}`);

  try {
    // Get the object
    const stream = await minioClient.getObject(TEMP_PDF_BUCKET, dataReference);

    // Convert stream to string
    const chunks: Buffer[] = [];
    for await (const chunk of stream) {
      chunks.push(chunk);
    }
    const dataString = Buffer.concat(chunks).toString('utf-8');

    // Parse JSON
    const dataWithMetadata = JSON.parse(dataString);
    const pdfData = dataWithMetadata.data || dataWithMetadata;

    logger.info(`[loadTempPdfData] Temporary PDF data loaded successfully`);
    return pdfData;

  } catch (error: any) {
    if (error.code === 'NoSuchKey') {
      logger.warn(`[loadTempPdfData] Temporary PDF data not found: ${dataReference}`);
      return null;
    }
    logger.error(`[loadTempPdfData] Failed to load temporary PDF data ${dataReference}: ${error.message}`);
    throw error;
  }
}

export async function deleteTempPdfData(dataReference: string): Promise<void> {
  logger.info(`[deleteTempPdfData] Deleting temporary PDF data with reference: ${dataReference}`);

  try {
    await minioClient.removeObject(TEMP_PDF_BUCKET, dataReference);
    logger.info(`[deleteTempPdfData] Temporary PDF data deleted successfully`);
  } catch (error: any) {
    if (error.code === 'NoSuchKey') {
      logger.warn(`[deleteTempPdfData] Temporary PDF data not found: ${dataReference}`);
      return;
    }
    logger.error(`[deleteTempPdfData] Failed to delete temporary PDF data ${dataReference}: ${error.message}`);
    // Don't throw error for cleanup operations
  }
}

export async function storeGeneratedPdf(pdfReference: string, pdfBuffer: Buffer, filename: string): Promise<string> {
  logger.info(`[storeGeneratedPdf] Storing generated PDF with reference: ${pdfReference}`);

  try {
    // Ensure bucket exists
    const bucketExists = await minioClient.bucketExists(GENERATED_PDF_BUCKET);
    if (!bucketExists) {
      logger.info(`[storeGeneratedPdf] Creating generated PDF bucket: ${GENERATED_PDF_BUCKET}`);
      await minioClient.makeBucket(GENERATED_PDF_BUCKET);
    }

    const metadata = {
      'filename': filename,
      'content-type': 'application/pdf'
    };

    await minioClient.putObject(
      GENERATED_PDF_BUCKET,
      pdfReference,
      pdfBuffer,
      pdfBuffer.length,
      {
        'Content-Type': 'application/pdf',
        ...metadata
      }
    );

    logger.info(`[storeGeneratedPdf] Generated PDF stored successfully. Size: ${pdfBuffer.length} bytes`);
    return pdfReference;

  } catch (error: any) {
    logger.error(`[storeGeneratedPdf] Failed to store generated PDF: ${error.message}`);
    throw error;
  }
}

// Initialize MinIO connection
export async function initializeMinioConnection(): Promise<void> {
  try {
    // Test connection by checking if buckets exist
    const tempBucketExists = await minioClient.bucketExists(TEMP_PDF_BUCKET);
    const pdfBucketExists = await minioClient.bucketExists(GENERATED_PDF_BUCKET);

    if (!tempBucketExists) {
      logger.warn(`[initializeMinioConnection] Bucket ${TEMP_PDF_BUCKET} does not exist`);
    }
    if (!pdfBucketExists) {
      logger.warn(`[initializeMinioConnection] Bucket ${GENERATED_PDF_BUCKET} does not exist`);
    }

    if (tempBucketExists || pdfBucketExists) {
      logger.info(`[initializeMinioConnection] Successfully connected to MinIO`);
    }
  } catch (error: any) {
    logger.error(`[initializeMinioConnection] Failed to connect to MinIO: ${error.message}`);
    throw error;
  }
}
