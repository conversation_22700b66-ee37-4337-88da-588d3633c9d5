import { useEffect, useState } from "react";
import { Button, CustomLabel, Input, Text, Select } from "@snap/design-system";
import { useUserData } from "~/store/userStore";
import { Check, Eye, EyeOff, Info, Save, Undo } from "lucide-react";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { useDialogActions } from "~/store/dialogStore";
import PasswordStrengthMeter, {
  validatePasswordNIST,
} from "~/components/PasswordStrengthMeter";
import { toast } from "sonner";
import { startNewAuthorizedSession } from "~/store/auth/events";
import { useCredentialsStoreActions, usePassword } from "~/store/credentials";
import { useAuthorization } from "~/hooks/useAuthorization";
import { SK_STORAGE_DURATION } from "~/helpers/constants";
import { arrayBufferToBase64, convertDurationToDate } from "~/helpers";
import { useCredentialStoreEvents } from "~/store/credentials/events";
import { useEncryption } from "~/hooks/useEncryption";

type UserVerificationState = "withVerifier" | "withoutVerifier";

const getUserState = (
  userData: ReturnType<typeof useUserData>
): UserVerificationState => {
  return userData?.verifier ? "withVerifier" : "withoutVerifier";
};

type PasswordInputProps = {
  isReadOnly?: boolean;
  value: string;
  onChange: (value: string) => void;
};

export const PasswordInput = ({ isReadOnly = false, value, onChange }: PasswordInputProps) => {
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  return (
    <div className="relative">
      <Input
        id="user-password"
        type={showPassword ? "text" : "password"}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full my-2 pr-10 pl-0"
        placeholder="Digitar senha"
        required
        autoFocus
        readOnly={isReadOnly}
        onFocus={(e) => e.target.select()}
        data-testid="input-secret-key"
      />
      <button
        type="button"
        onClick={togglePasswordVisibility}
        className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-80 hover:opacity-100 cursor-pointer focus:outline-none"
        aria-label={showPassword ? "Esconder senha" : "Mostrar senha"}
      >
        {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
      </button>
    </div>
  );
};

type StorageDurationSelectorProps = {
  value: string;
  onChange: (value: string) => void;
};

export const StorageDurationSelector = ({ value, onChange }: StorageDurationSelectorProps) => {
  return (
    <div className="mt-4">
      <label className="block text-sm font-medium mb-2">
        <div className="flex gap-2 items-center">
          <Tooltip>
            <TooltipTrigger className="cursor-help" asChild>
              <Info size={16} />
            </TooltipTrigger>
            <TooltipContent side="right">
              <div className="flex gap-1 items-center text-accent py-1">
                <Info size={14} />
                <Text>Armazenamento temporário</Text>
              </div>
              <Text variant="body-sm">
                Escolha por quanto tempo manter sua senha armazenada para evitar ter que digitar novamente.
              </Text>
            </TooltipContent>
          </Tooltip>
          <Text>Armazenar senha por:</Text>
        </div>
      </label>
      <Select
        options={SK_STORAGE_DURATION}
        value={value}
        onChange={onChange}
        placeholder="Selecione a duração"
        className="w-full"
      />
    </div>
  );
};

export function SecretKeyDialogContent() {
  const [password, setPassword] = useState("");
  const [storageDuration, setStorageDuration] = useState("15m");
  const { updatePassword } = useCredentialsStoreActions();
  const userData = useUserData();

  useEffect(() => {
    updatePassword("");
    (window as any).secretKeyDialogState = { password, setPassword, storageDuration, setStorageDuration };
  }, [password, storageDuration]);

  const phrases = {
    withVerifier: {
      infoTitle: "Você já possui uma senha associada.",
      infoDescription:
        "É necessário inserir a senha novamente para visualizar os dados.",
      inputLabel: "Favor inserir a senha para visualizar os dados:",
    },
    withoutVerifier: {
      infoTitle: "Você ainda não possui uma senha associada.",
      infoDescription:
        "É necessário criar uma senha para associar a sua conta.",
      inputLabel: "Favor inserir uma senha para associar a sua conta:",
    },
  };

  const getPhrase = (key: keyof typeof phrases.withVerifier): string => {
    const userState = getUserState(userData);
    return phrases[userState][key];
  };

  return (
    <div className="">
      <form
        id="secret-key-form"
        onSubmit={(e) => {
          e.preventDefault();
          document.dispatchEvent(new Event("submit-secret-key"));
        }}
      >
        <label htmlFor="user-password" className="flex gap-2 items-center">
          <Tooltip>
            <TooltipTrigger className="cursor-help" asChild>
              <Info size={16} />
            </TooltipTrigger>
            <TooltipContent side="right">
              <div className="flex gap-1 items-center text-accent py-1">
                <Info size={14} />
                <Text>{getPhrase("infoTitle")}</Text>
              </div>
              <Text variant="body-sm">{getPhrase("infoDescription")}</Text>
            </TooltipContent>
          </Tooltip>
          <Text>{getPhrase("inputLabel")}</Text>
        </label>
        <PasswordInput value={password} onChange={(value) => {
          setPassword(value);
          if ((window as any).secretKeyDialogState?.setPassword) {
            (window as any).secretKeyDialogState.setPassword(value);
          }
        }} />
        {!userData?.verifier && (
          <PasswordStrengthMeter password={password} />
        )}

        <StorageDurationSelector
          value={storageDuration}
          onChange={(value) => {
            setStorageDuration(value);
            if ((window as any).secretKeyDialogState?.setStorageDuration) {
              (window as any).secretKeyDialogState.setStorageDuration(value);
            }
          }}
        />
      </form>
    </div>
  );
}

export function SecretKeyDialogFooter({
  onOpen: onOpenUserSecretKeyDialog,
  onSuccess,
}: { onOpen: () => void; onSuccess?: () => void }) {
  const { persistPassword, updatePassword } = useCredentialsStoreActions();
  const { handleVerifier } = useAuthorization();
  const { closeDialog, openDialog } = useDialogActions();
  const { useHasValidVerifiers } = useCredentialStoreEvents();
  const hasValidVerifiers = useHasValidVerifiers();
  const userData = useUserData();
  const { deriveKey } = useEncryption();

  const getSharedState = () => (window as any).secretKeyDialogState || { password: "", storageDuration: "15m" };

  const checkPassword = () => {
    const { password } = getSharedState();
    const trimmedPassword = password?.trim();
    const validation = validatePasswordNIST(trimmedPassword || "");
    if (!validation.isValid) {
      toast.error("Senha insegura", {
        description: validation.errorMessage,
      });
      return false;
    }

    return true;
  };

  const handleOpenSaveDialog = () => {
    if (!checkPassword()) return;

    openDialog({
      title: "salvar senha".toUpperCase(),
      icon: <Save />,
      content: (
        <div className="flex flex-col gap-2">
          <Text variant="body-md">
            Sua senha não ficará armazenada em nosso sistema.
          </Text>
          <Text variant="body-md" className="pb-2">
            Guarde-a em um lugar seguro antes de prosseguir.
          </Text>
          <CustomLabel label="Senha confirmada:" colorClass="bg-accent" />
          <PasswordInput isReadOnly value={getSharedState().password} onChange={() => { }} />
        </div>
      ),
      footer: (
        <>
          <Button variant="default" onClick={onOpenUserSecretKeyDialog}>
            <div className="flex items-center gap-2">
              <Text> Alterar</Text>
              <Undo />
            </div>
          </Button>
          <Button variant="default" onClick={handleSubmit}>
            <div className="flex items-center gap-2">
              <Text> Salvar</Text>
              <Save />
            </div>
          </Button>
        </>
      ),
      className: "max-w-fit"
    })
  };

  const handleSubmit = async () => {
    const { password, storageDuration } = getSharedState();
    const trimmedPassword = password?.trim();
    if (!trimmedPassword) {
      toast.error("Erro ao validar senha.", {
        description: "A senha não pode estar vazia.",
      });
      return;
    }


    const ok = await handleVerifier(trimmedPassword)
    if (ok) {
      const rawKey = await deriveKey(trimmedPassword);
      const storedKey = arrayBufferToBase64(rawKey.buffer as ArrayBuffer);
      updatePassword(storedKey);
      const expirationDate = convertDurationToDate(storageDuration)
      if (expirationDate) {
        persistPassword(expirationDate);
      }
      startNewAuthorizedSession(userData?.roles || []);
      closeDialog();
      toast.success("Senha validada com sucesso!");

      if (onSuccess) {
        onSuccess();
      }
    }
  };

  useEffect(() => {
    const handleFormSubmit = () => handleSubmit();
    document.addEventListener("submit-secret-key", handleFormSubmit);
    return () =>
      document.removeEventListener("submit-secret-key", handleFormSubmit);
  }, [onSuccess]);

  return (
    <Button
      variant="default"
      onClick={!hasValidVerifiers ? handleOpenSaveDialog : handleSubmit}
      className="w-fit !bg-neutral-400"
      data-testid="button-confirm-secret-key"
    >
      <div className="flex items-center gap-2">
        <Text>Confirmar</Text>
        <Check />
      </div>
    </Button>
  );
}

export const SecretKeyDialog = {
  Content: SecretKeyDialogContent,
  Footer: SecretKeyDialogFooter,
};