import React from "react";
import { REPORT_SECTIONS } from "../../config/constants";
import type { ReportSection } from "../../global";
/* print strategies */
import { RenderPrintDadosPessoais } from "./renderPrintDadosPessoais.strategy";
import { RenderPrintMandados } from "./renderPrintMandados.strategy";
import { RenderPrintEndereco } from "./renderPrintEndereco.strategy";
import { RenderPrintEmail } from "./renderPrintEmail.strategy";
import { RenderPrintTelefone } from "./renderPrintTelefone.strategy";
import { RenderPrintProcessos } from "./renderPrintProcessos.strategy";
import { RenderPrintParentes } from "./renderPrintParentes.strategy";
import { RenderPrintVinculosEmpregaticios } from "./renderPrintVinculosEmpregaticios.strategy";
import { RenderPrintDiariosOficiais } from "./renderPrintDiariosOficiais.strategy";
import { RenderPrintPossiveisContatos } from "./renderPrintPossiveisContatos.strategy";
import { RenderPrintPossiveisContas } from "./renderPrintPossiveisContas.strategy";
import { RenderPrintSociedades } from "./renderPrintSociedades.strategy";
import { RenderPrintSocios } from "./renderPrintSocios.strategy";
import { RenderPrintRecursosPublicos } from "./renderPrintRecursosPublicos.strategy";
import { RenderPrintServicosPublicos } from "./renderPrintServicosPublicos.strategy";
import { RenderPrintFiliacaoPartidaria } from "./renderPrintFiliacaoPartidaria.strategy";
import { RenderPrintDoacoesEnviadas } from "./renderPrintDoacoesEnviadas.strategy";
import { RenderPrintDoacoesRecebidas } from "./renderPrintDoacoesRecebidas.strategy";
import { RenderPrintFornecimentosEnviados } from "./renderPrintFornecimentosEnviados.strategy";
import { RenderPrintFornecimentosRecebidos } from "./renderPrintFornecimentosRecebidos.strategy";
import { RenderPrintJuntasComerciais } from "./renderPrintJuntasComerciais.strategy";
import { RenderPrintImagens } from "./renderPrintImagens.strategy";
import { RenderPrintOutrasUrls } from "./renderPrintOutrasUrls.strategy";
import { RenderPrintNomeUsuarios } from "./renderPrintNomeUsuarios.strategy";
import { RenderPrintRedesSociais } from "./renderPrintRedesSociais.strategy";
import { RenderPrintPossiveisPessoasRelacionadas } from "./renderPrintPossiveisPessoasRelacionadas.strategy";
import { RenderPrintVinculosEducacionais } from "./renderPrintVinculosEducacionais.strategy";

export type PrintRenderer = (section: ReportSection) => React.ReactNode;
type ReportType = 'cpf' | 'telefone' | 'email' | 'cnpj';

const common_base_map: Record<string, PrintRenderer> = {
  [REPORT_SECTIONS.emails]: (section) => <RenderPrintEmail section={section}/>,
  [REPORT_SECTIONS.telefones]: (section) => <RenderPrintTelefone section={section}/>,
  [REPORT_SECTIONS.enderecos]: (section) => <RenderPrintEndereco section={section}/>
};

const cpf_telefone_email_map: Record<string, PrintRenderer> = {
  [REPORT_SECTIONS.dados_pessoais]: (section) => <RenderPrintDadosPessoais section={section}/>,
  [REPORT_SECTIONS.parentes]: (section) => <RenderPrintParentes section={section}/>,
  [REPORT_SECTIONS.possiveis_contatos]: (section) => <RenderPrintPossiveisContatos section={section}/>,
  [REPORT_SECTIONS.possiveis_contas_em_sites]: (section) => <RenderPrintPossiveisContas section={section}/>,
  [REPORT_SECTIONS.imagens]: (section) => <RenderPrintImagens section={section}/>,
  [REPORT_SECTIONS.nomes_usuario]: (section) => <RenderPrintNomeUsuarios section={section}/>,
  [REPORT_SECTIONS.outras_urls]: (section) => <RenderPrintOutrasUrls section={section}/>,
  [REPORT_SECTIONS.perfis_redes_sociais]: (section) => <RenderPrintRedesSociais section={section as any}/>,
  [REPORT_SECTIONS.vinculos_educacionais]: (section) => <RenderPrintVinculosEducacionais section={section}/>
};

const cpf_cnpj_map: Record<string, PrintRenderer> = {
  [REPORT_SECTIONS.processos]: (section) => <RenderPrintProcessos section={section}/>,
  [REPORT_SECTIONS.socios]: (section) => <RenderPrintSocios section={section}/>,
  [REPORT_SECTIONS.recursos_publicos_recebidos]: (section) => <RenderPrintRecursosPublicos section={section}/>,
  [REPORT_SECTIONS.doacoes_enviadas_campanha]: (section) => <RenderPrintDoacoesEnviadas section={section}/>,
  [REPORT_SECTIONS.doacoes_recebidas_campanha]: (section) => <RenderPrintDoacoesRecebidas section={section}/>,
  [REPORT_SECTIONS.fornecimentos_enviados_campanha]: (section) => <RenderPrintFornecimentosEnviados section={section}/>,
  [REPORT_SECTIONS.fornecimentos_recebidos_campanha]: (section) => <RenderPrintFornecimentosRecebidos
    section={section}/>,
  [REPORT_SECTIONS.diarios_oficiais_nome]: (section) => <RenderPrintDiariosOficiais section={section}/>
};

const telefone_email_map: Record<string, PrintRenderer> = {
  [REPORT_SECTIONS.empresas_relacionadas]: (section) => <RenderPrintSociedades section={section}/>,
  [REPORT_SECTIONS.possiveis_pessoas_relacionadas]: (section) => <RenderPrintPossiveisPessoasRelacionadas
    section={section}/>
};

const cpf_map: Record<string, PrintRenderer> = {
  [REPORT_SECTIONS.mandados_de_prisao]: (section) => <RenderPrintMandados section={section}/>,
  [REPORT_SECTIONS.sociedades]: (section) => <RenderPrintSociedades section={section}/>,
  [REPORT_SECTIONS.vinculos_empregaticios]: (section) => <RenderPrintVinculosEmpregaticios section={section}/>,
  [REPORT_SECTIONS.diarios_oficiais_cpf]: (section) => <RenderPrintDiariosOficiais section={section}/>,
  [REPORT_SECTIONS.servico_publico]: (section) => <RenderPrintServicosPublicos section={section}/>,
  [REPORT_SECTIONS.filiacao_partidaria]: (section) => <RenderPrintFiliacaoPartidaria section={section}/>
};

const cnpj_map: Record<string, PrintRenderer> = {
  [REPORT_SECTIONS.dados_pessoais]: (section) => <RenderPrintDadosPessoais section={section}/>,
  [REPORT_SECTIONS.juntas_comerciais]: (section) => <RenderPrintJuntasComerciais section={section}/>,
  [REPORT_SECTIONS.diarios_oficiais_cnpj]: (section) => <RenderPrintDiariosOficiais section={section}/>
};

const PrintStrategyMap = new Map<ReportType, Record<string, PrintRenderer>>([
  ['cpf', {...common_base_map, ...cpf_telefone_email_map, ...cpf_cnpj_map, ...cpf_map}],
  ['telefone', {...common_base_map, ...cpf_telefone_email_map, ...telefone_email_map}],
  ['email', {...common_base_map, ...cpf_telefone_email_map, ...telefone_email_map}],
  ['cnpj', {...common_base_map, ...cpf_cnpj_map, ...cnpj_map}]
]);

export function usePrintStrategyMap(reportType: ReportType): Record<string, PrintRenderer> {
  return PrintStrategyMap.get(reportType) || common_base_map;
}

