import React from 'react';
import { View, Text, StyleSheet, Svg, Rect, Path } from '../pdf-components';
import { ReportSection } from '../../global';
import { translatePropToLabel, parseValue, translateSource, formatFieldValue } from '../../helpers';

interface RenderPrintPossiveisContasProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      site?: {
        value: string;
        label: string;
        source: string[];
        is_deleted: boolean;
      };
      detalhes?: Record<string, {
        value: string;
        label: string;
        source: string[];
        is_deleted: boolean;
      }>;
    }>;
  };
}

export const RenderPrintPossiveisContas: React.FC<RenderPrintPossiveisContasProps> = ({ section }) => {
  const validEntries = section.data?.filter(entry =>
    entry.site && !entry.site.is_deleted && entry.detalhes
  ) || [];

  if (!validEntries.length) return null;

  return (
    <View style={styles.container} key={section.title}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill="#FE473C"/>
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>
      <View style={styles.grid}>
        {validEntries.map((entry, index) => {
          const site = entry.site;
          const detalhes = entry.detalhes;

          if (!site || !detalhes) return null;

          const existeValue = parseValue(detalhes.existe?.value || "") || parseValue(detalhes.found?.value || "");

          return (
            <View key={`site-${index}`} style={styles.siteBlock} >
              <View style={styles.subtitleContainer} wrap={false}>
                <Text style={styles.siteTitle}>
                  {(site.label || 'SITE').toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {site.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>

              <View style={styles.siteValueContainer}>
                {existeValue && (
                  existeValue === "Sim" ? (
                    <Svg width="12" height="12" viewBox="0 0 24 24">
                      <Path
                        d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"
                        fill="#4CAF50"
                        stroke="#4CAF50"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </Svg>
                  ) : (

                    <Svg width="12" height="12" viewBox="0 0 24 24">
                      <Path
                        d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"
                        fill="#FE473C"
                        stroke="#FE473C"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </Svg>
                  )
                )}
                <Text style={styles.siteValue}>{formatFieldValue(site.value || "")}</Text>
              </View>

              <View style={styles.fieldsGrid}>
                {Object.entries(detalhes)
                  .filter(([_, value]) => !value.is_deleted)
                  .map(([key, value], fieldIndex) => (
                    <View key={`field-${fieldIndex}`} style={styles.cell}>
                      <View style={styles.infoContainer} wrap={false}>
                        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                          <Rect width="8" height="8" fill='#CCCCCC' />
                        </Svg>
                        <Text style={styles.label}>
                          {(value.label || translatePropToLabel(key)).toUpperCase()}
                        </Text>
                        <Text style={styles.sourceText}>
                          {value.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <Text style={styles.value}>
                        {parseValue(formatFieldValue(value.value))}
                      </Text>
                    </View>
                  ))}
              </View>
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  subtitleContainer: {
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  grid: {
    paddingLeft: 8,
    paddingTop: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  siteBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  siteTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  siteValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 4,
  },
  siteValue: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  fieldsGrid: {
    paddingLeft: 8,
    paddingTop: 6,
  },
  cell: {
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
});