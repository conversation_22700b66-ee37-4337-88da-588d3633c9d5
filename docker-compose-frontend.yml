services:
  frontend:
    image: my-frontend-image
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=development
      - VITE_REPORTS_API_URL=${VITE_REPORTS_API_URL}
      - VITE_PDF_SERVICE_URL=${VITE_PDF_SERVICE_URL}
      - NGINX_REPORTS_API_URL=${NGINX_REPORTS_API_URL}
      - NGINX_PDF_SERVICE_URL=${NGINX_PDF_SERVICE_URL}
      - NGINX_KEYCLOAK_URL=${NGINX_KEYCLOAK_URL}
      - NGINX_SERVER_NAME=${NGINX_SERVER_NAME}
      - NGINX_SSL_CERTIFICATE=${NGINX_SSL_CERTIFICATE}
      - NGINX_SSL_CERTIFICATE_KEY=${NGINX_SSL_CERTIFICATE_KEY}
      - VITE_DOCX_SERVICE_URL=${VITE_DOCX_SERVICE_URL}
      - NGINX_DOCX_SERVICE_URL=${NGINX_DOCX_SERVICE_URL}
    depends_on:
      - pdf
    deploy:
      resources:
        limits:
          cpus: "0.1"
          memory: "128M"
        reservations:
          cpus: "0.06"
          memory: "64M"
    restart: unless-stopped
    networks:
      - mystack-net

networks:
  mystack-net:
    driver: overlay
