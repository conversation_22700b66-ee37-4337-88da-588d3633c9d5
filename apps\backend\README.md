# Backend Documentation

This document provides an overview of the backend services and components in this project. The backend is built using FastAPI and SQLAlchemy for asynchronous database operations.

## Table of Contents

- [Overview](#overview)
- [Directory Structure](#directory-structure)
- [API Endpoints](#api-endpoints)
- [Configuration](#configuration)
- [Database](#database)
- [Authentication](#authentication)
- [<PERSON>rro<PERSON>](#error-handling)
- [Running the Application](#running-the-application)

## Overview

The backend is responsible for handling API requests, managing database interactions, and providing authentication and authorization services. It is designed to be scalable and efficient, leveraging asynchronous programming.

## Directory Structure

- `api/`: Contains the API route definitions.
- `core/`: Core configurations and constants.
- `database/`: Database connection and session management.
- `models/`: Database models.
- `schemas/`: Pydantic models for request and response validation.
- `services/`: Business logic and service layer.
- `utils/`: Utility functions and helpers.
- `init/`: Initialization scripts and setup.

## API Endpoints

### WebSocket

- **`reports/ws/snap-status/{user_id}/{user_report_id}`**: WebSocket endpoint for real-time status updates.

### Reports

- **`POST reports/insert-report`**: Insert a new report.
- **`GET reports/get-one-report`**: Retrieve a specific report by ID.
- **`POST reports/get-data-from-snap-api`**: Fetch data from the SNAP API.
- **`GET reports/get-saved-reports`**: Retrieve saved reports with filters.

### Authentication

- **`GET reports/auth/user`**: Get authenticated user details.
- **`POST reports/logout`**: Logout the user.

### Verifier

- **`POST reports/verifier`**: Insert a verifier for a user.

### Reports Management

- **`POST reports/merge-reports`**: Merge multiple reports.
- **`POST reports/update-report`**: Update an existing report.

## Configuration

Configuration settings are managed in the `core/config.py` file. Environment variables are loaded from the `.env` file.

## Database

The application uses SQLAlchemy with an asynchronous engine for database operations. The database connection is managed in the `database/db.py` file.

## Authentication

Authentication is handled using JWT tokens. The `auth_service` provides middleware for protecting routes and verifying user credentials.

## Error Handling

Custom error messages and HTTP exceptions are defined in `core/constants.py`. These are used throughout the application to provide consistent error responses.

## Running the Application

To run the application, ensure you have Python and the required dependencies installed. You can start the server using:

Development:
```bash
uvicorn main:app --reload
```

Production (Gunicorn + Uvicorn workers):
```bash
gunicorn -k uvicorn.workers.UvicornWorker -w "$(python -c 'import os; print(2*os.cpu_count()+1)')" -b 0.0.0.0:8000 main:app
```

Tuning notes:
- **Workers**: start with 2 x CPU cores + 1. For CPU‑bound endpoints, cap at cores; for I/O‑heavy workloads, you can go higher but monitor latency and memory.
- **Timeouts**: `GUNICORN_TIMEOUT` 120s default; adjust to your slowest legitimate request.
- **Max requests**: set `GUNICORN_MAX_REQUESTS` (e.g., 1000) with jitter to mitigate leaks.
- **Env overrides** (already wired via compose): `GUNICORN_WORKERS`, `GUNICORN_BIND`, `GUNICORN_TIMEOUT`, `GUNICORN_GRACEFUL_TIMEOUT`, `GUNICORN_KEEPALIVE`, `GUNICORN_MAX_REQUESTS`, `GUNICORN_MAX_REQUESTS_JITTER`, `GUNICORN_LOG_LEVEL`, `GUNICORN_ACCESS_LOG`.

Ensure that your environment variables are set correctly in the `.env` file.

---


# We have 2 main process for now:

1) Create report: o processo deste está detalhado melhor no Miro. Mas o frontend chamará o endpoint /get-data-from-snap-api e a partir dai irá ser criado/atualizado um relatorio com erro já existente. O processo ocorre em background com a função snap_status_ws, que fica verificando na API do snap se o resultado já está pronto.
Caso o resultado esteja pronto será enviado uma notificação para o browser via websocket que irá atualizar os resultados.
O websocket enviará os resultados formatados pelo spark via websocket, como também os ngrams para serem criptografados e envaido para o endpoint /insert-report/{user_reports_id}, ele atualizara o banco de dados com as informações e também adicionará os ngrams criptografados.
2) List saved reports: o endpoint /get-saved-reports, trás do banco de dados todos os relatórios do usuário, por padrão os resultados estão vindo em ordem descendente pela data de criação. É possível alterar as configurações de filtro como número de relatórios para mostrar (limit, padrão 10), page(padrão 1),
order (desc ou asc), column_order (qual coluna será responsável pela ordenação), hmac_filter(irá avaliar no banco de dados user_columns_hmac os user_reports_id correspondente) e hmac_column (caso queira filtrar especificamente por aquela coluna na tabela de user_columns_hmac).