services:
  keycloak:
    image: ${KC_IMAGE:-my-keycloak-image}

    deploy:
      replicas: ${KC_REPLICAS:-1}
      resources:
        limits:
          cpus: ${KC_CPU_LIMIT:-2.0}
          memory: ${KC_MEMORY_LIMIT:-3G}
        reservations:
          cpus: ${KC_CPU_RESERVATION:-0.5}
          memory: ${KC_MEMORY_RESERVATION:-1G}
      restart_policy:
        condition: ${KC_RESTART_CONDITION:-on-failure}
        delay: ${KC_RESTART_DELAY:-10s}
        max_attempts: ${KC_RESTART_MAX_ATTEMPTS:-5}
        window: ${KC_RESTART_WINDOW:-120s}
      placement:
        constraints:
          - node.role == manager
        max_replicas_per_node: ${KC_MAX_REPLICAS_PER_NODE:-1}
      update_config:
        parallelism: ${KC_UPDATE_PARALLELISM:-1}
        delay: ${KC_UPDATE_DELAY:-30s}
        failure_action: rollback
        monitor: 60s

    secrets:
      - keycloak_admin_user
      - keycloak_admin_password

    environment:
      # Environment configuration
      KC_ENV: ${KC_ENV:-production}
      VM_IP: ${VM_IP}

      # Admin credentials
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN:-admin}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD:-admin123}
      KEYCLOAK_URL: ${KEYCLOAK_URL:-http://localhost:8080}

      # Database configuration
      KC_DB: ${KC_DB:-postgres}
      KC_DB_URL: ${KC_DB_URL:-*******************************************************************************}
      KC_DB_USERNAME: ${DB_USER}
      KC_DB_PASSWORD: ${DB_PASS}
      KC_DB_SCHEMA: keycloak
      KC_DB_POOL_INITIAL_SIZE: ${KC_DB_POOL_INITIAL_SIZE:-5}
      KC_DB_POOL_MIN_SIZE: ${KC_DB_POOL_MIN_SIZE:-5}
      KC_DB_POOL_MAX_SIZE: ${KC_DB_POOL_MAX_SIZE:-20}
      KC_DB_POOL_MAX_AGE: ${KC_DB_POOL_MAX_AGE:-300}
      # Proxy configuration - environment specific
      KC_PROXY_HEADERS: ${KC_PROXY_HEADERS:-xforwarded}
      KC_PROXY_TRUSTED_ADDRESSES: ${KC_PROXY_TRUSTED_ADDRESSES:-********/24}
      KC_HTTP_ENABLED: ${KC_HTTP_ENABLED:-true}
      KC_HTTP_RELATIVE_PATH: ${KC_HTTP_RELATIVE_PATH}

      # Hostname configuration
      KC_HOSTNAME_STRICT: ${KC_HOSTNAME_STRICT:-false}
      KC_HOSTNAME_STRICT_BACKCHANNEL: ${KC_HOSTNAME_STRICT_BACKCHANNEL:-false}
      KC_HOSTNAME_STRICT_HTTPS: ${KC_HOSTNAME_STRICT_HTTPS:-false}
      KC_SPI_HOSTNAME_DEFAULT_ADMIN_URL: ${KC_SPI_HOSTNAME_DEFAULT_ADMIN_URL:-""}
      KC_HOSTNAME_ADMIN_URL: ${KC_HOSTNAME_ADMIN_URL:-""}
      KC_HOSTNAME_URL: ${KC_HOSTNAME_URL:-""}

      # Frontend URL configuration
      KC_SPI_HOSTNAME_DEFAULT_FRONTEND_URL: ${KC_SPI_HOSTNAME_DEFAULT_FRONTEND_URL:-https://reportsbeta.snapforensics.com/authkc}
      KC_SPI_HOSTNAME_DEFAULT_FORCE_BACKEND_URL_TO_FRONTEND_URL: ${KC_SPI_HOSTNAME_DEFAULT_FORCE_BACKEND_URL_TO_FRONTEND_URL:-false}
      KC_SPI_HOSTNAME_DEFAULT_ALWAYS_HTTPS: ${KC_SPI_HOSTNAME_DEFAULT_ALWAYS_HTTPS:-false}

      # Additional reverse proxy configuration
      KC_SPI_X_FORWARDED_PROTOCOL_ENABLED: ${KC_SPI_X_FORWARDED_PROTOCOL_ENABLED:-true}
      KC_SPI_X_FORWARDED_HOST_ENABLED: ${KC_SPI_X_FORWARDED_HOST_ENABLED:-true}
      KC_SPI_X_FORWARDED_PREFIX_ENABLED: ${KC_SPI_X_FORWARDED_PREFIX_ENABLED:-true}

      # Session and sticky session configuration
      KC_SPI_STICKY_SESSION_ENCODER: ${KC_SPI_STICKY_SESSION_ENCODER:-infinispan}

      # Security settings
      KC_IMPORT: ${KC_IMPORT:-/opt/keycloak/data/import/realm.json}
      KC_FEATURES: ${KC_FEATURES:-admin-fine-grained-authz,token-exchange,admin2}

      # Health and performance
      KC_HEALTH_ENABLED: ${KC_HEALTH_ENABLED:-true}
      KC_HTTP_MAX_QUEUED_REQUESTS: ${KC_HTTP_MAX_QUEUED_REQUESTS:-1000}
      KC_HTTP_POOL_MAX_THREADS: ${KC_HTTP_POOL_MAX_THREADS:-200}
      KC_TRANSACTION_XA_ENABLED: ${KC_TRANSACTION_XA_ENABLED:-false}

      # Logging configuration
      KC_LOG_LEVEL: ${KC_LOG_LEVEL:-INFO}
      KC_LOG_CONSOLE_OUTPUT: ${KC_LOG_CONSOLE_OUTPUT:-default}
      KC_LOG_CONSOLE_FORMAT: ${KC_LOG_CONSOLE_FORMAT:-"%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c] (%t) %s%e%n"}

      # Cache and clustering
      KC_CACHE: ${KC_CACHE:-ispn}

      # Metrics
      KC_METRICS_ENABLED: ${KC_METRICS_ENABLED:-true}
      KC_HTTP_MANAGEMENT_PORT: ${KC_HTTP_MANAGEMENT_PORT:-9000}

      # Performance tuning
      JAVA_OPTS_APPEND: ${JAVA_OPTS_APPEND}

      # Cookie security
      KC_SPI_COOKIE_SAME_SITE: ${KC_SPI_COOKIE_SAME_SITE:-None}
      KC_SPI_COOKIE_SECURE: ${KC_SPI_COOKIE_SECURE:-false}
      KC_SPI_COOKIE_DOMAIN: ${KC_SPI_COOKIE_DOMAIN:-""}
      KC_SPI_COOKIE_HTTP_ONLY: ${KC_SPI_COOKIE_HTTP_ONLY:-false}
      KC_SPI_COOKIE_PATH: ${KC_SPI_COOKIE_PATH:-/authkc}

      # Session configuration
      KC_SPI_LOGIN_PROTOCOL_OPENID_CONNECT_LEGACY_LOGOUT_REDIRECT_URI: ${KC_SPI_LOGIN_PROTOCOL_OPENID_CONNECT_LEGACY_LOGOUT_REDIRECT_URI:-true}

      # Security headers
      KC_SPI_SECURITY_HEADERS_CONTENT_SECURITY_POLICY: ${KC_CSP}
      KC_SPI_SECURITY_HEADERS_X_FRAME_OPTIONS: ${KC_X_FRAME_OPTIONS:-DENY}
      KC_SPI_SECURITY_HEADERS_X_CONTENT_TYPE_OPTIONS: ${KC_X_CONTENT_TYPE_OPTIONS:-nosniff}
      KC_SPI_SECURITY_HEADERS_X_XSS_PROTECTION: ${KC_X_XSS_PROTECTION:-1; mode=block}
      KC_SPI_SECURITY_HEADERS_STRICT_TRANSPORT_SECURITY: ${KC_HSTS}

    volumes:
      - keycloak_data:/opt/keycloak/data
      - keycloak_heapdumps:/opt/keycloak/data/heapdumps
      - ${KC_REALM_FILE:-./apps/keycloak/realm.json}:/opt/keycloak/data/import/realm.json
      - ${KC_CACHE_FILE:-./apps/keycloak/cache/cache-ispn-jdbc-ping-production.xml}:/opt/keycloak/conf/cache-ispn-jdbc-ping.xml

    networks:
      - ${KC_NETWORK:-mystack-net}

    ports:
      - "${KC_PORT:-8080}:8080"

    depends_on:
      - postgres

    healthcheck:
      test:
        - "CMD-SHELL"
        - |
          exec 3<>/dev/tcp/localhost/9000
          echo -e "GET /health/ready HTTP/1.1\r\nhost: localhost:9000\r\n\r\n" >&3
          timeout 3 cat <&3 | grep -q "UP"
          exec 3<&-
      interval: ${KC_HEALTHCHECK_INTERVAL:-30s}
      timeout: ${KC_HEALTHCHECK_TIMEOUT:-10s}
      retries: ${KC_HEALTHCHECK_RETRIES:-3}
      start_period: ${KC_HEALTHCHECK_START_PERIOD:-900s}

    logging:
      driver: ${KC_LOG_DRIVER:-json-file}
      options:
        max-size: ${KC_LOG_MAX_SIZE:-100m}
        max-file: ${KC_LOG_MAX_FILE:-3}
        compress: ${KC_LOG_COMPRESS:-true}

volumes:
  keycloak_data:
  keycloak_heapdumps:

networks:
  mystack-net:
    external: true
