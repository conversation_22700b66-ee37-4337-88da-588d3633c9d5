{"user_reports_id": "fea0a198-6110-4621-9ef0-16b667317492", "report_name": "Relatório Telefone fulaninho de tal", "report_type": "telefone", "report_status": "success", "report_search_args": {"telefone": "31996264775"}, "subject_name": "<PERSON><PERSON><PERSON><PERSON>l", "subject_mother_name": "Siclaninha de tal", "subject_age": "1970-06-09", "subject_sex": "M", "created_at": "2025-09-10T14:36:21.490454+00:00", "modified_at": "2025-09-10T14:36:21.490454+00:00", "omitted_notes": null, "subject_person_count": 0, "subject_company_count": 0, "data": {"cpf": [{"title": "<PERSON><PERSON>", "subtitle": "Dados consultados na API SNAP.", "subsection": "", "is_deleted": false, "sources": ["SNAP"], "user_reports_id": "fea0a198-6110-4621-9ef0-16b667317492", "data_count": 4, "data": [{"full_name": {"value": "FULANO DA SILVA", "label": "Nome", "source": ["SNAP"], "is_deleted": false}, "nome_da_mae": {"value": "SICLANA DA SILVA", "label": "<PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "status receita": {"value": "REGULAR", "label": "Status na Receita", "source": ["SNAP"], "is_deleted": false}, "país do passaporte": {"value": "BRASIL", "label": "país do passaporte", "source": ["SNAP"], "is_deleted": false}}]}]}}