import React from 'react';
import { View, Text, StyleSheet } from './pdf-components';

interface SummaryItem {
  title: string;
  data_count: number;
}

interface PrintSummaryProps {
  items: SummaryItem[];
}

export const PrintSummary: React.FC<PrintSummaryProps> = ({
  items
}) => {
  const total = items.reduce((acc, item) => acc + item.data_count, 0);

  const midpoint = Math.ceil(items.length / 2);
  const firstColumn = items.slice(0, midpoint);
  const secondColumn = items.slice(midpoint);

  const renderColumn = (columnItems: SummaryItem[]) => (
    <View style={styles.column}>
      {columnItems.map((item, idx) => (
        <View key={idx} style={styles.registroItem}>
          <Text style={styles.registroTitle}>{item.title}</Text>
          <View style={styles.countBadge}>
            <Text style={styles.countText}>{item.data_count}</Text>
          </View>
        </View>
      ))}
    </View>
  );

  return (
    <View style={styles.summaryContainer}>
      <View style={styles.registrosSection}>
        <Text style={styles.registrosHeader}>
          <Text style={styles.totalNumber}>{total}</Text>
          {' REGISTROS ENCONTRADOS'}
        </Text>

        <View style={styles.registrosList}>
          {renderColumn(firstColumn)}
          {renderColumn(secondColumn)}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  summaryContainer: {
    width: "100%",
    paddingVertical: 8,
    marginBottom: 16,
  },
  registrosSection: {
    backgroundColor: "#F9F9FA",
    borderRadius: 4,
    padding: 10,
  },
  registrosHeader: {
    fontSize: 11,
    fontWeight: "bold",
    color: "#374151",
    marginBottom: 12,
    letterSpacing: 0.5,
  },
  totalNumber: {
    color: "#FE473C",
  },
  registrosList: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  column: {
    width: "48%",
  },
  registroItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginVertical: 4,
  },
  registroTitle: {
    fontSize: 11,
    fontWeight: "bold",
    color: "#6B7280",
    flex: 1,
  },
  countBadge: {
    backgroundColor: "#6B7280",
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 3,
    minWidth: 32,
    alignItems: "center",
  },
  countText: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#FFFFFF",
    textAlign: "center",
  },
});
