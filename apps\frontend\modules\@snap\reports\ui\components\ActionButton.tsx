import React from 'react';
import { LiaTrashRestoreAltSolid, LiaTrashAltSolid } from "react-icons/lia";
import { LoaderIcon } from 'lucide-react';
import { Loading } from '@snap/design-system';
import { useActionLoading, useIsSaving } from '../../context/ReportContext';

interface ActionButtonProps {
  onClick: (e: React.MouseEvent) => void;
  title: string;
  isTrashMode: boolean;
  className?: string;
  size: 'sm' | 'default' | 'lg';
}

export const ActionButton: React.FC<ActionButtonProps> = ({
  onClick,
  title,
  isTrashMode,
  className = "",
  size = 'lg'
}) => {
  const { isActionLoading } = useActionLoading();
  const isSaving = useIsSaving();
  const isDisabled = isSaving || isActionLoading;

  const iconSize = size === 'sm' ? 16 : size === 'lg' ? 32 : 24;

  const handleClick = (e: React.MouseEvent) => {
    if (!isDisabled) {
      onClick(e);
    }
  };

  return (
    <span
      onClick={handleClick}
      title={isDisabled ? "Salvando alterações..." : title}
      className={`
        flex items-center justify-center
        w-10 h-10
        opacity-0
        group-hover:opacity-100
        ${isDisabled ? '!cursor-default' : 'cursor-pointer'}
        rounded-full
        bg-background/10
        hover:bg-background/20
        transition-opacity duration-200
        ${className}
      `}
    >
      {isDisabled ? (
        <Loading size={size} />
      ) : isTrashMode ? (
        <LiaTrashRestoreAltSolid
          size={iconSize}
          color={"var(--foreground)"}
        />
      ) : (
        <LiaTrashAltSolid
          size={iconSize}
          color={"var(--primary)"}
        />
      )}
    </span>
  );
};

ActionButton.displayName = 'ActionButton';