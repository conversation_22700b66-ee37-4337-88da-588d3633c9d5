/**
 * Tipos e constantes para o sistema de permissões baseado em roles
 */
export enum Permission {
  CREATE_REPORT = "role-create-report",
  VIEW_REPORT_LIST = "role-view-report-list",
  VIEW_REPORT_DETAILS = "role-view-report-details",
  UPDATE_REPORT = "role-update-report",
  INVITE_USER = "role-invite-user",
  CANCEL_INVITE = "role-cancel-invite",
  GET_USER_INVITE = "role-get-user-invite",
  GET_DATA_FROM_USER_INVITE = "role-get-data-from-user-invite",
  GET_INVITE_DETAILS = "role-get-invite-details",
  ANSWER_INVITE = "role-answer-invite",
  EDIT_USER = "role-edit-user",
  GET_USER_LOGS = "role-get-user-logs",
  GET_ORGANIZATION_LOGS = "role-get-organization-logs",
  GET_ORGANIZATION_INVITE = "role-get-organization-invite",
  ADD_API_KEY = "role-add-api-key",
  REMOVE_ORGANIZATION = "role-remove-organization",
  PRINT_SNAP_LOGO = "role-print-snap-logo",
  ACCEPT_TERMS = "role-accept-terms",
}

// Tipo para função de verificação de permissão
export type PermissionChecker = () => boolean;

// Interface para configuração de rota protegida
export interface RoutePermissionConfig {
  path: string;
  permission: Permission;
  exact?: boolean;
}

// Mapeamento de rotas para permissões necessárias
export const ROUTE_PERMISSIONS: RoutePermissionConfig[] = [
  {
    path: "/",
    permission: Permission.VIEW_REPORT_LIST,
    exact: true,
  },
  {
    path: "/report/:type/:id",
    permission: Permission.VIEW_REPORT_DETAILS,
    exact: false,
  },
  // Nota: /conta/configuracoes não está incluída aqui pois deve ser sempre acessível para usuários autenticados
];

// Função utilitária para verificar se uma rota requer permissão específica
export const getRequiredPermissionForPath = (pathname: string): Permission | null => {
  for (const config of ROUTE_PERMISSIONS) {
    if (config.exact) {
      if (pathname === config.path) {
        return config.permission;
      }
    } else {
      // Para rotas com parâmetros, verificação mais flexível
      const pathPattern = config.path.replace(/:[^/]+/g, '[^/]+');
      const regex = new RegExp(`^${pathPattern}$`);
      if (regex.test(pathname)) {
        return config.permission;
      }
    }
  }
  return null;
};

// Função utilitária para verificar se o usuário tem permissão para uma rota específica
export const hasPermissionForRoute = (
  pathname: string,
  permissionChecker: (permission: Permission) => boolean
): boolean => {
  const requiredPermission = getRequiredPermissionForPath(pathname);

  if (!requiredPermission) {
    return true;
  }

  return permissionChecker(requiredPermission);
};

// Constantes para rotas especiais
export const SPECIAL_ROUTES = {
  NO_PERMISSION: "/sem-permissao",
  LOGIN: "/login",
  HOME: "/",
  ACCOUNT: "/conta/configuracoes",
} as const;

export type SpecialRoute = typeof SPECIAL_ROUTES[keyof typeof SPECIAL_ROUTES];
