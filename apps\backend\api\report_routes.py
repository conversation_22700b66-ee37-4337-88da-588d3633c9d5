import logging
import random
logger = logging.getLogger(__name__)
from fastapi import (APIR<PERSON><PERSON>, Request, Depends, WebSocket, WebSocketDisconnect, 
                     Query, HTTPException, Response)
from fastapi.encoders import jsonable_encoder
import sqlalchemy
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
from typing import Optional, List
import uuid
from datetime import datetime
import pytz
import time
import time

from database.db import get_db, get_database_async_engine, get_pool_status, async_session

from services.report_service import UserReportsService
from services.minio_service import load_from_minio
from services.auth_service import auth_guard
from services.credits_service import CreditsService
from services.organization_users_service import OrganizationUsersService
from services.organization_service import OrganizationService
from services.user_service import UserStandaloneService
from services.apikey_service import ApikeyService
from services.invite_service import InviteService
from services.report_executions_service import ReportExecutionsService
from services.eula_service import EulaService

from schemas.report_schema import SnapApiRequest, In<PERSON>tReport, InsertVerifier, ReportGetByFolder, RenameReport

from core.jwt_utils import logout_user_using_token
from core.constants import (DefaultReports, ReportStatus,
                            SummaryReportStatus, ReportMockValidator,DefaultPageLogs, Roles)

from models.enums import invite_status

from utils.jwt_utils import JWTUtils
from exceptions.business_exceptions import (
    InvitePermissionDeniedError,
    InvalidReportRequestFormatError,
    UserIdNotFoundTokenError,
    UserNotFoundError,
    FailToAccessDataToGetPendingReportsApiError,
    InputValueSnapWrongError,  # Added import
    SnapApiNoIdError,  # Added import
    ApiCreditsServiceUnavailableError,
    ApiCreditsFetchError,
    ReportOfThisProcessAlreadyRunning
)
from exceptions.base_exceptions import InternalServerError  # Add this import if missing


async def start_secondary_function(
    user_id: str,
    original_request: Request = None,
    original_response: Response = None
) -> None:
    """
    Secondary function that starts after 720 seconds if the main task is still running.
    This function triggers a re-authentication check using auth_guard.
    
    Args:
        user_id: User identifier
        original_request: Original request object for auth_guard
        original_response: Original response object for auth_guard
    """
    logger.info(f"[start_secondary_function][user({user_id})] Starting secondary auth_guard function")
    
    try:
        
        # Log that secondary auth_guard function is running
        logger.info(f"[start_secondary_function][user({user_id})] Secondary auth_guard function is now running in parallel with main task")

        
        # Call auth_guard function for re-authentication
        if original_request and original_response:
            try:
                from services.auth_service import auth_guard
                logger.info(f"[start_secondary_function][user({user_id})] Calling auth_guard for re-authentication at {datetime.now(pytz.timezone('UTC'))}")
                
                # Log request details for debugging
                logger.info(f"[start_secondary_function][user({user_id})] Original request cookies: {dict(original_request.cookies) if original_request.cookies else 'None'}")
                logger.info(f"[start_secondary_function][user({user_id})] Original request headers: {dict(original_request.headers) if original_request.headers else 'None'}")
                
                # Call auth_guard with the original request and response
                auth_result = await auth_guard(original_request, original_response)
                
                logger.info(f"[start_secondary_function][user({user_id})] auth_guard completed successfully for user_id={user_id}")
                logger.info(f"[start_secondary_function][user({user_id})] Auth result keys: {list(auth_result.keys()) if auth_result else 'None'}")
                logger.info(f"[start_secondary_function][user({user_id})] Auth result user_id: {auth_result.get('sub') if auth_result else 'None'}")
                
                
            except Exception as auth_error:
                logger.error(f"[start_secondary_function][user({user_id})] auth_guard failed: {str(auth_error)}")
                logger.error(f"[start_secondary_function][user({user_id})] auth_guard error type: {type(auth_error).__name__}")
                
                
        else:
            logger.warning(f"[start_secondary_function][user({user_id})] Cannot call auth_guard - missing original request or response objects")
            logger.warning(f"[start_secondary_function][user({user_id})] original_request: {original_request is not None}, original_response: {original_response is not None}")
        

        
        logger.info(f"[start_secondary_function][user({user_id})] Secondary auth_guard function completed successfully")
        
    except asyncio.CancelledError:
        logger.info(f"[start_secondary_function][user({user_id})] Secondary auth_guard function was cancelled (likely due to browser close)")
        # Re-raise the cancellation to properly handle it
        raise
    except Exception as e:
        logger.error(f"[start_secondary_function][user({user_id})] Error in secondary auth_guard function: {str(e)}")
        # Don't raise the exception to avoid affecting the main task
        # Just log it for monitoring purposes


router = APIRouter()


@router.websocket("/ws/snap-status/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    app = websocket.app
    manager = app.state.connection_manager

    logger.info(f"[websocket_endpoint][user({user_id})] WebSocket connection request received.")

    await manager.connect(websocket, user_id)
    logger.info(f"[websocket_endpoint][user({user_id})] WebSocket connection established and registered with manager.")
    

    try:
        while True:
            logger.info(f"[websocket_endpoint][user({user_id})] Starting new pending reports check loop.")
            
            db_gen = get_db()
            try:
                logger.debug(f"[websocket_endpoint][user({user_id})] Attempting to connect to DB for pending reports fetch.")
                db = await anext(db_gen)
                logger.info(f"[websocket_endpoint][user({user_id})] Connected to DB.")
            except Exception as e:
                logger.error(f"[get_pending_reports_by_user][user({user_id})] DB connection failed: {e}")
                raise FailToAccessDataToGetPendingReportsApiError()
            
            temp_service = UserReportsService(db=db, user_id=user_id)
            logger.debug(f"[websocket_endpoint][user({user_id})] Fetching pending reports from service.")
            pending_reports = await temp_service.get_pending_reports()
            pending_reports_no = len(pending_reports)
            logger.info(f"[websocket_endpoint][user({user_id})] Found {pending_reports_no} pending reports.")

            for report in pending_reports:
                report_id, report_type, report_status = report
                logger.info(f"[websocket_endpoint][user({user_id})] Processing pending report: id={report_id}, type={report_type}, status={report_status}")
                object_name = f"{user_id}_{report_id}.json"

                logger.debug(f"[websocket_endpoint][user({user_id})] Attempting to load report result from MinIO: object_name={object_name}")
                result = await load_from_minio(
                    bucket_name="processed-reports",
                    object_name=object_name,
                    user_id=user_id
                )

                if not result:
                    logger.warning(f"[websocket_endpoint][user({user_id})] Report {report_id} (type={report_type}) not found in MinIO bucket 'processed-reports' after polling.")
                    continue  # Optionally: send a status to the client about missing file

                logger.info(f"[websocket_endpoint][user({user_id})] Successfully loaded result from MinIO for report {report_id}.")
                message = {
                    "id": report_id,
                    "status_code": SummaryReportStatus.success,
                    "result": result,
                }
                safe_message = jsonable_encoder(message)

                try:
                    logger.debug(f"[websocket_endpoint][user({user_id})] Sending report result via WebSocket for report {report_id}.")
                    await websocket.send_json(safe_message)
                    logger.info(f"[websocket_endpoint][user({user_id})] Sent WebSocket report result for report {report_id}.")
                    report_executions_service = ReportExecutionsService(db=db, user_id=user_id)
                    await report_executions_service.update_column(
                        user_reports_id=report_id,
                        column_name="dt_envio_websocket",
                        value=datetime.now(pytz.timezone("UTC"))
                    )

                except Exception as send_err:
                    logger.warning(f"[websocket_endpoint][user({user_id})] Failed to send WebSocket message for report {report_id}: {send_err}")
                    continue

            logger.info(f"[websocket_endpoint][user({user_id})] Finished sending websocket results for {pending_reports_no} pending reports in this loop.")
            logger.debug(f"[websocket_endpoint][user({user_id})] Sleeping before next pending reports check.")
            sleep_time = random.randint(15, 30)
            await asyncio.sleep(sleep_time)
            
            logger.debug(f"[websocket_endpoint][user({user_id})] Checking for new pending reports after sleep.")
            pending_reports = await temp_service.get_pending_reports()
            pending_reports_no = len(pending_reports)
            
            if len(pending_reports) == 0:
                logger.info("[pending_reports_loop][user(%s)] No pending reports found.", user_id)
                break

    except WebSocketDisconnect:
        logger.info(f"[websocket_endpoint][user({user_id})] WebSocket disconnected for request.")
    finally:
        await manager.disconnect(user_id)
        logger.info(f"[websocket_endpoint][user({user_id})] Connection closed and cleaned up.")


@router.put("/insert-report/{user_reports_id}")
async def insert_report(request: InsertReport, user_reports_id: str, user=Depends(auth_guard), db: AsyncSession = Depends(get_db)):
    request_keys = list(request.model_dump().keys())

    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[insert-report] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.update_report not in user_roles:
        logger.warning("[insert-report] Access denied - User %s does not have update report role", user_id)
        raise InvitePermissionDeniedError("atualizar relatório")

    # Create service with user_reports_id
    user_reports_service = UserReportsService(db=db, user_id=user_id, user_reports_id=user_reports_id)
    
    # Use the service to handle the report insertion
    await user_reports_service.populate_report_handler(body=request)
    
    return {"message": "Report updated successfully"}


@router.get("/get-one-report/{user_reports_id}")
async def get_one_report(user_reports_id: str, user=Depends(auth_guard), db: AsyncSession = Depends(get_db)):
    logger.info("[get_one_report][user(%s)] Fetching report %s.", user, user_reports_id)

    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[get-one-report] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.view_report_details not in user_roles:
        logger.warning("[get-one-report] Access denied - User %s does not have view report details role", user_id)
        raise InvitePermissionDeniedError("visualizar detalhes do relatório")
    
    user_reports_service = UserReportsService(db=db, user_id=user_id, user_reports_id=user_reports_id)
    return await user_reports_service.get_one_report_handler()


@router.get("/get-logs")
async def get_logs_endpoint(limit: int = Query(DefaultPageLogs.pagedefault),
    page: int = Query(1),
    order: str = Query("desc", description="Sort direction: 'asc' or 'desc'"),
    column_order: str = Query("created_at", description="Column to order by"),
    is_user: Optional[bool] = Query(True, description="Column to choose if is user or org filter"),
    report_type: Optional[str] = Query(None, description="Type of report"),
    created_start_at: Optional[datetime] = Query(None, description="Intial date to filter"),
    created_end_at: Optional[datetime] = Query(None, description="Final date to filter"),
    user: dict = Depends(auth_guard),
    db: AsyncSession = Depends(get_db)):

    logger.info(f"[get_logs_endpoint] Called with params: limit={limit}, page={page}, order={order}, column_order={column_order}, is_user={is_user}, report_type={report_type}, created_start_at={created_start_at}, created_end_at={created_end_at}")
    logger.info(f"[get_logs_endpoint] Authenticated user: {user}")
    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    organization_id= None
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[get_logs_endpoint] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.get_user_logs not in user_roles:
        logger.warning("[get_logs_endpoint] Access denied - User %s does not have get user logs role", user_id)
        raise InvitePermissionDeniedError("obter logs do usuário")

    if Roles.get_organization_logs in user_roles:
        logger.info(f"[get_logs_endpoint] User has organization logs role. Fetching organization id for user {user_id}")
        is_user=False
        organization_user_service = OrganizationUsersService(db=db, user_id=user_id)
        organization_user_data = await organization_user_service.get_organization_user()
        logger.info(f"[get_logs_endpoint] organization_user_data: {organization_user_data}")
        organization_id = organization_user_data.organization_id
        logger.info(f"[get_logs_endpoint] Updated id to organization_id: {organization_id}")

    user_reports_service = UserReportsService(db=db, user_id=user_id, organization_id=organization_id)
    logger.info(f"[get_logs_endpoint] Created UserReportsService for user_id: {user_id}")

    result = await user_reports_service.get_all_logs(limit=limit, page=page, order=order, column_order=column_order,
                                               is_user=is_user, report_type=report_type, 
                                               created_start_at=created_start_at, created_end_at=created_end_at)
    logger.info(f"[get_logs_endpoint] get_all_logs result: {result}")

    return result


@router.post("/get-data-from-snap-api")
async def get_data_from_snap_api(
        request: Request,
        response: Response,
        user: dict = Depends(auth_guard),
        db: AsyncSession = Depends(get_db)):
    

    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[get_data_from_snap_api] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.create_report not in user_roles:
        logger.warning("[get_data_from_snap_api] Access denied - User %s does not have create report role", user_id)
        raise InvitePermissionDeniedError("criar relatório")

    organization_user_services = OrganizationUsersService(db=db, user_id=user_id)
    org_data = await organization_user_services.get_organization_user()
    organization_id = None
    if org_data:
        organization_id = org_data.organization_id
        logger.info(f"[get_data_from_snap_api] Active organization found for user: {user_id}")


    # Log request information with proper redaction
    raw_body = await request.body()
    safe_body = f"Length: {len(raw_body)} bytes"  # Avoid logging potentially sensitive data
    logger.info(f"[get_data_from_snap_api][user({user_id})] Request received: {safe_body}")

    # Parse request body with proper error handling
    try:
        parsed_body = SnapApiRequest(**await request.json())
        logger.info(f"[get_data_from_snap_api][user({user_id})] Processing request type: {parsed_body.report_type}")
    except ValueError as e:
        logger.error(f"[get_data_from_snap_api][user({user_id})] Invalid request format: {str(e)}")
        raise InvalidReportRequestFormatError(reason=str(e))
    except Exception as e:
        logger.error(f"[get_data_from_snap_api][user({user_id})] Failed to parse request: {str(e)}")
        raise InvalidReportRequestFormatError(reason=f"Falha ao processar requisição: {str(e)}")

    # Create report status information
    report_status = {"status_report": ReportStatus.InProgress.pending_low_case}
    if parsed_body.report_type=="relacoes":
        report_search_args = parsed_body.report_input_value
    else:
        report_search_args = {parsed_body.report_type: parsed_body.report_input_value}

    user_reports_id = parsed_body.user_reports_id
    client_id = None
    app = request.app
    # Deduct quota for the API call

    user_reports_service=UserReportsService(db=db, user_id=user_id, user_reports_id=user_reports_id, organization_id=organization_id)
    user_reports_service.set_user_data(user_data=user)

    api_key = await user_reports_service.validate_access_to_report(report_type=parsed_body.report_type)

    # Process the request with retry logic
    max_retries = 3
    for attempt in range(1, max_retries + 1):
        try:
            # Handle test cases differently from real API calls
            if user_reports_id is not None:
                logger.info(f"[get_data_from_snap_api][user({user_id})] Updating existing report {user_reports_id}")
                await user_reports_service.update_error_report_to_pending(status_report=report_status)
            
            if ReportMockValidator.is_test_case(parsed_body.report_type, parsed_body.report_input_value):
                client_id = str(uuid.uuid4())
                client = {'id': client_id}
                if org_data:
                    credits_service = CreditsService(db=db, user_id=user_id)
                    await credits_service.change_user_credits(credits_delta=1)
                logger.info(
                    f"[get_data_from_snap_api][user({user_id})] Processing test case, generated client_id: {client_id}")
            else:
                # Call the Snap API to get data
                client = await user_reports_service.get_data_from_snap_api_handler(body=parsed_body)
                client_id = client.get('id')
                if not client_id:
                    raise SnapApiNoIdError()

            # Update report status with client ID
            report_status["snap_request_id"] = client_id

            # Create or update report based on whether user_reports_id was provided
            if user_reports_id is None:
                logger.info(f"[get_data_from_snap_api][user({user_id})] Creating new report for client {client_id}")
                user_reports_id = await user_reports_service.create_blank_report_with_status(
                    status_report=report_status,
                    report_type=parsed_body.report_type,
                    report_input_encrypted=parsed_body.report_input_encrypted,
                    folder_id=parsed_body.parent_folder_id
                )

                await user_reports_service.insert_hmacs(hmac=parsed_body.hmac)

            # Get report metrics
            report_number = await user_reports_service.get_number_of_report_type(
                report_type=parsed_body.report_type
            )
            logger.info(f"[get_data_from_snap_api][user({user_id})] Report number: {report_number}")

            # Create unique task key and check if already running
            task_key = (user_id, user_reports_id)
            if task_key in app.state.running_snap_tasks:
                logger.info(f"[get_data_from_snap_api][user({user_id})] Task already running for {user_reports_id}")
                return client

            # Create and configure background task with time measurement
            async def monitored_snap_task():
                logger.info(f"[get_data_from_snap_api][user({user_id})] Starting monitored snap task")
                start_time = time.time()
                task_start_time = datetime.now()
                logger.info(f"[get_data_from_snap_api][user({user_id})] Starting monitored snap task at {task_start_time} for user_reports_id={user_reports_id}, client_id={client_id}, report_type={parsed_body.report_type}")
                
                # Create the main task
                main_task = asyncio.create_task(
                    user_reports_service.snap_status_ws(
                        app=app,
                        request_id=client_id,
                        report_type=parsed_body.report_type,
                        report_number=report_number,
                        report_search_args=report_search_args,
                        api_key=api_key
                    )
                )
                
                # Create a secondary task that will start after 720 seconds
                secondary_task = None
                
                try:
                    # Wait for either the main task to complete or 720 seconds to pass
                    logger.info(f"[get_data_from_snap_api][user({user_id})] Waiting for main task to complete or 720 seconds to pass for user_reports_id={user_reports_id}, client_id={client_id}")
                    
                    done, pending = await asyncio.wait(
                        [main_task],
                        timeout=1200.0,  # 1200 seconds = 20 minutes
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    
                    elapsed_time = time.time() - start_time
                    elapsed_minutes = elapsed_time / 60
                    logger.info(f"[get_data_from_snap_api][user({user_id})] Wait completed. Elapsed time: {elapsed_time:.2f} seconds ({elapsed_minutes:.2f} minutes) for user_reports_id={user_reports_id}, client_id={client_id}")
                    
                    if main_task in done:
                        # Main task completed before 720 seconds - don't run secondary function
                        logger.info(f"[get_data_from_snap_api][user({user_id})] Main task completed before 1200 seconds in {elapsed_time:.2f} seconds ({elapsed_minutes:.2f} minutes) for user_reports_id={user_reports_id}, client_id={client_id}. Secondary function will NOT run.")
                        return
                    else:
                        # 720 seconds have passed and main task is still running - check if browser is still open
                        logger.info(f"[get_data_from_snap_api][user({user_id})] 1200 seconds elapsed and main task is still running. Checking if browser is still open for user_reports_id={user_reports_id}, client_id={client_id}")
                        
                        # Check if user's WebSocket connection is still active
                        manager = app.state.connection_manager
                        user_websocket = manager.get_connection(user_id)
                        
                        if user_websocket is None:
                            logger.info(f"[get_data_from_snap_api][user({user_id})] User browser appears to be closed (no active WebSocket connection). Waiting for browser to reconnect (max 3600s) or main task to complete for user_reports_id={user_reports_id}, client_id={client_id}")

                            # Wait up to 1 hour for browser reconnection and then require
                            # 720 seconds of continuous connection before starting secondary
                            max_wait_for_reconnect_seconds = 3600
                            reconnect_deadline = time.time() + max_wait_for_reconnect_seconds

                            async def wait_for_continuous_connection(min_seconds: int, deadline_ts: float) -> bool:
                                """Return True if we observe min_seconds of continuous active websocket
                                before the deadline and before the main task completes. Otherwise False."""
                                connection_start_ts = None
                                check_interval_seconds = 5

                                while not main_task.done() and time.time() < deadline_ts:
                                    ws = manager.get_connection(user_id)
                                    now_ts = time.time()

                                    if ws is not None:
                                        if connection_start_ts is None:
                                            connection_start_ts = now_ts
                                            logger.info(f"[get_data_from_snap_api][user({user_id})] Detected browser reconnect. Starting continuous timer for 1200s.")
                                        else:
                                            elapsed_continuous = now_ts - connection_start_ts
                                            if elapsed_continuous >= min_seconds:
                                                logger.info(f"[get_data_from_snap_api][user({user_id})] Continuous connection satisfied: {elapsed_continuous:.2f}s >= {min_seconds}s")
                                                return True
                                    else:
                                        # Lost connection; reset timer
                                        if connection_start_ts is not None:
                                            logger.info(f"[get_data_from_snap_api][user({user_id})] Connection lost during countdown. Resetting 1200s timer.")
                                        connection_start_ts = None

                                    # Sleep in small increments to be responsive
                                    await asyncio.sleep(check_interval_seconds)

                                return False

                            # Outer wait loop: until main finishes or deadline
                            started_secondary = False
                            while not main_task.done() and time.time() < reconnect_deadline and not started_secondary:
                                # If connected now, require a fresh 720s continuous window
                                ws_now = manager.get_connection(user_id)
                                if ws_now is not None:
                                    remaining_deadline = reconnect_deadline - time.time()
                                    if remaining_deadline <= 0:
                                        break
                                    logger.info(f"[get_data_from_snap_api][user({user_id})] Browser reconnected. Waiting 1200s of continuous connection before starting secondary (deadline in {remaining_deadline:.0f}s).")
                                    ok = await wait_for_continuous_connection(1200, reconnect_deadline)
                                    if ok and not main_task.done():
                                        logger.info(f"[get_data_from_snap_api][user({user_id})] 1200s continuous connection achieved. Starting secondary auth_guard function for user_reports_id={user_reports_id}, client_id={client_id}")
                                        secondary_task = asyncio.create_task(
                                            start_secondary_function(
                                                user_id=user_id,
                                                original_request=request,
                                                original_response=response
                                            )
                                        )
                                        started_secondary = True
                                        break
                                    # If not ok, either deadline expired or main finished; loop will exit naturally
                                else:
                                    # Still disconnected; wait a bit and re-check
                                    await asyncio.sleep(30)

                            if not started_secondary:
                                if main_task.done():
                                    logger.info(f"[get_data_from_snap_api][user({user_id})] Main task completed before browser reconnection window. Secondary will not run.")
                                elif time.time() >= reconnect_deadline:
                                    logger.info(f"[get_data_from_snap_api][user({user_id})] Reconnect wait exceeded 3600s. Secondary will not run.")
                                return
                        else:
                            logger.info(f"[get_data_from_snap_api][user({user_id})] Browser is still open (active WebSocket connection found). Starting secondary auth_guard function for user_reports_id={user_reports_id}, client_id={client_id}")
                            
                            # Start the secondary auth_guard function
                            secondary_task = asyncio.create_task(
                                start_secondary_function(
                                    user_id=user_id,
                                    original_request=request,
                                    original_response=response
                                )
                            )
                        
                        # Wait for both tasks to complete, with browser status monitoring
                        try:
                            # Create a monitoring task that checks browser status and handles reconnections
                            async def monitor_browser_status():
                                secondary_task_started = True
                                while not main_task.done():
                                    await asyncio.sleep(30)  # Check every 30 seconds
                                    user_websocket = manager.get_connection(user_id)
                                    
                                    if user_websocket is None:
                                        if secondary_task_started and not secondary_task.done():
                                            logger.info(f"[get_data_from_snap_api][user({user_id})] Browser closed detected during secondary task execution. Cancelling secondary task for user_reports_id={user_reports_id}, client_id={client_id}")
                                            secondary_task.cancel()
                                            secondary_task_started = False
                                    else:
                                        # Browser is back online
                                        if not secondary_task_started and not secondary_task.done():
                                            logger.info(f"[get_data_from_snap_api][user({user_id})] Browser reconnected detected. Secondary task is still running for user_reports_id={user_reports_id}, client_id={client_id}")
                                            secondary_task_started = True
                                        elif secondary_task.done() and secondary_task.cancelled():
                                            # Secondary task was cancelled but browser is back - restart it
                                            logger.info(f"[get_data_from_snap_api][user({user_id})] Browser reconnected after secondary task cancellation. Restarting secondary task for user_reports_id={user_reports_id}, client_id={client_id}")
                                            
                                            # Create new secondary task
                                            new_secondary_task = asyncio.create_task(
                                                start_secondary_function(
                                                    user_id=user_id,
                                                    original_request=request,
                                                    original_response=response
                                                )
                                            )
                                            
                                            # Replace the old task
                                            secondary_task = new_secondary_task
                                            secondary_task_started = True
                            
                            # Start browser monitoring
                            monitor_task = asyncio.create_task(monitor_browser_status())
                            
                            # Wait for tasks to complete
                            await asyncio.gather(main_task, secondary_task, monitor_task, return_exceptions=True)
                            logger.info(f"[get_data_from_snap_api][user({user_id})] Tasks completed for user_reports_id={user_reports_id}, client_id={client_id}")
                            
                        except Exception as gather_error:
                            logger.error(f"[get_data_from_snap_api][user({user_id})] Error in task gathering: {str(gather_error)}")
                            # Ensure main task result is still returned if it completed successfully
                            if main_task.done() and not main_task.exception():
                                logger.info(f"[get_data_from_snap_api][user({user_id})] Main task completed successfully despite secondary task error")
                                return
                            raise
                        
                except Exception as e:
                    logger.error(f"[get_data_from_snap_api][user({user_id})] Error in monitored snap task: {str(e)}")
                    # Cancel secondary task if it was started
                    if secondary_task and not secondary_task.done():
                        secondary_task.cancel()
                    raise
                finally:
                    # Ensure cleanup
                    if secondary_task and not secondary_task.done():
                        secondary_task.cancel()
            
            task = asyncio.create_task(monitored_snap_task())

            # Add cleanup callback and store task reference
            task.add_done_callback(lambda _: app.state.running_snap_tasks.pop(task_key, None))
            app.state.running_snap_tasks[task_key] = task

            return client

        except ReportOfThisProcessAlreadyRunning as e:
            logger.warning(f"[get_data_from_snap_api][user({user_id})] Attempt {attempt} failed - report already running: {str(e)}")
            raise e

        except HTTPException as http_ex:
            if http_ex.status_code == 422:
                # Don't retry validation errors
                logger.error(f"[get_data_from_snap_api][user({user_id})] Invalid input format: {http_ex.detail}")
                raise InputValueSnapWrongError()  # Use business exception

            # Log other HTTP exceptions and potentially retry
            logger.warning(
                f"[get_data_from_snap_api][user({user_id})] Attempt {attempt} failed with HTTP status {http_ex.status_code}: {http_ex.detail}"
            )

            if attempt >= max_retries:
                await user_reports_service.handle_max_retries_exceeded(
                    app, client_id, user_reports_id, parsed_body, http_ex
                )
                credits_service = CreditsService(db=db, user_id=user_id)
                await credits_service.change_user_credits(credit_delta= 1)
                break

        except Exception as e:
            # Log general exceptions and potentially retry
            logger.warning(
                f"[get_data_from_snap_api][user({user_id})] Attempt {attempt} failed: {str(e)}"
            )

            if attempt >= max_retries:
                await user_reports_service.handle_max_retries_exceeded(
                    app, client_id, user_reports_id, parsed_body, e
                )
                credits_service = CreditsService(db=db, user_id=user_id)
                await credits_service.change_user_credits(credit_delta= 1)
                break

        # Wait before retry
        if attempt < max_retries:
            await asyncio.sleep(1 * attempt)  # Exponential backoff


@router.get("/get-saved-reports")
async def get_saved_reports(
    folder_id: Optional[str] = Query(None, description="Optional folder ID"),
    limit: int = Query(DefaultReports.DEFAULT_REPORTS_TO_FETCH),
    page: int = Query(1),
    order: str = Query("desc", description="Sort direction: 'asc' or 'desc'"),
    column_order: str = Query("modified_at", description="Column to order by"),
    hmac_filter: Optional[List[str]] = Query(None, description="Optional HMAC filter"),
    hmac_column: Optional[str] = Query(None, description="Column to filter hmac"),
    user: dict = Depends(auth_guard),
    db: AsyncSession = Depends(get_db)
):
    start_time = time.time()
    logger.info("[get_saved_reports][user(%s)] Starting endpoint execution - Fetching saved reports with parameters: folder_id=%s, limit=%s, page=%s, order=%s, column_order=%s, hmac_filter=%s, hmac_column=%s", 
                user, folder_id, limit, page, order, column_order, hmac_filter, hmac_column)

    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[get_saved_reports] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.view_report_list not in user_roles:
        logger.warning("[get_saved_reports] Access denied - User %s does not have view report list role", user_id)
        raise InvitePermissionDeniedError("visualizar lista de relatórios")
    
    user_reports = UserReportsService(db=db,user_id=user_id)
    user_reports.set_user_data(user_data=user)
    start_time_ms = time.perf_counter()
    saved_reports = await user_reports.list_user_items_unified(
                limit=limit,
                page=page,
                order=order,
                column_order=column_order, 
                hmac_filter=hmac_filter,
                hmac_column=hmac_column,
                folder_id=folder_id,
                get_data_child_folders=True)
    elapsed_ms = (time.perf_counter() - start_time_ms) * 1000
    logger.info(
        f"[get_saved_reports][user({user_id})] Completed in {elapsed_ms:.2f} ms | "
        f"params: folder_id={folder_id}, limit={limit}, page={page}, order={order}, column_order={column_order}, "
        f"hmac_filter_len={len(hmac_filter) if hmac_filter else 0}, hmac_column={hmac_column}"
    )


    return saved_reports


@router.get("/get-report-by-folder")
async def get_report_by_folder(folder_id: Optional[str] = Query(None, description="Optional folder ID"),
                                limit: int = Query(DefaultPageLogs.pagedefault),
                                page: int = Query(1),
                                order: str = Query("desc", description="Sort direction: 'asc' or 'desc'"),
                                column_order: str = Query("modified_at", description="Column to order by"),
                                hmac_filter: Optional[List[str]] = Query(None, description="Optional HMAC filter"),
                                hmac_column: Optional[str] = Query(None, description="Column to filter hmac"),
                                user: dict = Depends(auth_guard), db: AsyncSession = Depends(get_db)):


    logger.info("[get_report_by_folder] Fetching report by folder %s.", folder_id)
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[get_report_by_folder] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.view_report_list not in user_roles:
        logger.warning("[get_report_by_folder] Access denied - User %s does not have view report list role", user_id)
        raise InvitePermissionDeniedError("visualizar lista de relatórios")

    user_reports = UserReportsService(db=db,user_id=user_id)
    user_reports.set_user_data(user_data=user)
    saved_reports = await user_reports.get_reports_by_folder(
                limit=limit,
                page=page,
                order=order,
                column_order=column_order, 
                hmac_filter=hmac_filter,
                hmac_column=hmac_column,
                folder_id=folder_id)

    return saved_reports


@router.get("/auth/user")
async def get_authenticated_user(
    user: dict = Depends(auth_guard),
    db: AsyncSession = Depends(get_db)
):
    logger.info("[get_authenticated_user] Called endpoint.")

    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    user_email = token_decoded.get_user_email()
    logger.info(f"[get_authenticated_user] Decoded user_id: {user_id}, user_roles: {user_roles}")

    logger.info(f"[get_authenticated_user][user({user_id})] Extracted user_id from token: {user_id}")
    if not user_id:
        logger.error("[get_authenticated_user][user(%s)] No user ID found in token.", user_id)
        raise UserIdNotFoundTokenError()

    # Initialize user_data with default values to prevent UnboundLocalError
    user_data = {
        "roles": user_roles,
        "organization_name": None,
        "organization_logo": None,
        "print_snap_logo": False,
        "api_next_reset_credits": None,
        "credits_minimun": None,
        "has_active_invite": False,
        "accept_terms": False
    }

    logger.info("[get_authenticated_user][user(%s)] Fetching user data from DB.", user_id)

    # Fetch user data first (required for other operations)
    query = """
        SELECT * FROM reports.users
        WHERE user_id = :user_id;
    """

    async with async_session() as session:
        logger.info(f"[get_authenticated_user][user({user_id})] Executing user query: {query} with user_id={user_id}")
        result = await session.execute(sqlalchemy.text(query), {"user_id": user_id})
        row = result.fetchone()
        logger.info(f"[get_authenticated_user][user({user_id})] Query result: {row}")

    if not row:
        logger.error("[get_authenticated_user][user(%s)] User not found.", user_id)
        raise UserNotFoundError(user_id)
    else:
        logger.info("[get_authenticated_user][user(%s)] User found.", user_id)
        
        # Convert to dictionary and update user_data with database values
        db_user_data = dict(row._mapping)
        user_data.update(db_user_data)

        if isinstance(user_data["report_types"], dict) and not user_data["report_types"]:
            logger.info(f"[get_authenticated_user][user({user_id})] report_types is an empty dict, converting to empty list.")
            user_data["report_types"] = []
            logger.info(f"[get_authenticated_user][user({user_id})] report_types after conversion: {user_data['report_types']}")

    # Initialize services
    organization_user_service = OrganizationUsersService(db=db, user_id=user_id)
    user_standalone_service = UserStandaloneService(db=db, user_id=user_id)
    api_key_service = ApikeyService(db=db, user_id=user_id)
    invite_service = InviteService(db=db, user_id=user_id)
    eula_service = EulaService(db=db, user_id=user_id)

    # Set year and month
    year = datetime.today().year
    month = datetime.today().month

    logger.info(f"[get_authenticated_user][user({user_id})] Current year: {year}, month: {month}")

    # Run all async operations concurrently
    try:
        # Create separate database sessions for concurrent operations
        async def get_organization_user():
            async with async_session() as session:
                # Create a new service instance with the new session
                org_service = OrganizationUsersService(db=session, user_id=user_id)
                return await org_service.get_organization_user()

        async def get_api_key():
            async with async_session() as session:
                # Create a new service instance with the new session
                api_service = ApikeyService(db=session, user_id=user_id)
                return await api_service.get_api_key()

        async def get_eula():
            async with async_session() as session:
                # Create a new service instance with the new session
                eula_service_instance = EulaService(db=session, user_id=user_id)
                return await eula_service_instance.get_eula()

        async def get_user_invite():
            async with async_session() as session:
                # Create a new service instance with the new session
                invite_service_instance = InviteService(db=session, user_id=user_id)
                return await invite_service_instance.get_user_invite(email=user_email, status_invite=invite_status.enviado)

        # Execute all async operations in parallel with separate sessions
        (
            organization_user,
            api_key,
            eula_user,
            number_of_active_invite
        ) = await asyncio.gather(
            get_organization_user(),
            get_api_key(),
            get_eula(),
            get_user_invite(),
            return_exceptions=True
        )

        logger.info(f"[get_authenticated_user][user({user_id})] Concurrent operations completed successfully")
        user_data["api_next_reset_credits"] = None
        user_data["credits_minimun"] = 0
        # Process organization data
        organization_id = None
        if organization_user is not None and not isinstance(organization_user, Exception):
            organization_id = organization_user.organization_id
            logger.info(f"[get_authenticated_user][user({user_id})] User belongs to organization: {organization_id}")

            # Fetch organization data with separate session
            async with async_session() as session:
                organization_service = OrganizationService(db=session, organization_id=organization_id)
                organization_data = await organization_service.get_organization_data()

            if organization_data.name:
                user_data["organization_name"] = organization_data.name
                logger.info(f"[get_authenticated_user][user({user_id})] Organization name: {organization_data.name}")

            if organization_data.image_logo:
                user_data["organization_logo"] = organization_data.image_logo
                logger.info(f"[get_authenticated_user][user({user_id})] Organization logo: {organization_data.image_logo}")

            if organization_data.api_key and Roles.add_api_key in user_roles:
                user_data["api_key"] = organization_data.api_key
                logger.info(f"[get_authenticated_user][user({user_id})] Organization api_key: {organization_data.api_key}")
            
            if Roles.print_snap_logo in user_roles:
                user_data["print_snap_logo"] = organization_data.print_snap_logo
                logger.info(f"[get_authenticated_user][user({user_id})] Organization print snap logo: {organization_data.print_snap_logo}")
        else:
            logger.info(f"[get_authenticated_user][user({user_id})] User does not belong to any organization.")

        # Set organization ID for report counting
        if organization_id is not None:
            user_standalone_service.set_organization_id(organization_id=organization_id)

        # Process API key and credits
        if api_key is not None and not isinstance(api_key, Exception):
            # Use separate session for credits service
            async with async_session() as session:
                credits_service = CreditsService(db=session, user_id=user_id)
                credits_service.set_api_key(api_key=api_key)
            
            try:
                logger.info(f"[get_authenticated_user][user({user_id})] Attempting to fetch API credits from external service...")
                api_credits, api_next_reset_credits = await credits_service.get_api_credits()
                user_data["api_next_reset_credits"] = api_next_reset_credits
                logger.info(f"[get_authenticated_user][user({user_id})] Successfully fetched API credits: {api_credits}, next reset: {api_next_reset_credits}")

                try:
                    minimun_credits, _ = await credits_service.compare_user_credits_with_api_credits(user_data)
                    user_data["credits_minimun"] = minimun_credits
                    logger.info(f"[get_authenticated_user][user({user_id})] Minimum credits: {minimun_credits}")
                except ApiCreditsServiceUnavailableError as e:
                    logger.warning(f"[get_authenticated_user][user({user_id})] Caught ApiCreditsServiceUnavailableError during credits comparison: {e.detail}. Setting minimum credits to 0.")
                    user_data["credits_minimun"] = 0
                    logger.info(f"[get_authenticated_user][user({user_id})] Successfully handled API credits service unavailability during comparison. Minimum credits set to 0.")
                except HTTPException as e:
                    logger.error(f"[get_authenticated_user][user({user_id})] Unexpected HTTP error during credits comparison (status {e.status_code}): {e.detail}")
                    raise InternalServerError(detail=str(e))
                        
            except ApiCreditsServiceUnavailableError as e:
                logger.warning(f"[get_authenticated_user][user({user_id})] Caught ApiCreditsServiceUnavailableError: {e.detail}. Setting default values for user.")
                user_data["api_next_reset_credits"] = None
                user_data["credits_minimun"] = 0
                logger.info(f"[get_authenticated_user][user({user_id})] Successfully handled API credits service unavailability. User data updated with default values: api_next_reset_credits=None, credits_minimun=0")
            except ApiCreditsFetchError as e:
                logger.warning(f"[get_authenticated_user][user({user_id})] Caught ApiCreditsFetchError: {e.detail}. Setting default values for user.")
                user_data["api_next_reset_credits"] = None
                user_data["credits_minimun"] = 0
                logger.info(f"[get_authenticated_user][user({user_id})] Successfully handled API credits fetch error. User data updated with default values: api_next_reset_credits=None, credits_minimun=0")
            except HTTPException as e:
                logger.error(f"[get_authenticated_user][user({user_id})] Unexpected HTTP error from API credits service (status {e.status_code}): {e.detail}")
                user_data["api_next_reset_credits"] = None
                user_data["credits_minimun"] = 0
                logger.info(f"[get_authenticated_user][user({user_id})] Successfully handled API credits fetch error. User data updated with default values: api_next_reset_credits=None, credits_minimun=0")

        # Process invite status
        if not isinstance(number_of_active_invite, Exception) and len(number_of_active_invite) > 0:
            user_data["has_active_invite"] = True

        # Process EULA acceptance
        # Add detailed logging to understand what type of object is arriving
        logger.info(f"[get_authenticated_user][user({user_id})] EULA object type: {type(eula_user)}")
        logger.info(f"[get_authenticated_user][user({user_id})] EULA object: {eula_user}")
        user_data["accept_terms"] = False
        
        # Check if eula is a valid object and not an exception
        if eula_user and not isinstance(eula_user, Exception):
            # Verify that eula_user has the expected attributes
            if hasattr(eula_user, 'version'):
                logger.info(f"[get_authenticated_user][user({user_id})] EULA version from service: {eula_user.version}")
                if user_data["eula_version"] == eula_user.version:
                    user_data["accept_terms"] = True
                    logger.info(f"[get_authenticated_user][user({user_id})] User has accepted the terms of the EULA.")
                else:
                    logger.info(f"[get_authenticated_user][user({user_id})] User EULA version ({user_data['eula_version']}) does not match current EULA version ({eula_user.version})")
            else:
                logger.warning(f"[get_authenticated_user][user({user_id})] EULA object missing version attribute. Object: {eula_user}, Type: {type(eula_user)}")
                if hasattr(eula_user, '__dict__'):
                    logger.error(f"[get_authenticated_user][user({user_id})] EULA object attributes: {eula_user.__dict__}")
        else:
            if isinstance(eula_user, Exception):
                logger.warning(f"[get_authenticated_user][user({user_id})] EULA service returned an exception: {eula_user}")
            else:
                logger.warning(f"[get_authenticated_user][user({user_id})] EULA is not available: {eula_user}")

        # Fetch report count with separate session (this depends on organization_id being set)
        async with async_session() as session:
            user_standalone_service_instance = UserStandaloneService(db=session, user_id=user_id)
            if organization_id is not None:
                user_standalone_service_instance.set_organization_id(organization_id=organization_id)
            report_count = await user_standalone_service_instance.count_reports_by_month_year(month=month, year=year)
            logger.info(f"[get_authenticated_user][user({user_id})] Report count: {report_count}")
            if report_count:
                user_data["report_count"] = report_count

        logger.info(f"[get_authenticated_user][user({user_id})] Returning user data: {user_data}")
        return user_data
    
    except Exception as e:
        logger.error(f"[get_authenticated_user][user({user_id})] Error during concurrent operations: {str(e)}")
        raise InternalServerError(detail=f"Error processing user data: {str(e)}")


@router.post("/verifier")
async def insert_verifier(request: InsertVerifier, user: dict = Depends(auth_guard), db: AsyncSession = Depends(get_db)):
    logger.info("[insert_verifier] Inserting verifier for user %s.", user.get('sub'))
    logger.info("[insert_verifier] Received request: %s", request.model_dump())

    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()

    # Create service without user_reports_id (not needed for this operation)
    user_service = UserStandaloneService(db=db, user_id=user_id)
    
    # Use the service to handle the verifier insertion
    return await user_service.insert_verifier_handler(body=request)


@router.patch("/rename-report/{user_reports_id}")
async def rename_report(user_reports_id: str, request: RenameReport, user: dict = Depends(auth_guard), db: AsyncSession = Depends(get_db)):
    logger.info("[rename_report] Renaming report %s.", user_reports_id)
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[rename_report] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.rename_report not in user_roles:
        logger.warning("[rename_report] Access denied - User %s does not have rename report role", user_id)
        raise InvitePermissionDeniedError("renomear relatório")

    user_reports_service = UserReportsService(db=db, user_id=user_id, user_reports_id=user_reports_id)
    await user_reports_service.rename_report_handler(body=request)
    return {"message": "Relatório renomeado com sucesso"}


@router.post("/logout")
async def logout_user(request: Request, response: Response):
    body = await request.body()
    logger.info("[logout_user] Received request: %s", body.decode("utf-8"))
    refresh_token = request.cookies.get("refresh_token")
    logger.info("[logout_user] Logging out user.")
    return await logout_user_using_token(refresh_token=refresh_token, response=response)


@router.delete("/delete-report/{user_reports_id}")
async def delete_report(user_reports_id: str, user: dict = Depends(auth_guard), db: AsyncSession = Depends(get_db)):
    logger.info("[delete_report] Deleting report %s.", user_reports_id)

    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[delete_report] Decoded user_id: {user_id}, user_roles: {user_roles}")  
      
    if Roles.delete_report not in user_roles:
        logger.warning("[delete_report] Access denied - User %s does not have delete report role", user_id)
        raise InvitePermissionDeniedError("deletar relatório")

    user_reports_service = UserReportsService(db=db, user_id=user_id, user_reports_id=user_reports_id)
    return await user_reports_service.delete_report_handler()


@router.get("/health")
async def health_check(db: AsyncSession = Depends(get_db)):
    """
    Health check endpoint to monitor database connection and application status.
    """
    try:
        # Attempt to execute a simple query to check DB connection
        await db.execute(sqlalchemy.text("SELECT 1"))
        logger.info("[health_check] Database connection successful.")
        
        # Get pool status for monitoring
        pool_status = await get_pool_status()
        
        return {
            "status": "ok", 
            "message": "Database connection successful.",
            "pool_status": pool_status
        }
    except Exception as e:
        logger.error(f"[health_check] Database connection failed: {e}")
        return {"status": "error", "message": f"Database connection failed: {e}"}