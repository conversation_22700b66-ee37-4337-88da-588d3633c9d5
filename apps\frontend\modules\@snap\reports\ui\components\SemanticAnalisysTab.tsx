import { Separator, Text } from '@snap/design-system'
import { getTypeIcon, getRelationshipLabels } from "../../helpers";

interface NameCardProps {
  name: string;
  type: string;
  index: number;
}

interface VinculoCardProps {
  vinculo: any;
  index: number;
  usePrefix: boolean;
  previousType?: string;
}

const SemanticAnalisysTab = ({ data }: { data: any }) => {
  console.log("[SemanticAnalisysTab] data", data);

  const renderNameCard = ({ name, type, index }: NameCardProps) => {
    let entityType = type;
    if (type === "pessoa") {
      entityType = "cpf";
    } else if (type === "empresa") {
      entityType = "cnpj";
    }

    let bgColor = "bg-accent";
    if (index !== 0 && index !== data.length - 1) {
      bgColor = "bg-border";
    }

    return (
      <div className={`flex gap-2 items-center ${bgColor} p-2 rounded-sm`}>
        {getTypeIcon(entityType, 24, "text-foreground")}
        <Text variant="body-md" className='font-semibold'>{name}</Text>
      </div>
    )
  }

  const renderVinculoCard = ({ vinculo, index, usePrefix, previousType }: VinculoCardProps) => {
    if (!vinculo) {
      return null;
    }

    const relationshipLabel = getRelationshipLabels(vinculo, index, usePrefix, previousType);

    return (
      <div className='flex items-center'>
        <Text variant="body-md">{relationshipLabel}</Text>
      </div>
    )
  }

  return (
    <div className='flex flex-col gap-5 pb-4'>
      <div className='flex items-center gap-5'>
        <div className='flex gap-2 items-center'>
          <div className='w-4 h-4 bg-accent' />
          <Text className='font-semibold capitalize'>Vinculos principais</Text>
        </div>
        <div className='flex gap-2 items-center'>
          <div className='w-4 h-4 bg-border' />
          <Text className='font-semibold capitalize'>Vinculos intermediários</Text>
        </div>
      </div>
      <Separator dashed={false} />
      {data.length === 0 ? (
        <Text variant="body-md" className='text-center w-full text-foreground/50'>Nenhum vínculo encontrado.</Text>
      ) : <Text> Para ver mais detalhes sobre cada vínculo, basta clicar sobre os mesmos.</Text>}
      <div className='flex flex-wrap gap-4 max-w-[80%]'>
        {data?.map((item: any, index: number) => {
          const previousItem = index > 0 ? data[index - 1] : null;
          const previousType = previousItem?.tipo?.value
          const personName = item.nome_completo?.value || item["full name"]?.value || item.nome_completo;
          const companyName = item["razao social"]?.value || item["razao_social"]?.value;
          const entityType = item.tipo?.value || item.tipo;
          const relationshipLabel = item["label default key"]?.value || item["label default key"];

          const displayName = entityType === "pessoa" ? personName : companyName;

          return (
            <div key={index} className='flex gap-4'>
              {renderVinculoCard({
                vinculo: relationshipLabel,
                index,
                usePrefix: true,
                previousType
              })}
              {renderNameCard({
                name: displayName || 'Nome não disponível',
                type: entityType || 'pessoa',
                index
              })}
            </div>
          );
        })}
      </div>
    </div>
  )
}

export default SemanticAnalisysTab