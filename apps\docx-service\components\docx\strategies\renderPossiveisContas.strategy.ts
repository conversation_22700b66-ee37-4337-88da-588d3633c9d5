import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection } from "../../../global";
import { translatePropToLabel, parseValue, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintPossiveisContasProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      site?: {
        value: string;
        label: string;
        source: string[];
        is_deleted: boolean;
      };
      detalhes?: Record<string, {
        value: string;
        label: string;
        source: string[];
        is_deleted: boolean;
      }>;
    }>;
  };
}

export const renderPossiveisContas = ({ section }: RenderPrintPossiveisContasProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  const validEntries = section.data?.filter(entry =>
    entry.site && !entry.site.is_deleted && entry.detalhes
  ) || [];

  if (!validEntries.length) {
    return { children };
  }

  const rows: TableRow[] = [];
  let cells: TableCell[] = [];

  validEntries.forEach((entry) => {
    const site = entry.site!;
    const detalhes = entry.detalhes!;

    const cellParagraphs = [
        new Paragraph({children: [new TextRun({text: (site.label || 'SITE').toUpperCase(), bold: true, color: "889EA3"})]}),
        new Paragraph(site.value)
    ];

    Object.entries(detalhes)
        .filter(([_, value]) => !value.is_deleted)
        .forEach(([key, value]) => {
            cellParagraphs.push(new Paragraph({children: [new TextRun({text: (value.label || translatePropToLabel(key)).toUpperCase(), bold: true})]}));
            cellParagraphs.push(new Paragraph(parseValue(String(value.value || ""))))
        });

    cells.push(new TableCell({ children: cellParagraphs }));

    if (cells.length === 2) {
        rows.push(new TableRow({ children: cells }));
        cells = [];
    }
  });

  if (cells.length > 0) {
    rows.push(new TableRow({ children: [...cells, new TableCell({ children: [new Paragraph("")] })] }));
  }

  if(rows.length > 0) {
      children.push(new Table({rows, width: {size: 100, type: WidthType.PERCENTAGE}, columnWidths: [4500, 4500]}));
  }

  return { children };
};
