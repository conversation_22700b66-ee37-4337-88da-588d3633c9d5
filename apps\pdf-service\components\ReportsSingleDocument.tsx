import React from 'react';
import {
  View,
} from './pdf-components';
import {
  IGenerateReportsPDFRequestInput,
  ReportSection,
  ReportType
} from '../global';
import {usePrintStrategyMap} from './strategies/printStrategyFactory';
import {PrintProfileHeader} from './PrintProfileHeader';
import {PrintSummary} from './PrintSummary';
import {REPORT_CONSTANTS} from '../config/constants';

type ReportSearchArgs = Record<string, string>;

export const ReportsSingleDocument: React.FC<IGenerateReportsPDFRequestInput> = ({
  sections,
  metadata,
  profile_image,
}) => {

  const reportType = (metadata[REPORT_CONSTANTS.new_report.report_type] as ReportType) || '';
  const reportSearchArgs = (metadata[REPORT_CONSTANTS.new_report.report_search_args] as ReportSearchArgs) || {};
  const searchValue = Object.values(reportSearchArgs)[0] || '';

  const printMap = usePrintStrategyMap(reportType);
  const printableSections = Array.isArray(sections)
    ? sections.filter(
      (section) =>
        !section.subsection &&
        printMap[section.title as keyof typeof printMap]
    )
    : [];
  const summaryItems = printableSections.map(section => ({
    title: section.title,
    data_count: section.data_count || 0
  }));

  return (
    <>
      <PrintProfileHeader metadata={metadata} report_type={reportType} searchValue={searchValue} profile_image={profile_image}/>
      <PrintSummary items={summaryItems}/>
      {
        printableSections.map((section, sectionIdx) => {
          const Renderer = printMap[section.title as keyof typeof printMap];
          if (typeof Renderer === 'function') {
            return <View key={sectionIdx}>
              {Renderer(section as any)}
            </View>
          }
          return null;
        })
      }
    </>
  );
};
