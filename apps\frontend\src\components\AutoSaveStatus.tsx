import React from 'react';
import { useLocation } from 'react-router';
import { Button, Loading } from '@snap/design-system';
import { Check } from 'lucide-react';
import { useReportDetailStore } from '~/store/details/store';
import { LuClock } from 'react-icons/lu';

const AutoSaveStatus: React.FC = () => {
  const location = useLocation();
  const { autoSave } = useReportDetailStore();
  const isReportDetailsPage = location.pathname.startsWith("/report/");

  if (!isReportDetailsPage) {
    return null;
  }

  const getStatusConfig = () => {
    if (autoSave.isPending) {
      return {
        text: 'Salvando alterações...',
        icon: <Loading size="sm" />,
        textColor: '!text-accent',
        borderColor: '!border-accent',
      };
    }

    if (autoSave.hasPendingChanges) {
      return {
        text: 'Autosave ativo...',
        icon: <LuClock size={16} className="animate-spin" />,
        textColor: '!text-foreground',
        borderColor: '!border-foreground',
      };
    }

    return {
      text: 'Dados atualizados',
      icon: <Check size={16} />,
      textColor: '!text-secondary',
      borderColor: '!border-secondary',
    };
  };

  const { text, icon, textColor, borderColor } = getStatusConfig();

  return (
    <Button
      variant="outline"
      className={`
        !px-3 
        w-[288px] 
        uppercase 
        hover:!bg-transparent 
        !cursor-default 
        transition-all duration-300 ease-in-out min-w-0
        ${textColor}
        ${borderColor}
        `}
      size="lg"
      onClick={(e) => e.preventDefault()}
      icon={icon}
      iconPosition='right'
    >
      {text}
    </Button>
  );
};

export default AutoSaveStatus;