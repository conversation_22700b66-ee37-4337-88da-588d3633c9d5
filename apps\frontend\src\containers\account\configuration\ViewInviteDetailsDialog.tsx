import { Button, Input, Text } from "@snap/design-system";
import { Loader2, X } from "lucide-react";
import { useEffect, useState } from "react";
import { Checkbox } from "~/components/ui/checkbox";
import { Label } from "~/components/ui/label";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { REPORT_TYPES } from "~/helpers/constants";
import { useUserCRUD } from "~/hooks/useUserCRUD";
import { useDialogActions } from "~/store/dialogStore";
import { ReportCredits, UserInviteResponse } from "~/types/global";
import { parseValue } from "~/helpers";

interface ViewInviteDetailsDialogContentProps {
  selectedRow: UserInviteResponse;
}

export function ViewInviteDetailsDialogContent({ selectedRow }: ViewInviteDetailsDialogContentProps) {
  const [email_invited, setEmailInvited] = useState("");
  const [type_invite, setTypeInvite] = useState("investigador");
  const [report_types, setReportTypes] = useState<{ [key: string]: boolean }>({});
  const [credits_sent, setCreditsSent] = useState<string>("100");
  const [email_sender, setEmailSender] = useState("");
  const [name_sender, setNameSender] = useState("");
  const [status_invite, setStatusInvite] = useState("");
  const { inviteDetailsQuery } = useUserCRUD();
  const inviteDetailsData = inviteDetailsQuery(selectedRow?.invite_id || "");

  useEffect(() => {
    if (inviteDetailsData.data && selectedRow) {
      // api está retornando um array com um único elemento
      const apiData = Array.isArray(inviteDetailsData.data)
        ? inviteDetailsData.data[0]
        : inviteDetailsData.data;

      // Use API data for fields that are available, fallback to selectedRow for missing fields
      setEmailInvited(apiData?.email_invited || selectedRow.email_invited || "");
      setTypeInvite(apiData?.type_invite || selectedRow.type_invite || "investigador");

      // Convert array of report types to object with boolean values
      const initialReportTypes = {
        cpf: false,
        cnpj: false,
        email: false,
        telefone: false,
        relacoes: false,
        combinado: false,
        relacao: false,
      };

      const userReportTypes = { ...initialReportTypes };
      // Use report_types from API if available, otherwise from selectedRow
      const reportTypesSource = apiData?.report_types || selectedRow.report_types;
      if (reportTypesSource && Array.isArray(reportTypesSource)) {
        reportTypesSource.forEach(type => {
          if (type in userReportTypes) {
            userReportTypes[type as keyof typeof userReportTypes] = true;
          }
        });
      }

      setReportTypes(userReportTypes);
      const creditsSentValue = apiData?.credits_sent || selectedRow.credits_sent;
      setCreditsSent(creditsSentValue?.toString() || "100");
      setEmailSender(apiData?.email_sender || "");
      setNameSender(apiData?.name_sender || "");
      setStatusInvite(apiData?.status_invite || "");
    }
  }, [inviteDetailsData.data]);

  if (inviteDetailsData.isLoading) {
    return (
      <div className="flex flex-col gap-6 p-4">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <Text className="ml-2">Carregando dados...</Text>
        </div>
      </div>
    );
  }

  if (inviteDetailsData.error) {
    return (
      <div className="flex flex-col gap-6 p-4">
        <div className="flex items-center justify-center py-8 text-accent">
          <Text>Erro ao carregar dados.</Text>
        </div>
      </div>
    );
  }

  const options = Object.values(REPORT_TYPES as ReportCredits | object).map(
    ([value]) => ({
      value: value,
      label: parseValue(value).toUpperCase(),
    })
  );

  return (
    <div className="flex flex-col gap-6 p-4">
      {/* info do convite */}
      <div className="flex flex-col gap-2 p-4 border border-border rounded-md">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-xs mb-1 font-semibold">Email Convidado:</label>
            <Input
              type="email"
              placeholder="Email do usuário"
              variant="filled"
              value={email_invited}
              disabled
              className="rounded-none border-0 py-1.5 bg-background"
              wrapperClassName="border-1 border-foreground"
            />
          </div>
          <div>
            <label className="block text-xs mb-1 font-semibold">Status do Convite:</label>
            <Input
              type="email"
              variant="filled"
              value={status_invite}
              disabled
              className="rounded-none border-0 py-1.5 bg-background"
              wrapperClassName="border-1 border-foreground"
            />
          </div>
        </div>
      </div>

      <div className="flex gap-4 border border-border rounded-md p-4">
        <div className="flex items-center flex-1 gap-4">
          <Text variant="body-md" className="font-semibold">
            Tipo de Perfil:
          </Text>
          <RadioGroup
            value={type_invite}
            className="flex flex-1 gap-8"
            disabled
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="investigador"
                id="investigador"
                className="border-white"
                disabled
              />
              <Label htmlFor="investigador" className="text-[16px]">
                Investigador
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="administrador"
                id="administrador"
                className="border-white"
                disabled
              />
              <Label htmlFor="administrador" className="text-[16px]">
                Administrador
              </Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      <div className="space-y-4">
        <Text variant="body-md" className="font-semibold">
          <span className="text-accent">Tipos de relatório</span> que esse usuário terá acesso:
        </Text>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {options.map((item) => {
            const checked = report_types[item.value as keyof typeof report_types];
            return (
              <label
                key={item.value}
                htmlFor={`view-${item.value}`}
                className={`
                  flex items-center justify-between gap-2
                  bg-neutral-600 py-2 px-3 rounded
                  ${checked ? "" : "opacity-80"}
                `}
              >
                <span className="font-mono text-[16px]">
                  {item.label}
                </span>

                <Checkbox
                  id={`view-${item.value}`}
                  checked={checked}
                  disabled
                  className="border-foreground data-[state=checked]:bg-white rounded-none"
                />
              </label>
            );
          })}
        </div>
      </div>

      <div className="flex items-center justify-between gap-8 border-t border-b border-dashed border-border py-4">
        <Text variant="body-md" className="font-semibold">
          Quantidade de <span className="text-accent">consultas</span> para uso desse usuário:
        </Text>

        <Input
          type="number"
          variant="filled"
          min={0}
          value={credits_sent}
          disabled
          className="rounded-none border-0 py-1.5 bg-muted opacity-60"
          wrapperClassName="flex-1 max-w-[140px] border-1 border-border"
        />
      </div>

      {/* info do remetente do convite */}
      <div className="flex flex-col gap-2">
        <Text className="font-semibold mb-2">Informações do Remetente:</Text>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-xs mb-1 font-semibold">Nome do Remetente:</label>
            <Input
              type="text"
              variant="filled"
              value={name_sender || ""}
              disabled
              className="rounded-none border-0 py-1.5 bg-background"
              wrapperClassName="border-1 border-foreground"
            />
          </div>
          <div>
            <label className="block text-xs mb-1 font-semibold">Email do Remetente:</label>
            <Input
              type="email"
              variant="filled"
              value={email_sender || ""}
              disabled
              className="rounded-none border-0 py-1.5 bg-background"
              wrapperClassName="border-1 border-foreground"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export function ViewInviteDetailsDialogFooter() {
  const { closeDialog } = useDialogActions();

  return (
    <div className="flex gap-4">
      <Button
        className="uppercase"
        onClick={closeDialog}
      >
        Fechar <X />
      </Button>
    </div>
  );
}

// Export as static properties so you can use the composition pattern
export const ViewInviteDetailsDialog = {
  Content: ViewInviteDetailsDialogContent,
  Footer: ViewInviteDetailsDialogFooter,
};
