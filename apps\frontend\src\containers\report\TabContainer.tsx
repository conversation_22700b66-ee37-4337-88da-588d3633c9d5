import { cn } from "~/lib/utils";
import { Tabs } from '@snap/design-system'
import * as T from "../report/details/tabs"
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";
import { REPORT_DETAIL_STORE_INSTANCE } from "~/store/details";
import { REPORT_CONSTANTS } from "~/helpers/constants";

const TabContainer = () => {
  const { checkPermission } = usePermissionCheck();
  const canUpdateReport = checkPermission(Permission.UPDATE_REPORT);
  const {
    selectors: { useReportType, useSelectedCombinedFilter }
  } = REPORT_DETAIL_STORE_INSTANCE;
  const reportType = useReportType();
  const selectedCombinedFilter = useSelectedCombinedFilter();
  const isRelacoes = reportType === REPORT_CONSTANTS.types.relacoes;
  const isCombinado = reportType === REPORT_CONSTANTS.types.combinado;

  // For combinado reports, trash is only available when viewing combined_data
  const isTrashAvailable = isCombinado ? selectedCombinedFilter === 'combined_data' : true;

  const renderTabList = () => {
    const registriesTab = {
      value: 'registries',
      key: 'registries',
      label: 'índice',
      children: <T.ReportRecordsList />
    };
    const trashTab = {
      value: 'trash',
      key: 'trash',
      label: 'LIXEIRA',
      children: <T.ReportTrash />
    };

    if (!canUpdateReport || isRelacoes || !isTrashAvailable) {
      return [
        registriesTab
      ];
    }

    return [
      registriesTab,
      trashTab
    ];
  }

  return (
    <Tabs
      items={renderTabList()}
      className={cn(
        "[&_[role=tab]]:cursor-pointer [&_[role=tab]]:!border-none [&_[role=tabpanel]]:border-none [&_[role=tabpanel]]:px-3",
        "[&_[role=tab][data-state=inactive]_p]:opacity-20",
        "[&_[role=tab][data-state=inactive]>div>div]:bg-",
        "[&_[role=tab][data-state=inactive]>div>div]:bg-muted-foreground",
      )}
    />
  );
};

export default TabContainer;
