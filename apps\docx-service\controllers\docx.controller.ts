import { AppError } from "../middlewares/error.middleware";
import { IControllerMethod, IReportsDocxControllerBody } from "../global";
import * as logger from "../utils/logger";
import { DOCX_SERVICE } from "../service/docx.service";

export interface IDocxController {
  generate: IControllerMethod;
}

const createDocxController: () => Record<string, IControllerMethod> = () => {
  const generate: IControllerMethod = async (req, res, next) => {
    const startTime = Date.now();
    try {
      // Validacoes
      if (!req.body || !req.body.sections || !req.body.metadata) {
        const error = new Error(
          "Missing required fields: sections, metadata and browserRef"
        ) as AppError;
        error.statusCode = 400;
        error.isOperational = true;
        throw error;
      }
      if (!Array.isArray(req.body.sections)) {
        const error = new Error("Sections must be an array") as AppError;
        error.statusCode = 400;
        error.isOperational = true;
        throw error;
      }

      // Extrair campos do corpo da requisição
      const {
        sections,
        metadata,
        profile_image,
        should_print_snap_logo,
        organization_logo,
      }: IReportsDocxControllerBody = req.body;
      const filename = `${metadata.report_name || "report"}.docx`.replace(
        /[^a-zA-Z0-9.-]/g,
        "_"
      );

      // Logs iniciais de geração
      logger.info("=== Docx Generation Request ===");
      logger.info("Report details", {
        reportName: metadata.report_name || "Unnamed Report",
        reportType: metadata.report_type || "Unknown",
        sectionsCount: sections.length,
        requestSize: JSON.stringify(req.body).length,
      });
      logger.info("Generating Docx file");

      // Chamada ao serviço de geração de Docx
      const docxBuffer = await DOCX_SERVICE.generateDocx({
        sections,
        metadata,
        profile_image,
        should_print_snap_logo,
        organization_logo,
      });

      // Enviar resposta
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );
      res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
      res.send(docxBuffer);

      // Logs finais de geração
      const totalTime = Date.now() - startTime;
      logger.info("=== Docx Generation Complete ===");
      logger.info("Total processing time", { duration: totalTime });
      logger.info("================================");
    } catch (error) {
      const totalTime = Date.now() - startTime;
      logger.error("=== Docx Generation Failed ===");
      logger.error("Docx generation error", {
        duration: totalTime,
        error: error instanceof Error ? error : new Error(String(error)),
      });
      logger.error("==============================");
      const appError = (error as AppError) || new Error("Unknown Docx error");
      appError.statusCode = appError.statusCode || 500;
      appError.isOperational = true;
      return next(appError);
    }
  };
  return { generate };
};

export const DOCX_CONTROLLER = createDocxController();
