# apps/backend/middleware/error_middleware.py

from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.middleware.base import BaseHTTPMiddleware
import logging
from exceptions.base_exceptions import ProblemDetail, ValidationError, InternalServerError
from pydantic import ValidationError as PydanticValidationError

logger = logging.getLogger(__name__)

class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except Exception as exc:
            return await self.handle_exception(request, exc)

    async def handle_exception(self, request: Request, exc: Exception):
        # Log all errors (customize as needed)
        logger.error(f"Error: {exc}", exc_info=True)

        # Already RFC 7807
        if isinstance(exc, ProblemDetail):
            exc.instance = str(request.url)
            return JSONResponse(
                status_code=exc.status,
                content=exc.to_dict()
            )

        # FastAPI HTTPException
        if isinstance(exc, HTTPException):
            problem = ProblemDetail(
                type_uri="https://api.example.com/errors/http-error",
                title="HTTP Error",
                status=exc.status_code,
                detail=str(exc.detail),
                instance=str(request.url)
            )
            return JSONResponse(
                status_code=exc.status_code,
                content=problem.to_dict()
            )

        # Pydantic request validation error
        if isinstance(exc, (RequestValidationError, PydanticValidationError)):
            errors = []
            try:
                for error in exc.errors():
                    errors.append({
                        "field": ".".join(str(x) for x in error.get("loc", [])),
                        "code": error.get("type", "validation_error").upper(),
                        "message": error.get("msg", "Invalid value")
                    })
            except Exception:
                errors = []

            problem = ValidationError(
                detail="Request validation failed",
                errors=errors,
                instance=str(request.url)
            )
            return JSONResponse(
                status_code=400,
                content=problem.to_dict()
            )

        # Unhandled errors (Internal server error)
        problem = InternalServerError(
            detail="An unexpected error occurred.",
            instance=str(request.url)
        )
        return JSONResponse(
            status_code=500,
            content=problem.to_dict()
        )
