import { createContext, useContext, PropsWith<PERSON>hildren, useMemo } from "react";
import { ReportSection, ReportMetadata, CombinedReportFilterOption, ReportDataCombined } from "../global";
import { Draft } from "immer";
import { REPORT_CONSTANTS } from "../config/constants";

// --- Section State Context ---
const SectionsContext = createContext<ReportSection[]>([]);

// --- Deleted Sections State Context ---
const DeletedSectionsContext = createContext<ReportSection[] | undefined>(undefined);

// --- Metadata State Context ---
const MetadataContext = createContext<ReportMetadata | undefined>(undefined);

// --- Report Type State Context ---
const ReportTypeContext = createContext<string>("");

// --- Is Trash Enabled Context ---
const IsTrashEnabledContext = createContext<boolean>(false);

// --- Is Print Enabled Context ---
const IsPrintEnabledContext = createContext<boolean>(false);

// --- Profile Image State Context ---
const ProfileImageContext = createContext<string | undefined>(undefined);

// --- Organization Logo State Context ---
const OrganizationLogoContext = createContext<string | undefined>(undefined);

// --- Should Print Snap Logo Context ---
const ShouldPrintSnapLogoContext = createContext<boolean>(false);

// --- Mode Context ---
export type RenderMode = 'default' | 'trash' | 'print-pdf';
const ModeContext = createContext<RenderMode>('default');

// --- Combinado Filter Context ---
const CombinadoFilterContext = createContext<{
  selectedFilterId: string;
  reportSources: Array<CombinedReportFilterOption>;
}>({
  selectedFilterId: 'combined_data',
  reportSources: []
});

const LoadingStateContext = createContext<{
  isActionLoading: boolean;
}>({
  isActionLoading: false,
});

// --- Unified Actions Context ---
export interface ReportActions {
  setReportSections: (sections: ReportSection[]) => void;
  setDeletedSections?: (sections: ReportSection[]) => void;
  setMetadata: (metadata: ReportMetadata) => void;
  setReportType: (type: string) => void;
  setProfileImage?: (image: string) => void;
  isPendingSave?: () => boolean;
  setSelectedCombinedFilter?: (filterId: string) => void;
  setCombinedReportSources?: (sources: Array<CombinedReportFilterOption>) => void;
  updateSectionEntries?: (
    sectionTitle: string,
    updaterFn: (entry: Draft<ReportSection["data"][0]> | any, index?: number) => void,
    testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
    testSectionDeletedFn: (section: ReportSection) => boolean,
    calculateDataCountFn?: (section: ReportSection) => number,
    includeSubsections?: boolean,
    crossSectionUpdate?: { matchingProp: string; updaterFn: (entry: any, index?: number) => void }
  ) => void;
  updateSubsectionWithMainSection?: (
    sectionTitle: string,
    subsectionName: string,
    matchingProp: string,
    updaterFn: (entry: Draft<ReportSection["data"][0]> | any, index?: number) => void,
    testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
    testSectionDeletedFn: (section: ReportSection) => boolean,
    calculateDataCountFn?: (section: ReportSection) => number
  ) => void;
}
const ActionsContext = createContext<ReportActions | undefined>(undefined);

interface ProviderProps {
  sections: ReportSection[];
  metadata: ReportMetadata;
  reportType: string;
  isTrashEnabled: boolean,
  isPrintEnabled: boolean,
  image?: string;
  organizationLogo?: string | null;
  shouldPrintSnapLogo?: boolean;
  actions: ReportActions;
  renderMode: RenderMode;
  isActionLoading: boolean;
  selectedCombinedFilter?: string;
  combinedReportSources?: Array<CombinedReportFilterOption>;
}

export function ReportProvider({
  children,
  sections,
  metadata,
  reportType,
  isTrashEnabled,
  isPrintEnabled,
  image,
  organizationLogo,
  shouldPrintSnapLogo,
  actions,
  renderMode,
  isActionLoading,
  selectedCombinedFilter = 'combined_data',
  combinedReportSources = [],
}: PropsWithChildren<ProviderProps>) {
  const sectionsValue = useMemo(() => sections, [sections]);
  const metadataValue = useMemo(() => metadata, [metadata]);
  const reportTypeValue = useMemo(() => reportType, [reportType]);

  // For combinado reports, trash is only enabled when viewing combined_data
  const isTrashEnabledValue = useMemo(() => {
    if (reportType === REPORT_CONSTANTS.types.combinado) {
      return isTrashEnabled && selectedCombinedFilter === 'combined_data';
    }
    return isTrashEnabled;
  }, [isTrashEnabled, reportType, selectedCombinedFilter]);

  const isPrintEnabledValue = useMemo(() => isPrintEnabled, [isPrintEnabled]);
  const profileImageValue = useMemo(() => image, [image]);
  const organizationLogoValue = useMemo(() => organizationLogo ?? undefined, [organizationLogo]);
  const shouldPrintSnapLogoValue = useMemo(() => shouldPrintSnapLogo ?? false, [shouldPrintSnapLogo]);
  const modeValue = useMemo(() => renderMode, [renderMode]);

  const actionsValue = useMemo(() => actions, [actions]);

  const loadingStateValue = useMemo(() => ({
    isActionLoading
  }), [isActionLoading]);

  const combinadoFilterValue = useMemo(() => ({
    selectedFilterId: selectedCombinedFilter,
    reportSources: combinedReportSources
  }), [selectedCombinedFilter, combinedReportSources]);

  return (
    <SectionsContext.Provider value={sectionsValue}>
      <MetadataContext.Provider value={metadataValue}>
        <IsTrashEnabledContext.Provider value={isTrashEnabledValue}>
          <IsPrintEnabledContext.Provider value={isPrintEnabledValue}>
            <ReportTypeContext.Provider value={reportTypeValue}>
              <ProfileImageContext.Provider value={profileImageValue}>
                <OrganizationLogoContext.Provider value={organizationLogoValue}>
                  <ShouldPrintSnapLogoContext.Provider value={shouldPrintSnapLogoValue}>
                    <ModeContext.Provider value={modeValue}>
                      <LoadingStateContext.Provider value={loadingStateValue}>
                        <CombinadoFilterContext.Provider value={combinadoFilterValue}>
                          <ActionsContext.Provider value={actionsValue}>
                            {children}
                          </ActionsContext.Provider>
                        </CombinadoFilterContext.Provider>
                      </LoadingStateContext.Provider>
                    </ModeContext.Provider>
                  </ShouldPrintSnapLogoContext.Provider>
                </OrganizationLogoContext.Provider>
              </ProfileImageContext.Provider>
            </ReportTypeContext.Provider>
          </IsPrintEnabledContext.Provider>
        </IsTrashEnabledContext.Provider>
      </MetadataContext.Provider>
    </SectionsContext.Provider>
  );
}// --- Hooks for state consumption ---
export function useReportSections() {
  const ctx = useContext(SectionsContext);
  if (ctx === undefined)
    throw new Error("useReportSections must be used within ReportProvider");
  return ctx;
}

export function useDeletedSections() {
  return useContext(DeletedSectionsContext) ?? [];
}

export function useReportMetadata() {
  const ctx = useContext(MetadataContext);
  if (!ctx)
    throw new Error("useReportMetadata must be used within ReportProvider");
  return ctx;
}

export function useReportType() {
  return useContext(ReportTypeContext);
}

export function useIsTrashEnabled() {
  return useContext(IsTrashEnabledContext);
}

export function useIsPrintEnabled() {
  return useContext(IsPrintEnabledContext);
}

export function useIsSaving() {
  const actions = useReportActions();
  return actions.isPendingSave?.() || false;
}

export function useProfileImage() {
  return useContext(ProfileImageContext);
}

export function useOrganizationLogo() {
  return useContext(OrganizationLogoContext);
}

export function useShouldPrintSnapLogo() {
  return useContext(ShouldPrintSnapLogoContext);
}

export function useReportMode(): RenderMode {
  return useContext(ModeContext);
}

export function useActionLoading() {
  return useContext(LoadingStateContext);
}

// --- Unified hook for actions ---
export function useReportActions(): ReportActions {
  const ctx = useContext(ActionsContext);
  if (!ctx)
    throw new Error("useReportActions must be used within ReportProvider");
  return ctx;
}

export function useCombinadoFilter() {
  return useContext(CombinadoFilterContext);
}

// Helper hook to get sections that works for both normal and combinado reports
export function useReportSections(): ReportSection[] {
  const sections = useContext(SectionsContext);
  const reportType = useContext(ReportTypeContext);
  const { selectedFilterId } = useContext(CombinadoFilterContext);
  const isCombinado = reportType === REPORT_CONSTANTS.types.combinado;

  if (sections === undefined) {
    throw new Error("useReportSections must be inside ReportProvider");
  }

  // For normal reports, return sections as-is
  if (!isCombinado) {
    return sections;
  }

  // For combinado reports, extract the appropriate sections
  const combinadoContainer = sections.find((section: any) =>
    section.combined_data || section.report_sources_data
  ) as any;

  if (!combinadoContainer) {
    return sections;
  }

  const showCombinedData = selectedFilterId === 'combined_data';

  if (showCombinedData && Array.isArray(combinadoContainer.combined_data)) {
    return combinadoContainer.combined_data;
  } else if (Array.isArray(combinadoContainer.report_sources_data)) {
    return combinadoContainer.report_sources_data.filter(
      (sourceSection: any) =>
        !!sourceSection && sourceSection.user_reports_id === selectedFilterId
    );
  }

  return sections;
}

/**
 * Walk a value tree looking for any nested `is_deleted === true`.
 * Only looks at objects and arrays; primitives are ignored.
 */
function hasAnyDeleted(obj: any): boolean {
  if (obj && typeof obj === "object") {
    // If this object itself is a deleted‐flag container, pick it up.
    if (obj.is_deleted === true) return true;

    // Otherwise, check all its properties or array entries
    return Object.values(obj).some((v) => hasAnyDeleted(v));
  }
  return false;
}

function isReportDataCombined(value: unknown): value is ReportDataCombined {
  if (!value || typeof value !== 'object') return false;
  const obj = value as Record<string, ReportSection[]>;
  return Array.isArray(obj[REPORT_CONSTANTS.combined_data_keys.combined_data] as ReportSection[]) ||
    Array.isArray(obj[REPORT_CONSTANTS.combined_data_keys.report_sources_data] as ReportSection[]);
}


function filterCombinadoSections(
  sections: ReportSection[],
  selectedFilterId: string,
  reportType: string
): ReportSection[] {

  if (reportType !== REPORT_CONSTANTS.types.combinado || !Array.isArray(sections)) {
    console.warn('Sem dados combinados disponíveis');
    throw new Error('Sem dados combinados disponíveis');
  }

  const combinadoContainer = sections.find((section) =>
    isReportDataCombined(section as unknown)
  ) as ReportDataCombined | undefined;

  const showCombinedData = selectedFilterId === 'combined_data';

  if (!combinadoContainer) {
    console.warn('Sem dados combinados disponíveis');
    throw new Error('Sem dados combinados disponíveis');
  }

  let sectionsToReturn: ReportSection[] = [];

  if (showCombinedData && Array.isArray(combinadoContainer.combined_data)) {
    sectionsToReturn = combinadoContainer.combined_data;
  } else if (Array.isArray(combinadoContainer.report_sources_data)) {
    sectionsToReturn = combinadoContainer.report_sources_data.filter(
      (sourceSection: ReportSection) =>
        !!sourceSection && sourceSection.user_reports_id === selectedFilterId
    );
  }

  return sectionsToReturn;
}

export function useVisibleReportSections() {
  const sections = useContext(SectionsContext);
  const mode = useContext(ModeContext);
  const reportType = useContext(ReportTypeContext);
  const { selectedFilterId } = useContext(CombinadoFilterContext);
  const isCombinado = reportType === REPORT_CONSTANTS.types.combinado;

  if (sections === undefined) {
    throw new Error("useVisibleReportSections must be inside ReportProvider");
  }

  return useMemo(() => {
    const safeSections = Array.isArray(sections) ? sections : [];

    let filteredSections: ReportSection[] = safeSections;

    if (isCombinado) {
      try {
        filteredSections = filterCombinadoSections(safeSections, selectedFilterId, reportType);
      } catch (err) {
        console.warn('Erro ao filtrar seções combinadas:', err);
        filteredSections = safeSections;
      }
    }

    if (mode === "trash") {
      // show sections that have _any_ deleted flag, or
      // were deleted as a whole
      return filteredSections.filter(
        (sec) => sec.is_deleted === true || hasAnyDeleted(sec.data)
      );
    } else {
      // default & print-pdf: only fully non-deleted sections
      return filteredSections.filter((sec) => sec.is_deleted !== true && sec.data_count > 0);
    }
  }, [sections, mode, reportType, selectedFilterId]);
}