/**
 * Base validator class that provides common validation methods
 * for all DTOs in the application
 */
export abstract class BaseValidator<T> {
  /**
   * Validates if a single item matches the expected structure
   */
  abstract isValid(item: any): item is T;

  /**
   * Validates if an array contains at least one valid item
   */
  validateArray(items: any[]): boolean {
    if (!Array.isArray(items)) return false;
    return items.some(item => this.isValid(item));
  }

  /**
   * Filters an array to only include valid items
   */
  filterValid(items: any[]): T[] {
    if (!Array.isArray(items)) return [];
    return items.filter(item => this.isValid(item)) as T[];
  }

  /**
   * Sanitizes a valid item to ensure all values are properly formatted
   */
  abstract sanitize(item: T): T;

  /**
   * Processes an array by filtering valid items and sanitizing them
   */
  processData(items: any[]): T[] {
    return this.filterValid(items).map(item => this.sanitize(item));
  }
}