import { PDFDocument } from 'pdf-lib';
import * as logger from '../utils/logger';

export async function mergePdfs(coverPdfBuffer: Uint8Array<ArrayBufferLike>, reportPdfBuffer: Uint8Array<ArrayBufferLike>): Promise<Uint8Array> {
  try {
    logger.info('Starting PDF merge process');
    const startTime = Date.now();

    const mergedPdf = await PDFDocument.create();

    const coverPdf = await PDFDocument.load(coverPdfBuffer);
    const coverPages = await mergedPdf.copyPages(coverPdf, coverPdf.getPageIndices());

    coverPages.forEach((page) => mergedPdf.addPage(page));
    logger.debug(`Added ${coverPages.length} cover pages`);

    const reportPdf = await PDFDocument.load(reportPdfBuffer);
    const reportPages = await mergedPdf.copyPages(reportPdf, reportPdf.getPageIndices());

    reportPages.forEach((page) => mergedPdf.addPage(page));
    logger.debug(`Added ${reportPages.length} report pages`);
    const mergedPdfBytes = await mergedPdf.save();

    const mergeTime = Date.now() - startTime;
    logger.info('PDF merge completed', {
      duration: mergeTime,
      totalPages: coverPages.length + reportPages.length,
      coverPages: coverPages.length,
      reportPages: reportPages.length,
      finalSize: mergedPdfBytes.length
    });

    return mergedPdfBytes;

  } catch (err) {
    logger.error('PDF merge error', {
      error: err as Error
    });
    throw new Error(`Failed to merge PDFs: ${(err as Error).message}`);
  }
}
