import logging
from fastapi import (APIRouter, Depends)
from sqlalchemy.ext.asyncio import AsyncSession

from database.db import get_db

from services.auth_service import auth_guard
from services.apikey_service import ApikeyService

from schemas.apikey_schema import Api<PERSON>eyBase

from utils.jwt_utils import JW<PERSON>tils

from core.constants import Roles

from exceptions.business_exceptions import MissingSetApiKeyAccessError, SetApiKeyError
from exceptions.base_exceptions import ValidationError, BusinessLogicError, ProblemDetail

logger = logging.getLogger(__name__)

router = APIRouter()



@router.post("/set_apikey")
async def set_apikey_endpoint(
    apikey_info: ApiKeyBase,
    user: dict = Depends(auth_guard),
    db: AsyncSession = Depends(get_db)):
    
    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    logger.info(f"API key set request received for user: {user_id}")
    logger.info(f"ApiKey headers: {apikey_info}")
    
    if Roles.add_api_key not in user_roles:
        raise MissingSetApiKeyAccessError()

    try:
        api_key = apikey_info.apiKey
        logger.debug(f"API key extracted from request for user: {user_id}")
        
        api_key_service = ApikeyService(db=db, user_id=user_id)
        logger.info(f"ApikeyService initialized for user: {user_id}")
        
        api_key_service.set_api_key(api_key=api_key)
        logger.info(f"API key set successfully for user: {user_id}")
        
        await api_key_service.update_api_key_on_db(user_roles=user_roles)
        logger.info(f"API key updated in database for user: {user_id}")
        
        logger.info(f"API key set operation completed successfully for user: {user_id}")
        return {"message": "API key set successfully"}
        
    except (ValidationError, BusinessLogicError, ProblemDetail) as e:
        logger.error(f"Business/validation error setting API key for user {user_id}: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"Error setting API key for user {user_id}: {str(e)}")
        logger.exception("Full exception details:")
        raise SetApiKeyError()
