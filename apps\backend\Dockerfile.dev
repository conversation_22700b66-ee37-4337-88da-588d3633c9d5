FROM python:3.12-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# Create working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    curl \
    netcat-openbsd \
    bash \
    git \
    vim \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for development
RUN useradd -m -s /bin/bash vscode && \
    chown -R vscode:vscode /app

# Copy requirements first for better caching
COPY ./apps/backend/requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir \
    black \
    flake8 \
    pytest \
    pytest-asyncio \
    debugpy

    # Install Node.js (LTS)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs


# Copy application source code
COPY ./apps/backend /app

# Copy wait script and make executable
COPY ./scripts/wait_until_kafka_alive.sh /app/scripts/wait_until_kafka_alive.sh
RUN chmod +x /app/scripts/wait_until_kafka_alive.sh

# Change ownership to vscode user
RUN chown -R vscode:vscode /app

# Expose port
EXPOSE 8000

# Switch to vscode user
USER vscode

# Default command (will be overridden by devcontainer)
CMD ["tail", "-f", "/dev/null"] 