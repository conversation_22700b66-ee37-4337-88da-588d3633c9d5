from reports_processor.constants import PreProcessEntity, CacheableEntityTypes, ReportKeys, logger
from reports_processor.dutils.EntityCache import EntityCache
from collections import deque

def pre_process_entity(entity_type, data):
    if entity_type is PreProcessEntity.TELEFONE:
        TelefonePreProcessEntity.pre_process(data)

    return


def pre_process_entities(entity_type: str, entities: list, source):
    stack = deque()
    stack.append((entity_type, entities))

    while stack:
        current_type, current_entities = stack.pop()

        try:
            ent_type = CacheableEntityTypes(current_type)
            for entity in current_entities:
                EntityCache.add_entity(ent_type, entity, source)
        except ValueError:
            pass

        try:
            ent_type = PreProcessEntity(current_type)
            for entity in current_entities:
                pre_process_entity(ent_type, entity)
        except ValueError:
            pass

        for entity in current_entities:
            if type(entity) is not dict:
                logger.warning(f"cannot pre process entity of type {type(entity)}")
            for k, v in entity.items():
                if isinstance(v, list):
                    stack.append((k, v))



class BasePreProcessEntity:
    @classmethod
    def pre_process(cls, data):
        pass

class TelefonePreProcessEntity(BasePreProcessEntity):
    @classmethod
    def pre_process(cls, data):

        # Extract the phone number
        numero = str(data.get(ReportKeys.NUMERO, ''))
        area_code = str(data.get(ReportKeys.AREA_CODE, ''))
        country_code = str(data.get(ReportKeys.COUNTRY_CODE, ''))  # Default to Brazil

        if not numero and not area_code and not country_code:
            return

        if not country_code:
            country_code = '55'

        if numero.startswith('+'):
            numero = numero[1:]

        if numero.endswith('*0'):
            numero = numero[:-2]

        if area_code.endswith('.0'):
            area_code = area_code[:-2]

        if country_code and country_code != '55':
            if area_code:
                if numero.startswith(f'{country_code}{area_code}'):
                    numero = numero[len(f'{country_code}{area_code}'):]

            elif numero.startswith(country_code):
                numero = numero[len(f'{country_code}'):]

        else:
                # Case: numero contains everything
                # If it already starts with country code (more sophisticated detection)
                if len(numero) >= 12 and numero[:2] in ['55'] and numero[2:4].isdigit() and 11 <= int(
                        numero[2:4]):
                    # Looks like country code + area code + phone
                    country_code = numero[:2]
                    area_code = numero[2:4]
                    numero = numero[4:]

                elif len(numero) >= 10 and numero[:2].isdigit() and 11 <= int(numero[:2]) <= 99:
                    # Starts with valid area code, probably missing country code
                    area_code = numero[:2]
                    numero = numero[2:]
                else:
                    area_code = area_code if area_code else '??'

        data.pop(ReportKeys.AREA_CODE, None)
        data.pop(ReportKeys.COUNTRY_CODE, None)
        data[ReportKeys.NUMERO] = f'+{country_code} ({area_code}) {numero}'







