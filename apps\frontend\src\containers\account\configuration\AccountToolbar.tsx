import { Button, Select } from '@snap/design-system'
import { Calendar, SortAsc } from 'lucide-react';
import { useState } from 'react'

const AccountToolbar = () => {
  const [selectedReportType, setSelectedReportType] = useState<string>("TODOS OS TIPOS");

  return (
    <div className="py-4 flex items-center gap-4 justify-between">
      <Select
        options={
          [
            { label: "TODOS OS TIPOS", value: "TODOS OS TIPOS" },
            { label: "CPF", value: "cpf" },
            { label: "CNPJ", value: "cnpj" },
            { label: "EMAIL", value: "email" },
            { label: "TELEFONE", value: "telefone" }
          ]
        }
        value={selectedReportType}
        onChange={setSelectedReportType}
        placeholder="Selecionar tipo"
        data-testid="select-report-type"
        className="w-1/4"
      />
      <div className="flex gap-1 items-center">
        <Button variant="ghost">
          <Calendar size={32} />
        </Button>
        <Button variant="ghost">
          <SortAsc size={32} />
        </Button>
      </div>
    </div>
  )
}

export default AccountToolbar