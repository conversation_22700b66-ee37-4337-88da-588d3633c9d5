import { useEffect } from "react";
import { useAuth } from "~/hooks/useAuth";
import { eventBus } from "~/helpers/eventBus.helper";

export const AuthEventHandler = () => {
  const { logoutMutation } = useAuth();

  useEffect(() => {
    const unsubscribe = eventBus.subscribe("UNAUTHORIZED_ERROR", async () => {
      try {
        console.log("[AuthEventHandler] Handling UNAUTHORIZED_ERROR event - starting logout");
        await logoutMutation.mutateAsync();
        console.log("[AuthEventHandler] Logout completed successfully");
        eventBus.resetLogoutFlag();
      } catch (error) {
        console.error("[AuthEventHandler] Logout mutation failed:", error);
        eventBus.resetLogoutFlag();
      }
    });

    return () => {
      unsubscribe();
    };
  }, [logoutMutation]);

  return null;
};
