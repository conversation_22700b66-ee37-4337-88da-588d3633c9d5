import { ISectionOptions } from "docx";
import { REPORT_SECTIONS } from "../../../config/constants";
import type { ReportSection } from "../../../global";

import { renderDadosPessoais } from "./renderDadosPessoais.strategy";
import { renderMandados } from "./renderMandados.strategy";
import { renderEndereco } from "./renderEndereco.strategy";
import { renderEmail } from "./renderEmail.strategy";
import { renderTelefone } from "./renderTelefone.strategy";
import { renderProcessos } from "./renderProcessos.strategy";
import { renderParentes } from "./renderParentes.strategy";
import { renderVinculosEmpregaticios } from "./renderVinculosEmpregaticios.strategy";
import { renderDiariosOficiais } from "./renderDiariosOficiais.strategy";
import { renderPossiveisContatos } from "./renderPossiveisContatos.strategy";
import { renderPossiveisContas } from "./renderPossiveisContas.strategy";
import { renderSociedades } from "./renderSociedades.strategy";
import { renderSocios } from "./renderSocios.strategy";
import { renderRecursosPublicos } from "./renderRecursosPublicos.strategy";
import { renderServicosPublicos } from "./renderServicosPublicos.strategy";
import { renderFiliacaoPartidaria } from "./renderFiliacaoPartidaria.strategy";
import { renderDoacoesEnviadas } from "./renderDoacoesEnviadas.strategy";
import { renderDoacoesRecebidas } from "./renderDoacoesRecebidas.strategy";
import { renderFornecimentosEnviados } from "./renderFornecimentosEnviados.strategy";
import { renderFornecimentosRecebidos } from "./renderFornecimentosRecebidos.strategy";
import { renderJuntasComerciais } from "./renderJuntasComerciais.strategy";
import { renderImagens } from "./renderImagens.strategy";
import { renderOutrasUrls } from "./renderOutrasUrls.strategy";
import { renderNomeUsuarios } from "./renderNomeUsuarios.strategy";
import { renderRedesSociais } from "./renderRedesSociais.strategy";
import { renderPossiveisPessoasRelacionadas } from "./renderPossiveisPessoasRelacionadas.strategy";
import { renderVinculosEducacionais } from "./renderVinculosEducacionais.strategy";

export type DocxRenderer = (section: ReportSection) => ISectionOptions;
type ReportType = 'cpf' | 'telefone' | 'email' | 'cnpj';

const common_base_map: Record<string, DocxRenderer> = {
  [REPORT_SECTIONS.emails]: (section) => renderEmail({ section } as any),
  [REPORT_SECTIONS.telefones]: (section) => renderTelefone({ section } as any),
  [REPORT_SECTIONS.enderecos]: (section) => renderEndereco({ section } as any)
};

const cpf_telefone_email_map: Record<string, DocxRenderer> = {
  [REPORT_SECTIONS.dados_pessoais]: (section) => renderDadosPessoais({ section } as any),
  [REPORT_SECTIONS.parentes]: (section) => renderParentes({ section } as any),
  [REPORT_SECTIONS.possiveis_contatos]: (section) => renderPossiveisContatos({ section } as any),
  [REPORT_SECTIONS.possiveis_contas_em_sites]: (section) => renderPossiveisContas({ section } as any),
  [REPORT_SECTIONS.imagens]: (section) => renderImagens({ section } as any),
  [REPORT_SECTIONS.nomes_usuario]: (section) => renderNomeUsuarios({ section } as any),
  [REPORT_SECTIONS.outras_urls]: (section) => renderOutrasUrls({ section } as any),
  [REPORT_SECTIONS.perfis_redes_sociais]: (section) => renderRedesSociais({ section: section as any }),
  [REPORT_SECTIONS.vinculos_educacionais]: (section) => renderVinculosEducacionais({ section } as any)
};

const cpf_cnpj_map: Record<string, DocxRenderer> = {
  [REPORT_SECTIONS.processos]: (section) => renderProcessos({ section } as any),
  [REPORT_SECTIONS.socios]: (section) => renderSocios({ section } as any),
  [REPORT_SECTIONS.recursos_publicos_recebidos]: (section) => renderRecursosPublicos({ section } as any),
  [REPORT_SECTIONS.doacoes_enviadas_campanha]: (section) => renderDoacoesEnviadas({ section } as any),
  [REPORT_SECTIONS.doacoes_recebidas_campanha]: (section) => renderDoacoesRecebidas({ section } as any),
  [REPORT_SECTIONS.fornecimentos_enviados_campanha]: (section) => renderFornecimentosEnviados({ section } as any),
  [REPORT_SECTIONS.fornecimentos_recebidos_campanha]: (section) => renderFornecimentosRecebidos({ section } as any),
  [REPORT_SECTIONS.diarios_oficiais_nome]: (section) => renderDiariosOficiais({ section } as any)
};

const telefone_email_map: Record<string, DocxRenderer> = {
  [REPORT_SECTIONS.empresas_relacionadas]: (section) => renderSociedades({ section } as any),
  [REPORT_SECTIONS.possiveis_pessoas_relacionadas]: (section) => renderPossiveisPessoasRelacionadas({ section } as any)
};

const cpf_map: Record<string, DocxRenderer> = {
  [REPORT_SECTIONS.mandados_de_prisao]: (section) => renderMandados({ section } as any),
  [REPORT_SECTIONS.sociedades]: (section) => renderSociedades({ section } as any),
  [REPORT_SECTIONS.vinculos_empregaticios]: (section) => renderVinculosEmpregaticios({ section } as any),
  [REPORT_SECTIONS.diarios_oficiais_cpf]: (section) => renderDiariosOficiais({ section } as any),
  [REPORT_SECTIONS.servico_publico]: (section) => renderServicosPublicos({ section } as any),
  [REPORT_SECTIONS.filiacao_partidaria]: (section) => renderFiliacaoPartidaria({ section } as any)
};

const cnpj_map: Record<string, DocxRenderer> = {
  [REPORT_SECTIONS.dados_pessoais]: (section) => renderDadosPessoais({ section } as any),
  [REPORT_SECTIONS.juntas_comerciais]: (section) => renderJuntasComerciais({ section } as any),
  [REPORT_SECTIONS.diarios_oficiais_cnpj]: (section) => renderDiariosOficiais({ section } as any)
};

const DocxStrategyMap = new Map<ReportType, Record<string, DocxRenderer>>([
  ['cpf', {...common_base_map, ...cpf_telefone_email_map, ...cpf_cnpj_map, ...cpf_map}],
  ['telefone', {...common_base_map, ...cpf_telefone_email_map, ...telefone_email_map}],
  ['email', {...common_base_map, ...cpf_telefone_email_map, ...telefone_email_map}],
  ['cnpj', {...common_base_map, ...cpf_cnpj_map, ...cnpj_map}]
]);

export function useDocxStrategyMap(reportType: ReportType): Record<string, DocxRenderer> {
  return DocxStrategyMap.get(reportType) || common_base_map;
}
