// import puppeteer from 'puppeteer';
// import fs from 'fs';
// import path from 'path';
// import { ReportMetadata, ReportSection } from '../global';
// import { GenerateReportsHtml } from '../service/generateReportsHtml';
// import { GenerateReportsPdfWithDebug } from './generateReportsPdfWithDebug';

// const reportJsonPath = path.join(__dirname, 'report_cpf.json');
// const reportData = JSON.parse(fs.readFileSync(reportJsonPath, 'utf8'));

// function extractMetadata(data: any): ReportMetadata {
//   const metadata: ReportMetadata = {
//     report_id: data.user_reports_id,
//     report_status: data.report_status,
//     report_type: data.report_type,
//     report_search_args: data.report_search_args,
//     report_name: data.report_name,
//     creation_at: data.created_at,
//     modified_at: data.modified_at,
//     subject_name: data.subject_name,
//     subject_mother_name: data.subject_mother_name,
//     subject_age: data.subject_age,
//     subject_sex: data.subject_sex
//   };

//   return metadata;
// }

// function extractSections(data: any): ReportSection[] {
//   if (!data.data || !data.data.cpf || !Array.isArray(data.data.cpf)) {
//     console.warn('No sections found in data.cpf');
//     return [];
//   }

//   return data.data.cpf.map((section: any) => ({
//     title: section.title || "Unknown Section",
//     subtitle: section.subtitle || "",
//     subsection: section.subsection || "",
//     source: section.sources || [],
//     data_count: section.data_count || section.data?.length || 0,
//     is_deleted: section.is_deleted || false,
//     data: section.data || []
//   }));
// }

// const mockMetadata = extractMetadata(reportData);
// const mockSections = extractSections(reportData);

// console.log('🔍 Extracted metadata:', {
//   report_name: mockMetadata.report_name,
//   report_type: mockMetadata.report_type,
//   subject_name: mockMetadata.subject_name,
//   sections_count: mockSections.length
// });

// console.log('📋 Sections found:', mockSections.map(s => `${s.title} (${s.data_count} items)`));

// const mockUserPrintSnapLogo = true;
// const mockOrganizationLogoUrl = "https://snapforensics.com/wp-content/uploads/2025/01/SNAP-LOGO-1.svg";

// async function testCpfReportDebug() {
//   console.log('🚀 Starting CPF Report Debug test...');
//   console.log(`📊 Testing with ${mockSections.length} sections and ${mockSections.reduce((sum, s) => sum + s.data_count, 0)} total data items`);
//   console.log(`🏷️ Testing with organization logo URL: ${mockOrganizationLogoUrl}`);

//   let browser;
//   try {
//     browser = await puppeteer.launch({
//       headless: false,
//       args: ['--no-sandbox', '--disable-setuid-sandbox'],
//       devtools: true,
//     });

//     console.log('✅ Browser launched');

//     console.log('🔄 Using GenerateReportsHtml service...');
//     const startHtmlTime = Date.now();

//     const htmlResult = await GenerateReportsHtml({
//       sections: mockSections,
//       metadata: mockMetadata,
//       profile_image: undefined,
//       should_print_snap_logo: mockUserPrintSnapLogo,
//       organization_logo: mockOrganizationLogoUrl
//     });

//     const htmlGenerationTime = Date.now() - startHtmlTime;
//     console.log(`✅ HTML generation completed in ${htmlGenerationTime}ms`);
//     console.log('� HTML result structure:', {
//       hasHeader: !!htmlResult.header,
//       hasContent: !!htmlResult.content,
//       hasFooter: !!htmlResult.footer,
//       hasOrganizationLogo: htmlResult.hasOrganizationLogo,
//       shouldPrintSnapLogo: htmlResult.should_print_snap_logo,
//       headerLength: htmlResult.header?.length || 0,
//       contentLength: htmlResult.content?.length || 0,
//       footerLength: htmlResult.footer?.length || 0
//     });

//     console.log('🔄 Using GenerateReportsPdf service...');
//     const startPdfTime = Date.now();

//     console.log('📄 About to call GenerateReportsPdf with params:');
//     console.log('📄 Header length:', htmlResult.header?.length || 0);
//     console.log('📄 Footer length:', htmlResult.footer?.length || 0);
//     console.log('📄 Content length:', htmlResult.content?.length || 0);
//     console.log('📄 Has organization logo:', htmlResult.hasOrganizationLogo);
//     console.log('📄 Should print snap logo:', htmlResult.should_print_snap_logo);

//     const pdfBuffer = await GenerateReportsPdfWithDebug({
//       browserRef: browser,
//       header: htmlResult.header,
//       content: htmlResult.content,
//       footer: htmlResult.footer,
//       hasOrganizationLogo: htmlResult.hasOrganizationLogo,
//       should_print_snap_logo: htmlResult.should_print_snap_logo
//     });

//     const pdfGenerationTime = Date.now() - startPdfTime;
//     console.log(`✅ PDF generation completed in ${pdfGenerationTime}ms`); const outputPath = path.join(__dirname, 'test-cpf-report-debug-output.pdf');
//     fs.writeFileSync(outputPath, pdfBuffer);

//     console.log(`✅ CPF report debug PDF saved to: ${outputPath}`);
//     console.log(`📄 File size: ${(pdfBuffer.length / 1024 / 1024).toFixed(2)} MB`);
//     console.log('\n📊 Test Details:');
//     console.log(`📝 Sections tested: ${mockSections.length}`);
//     console.log(`📈 Total data items: ${mockSections.reduce((sum, s) => sum + s.data_count, 0)}`);
//     console.log(`🏷️ Report type: ${mockMetadata.report_type}`);
//     console.log(`👤 Subject: ${mockMetadata.subject_name}`);
//     console.log(`🔍 Search value: ${Object.values(mockMetadata.report_search_args)[0]}`);
//     console.log(`🖼️ Organization logo: ${mockOrganizationLogoUrl}`);
//     console.log(`⏱️ HTML generation time: ${htmlGenerationTime}ms`);
//     console.log(`⏱️ PDF generation time: ${pdfGenerationTime}ms`);
//     console.log(`⏱️ Total time: ${htmlGenerationTime + pdfGenerationTime}ms`);

//   } catch (error) {
//     console.error('Error:', error instanceof Error ? error.message : String(error));
//     console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
//     throw error;
//   } finally {
//     if (browser) {
//       await browser.close();
//       console.log('✅ Browser closed');
//     }
//   }
// }

// if (require.main === module) {
//   testCpfReportDebug()
//     .then(() => {
//       console.log('CPF report debug test completed successfully!');
//       process.exit(0);
//     })
//     .catch((error) => {
//       console.error('CPF report debug test failed:', error instanceof Error ? error.message : String(error));
//       process.exit(1);
//     });
// }

// export { testCpfReportDebug };
