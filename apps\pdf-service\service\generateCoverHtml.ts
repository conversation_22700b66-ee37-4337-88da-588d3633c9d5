import React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import { Cover } from '../components/Cover';
import { ReportMetadata } from '../global';
import { Document } from '../components/pdf-components';
import { getImageAsDataUrlAsync } from '../helpers';

interface IGenerateCoverHTMLInput {
  metadata: ReportMetadata;
  should_print_snap_logo?: boolean;
  organization_logo?: string;
}

export async function GenerateCoverHtml({
  metadata,
  should_print_snap_logo = true,
  organization_logo
}: IGenerateCoverHTMLInput): Promise<string> {
  const organizationLogoBase64 = organization_logo ? await getImageAsDataUrlAsync(organization_logo) : undefined;

  const coverComponent = React.createElement(
    Document,
    {},
    React.createElement(Cover, { metadata, organization_logo: organizationLogoBase64, should_print_snap_logo })
  );
  const coverHtml = renderToStaticMarkup(coverComponent);

  const fullHtml = `
    <!DOCTYPE html>
    <html lang="pt-br">
    <head>
      <meta charset="UTF-8">
      <title>Cover Page</title>
    </head>
    <body>
      ${coverHtml}
    </body>
    </html>
  `.trim();

  return fullHtml;
}
