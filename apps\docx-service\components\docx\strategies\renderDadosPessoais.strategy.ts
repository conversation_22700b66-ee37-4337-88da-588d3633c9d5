import {
  ISectionOptions,
  Paragraph,
  TextRun,
  Table,
  TableRow,
  TableCell,
  ShadingType,
  WidthType,
} from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import {
  parseValue,
  translatePropToLabel,
  translateSource,
} from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderDadosPessoaisProps {
  section: Omit<ReportSection, "data"> & {
    data: Array<{
      detalhes?: Record<string, ValueWithSource<string>>;
    }>;
  };
}

export const renderDadosPessoais = ({
  section,
}: RenderDadosPessoaisProps): ISectionOptions => {
  const entry = section.data?.[0]?.detalhes ?? {};
  const items = Object.entries(entry).filter(
    ([, v]) => typeof v.value === "string" && v.is_deleted === false
  ) as Array<[string, ValueWithSource<string>]>;

  const tableRows = items.map(
    ([key, { label, value, source }]) =>
      new TableRow({
        children: [
          new TableCell({
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: "■ ",
                    color: "CCCCCC",
                    bold: true,
                  }),
                  new TextRun({
                    text: translatePropToLabel(label || key).toUpperCase(),
                    color: "889EA3",
                    bold: true,
                    size: 20,
                  }),
                  new TextRun({
                    text:
                      source
                        ?.map(
                          (src: string) => `| ${translateSource(src)}\u00A0`
                        )
                        .join("") || "",
                    color: "FE473C",
                    size: 16,
                  }),
                ],
              }),
            ],
            shading: {
              fill: "F9F9FA",
              type: ShadingType.CLEAR,
            },
          }),
          new TableCell({
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: parseValue(value),
                    size: 20,
                  }),
                ],
              }),
            ],
            shading: {
              fill: "F9F9FA",
              type: ShadingType.CLEAR,
            },
          }),
        ],
      })
  );

  const infoTable = new Table({
    rows: tableRows,
    width: {
      size: 100,
      type: WidthType.PERCENTAGE,
    },
    columnWidths: [3500, 5500],
    borders: {
      top: { style: "none", size: 0, color: "FFFFFF" },
      bottom: { style: "none", size: 0, color: "FFFFFF" },
      left: { style: "none", size: 0, color: "FFFFFF" },
      right: { style: "none", size: 0, color: "FFFFFF" },
      insideHorizontal: { style: "none", size: 0, color: "FFFFFF" },
      insideVertical: { style: "none", size: 0, color: "FFFFFF" },
    },
  });

  return {
    children: [createSectionTitle(section.title), infoTable],
  };
};
