import express, { Request, Response, NextFunction } from "express";
import cors from "cors";
import { errorHand<PERSON> } from "./middlewares/error.middleware";
import * as logger from "./utils/logger";
import { ServerConfig } from "./global";
import { DOCX_CONTROLLER } from "./controllers/docx.controller";

/**
 * Initialize and start the Express server
 * @param config - Server configuration
 * @returns The Express application instance
 */
export interface IServerParams {
  config: ServerConfig;
}
export function initializeServer({ config }: IServerParams) {
  logger.info("Initializing Express server");

  const app = express();

  const corsOrigin = process.env.FRONTEND_REDIRECT_URL || ["http://localhost:3000", "http://127.0.0.1:3000"];
  logger.info("CORS origin configured:", { corsOrigin });

  app.use(errorHandler);
  app.use(
    cors({
      origin: corsOrigin,
      methods: ["GET", "POST", "OPTIONS"],
      allowedHeaders: ["Content-Type", "Connect-Protocol-Version"],
      credentials: true,
    })
  );
  app.use(express.json({ limit: "50mb" }));
  app.use(express.urlencoded({ limit: "50mb", extended: true }));

  app.get("/health", (req: Request, res: Response) => {
    logger.debug("Health check requested");
    res.status(200).send("OK");
  });

  app.post(
    "/docx/reports",
    (req: Request, res: Response, next: NextFunction) => {
      logger.info("DOCX generation requested", {
        contentType: req.headers["content-type"],
        bodySize: JSON.stringify(req.body).length,
      });

      return DOCX_CONTROLLER.generate(req, res, next);
    }
  );

  app.listen(config.port, config.host, () => {
    logger.info(`Server running on ${config.host}:${config.port}`);
  });

  return app;
}
