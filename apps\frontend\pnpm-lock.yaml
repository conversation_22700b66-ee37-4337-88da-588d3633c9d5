lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@grafana/faro-web-sdk':
        specifier: ^1.13.1
        version: 1.15.0
      '@grafana/faro-web-tracing':
        specifier: ^1.13.1
        version: 1.15.0
      '@radix-ui/react-accordion':
        specifier: ^1.2.4
        version: 1.2.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-avatar':
        specifier: ^1.1.3
        version: 1.1.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-checkbox':
        specifier: ^1.3.2
        version: 1.3.2(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-dialog':
        specifier: ^1.1.6
        version: 1.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-dropdown-menu':
        specifier: ^2.1.6
        version: 2.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-label':
        specifier: ^2.1.2
        version: 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-radio-group':
        specifier: ^1.3.7
        version: 1.3.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-select':
        specifier: ^2.1.6
        version: 2.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot':
        specifier: ^1.1.2
        version: 1.2.0(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-tooltip':
        specifier: ^1.1.8
        version: 1.2.0(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@snap/design-system':
        specifier: 1.2.4
        version: 1.2.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(immer@10.1.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(tailwindcss@4.1.3)
      '@tailwindcss/postcss':
        specifier: ^4.0.14
        version: 4.1.3
      '@tailwindcss/vite':
        specifier: ^4.0.14
        version: 4.1.3(vite@6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1))
      '@tanstack/react-query':
        specifier: ^5.66.0
        version: 5.72.2(react@19.1.0)
      '@vitejs/plugin-react':
        specifier: ^4.3.4
        version: 4.3.4(vite@6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1))
      autoprefixer:
        specifier: ^10.4.20
        version: 10.4.21(postcss@8.5.3)
      axios:
        specifier: ^1.7.9
        version: 1.8.4
      class-variance-authority:
        specifier: ^0.7.1
        version: 0.7.1
      clsx:
        specifier: ^2.1.1
        version: 2.1.1
      cross-env:
        specifier: ^7.0.3
        version: 7.0.3
      immer:
        specifier: ^10.1.1
        version: 10.1.1
      inputmask:
        specifier: ^5.0.9
        version: 5.0.9
      js-cookie:
        specifier: ^3.0.5
        version: 3.0.5
      lucide-react:
        specifier: ^0.476.0
        version: 0.476.0(react@19.1.0)
      masonic:
        specifier: ^4.0.1
        version: 4.0.1(react@19.1.0)
      next-themes:
        specifier: ^0.4.4
        version: 0.4.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      postcss:
        specifier: ^8.5.2
        version: 8.5.3
      react:
        specifier: ^19.0.0
        version: 19.1.0
      react-dom:
        specifier: ^19.0.0
        version: 19.1.0(react@19.1.0)
      react-icons:
        specifier: ^5.4.0
        version: 5.5.0(react@19.1.0)
      react-layout-masonry:
        specifier: ^1.2.0
        version: 1.2.0(react@19.1.0)
      react-router:
        specifier: ^7.1.5
        version: 7.5.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react-use-websocket:
        specifier: ^4.13.0
        version: 4.13.0
      sonner:
        specifier: ^2.0.1
        version: 2.0.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      tailwind-merge:
        specifier: ^3.0.2
        version: 3.2.0
      tailwindcss:
        specifier: ^4.0.14
        version: 4.1.3
      tailwindcss-animate:
        specifier: ^1.0.7
        version: 1.0.7(tailwindcss@4.1.3)
      vite:
        specifier: ^6.1.0
        version: 6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1)
      zustand:
        specifier: ^5.0.3
        version: 5.0.3(@types/react@19.1.0)(immer@10.1.1)(react@19.1.0)
    devDependencies:
      '@commitlint/cli':
        specifier: ^19.7.1
        version: 19.8.0(@types/node@22.14.0)(typescript@5.8.3)
      '@commitlint/config-conventional':
        specifier: ^19.7.1
        version: 19.8.0
      '@eslint/js':
        specifier: ^9.20.0
        version: 9.24.0
      '@playwright/test':
        specifier: ^1.50.1
        version: 1.51.1
      '@tanstack/react-query-devtools':
        specifier: ^5.66.5
        version: 5.72.2(@tanstack/react-query@5.72.2(react@19.1.0))(react@19.1.0)
      '@testing-library/dom':
        specifier: ^10.4.0
        version: 10.4.0
      '@testing-library/jest-dom':
        specifier: ^6.6.3
        version: 6.6.3
      '@testing-library/react':
        specifier: ^16.2.0
        version: 16.3.0(@testing-library/dom@10.4.0)(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@testing-library/user-event':
        specifier: ^14.6.1
        version: 14.6.1(@testing-library/dom@10.4.0)
      '@types/inputmask':
        specifier: ^5.0.7
        version: 5.0.7
      '@types/js-cookie':
        specifier: ^3.0.6
        version: 3.0.6
      '@types/node':
        specifier: ^22.7.7
        version: 22.14.0
      '@types/react':
        specifier: ^19.0.10
        version: 19.1.0
      '@types/react-dom':
        specifier: ^19.0.4
        version: 19.1.2(@types/react@19.1.0)
      eslint:
        specifier: ^9.20.1
        version: 9.24.0(jiti@2.4.2)
      eslint-plugin-react:
        specifier: ^7.37.4
        version: 7.37.5(eslint@9.24.0(jiti@2.4.2))
      globals:
        specifier: ^15.14.0
        version: 15.15.0
      husky:
        specifier: ^9.1.6
        version: 9.1.7
      jsdom:
        specifier: ^26.0.0
        version: 26.0.0
      lint-staged:
        specifier: ^15.4.3
        version: 15.5.0
      npm-run-all:
        specifier: ^4.1.5
        version: 4.1.5
      typescript:
        specifier: ^5.7.3
        version: 5.8.3
      typescript-eslint:
        specifier: ^8.24.0
        version: 8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)
      vite-plugin-pwa:
        specifier: ^0.21.1
        version: 0.21.2(vite@6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1))(workbox-build@7.3.0(@types/babel__core@7.20.5))(workbox-window@7.3.0)
      vite-plugin-wasm:
        specifier: ^3.4.1
        version: 3.4.1(vite@6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1))
      vitest:
        specifier: ^3.0.5
        version: 3.1.1(@types/node@22.14.0)(jiti@2.4.2)(jsdom@26.0.0)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1)

packages:

  '@adobe/css-tools@4.4.2':
    resolution: {integrity: sha1-yDaxvYHm1izWzfPuSUi83Ojqecg=}

  '@alloc/quick-lru@5.2.0':
    resolution: {integrity: sha1-e/aLIMCjUPk2kV/K4G9Y4yAHzjA=}
    engines: {node: '>=10'}

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=}
    engines: {node: '>=6.0.0'}

  '@apideck/better-ajv-errors@0.3.6':
    resolution: {integrity: sha1-lX1MKOiGpkqBQfdSJ4O+ZXM/8Jc=}
    engines: {node: '>=10'}
    peerDependencies:
      ajv: '>=8'

  '@asamuzakjp/css-color@3.1.1':
    resolution: {integrity: sha1-QaYSg02v2TU7iYVbN7qooD+2e/I=}

  '@babel/code-frame@7.26.2':
    resolution: {integrity: sha1-S1+rl9MzOO/5FiNQVfDrwh5XOoU=}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.26.8':
    resolution: {integrity: sha1-ghwdNWQcNVKE1KhwuKSnsMFB42c=}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.26.10':
    resolution: {integrity: sha1-XIdvg8jE3LIz7ktnDAYG8qwwAPk=}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.27.0':
    resolution: {integrity: sha1-dkOCtTkuW5r/k8rbGQ0HRYZsvCw=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.25.9':
    resolution: {integrity: sha1-2OrE0twNe24R+m5TUzLg0xhPBrQ=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.0':
    resolution: {integrity: sha1-3gx1OxzR2atV1HPFpc9xcPCoGIA=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.27.0':
    resolution: {integrity: sha1-UY+tajB8apb0SvFJErLCCr6b/DA=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.27.0':
    resolution: {integrity: sha1-DkH304wuvgbr2c8OAvsmAZx3zZU=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.6.4':
    resolution: {integrity: sha1-Feh0Y2i/pnF4X1km/3SzBkwpH6s=}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-member-expression-to-functions@7.25.9':
    resolution: {integrity: sha1-nf/+RvcnAFpeopBRrINftzXkwaM=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.25.9':
    resolution: {integrity: sha1-5/jSBgLr2/nrvqCgdR+w8qQUFxU=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.26.0':
    resolution: {integrity: sha1-jOVOydWSaV5Y2EzYhLe1xqL97q4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.25.9':
    resolution: {integrity: sha1-MySuULrn4qs8M/YMmod7agFGtU4=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.26.5':
    resolution: {integrity: sha1-GFgNAMmTQRetcZOSxPZYXJMzzDU=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.25.9':
    resolution: {integrity: sha1-5TlWqz1bn7iL4Es+LzG1I6/TS5I=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.26.5':
    resolution: {integrity: sha1-bLBOgq4pHa6OcjNd/kOLByXxTI0=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    resolution: {integrity: sha1-Cy4bYtVg1rGVSJP9K3BdwXyR8Mk=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.25.9':
    resolution: {integrity: sha1-Gqu3Lucu01eJtLvK08ooYs5hTow=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.25.9':
    resolution: {integrity: sha1-JLZOLD7HzTs8VHcpuNFocfIsvcc=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.25.9':
    resolution: {integrity: sha1-huRb2KSat+A/J2V3+WF5ZT1B2nI=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.25.9':
    resolution: {integrity: sha1-2Z39WVMS5siUvX0jdHACXIXuqdA=}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.0':
    resolution: {integrity: sha1-U9FWCY3vqCQ+qw8y+hdYkHWhuAg=}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.27.0':
    resolution: {integrity: sha1-PX1u4mjkHSYACRy9ThRf/uhaROw=}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.9':
    resolution: {integrity: sha1-zC5T6/CgNAd3//XtUhlD4lO02P4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.9':
    resolution: {integrity: sha1-r55PtjzLiry5I3Wy/P42tgx3TTA=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.9':
    resolution: {integrity: sha1-6Nwm/NYW5sW/K9DVosFR1PkqkTc=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.25.9':
    resolution: {integrity: sha1-gHpmf5FYrKxvYWS0vrha2evJ4dE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.9':
    resolution: {integrity: sha1-3nCT8efer2jq3XzGsH8quCVDJp4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2':
    resolution: {integrity: sha1-eET5KJVG76n+usLeTP41igUL1wM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.26.0':
    resolution: {integrity: sha1-YgQSQFBY76VuSlZJA7eTVQIPRF8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.26.0':
    resolution: {integrity: sha1-OxQShHaZ7qc5tPJgLHTONvawsPc=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6':
    resolution: {integrity: sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-arrow-functions@7.25.9':
    resolution: {integrity: sha1-eCHUQQvuXaqtu0zdmmZJcE4XaEU=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-generator-functions@7.26.8':
    resolution: {integrity: sha1-XjmRE147nG6q9e/1bRrloR30X/g=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.25.9':
    resolution: {integrity: sha1-yAAI2srlFIJ5PlqcCLOaW+fhLXE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.26.5':
    resolution: {integrity: sha1-PcRAXTGtHL5FKTqlcgWm47AJ1T4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.27.0':
    resolution: {integrity: sha1-rMLA2Yp0ObveQkRYjdvUkEcB1H8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-properties@7.25.9':
    resolution: {integrity: sha1-qM6E/tua1RJUmYQQH6hAgKn19R8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-static-block@7.26.0':
    resolution: {integrity: sha1-bI2iGfTrFcrpg07ENI/46eCWZKA=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0

  '@babel/plugin-transform-classes@7.25.9':
    resolution: {integrity: sha1-cVJFf3iAtZOmOt6Khh5uJqRGn1I=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.25.9':
    resolution: {integrity: sha1-2zZJLHhGDlNLiFKx1b7+PJI+8Qs=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.25.9':
    resolution: {integrity: sha1-lm6iWVxJgiQ0CINgLTz9egx5zqE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.25.9':
    resolution: {integrity: sha1-uteUXdB3NMpS/jrU6HK0DtCbsJo=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.25.9':
    resolution: {integrity: sha1-iFDd9X3OKuu0OUu0NKdZgDEFnm0=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.9':
    resolution: {integrity: sha1-b3JZtN4SdyGgjx5RZbhS/KppbTE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-dynamic-import@7.25.9':
    resolution: {integrity: sha1-I+kX3mPtI8ZgDF3QbZRmnc5597g=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.26.3':
    resolution: {integrity: sha1-4p8Btt4wLHwseUJ3pI8Eqcp/A7w=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-export-namespace-from@7.25.9':
    resolution: {integrity: sha1-kHRf5VBTOU9VTkBYTNqB8sikAqI=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.26.9':
    resolution: {integrity: sha1-JyMfedUXDvM7URHwf+XK/rLJalY=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.25.9':
    resolution: {integrity: sha1-k52VbmimBmYQBb/VUMT8Lvlfe5c=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-json-strings@7.25.9':
    resolution: {integrity: sha1-yG20B8uCfN7ZAqkMcH0ngaqolmA=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.25.9':
    resolution: {integrity: sha1-GhxrTUqlm8TK1bazoiOgq9aFyd4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-logical-assignment-operators@7.25.9':
    resolution: {integrity: sha1-sZRBqMOaL9oJApALMG6gWuEFXbc=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.25.9':
    resolution: {integrity: sha1-Y9/xl2PqZKMfXmwglX5qJeQe1d4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.25.9':
    resolution: {integrity: sha1-SbpHjyKVEBVEq9eUSGzTCI3dtsU=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.26.3':
    resolution: {integrity: sha1-jwEdRLINAsPeRNiFDZcdhJf5gfs=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.25.9':
    resolution: {integrity: sha1-i9G0ODYmnj0zMHFRoRS887pnk/g=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.25.9':
    resolution: {integrity: sha1-ZxAHnN18aU2zZSmh6EEeSfy/FMk=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9':
    resolution: {integrity: sha1-RUmQrmzCL9Kg+mCzosb2OjgGTmo=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.25.9':
    resolution: {integrity: sha1-QuYXESlLEFwkgzbcsEt3BU6ovs0=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-nullish-coalescing-operator@7.26.6':
    resolution: {integrity: sha1-+/azySy1CeezGe5G49qJxb7dMf4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-numeric-separator@7.25.9':
    resolution: {integrity: sha1-v+11hmJhqLZDRosMz9J18gMyFKE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-rest-spread@7.25.9':
    resolution: {integrity: sha1-AgNyUCUHQWSAi88aLPqQxlLJnxg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.25.9':
    resolution: {integrity: sha1-OF1d4TUWKTO+tKPSJ6K35Su0zwM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-catch-binding@7.25.9':
    resolution: {integrity: sha1-EOcNltUrsfEMXKqsWaxUXqK6f/M=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-chaining@7.25.9':
    resolution: {integrity: sha1-4ULriZ0m73FUNfIBq24TlUHu590=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.25.9':
    resolution: {integrity: sha1-uFaEIgWz534Yt6ehuUlYBpx7olc=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-methods@7.25.9':
    resolution: {integrity: sha1-hH9BOSY1d1JkVdfTIjzYvaUeO1c=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-property-in-object@7.25.9':
    resolution: {integrity: sha1-nItz5k5sw8uydDYziFp90sOF/jM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.25.9':
    resolution: {integrity: sha1-1y1Yi9iLDeyLYuNvb9qRzt/ijj8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-self@7.25.9':
    resolution: {integrity: sha1-wLbK6cG3OWf3+esvypU2ui+tKFg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-source@7.25.9':
    resolution: {integrity: sha1-TGuNqlILXxVbX7VVR9fJ+pFBdQM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.27.0':
    resolution: {integrity: sha1-gi/uvvQ9almoH2lrJRLfWxaC2zE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regexp-modifiers@7.26.0':
    resolution: {integrity: sha1-L1g3pbXNOEKpGdgUfpkDzHRVuFA=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-reserved-words@7.25.9':
    resolution: {integrity: sha1-A5iu0vHxC6P3ipPbIZsn70F/uc4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.25.9':
    resolution: {integrity: sha1-u3heYJH5n4JqlfmJT8Fv3mHBY/I=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.25.9':
    resolution: {integrity: sha1-JKNRU5MbS6PRPOxKd0jCGrVRTvk=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.25.9':
    resolution: {integrity: sha1-x/ArlE6YakF4F7ILosUE38FFPTI=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.26.8':
    resolution: {integrity: sha1-lmsV0VOpkRcqVApprV4YRc7ZkLU=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.27.0':
    resolution: {integrity: sha1-BEoIkPPKaUIHx4JtDHpl5awAiq4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-escapes@7.25.9':
    resolution: {integrity: sha1-p17zlHzhU2P8yqOOLdm8cLJ4i4I=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-property-regex@7.25.9':
    resolution: {integrity: sha1-qQHpbywdBxsNG7XcDTyIDOj1PdM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.25.9':
    resolution: {integrity: sha1-Xq50f+OerPE6i9AGpPsLXR+l6bE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-sets-regex@7.25.9':
    resolution: {integrity: sha1-ZRFMF7T/wg+lsWPGPHDA0lYh+r4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/preset-env@7.26.9':
    resolution: {integrity: sha1-LsZOkD0O/nQ2mfd6EL33lVwhI8M=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-modules@0.1.6-no-external-plugins':
    resolution: {integrity: sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0

  '@babel/runtime@7.27.0':
    resolution: {integrity: sha1-++58+XxwlRjswfWQmESB1UYNR2I=}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.0':
    resolution: {integrity: sha1-slPlQGzB3xxX3NGPEXYMLb9AwLQ=}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.27.0':
    resolution: {integrity: sha1-EdfmRHeeFmwEQvmgcnTQLNkdSnA=}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.27.0':
    resolution: {integrity: sha1-75rLawbDFz9mMtmT7LbUrkcLRVk=}
    engines: {node: '>=6.9.0'}

  '@commitlint/cli@19.8.0':
    resolution: {integrity: sha1-5m5aUmhDfkK3xvKkR+lOqDuKMnI=}
    engines: {node: '>=v18'}
    hasBin: true

  '@commitlint/config-conventional@19.8.0':
    resolution: {integrity: sha1-ShRwoBVvXR9WBCbAXPw6anlsK5w=}
    engines: {node: '>=v18'}

  '@commitlint/config-validator@19.8.0':
    resolution: {integrity: sha1-CzDCdORCfTlC/WLs9TwZ2Z9DrEo=}
    engines: {node: '>=v18'}

  '@commitlint/ensure@19.8.0':
    resolution: {integrity: sha1-SBwwcG3EqkqOhefR8Yd8MOUgGg0=}
    engines: {node: '>=v18'}

  '@commitlint/execute-rule@19.8.0':
    resolution: {integrity: sha1-68rCbcddSB4iOww31Z5b3XoWTUo=}
    engines: {node: '>=v18'}

  '@commitlint/format@19.8.0':
    resolution: {integrity: sha1-sGCE+zuAfyFCwZyUVyEn1KOvXaE=}
    engines: {node: '>=v18'}

  '@commitlint/is-ignored@19.8.0':
    resolution: {integrity: sha1-PkExaLEiLmJ5jdGjbGTSRU57spE=}
    engines: {node: '>=v18'}

  '@commitlint/lint@19.8.0':
    resolution: {integrity: sha1-wr+F3m0uhuE8lyzxmi1CXmLpsFc=}
    engines: {node: '>=v18'}

  '@commitlint/load@19.8.0':
    resolution: {integrity: sha1-fHB4+9ycTjoU+lqGhMPLhUNTVR4=}
    engines: {node: '>=v18'}

  '@commitlint/message@19.8.0':
    resolution: {integrity: sha1-GsHFJ6YBsyk3O6UP/XCnE6ywuMo=}
    engines: {node: '>=v18'}

  '@commitlint/parse@19.8.0':
    resolution: {integrity: sha1-vOQV3aYOEVI5zTN7FSYuRZyrjro=}
    engines: {node: '>=v18'}

  '@commitlint/read@19.8.0':
    resolution: {integrity: sha1-0WvqhGYZEA4jxNVrzxkcOX2nVC8=}
    engines: {node: '>=v18'}

  '@commitlint/resolve-extends@19.8.0':
    resolution: {integrity: sha1-LvbESO1/klhAzKKC4843vqjptyY=}
    engines: {node: '>=v18'}

  '@commitlint/rules@19.8.0':
    resolution: {integrity: sha1-DKK3vZ3CJAkXOWPxUGHYysZxvao=}
    engines: {node: '>=v18'}

  '@commitlint/to-lines@19.8.0':
    resolution: {integrity: sha1-9733h4mZwGIPOi9G+Cn8wfHx0Rg=}
    engines: {node: '>=v18'}

  '@commitlint/top-level@19.8.0':
    resolution: {integrity: sha1-X/TZuL5EUB7dLJS5NgKnEE1j2S0=}
    engines: {node: '>=v18'}

  '@commitlint/types@19.8.0':
    resolution: {integrity: sha1-WnfHpyOnFJlaUZF+3Q7PzylJXTw=}
    engines: {node: '>=v18'}

  '@csstools/color-helpers@5.0.2':
    resolution: {integrity: sha1-glksmnwrg8KT2RYYlOKmRx/rl7g=}
    engines: {node: '>=18'}

  '@csstools/css-calc@2.1.2':
    resolution: {integrity: sha1-v/1V8ALasRm3bUAj+VzZQ+bIwR4=}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.4
      '@csstools/css-tokenizer': ^3.0.3

  '@csstools/css-color-parser@3.0.8':
    resolution: {integrity: sha1-X+kyKSCFFFC/XgZcKw5zG54WU5Q=}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.4
      '@csstools/css-tokenizer': ^3.0.3

  '@csstools/css-parser-algorithms@3.0.4':
    resolution: {integrity: sha1-dEJuk70cTcqz5EH1zHuk+zXZQ1Y=}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-tokenizer': ^3.0.3

  '@csstools/css-tokenizer@3.0.3':
    resolution: {integrity: sha1-pVAshTkmX+y9hzweOVqJAznxGcI=}
    engines: {node: '>=18'}

  '@esbuild/aix-ppc64@0.25.2':
    resolution: {integrity: sha1-uHA29kT1cu+ys8dXRsl9HS2HrOg=}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.25.2':
    resolution: {integrity: sha1-XKfcIKGPGJYK2NXm71z3sKJW4ZY=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.25.2':
    resolution: {integrity: sha1-PEn2B7cILN5wxs4MARw2LFehlO4=}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.25.2':
    resolution: {integrity: sha1-igAUd4ABav9Z4E8QNufLG2g4WeI=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.25.2':
    resolution: {integrity: sha1-SG7+dZmo2QoneA8rsDGNmoXGxCM=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.2':
    resolution: {integrity: sha1-le4iKqz2aMek89fuh7MkClG683Q=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.25.2':
    resolution: {integrity: sha1-Z+/O2oVUtvxqQ0dv66Bo+zf6LvY=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.2':
    resolution: {integrity: sha1-iKnX7N062tv+UifCEi0kgWlZuAk=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.25.2':
    resolution: {integrity: sha1-h74QmbK75hKCMzsIRzfUa8gwgFg=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.25.2':
    resolution: {integrity: sha1-cqKFsP5kSW4ZH8rSIhhde/n4FvY=}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.25.2':
    resolution: {integrity: sha1-M3qHpMTdSKgyuu1cuwIr4ggJ1zc=}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.25.2':
    resolution: {integrity: sha1-G4GqdxA9a4qM+nwJTtPSXHV5uio=}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.2':
    resolution: {integrity: sha1-r744C2mS50Wb98LDuVVmM7LkfzA=}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.2':
    resolution: {integrity: sha1-a/hpXKuKKxNcyhqlVSJtyTLVIGc=}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.2':
    resolution: {integrity: sha1-Q8LWeho5GZ+wa6l4rrtEmS177MM=}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.25.2':
    resolution: {integrity: sha1-QZ4lc37IFcbc4s0g0CbjR8u3pgI=}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.25.2':
    resolution: {integrity: sha1-IkUfbtu6hKvnVKjL2FKP9uKNm8s=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.25.2':
    resolution: {integrity: sha1-dEr/07jYI2sIxSENgosGmKYsWKw=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.2':
    resolution: {integrity: sha1-2751If1tc1LzQyjWdq+SP8D4p48=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.25.2':
    resolution: {integrity: sha1-+cr5h+PgVwUAgytIfOMDnKZIzp8=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.2':
    resolution: {integrity: sha1-0rtqD4/+p7OUu0PfzLsHyr2J92g=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.25.2':
    resolution: {integrity: sha1-SbQ37WP+MzuSE3t6DGWmWFIDGvs=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.25.2':
    resolution: {integrity: sha1-CBQkFoRjx9bH+3j2Ma7eDBBDc88=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.25.2':
    resolution: {integrity: sha1-P56HFD3dADEz0hOElEpsbK35aT8=}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.25.2':
    resolution: {integrity: sha1-g59ywt7NN4+GuPUl4Zeal7kgxn0=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@4.5.1':
    resolution: {integrity: sha1-sPx+BtDJT4AVN/1CN+3CcG07jkw=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/config-array@0.20.0':
    resolution: {integrity: sha1-ehIy6CN2cS0zQAEqL1YaJ2TRmI8=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/config-helpers@0.2.1':
    resolution: {integrity: sha1-JgQsAo0b7uXOIjWnkpuRxSZRZG0=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.12.0':
    resolution: {integrity: sha1-X5YMPVdyi+n2xlvYSqaqYTB4eY4=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.13.0':
    resolution: {integrity: sha1-vwLyCYRtO/mW+egAnbYt8nObRYw=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@3.3.1':
    resolution: {integrity: sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@9.24.0':
    resolution: {integrity: sha1-aFJ3mAu3v4TsyOThM8zdp1RaaR4=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.6':
    resolution: {integrity: sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.2.8':
    resolution: {integrity: sha1-R0iNj4FxtdRhPoMzE/POcI41Jfg=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@essentials/memoize-one@1.1.0':
    resolution: {integrity: sha1-mOfUJdFUiSGkwNKHeiv+LVrbhzU=}

  '@essentials/one-key-map@1.2.0':
    resolution: {integrity: sha1-lLtlfEIYojvUCh94Yhr3sq1JRhw=}

  '@essentials/raf@1.2.0':
    resolution: {integrity: sha1-J1VnACFhhDOqQ3RT3JitS8hDYXc=}

  '@essentials/request-timeout@1.3.0':
    resolution: {integrity: sha1-1HnFISYKTvxDww2mrqZT6kB8MMA=}

  '@floating-ui/core@1.6.9':
    resolution: {integrity: sha1-ZNHaJRQzAZ2voJHemyiG/zXsFOY=}

  '@floating-ui/dom@1.6.13':
    resolution: {integrity: sha1-qKk4UyrqJ6lRIewW5meny+jFnjQ=}

  '@floating-ui/react-dom@2.1.2':
    resolution: {integrity: sha1-oTSbv2oOXLXe1V0CN2byCk1DmjE=}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/utils@0.2.9':
    resolution: {integrity: sha1-UN6jYWvIGR+44RIoO0nq/wPnhCk=}

  '@grafana/faro-core@1.15.0':
    resolution: {integrity: sha1-cPzBZJIx62vmjafJfmBh6Aolnxc=}
    engines: {node: '>=18.0.0'}

  '@grafana/faro-web-sdk@1.15.0':
    resolution: {integrity: sha1-idwCXyeZetKa7bhU2miuzcZ6AAk=}
    engines: {node: '>=18.0.0'}

  '@grafana/faro-web-tracing@1.15.0':
    resolution: {integrity: sha1-lMVEcHYFMzV8rlmZiPUt9ao1yWA=}
    engines: {node: '>=18.0.0'}

  '@humanfs/core@0.19.1':
    resolution: {integrity: sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=}
    engines: {node: '>=18.18.0'}

  '@humanfs/node@0.16.6':
    resolution: {integrity: sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=}
    engines: {node: '>=18.18.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=}
    engines: {node: '>=12.22'}

  '@humanwhocodes/retry@0.3.1':
    resolution: {integrity: sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=}
    engines: {node: '>=18.18'}

  '@humanwhocodes/retry@0.4.2':
    resolution: {integrity: sha1-GGBHPeffoVRnZ0SPMz24DLD/IWE=}
    engines: {node: '>=18.18'}

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.6':
    resolution: {integrity: sha1-nXHKiG4yUC65NiyadKRnh8Nt+Bo=}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=}
    engines: {node: '>= 8'}

  '@opentelemetry/api-logs@0.57.2':
    resolution: {integrity: sha1-1AAbmqNYA2e0D+iJ81QAFPdmzIc=}
    engines: {node: '>=14'}

  '@opentelemetry/api@1.9.0':
    resolution: {integrity: sha1-0D66aCc9wPdQnio9XLoh6uEDef4=}
    engines: {node: '>=8.0.0'}

  '@opentelemetry/core@1.30.1':
    resolution: {integrity: sha1-oLRouzljWN+AGIFwnqOCmfwwqyc=}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/core@2.0.0':
    resolution: {integrity: sha1-N+nw6d3sRHmyZ6ym8y2IdXyUGzo=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/exporter-trace-otlp-http@0.57.2':
    resolution: {integrity: sha1-CrjpfcMNuruCUraBKLgMRoX3xpE=}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-fetch@0.57.2':
    resolution: {integrity: sha1-AU4UAS6FBqyUDw+1BGHo8LLL5As=}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-xml-http-request@0.57.2':
    resolution: {integrity: sha1-cDOV1rsSgLVT3pMs/7dtLk4mS9M=}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation@0.57.2':
    resolution: {integrity: sha1-iSRUnXlBuhtcbwTVUpz0gzBFbR0=}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/otlp-exporter-base@0.57.2':
    resolution: {integrity: sha1-EGNsjQ43fzMR5VdBsFULBvMqPpg=}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/otlp-transformer@0.57.2':
    resolution: {integrity: sha1-o73SyC3db9h/UThg+09iYOVV0sA=}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/resources@1.30.1':
    resolution: {integrity: sha1-pOrhfr2WlH/cemT5McpLceGM6WQ=}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/sdk-logs@0.57.2':
    resolution: {integrity: sha1-3cnR4rhgUrS2u5VN2Q+jh4vtiiM=}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.4.0 <1.10.0'

  '@opentelemetry/sdk-metrics@1.30.1':
    resolution: {integrity: sha1-cOK80nW5325+kl4/5Tz+cTKbX8g=}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.3.0 <1.10.0'

  '@opentelemetry/sdk-trace-base@1.30.1':
    resolution: {integrity: sha1-QaQiNAltyY6PRU0kVR/IC4Fv6zQ=}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/sdk-trace-web@1.30.1':
    resolution: {integrity: sha1-PLI9o21sLp7TMJPUbeUsAUlu2eA=}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/semantic-conventions@1.28.0':
    resolution: {integrity: sha1-M3+yvKBFPQcmaW50X1AGRBH2RtY=}
    engines: {node: '>=14'}

  '@opentelemetry/semantic-conventions@1.32.0':
    resolution: {integrity: sha1-oV6PePMjiKfkZV5/U5Vw5AlYyj8=}
    engines: {node: '>=14'}

  '@playwright/test@1.51.1':
    resolution: {integrity: sha1-dTV9UTIhp74LqtdfAelmuvnEGi4=}
    engines: {node: '>=18'}
    hasBin: true

  '@protobufjs/aspromise@1.1.2':
    resolution: {integrity: sha1-m4sMxmPWaafY9vXQiToU00jzD78=}

  '@protobufjs/base64@1.1.2':
    resolution: {integrity: sha1-TIVzDlm5ofHzSQR9vyQpYDS7JzU=}

  '@protobufjs/codegen@2.0.4':
    resolution: {integrity: sha1-fvN/DQEPsCitGtWXIuUG2SYoFcs=}

  '@protobufjs/eventemitter@1.1.0':
    resolution: {integrity: sha1-NVy8mLr61ZePntCV85diHx0Ga3A=}

  '@protobufjs/fetch@1.1.0':
    resolution: {integrity: sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=}

  '@protobufjs/float@1.0.2':
    resolution: {integrity: sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E=}

  '@protobufjs/inquire@1.1.0':
    resolution: {integrity: sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik=}

  '@protobufjs/path@1.1.2':
    resolution: {integrity: sha1-bMKyDFya1q0NzP0hynZz2Nf79o0=}

  '@protobufjs/pool@1.1.0':
    resolution: {integrity: sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q=}

  '@protobufjs/utf8@1.1.0':
    resolution: {integrity: sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA=}

  '@radix-ui/number@1.1.1':
    resolution: {integrity: sha1-eyySJfvxsSZTlVH1mFdp0ASNkJA=}

  '@radix-ui/primitive@1.1.2':
    resolution: {integrity: sha1-g/QVxEJfIePSeRTBKzJyoy49rmU=}

  '@radix-ui/react-accordion@1.2.4':
    resolution: {integrity: sha1-fM/OuVm7s7rC+S5edbJlSkiKL/A=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-arrow@1.1.3':
    resolution: {integrity: sha1-iSbrHYf3PC4EfqyWcDlJ8WjIWGE=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-avatar@1.1.4':
    resolution: {integrity: sha1-Vd2HnNV7CCZc8xO5K6ECH39NWr4=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-checkbox@1.3.2':
    resolution: {integrity: sha1-KAlyRNloqo+TJJsNPfAqFy/UvuU=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collapsible@1.1.4':
    resolution: {integrity: sha1-WXKZOcDF25eJNMaIql+CKYdtjz8=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collection@1.1.3':
    resolution: {integrity: sha1-z9RtzqWoqwZNkXmP7rRvq6QDKTA=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collection@1.1.6':
    resolution: {integrity: sha1-/s90R15GYO6Zx+sev6XM+xohn+Q=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collection@1.1.7':
    resolution: {integrity: sha1-0FwlyprEaVzBm6kfQvaG4+otmuw=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-compose-refs@1.1.2':
    resolution: {integrity: sha1-osTEevYzcEjueP9twNCQs5DSuzA=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-context@1.1.2':
    resolution: {integrity: sha1-YWKO8mmkMzgsNk9vHjeIptwhOjY=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dialog@1.1.7':
    resolution: {integrity: sha1-yCbZl9nlEr0qKtsyRbud/+VuAqo=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-direction@1.1.1':
    resolution: {integrity: sha1-OeWldp5nbHUyBLeS++bPUI5VChQ=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dismissable-layer@1.1.6':
    resolution: {integrity: sha1-5ywVbKx7B2FP6OOgOatwgc5BNoY=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-dropdown-menu@2.1.7':
    resolution: {integrity: sha1-oYhg/2m98GG3+KzlHbAdsusiUWE=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-focus-guards@1.1.2':
    resolution: {integrity: sha1-Tsmn5Qkl9/tmE5RGAEW0YhKjO+0=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-focus-scope@1.1.3':
    resolution: {integrity: sha1-6sg6OqxwDbF2ULQbMHJN7/rFsoo=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-id@1.1.1':
    resolution: {integrity: sha1-FAQALnmgP+Bit+OGSqAeJL0Ucfc=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-label@2.1.3':
    resolution: {integrity: sha1-3oNkHJDFfljw0eGUPUUNvDk2oj8=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-menu@2.1.7':
    resolution: {integrity: sha1-6F9H43YiRdiNejutPyJowh+DURE=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popper@1.2.3':
    resolution: {integrity: sha1-O27zOI/SCbtGNB4eQBJbdfB/EwQ=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-portal@1.1.5':
    resolution: {integrity: sha1-UO1r7i2JXJqd/ChiXyS4SDt01DE=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-presence@1.1.3':
    resolution: {integrity: sha1-zjQAyuyYks64Yvlt2qKt0IDAm5A=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-presence@1.1.4':
    resolution: {integrity: sha1-JTrArUlGxbSpxmh4M19c8HyWfO0=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-primitive@2.0.3':
    resolution: {integrity: sha1-E8ZU3EdUVYhwqcdp9v6+WYChutg=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-primitive@2.1.2':
    resolution: {integrity: sha1-A/ZPlXcZx2HSLC+SzEP/tkvULMg=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-primitive@2.1.3':
    resolution: {integrity: sha1-25uLz/SeAb5RCteYk/sOTNpQ8bw=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-radio-group@1.3.7':
    resolution: {integrity: sha1-Sfgi2XwmxHRZdhCKMBui6FRdiSg=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-roving-focus@1.1.10':
    resolution: {integrity: sha1-RgMEltKkkMSXnSmn4SUkZeUeSws=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-roving-focus@1.1.3':
    resolution: {integrity: sha1-yZK50wx5X19aZohT249Kbge3KE0=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-roving-focus@1.1.9':
    resolution: {integrity: sha1-N/yst9/MnqRUAbLdB72XzLuJEbI=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-select@2.1.7':
    resolution: {integrity: sha1-aFYUiMpUytBzUrPywtKeDaKLuqA=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-separator@1.1.6':
    resolution: {integrity: sha1-5YUAsOVLpxAjlVF1x4l1Qr8UMnE=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-slider@1.3.4':
    resolution: {integrity: sha1-xSxP+yDNFkcEgNcPKKtz3S+K7E0=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-slot@1.2.0':
    resolution: {integrity: sha1-V3J/wYbdtAckzPvilOGjUdkkYro=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-slot@1.2.2':
    resolution: {integrity: sha1-GOZTPneKIFHtwq0Hc9qOIvA/Ymo=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-slot@1.2.3':
    resolution: {integrity: sha1-UC1uNU/IR9QWnDvF8Yned39oz+E=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-tabs@1.1.11':
    resolution: {integrity: sha1-ncAC6m+K1oMLwg80mv3FfGA5AJw=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-tooltip@1.2.0':
    resolution: {integrity: sha1-run+2dtT5MhPUlqto0NrZRRkpTI=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-use-callback-ref@1.1.1':
    resolution: {integrity: sha1-YqTbqLMlX9xcx3h/rqwcbkzFjUA=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-controllable-state@1.1.1':
    resolution: {integrity: sha1-7JxXIHKm8mnfdDXBZS++66vg8ME=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-controllable-state@1.2.2':
    resolution: {integrity: sha1-kFeTQF3lfWGkOfSv67sX0GRfMZA=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-effect-event@0.0.2':
    resolution: {integrity: sha1-CQzzDQCkx2MqFVSFEukVIhdZOQc=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-escape-keydown@1.1.1':
    resolution: {integrity: sha1-s/7Zu+o2ahGPQEJ6xAUAqhQjzCk=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-layout-effect@1.1.1':
    resolution: {integrity: sha1-DEIwqe7UnUWJyWfi2cDZ1gojlx4=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-previous@1.1.1':
    resolution: {integrity: sha1-GhrVVolz0kBR7Qr2h3ZvbHy5tbU=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-rect@1.1.1':
    resolution: {integrity: sha1-AUQ8qO0HHTMCPBET5Rc7Xth2kVI=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-size@1.1.1':
    resolution: {integrity: sha1-beJ2/7w4mlN//kMW9bDyQSlAWzc=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-visually-hidden@1.1.3':
    resolution: {integrity: sha1-9wTEkSGFmUGou1D/Hk8VYFjKzQs=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/rect@1.1.1':
    resolution: {integrity: sha1-eCRO/hKTDFb9JV15I4ZYV8QayMs=}

  '@react-hook/debounce@3.0.0':
    resolution: {integrity: sha1-nuqLXYHUy2fNct2GV7P/ckr8fK0=}
    peerDependencies:
      react: '>=16.8'

  '@react-hook/event@1.2.6':
    resolution: {integrity: sha1-UvkVeK3ZNKzBIDMoygmrFPx+5Y4=}
    peerDependencies:
      react: '>=16.8'

  '@react-hook/latest@1.0.3':
    resolution: {integrity: sha1-wtHQsK+LaexuKzokEroHaKyC24A=}
    peerDependencies:
      react: '>=16.8'

  '@react-hook/passive-layout-effect@1.2.1':
    resolution: {integrity: sha1-wG2sLQEfNtYSWaocbfTw1eKLxV4=}
    peerDependencies:
      react: '>=16.8'

  '@react-hook/throttle@2.2.0':
    resolution: {integrity: sha1-0EAnFKBuG6C8HaH99cPFzQ4I1Fo=}
    peerDependencies:
      react: '>=16.8'

  '@react-hook/window-scroll@1.3.0':
    resolution: {integrity: sha1-B5FCOaRSB1x+so0V7Q4v1Fqi1ls=}
    peerDependencies:
      react: '>=16.8'

  '@react-hook/window-size@3.1.1':
    resolution: {integrity: sha1-GlZJAs/iEujCf/wudK3KoYp9ZbM=}
    peerDependencies:
      react: '>=16.8'

  '@rollup/plugin-babel@5.3.1':
    resolution: {integrity: sha1-BLwGCPSqSy5LGuvyhDRND2j9ooM=}
    engines: {node: '>= 10.0.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
      '@types/babel__core': ^7.1.9
      rollup: ^1.20.0||^2.0.0
    peerDependenciesMeta:
      '@types/babel__core':
        optional: true

  '@rollup/plugin-node-resolve@15.3.1':
    resolution: {integrity: sha1-ZgCJU8JSS+eGqjGdSeMvISgpang=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^2.78.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/plugin-replace@2.4.2':
    resolution: {integrity: sha1-otU5MU+8d8JEhY+qUjASglBoUQo=}
    peerDependencies:
      rollup: ^1.20.0 || ^2.0.0

  '@rollup/plugin-terser@0.4.4':
    resolution: {integrity: sha1-Fd/9s/c/Ehqk+7N+fKa+mu6pGWI=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/pluginutils@3.1.0':
    resolution: {integrity: sha1-cGtFJO5tyLEDs8mVUz5a1oDAK5s=}
    engines: {node: '>= 8.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0

  '@rollup/pluginutils@5.1.4':
    resolution: {integrity: sha1-u5Tx+eqqyUTaI3dnzf7mxbImLUo=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/rollup-android-arm-eabi@4.39.0':
    resolution: {integrity: sha1-HYzF3T2P/ladj39npFx5CYKKD2Y=}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.39.0':
    resolution: {integrity: sha1-nBNgNNPZ7SnQsTjHTdY8V0RQf8o=}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.39.0':
    resolution: {integrity: sha1-gw0HeU1qQHwStIS4z3Gv/U04AKY=}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.39.0':
    resolution: {integrity: sha1-sm8PRwBcH6VBmogPMj7VCdyNiFw=}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.39.0':
    resolution: {integrity: sha1-K2DIGsAf99G8jfZq7ngItmkMbRk=}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.39.0':
    resolution: {integrity: sha1-SCavMPTZM9giISiQaIRslinMYow=}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.39.0':
    resolution: {integrity: sha1-ofT5Y9XcyeVXXHrPmRGCSAZDa/c=}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm-musleabihf@4.39.0':
    resolution: {integrity: sha1-6SSwqLfEAAiRRvYnhEbms5i3WgY=}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-arm64-gnu@4.39.0':
    resolution: {integrity: sha1-y0MwMnTsmnFvRECwGrTiDCOuviA=}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm64-musl@4.39.0':
    resolution: {integrity: sha1-UxySUzzj0WfyERv80qoaIEEmaYc=}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-loongarch64-gnu@4.39.0':
    resolution: {integrity: sha1-U0A4iXVdDDfJJlCq0BbVsGwbBho=}
    cpu: [loong64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-powerpc64le-gnu@4.39.0':
    resolution: {integrity: sha1-9mnxYuKQlMgZxQnpnb7O1Y/HCPk=}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-gnu@4.39.0':
    resolution: {integrity: sha1-S6s3NTsRvNpadMoRuZ3qkpZX/V8=}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-musl@4.39.0':
    resolution: {integrity: sha1-TWa+HOPP1Ap5EOs03dx8vUwt0qU=}
    cpu: [riscv64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-s390x-gnu@4.39.0':
    resolution: {integrity: sha1-cYHDKTle1TNAoMWWeK0wSplif20=}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-gnu@4.39.0':
    resolution: {integrity: sha1-AIJbNFgJTVwny07Wboi/6fHmX5A=}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-musl@4.39.0':
    resolution: {integrity: sha1-gcqsKjG4dUGG86zBQpU6F4/Nb7o=}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-win32-arm64-msvc@4.39.0':
    resolution: {integrity: sha1-Oj9CH1zpvZntIM4WYMznzuPp8Zk=}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.39.0':
    resolution: {integrity: sha1-pEly1c3UhN/ZzzcFqIS/DCt3hac=}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.39.0':
    resolution: {integrity: sha1-v+AhThY/cMT+wcj3u4ziZvTAW34=}
    cpu: [x64]
    os: [win32]

  '@snap/design-system@1.2.4':
    resolution: {integrity: sha1-Y57MZGldj4ca20sWlhDn6udjGhM=}
    engines: {pnpm: '>=10'}
    peerDependencies:
      react: ^19.0.0
      react-dom: ^19.0.0

  '@surma/rollup-plugin-off-main-thread@2.2.3':
    resolution: {integrity: sha1-7jSYWVLKIVWKsNlS8AKYrSGQwFM=}

  '@tailwindcss/node@4.1.3':
    resolution: {integrity: sha1-8pCIZYLOjrGXiFPQfKTaRfLUP9s=}

  '@tailwindcss/oxide-android-arm64@4.1.3':
    resolution: {integrity: sha1-bBg0596EqlVE9MiqyzgOAOAZoR8=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]

  '@tailwindcss/oxide-darwin-arm64@4.1.3':
    resolution: {integrity: sha1-7Tq9SlnwWhrFgze2PW/oK7mQNGI=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@tailwindcss/oxide-darwin-x64@4.1.3':
    resolution: {integrity: sha1-2KB4b06uggP4NF/PWwPzKE7ugq8=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@tailwindcss/oxide-freebsd-x64@4.1.3':
    resolution: {integrity: sha1-52Ug5TQcOkSVmQG4/v7njU/C8HQ=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [freebsd]

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.3':
    resolution: {integrity: sha1-EwwnblkLa6YhxEOsf6pwKnCWIMc=}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.3':
    resolution: {integrity: sha1-Vec2qJ2FR4NQJt88XWzlBGfXEkE=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@tailwindcss/oxide-linux-arm64-musl@4.1.3':
    resolution: {integrity: sha1-T/VOSkD+3npm4gmwf5tdpDLZZng=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@tailwindcss/oxide-linux-x64-gnu@4.1.3':
    resolution: {integrity: sha1-Zkd6cfuq1VK+iC6Lela7dRm0eDg=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@tailwindcss/oxide-linux-x64-musl@4.1.3':
    resolution: {integrity: sha1-QDFFzkM2Hn1jiGyHj9sJzYaJINo=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.3':
    resolution: {integrity: sha1-DMK8WcIozh1kFWCJryGsxDAggdo=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@tailwindcss/oxide-win32-x64-msvc@4.1.3':
    resolution: {integrity: sha1-m9UQi5WwPazoouXnOLGyOJ+KbQk=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@tailwindcss/oxide@4.1.3':
    resolution: {integrity: sha1-0BFiE3/O/n1MKjRQC57VwUI4g1I=}
    engines: {node: '>= 10'}

  '@tailwindcss/postcss@4.1.3':
    resolution: {integrity: sha1-gr+LkME0+J9w2NApO1sU8jSRj68=}

  '@tailwindcss/vite@4.1.3':
    resolution: {integrity: sha1-2ygYTXodi59zUsYaCn5z+nbhMSk=}
    peerDependencies:
      vite: ^5.2.0 || ^6

  '@tanstack/query-core@5.72.2':
    resolution: {integrity: sha1-RI29xoxsg217IC6v3RuJ+5N589Q=}

  '@tanstack/query-devtools@5.72.2':
    resolution: {integrity: sha1-Nai8eVHTHP277R93dJCaYcYPNe8=}

  '@tanstack/react-query-devtools@5.72.2':
    resolution: {integrity: sha1-6EvTzP0+Icubhtg6zhNGnmEv/eM=}
    peerDependencies:
      '@tanstack/react-query': ^5.72.2
      react: ^18 || ^19

  '@tanstack/react-query@5.72.2':
    resolution: {integrity: sha1-Z3orI5v+4tvrdcP03FQaj/+jPW0=}
    peerDependencies:
      react: ^18 || ^19

  '@testing-library/dom@10.4.0':
    resolution: {integrity: sha1-gqnZRi8R0kDsrb9AZgfGzu7/Q6g=}
    engines: {node: '>=18'}

  '@testing-library/jest-dom@6.6.3':
    resolution: {integrity: sha1-JrqQbPkowPgXLhgsb+IU60+fK9I=}
    engines: {node: '>=14', npm: '>=6', yarn: '>=1'}

  '@testing-library/react@16.3.0':
    resolution: {integrity: sha1-OoW7m96/GAzXbboWRU4kJWTVmKY=}
    engines: {node: '>=18'}
    peerDependencies:
      '@testing-library/dom': ^10.0.0
      '@types/react': ^18.0.0 || ^19.0.0
      '@types/react-dom': ^18.0.0 || ^19.0.0
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@testing-library/user-event@14.6.1':
    resolution: {integrity: sha1-E+CaMteotwYP44MEeI6/QZfNIUk=}
    engines: {node: '>=12', npm: '>=6'}
    peerDependencies:
      '@testing-library/dom': '>=7.21.4'

  '@types/aria-query@5.0.4':
    resolution: {integrity: sha1-GjHD03iFDSd42rtjdNA23LpLpwg=}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=}

  '@types/babel__generator@7.27.0':
    resolution: {integrity: sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=}

  '@types/babel__traverse@7.20.7':
    resolution: {integrity: sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=}

  '@types/conventional-commits-parser@5.0.1':
    resolution: {integrity: sha1-jLgc8XCFNJbLxQGjsy3PXkb/tho=}

  '@types/cookie@0.6.0':
    resolution: {integrity: sha1-6sOX8ovx1q4K4IE2PsovQlvt8NU=}

  '@types/estree@0.0.39':
    resolution: {integrity: sha1-4Xfmme4bjCLSMXTKqnQiZEOJUJ8=}

  '@types/estree@1.0.7':
    resolution: {integrity: sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=}

  '@types/inputmask@5.0.7':
    resolution: {integrity: sha1-rhr9uwp7gluQcD4LCLTvW+fSw04=}

  '@types/js-cookie@3.0.6':
    resolution: {integrity: sha1-oEyhnod2h71En1rTfTOxBLcf35U=}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=}

  '@types/node@22.14.0':
    resolution: {integrity: sha1-07+jk2/vDbrNeeo+sX1SHGKLtH4=}

  '@types/react-dom@19.1.2':
    resolution: {integrity: sha1-vR/juMKKOi6UL4UxTc+3H1MaJC8=}
    peerDependencies:
      '@types/react': ^19.0.0

  '@types/react@19.1.0':
    resolution: {integrity: sha1-c8Q62bxDSWyoGEMysRHirvY/ydo=}

  '@types/resolve@1.20.2':
    resolution: {integrity: sha1-l9JuAM1KBCO0r2IKvs8+b0QreXU=}

  '@types/shimmer@1.2.0':
    resolution: {integrity: sha1-m3Bq+W+gZBaCiEI5enDfu/HBTe0=}

  '@types/trusted-types@2.0.7':
    resolution: {integrity: sha1-usywepcLkXB986PoumiWxX6tLRE=}

  '@typescript-eslint/eslint-plugin@8.29.1':
    resolution: {integrity: sha1-WTY52btSObLYd9ZXV7fiyRAKLoQ=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.0.0 || ^8.0.0-alpha.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/parser@8.29.1':
    resolution: {integrity: sha1-EL83QRvgoZnCe2UVcm4i/o09+NA=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/scope-manager@8.29.1':
    resolution: {integrity: sha1-z9/UFE8gw4udPkMO/WSA4pfvUvY=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/type-utils@8.29.1':
    resolution: {integrity: sha1-ZT3/9cFxG8kgpqRqWiwnSJnwAXk=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/types@8.29.1':
    resolution: {integrity: sha1-mE7RKD/tv7QdOZOpq9y3spmXFQA=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/typescript-estree@8.29.1':
    resolution: {integrity: sha1-SsCFZl7VOQ0RwONCZCeXhXDjt0c=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/utils@8.29.1':
    resolution: {integrity: sha1-PSBsjI3vNSeo6wWI6U4+YPfhZ8k=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/visitor-keys@8.29.1':
    resolution: {integrity: sha1-m3TlCYxxE41Cu/IXj75N+tRda5o=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@vitejs/plugin-react@4.3.4':
    resolution: {integrity: sha1-xkvhC1TEZAE1pbKKJDIzDoitfCA=}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.2.0 || ^5.0.0 || ^6.0.0

  '@vitest/expect@3.1.1':
    resolution: {integrity: sha1-1k3f3Pnod9gF4e7me9hFvwcIxsI=}

  '@vitest/mocker@3.1.1':
    resolution: {integrity: sha1-donZn4dJhoTHHp/p3v29E/+38aw=}
    peerDependencies:
      msw: ^2.4.9
      vite: ^5.0.0 || ^6.0.0
    peerDependenciesMeta:
      msw:
        optional: true
      vite:
        optional: true

  '@vitest/pretty-format@3.1.1':
    resolution: {integrity: sha1-W01Xd3HazPztR7rzvwJq1ZtSwoM=}

  '@vitest/runner@3.1.1':
    resolution: {integrity: sha1-drWYcAc3CJ1mx0JysuHJTKKJGkk=}

  '@vitest/snapshot@3.1.1':
    resolution: {integrity: sha1-QraqDQ4rO0i5Wlx279zGakTLEfM=}

  '@vitest/spy@3.1.1':
    resolution: {integrity: sha1-3soLAl4VEwKrUU84OQ/Xd34pSDc=}

  '@vitest/utils@3.1.1':
    resolution: {integrity: sha1-KJPDAhmra98QnwfOXNKH/oBYQ40=}

  JSONStream@1.3.5:
    resolution: {integrity: sha1-MgjB8I06TZkmGrZPkjArwV4RHKA=}
    hasBin: true

  acorn-import-attributes@1.9.5:
    resolution: {integrity: sha1-frFVexugXvGLXtDsZ1kb+rBGiO8=}
    peerDependencies:
      acorn: ^8

  acorn-jsx@5.3.2:
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.14.1:
    resolution: {integrity: sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=}
    engines: {node: '>=0.4.0'}
    hasBin: true

  agent-base@7.1.3:
    resolution: {integrity: sha1-KUNeuCG8QZRjOluJ5bxHA7r8JaE=}
    engines: {node: '>= 14'}

  ajv@6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=}

  ajv@8.17.1:
    resolution: {integrity: sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=}

  ansi-escapes@7.0.0:
    resolution: {integrity: sha1-APwZ9JG7sY4dSBuXhoIE+SEJv+c=}
    engines: {node: '>=18'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=}
    engines: {node: '>=12'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=}
    engines: {node: '>=10'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=}
    engines: {node: '>=12'}

  argparse@2.0.1:
    resolution: {integrity: sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=}

  aria-hidden@1.2.4:
    resolution: {integrity: sha1-t444P9vATQV2LHi0olpQHnNsRSI=}
    engines: {node: '>=10'}

  aria-query@5.3.0:
    resolution: {integrity: sha1-ZQxWnkGtkLUbPX315e7Rx1ScED4=}

  aria-query@5.3.2:
    resolution: {integrity: sha1-k/gaQ0gOM6M48ZFjo9EKUMAdzVk=}
    engines: {node: '>= 0.4'}

  array-buffer-byte-length@1.0.2:
    resolution: {integrity: sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=}
    engines: {node: '>= 0.4'}

  array-ify@1.0.0:
    resolution: {integrity: sha1-nlKHYrSpBmrRY6aWKjZEGOlibs4=}

  array-includes@3.1.8:
    resolution: {integrity: sha1-XjcMvhcv3V3WUwwdSq3aJSgbqX0=}
    engines: {node: '>= 0.4'}

  array.prototype.findlast@1.2.5:
    resolution: {integrity: sha1-Pk+8swoVp/W/ZM8vquItE5wuSQQ=}
    engines: {node: '>= 0.4'}

  array.prototype.flat@1.3.3:
    resolution: {integrity: sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=}
    engines: {node: '>= 0.4'}

  array.prototype.flatmap@1.3.3:
    resolution: {integrity: sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=}
    engines: {node: '>= 0.4'}

  array.prototype.tosorted@1.1.4:
    resolution: {integrity: sha1-/pVGeP9TA05xfqM1KgPwsLhvf/w=}
    engines: {node: '>= 0.4'}

  arraybuffer.prototype.slice@1.0.4:
    resolution: {integrity: sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=}
    engines: {node: '>= 0.4'}

  assertion-error@2.0.1:
    resolution: {integrity: sha1-9kGhlrM1aQsQcL8AtudZP+wZC/c=}
    engines: {node: '>=12'}

  async-function@1.0.0:
    resolution: {integrity: sha1-UJyfymDq+FA0xoKYOBiOTkyP+ys=}
    engines: {node: '>= 0.4'}

  async@3.2.6:
    resolution: {integrity: sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=}

  asynckit@0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=}

  at-least-node@1.0.0:
    resolution: {integrity: sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=}
    engines: {node: '>= 4.0.0'}

  autoprefixer@10.4.21:
    resolution: {integrity: sha1-dxiUaOeorR2aN/vAjvyfSAzwqV0=}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  available-typed-arrays@1.0.7:
    resolution: {integrity: sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=}
    engines: {node: '>= 0.4'}

  axios@1.8.4:
    resolution: {integrity: sha1-eJkLtLxj0srgcpUtN0g1lQqC9Ec=}

  babel-plugin-polyfill-corejs2@0.4.13:
    resolution: {integrity: sha1-fURfDgYH68j7awHX6PsCBpuR3Ys=}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.11.1:
    resolution: {integrity: sha1-Tk4YLxuzfHumLir4HY3QnfMTRPY=}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.6.4:
    resolution: {integrity: sha1-QoxhXTwXcpKiK0+T7ZnjWNeQaps=}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  balanced-match@1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=}

  brace-expansion@1.1.11:
    resolution: {integrity: sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=}

  brace-expansion@2.0.1:
    resolution: {integrity: sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=}

  braces@3.0.3:
    resolution: {integrity: sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=}
    engines: {node: '>=8'}

  browserslist@4.24.4:
    resolution: {integrity: sha1-xrKGWj8IvLhgoOgnOJADuf5obks=}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-from@1.1.2:
    resolution: {integrity: sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=}

  cac@6.7.14:
    resolution: {integrity: sha1-gE4eb1Bu42PLDjzLsJytXdmHCVk=}
    engines: {node: '>=8'}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha1-S1QowiK+mF15w9gmV0edvgtZstY=}
    engines: {node: '>= 0.4'}

  call-bind@1.0.8:
    resolution: {integrity: sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha1-I43pNdKippKSjFOMfM+pEGf9Bio=}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=}
    engines: {node: '>=6'}

  caniuse-lite@1.0.30001713:
    resolution: {integrity: sha1-azOohX5sfctBoMqi3Q8EicgjpS0=}

  chai@5.2.0:
    resolution: {integrity: sha1-E1juEGdjYkEUrd+EqwJpfkEcnAU=}
    engines: {node: '>=12'}

  chalk@2.4.2:
    resolution: {integrity: sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=}
    engines: {node: '>=4'}

  chalk@3.0.0:
    resolution: {integrity: sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=}
    engines: {node: '>=8'}

  chalk@4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=}
    engines: {node: '>=10'}

  chalk@5.4.1:
    resolution: {integrity: sha1-G0i/CWPsFY3OKqz2nAk64t0gktg=}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  check-error@2.1.1:
    resolution: {integrity: sha1-h+uHauce44j6BHH+Qj9JS+HZbMw=}
    engines: {node: '>= 16'}

  cjs-module-lexer@1.4.3:
    resolution: {integrity: sha1-D3lzHrjP4exyrNQGbvrJ1hmRsA0=}

  class-variance-authority@0.7.1:
    resolution: {integrity: sha1-QAinmKDkVTp4GlesUXfJ+10EN4c=}

  cli-cursor@5.0.0:
    resolution: {integrity: sha1-JKSDHs9aawHd6zL7caSyCIsNzjg=}
    engines: {node: '>=18'}

  cli-truncate@4.0.0:
    resolution: {integrity: sha1-bMKKKST+6eJc6R6XPbVscGbmFyo=}
    engines: {node: '>=18'}

  cliui@8.0.1:
    resolution: {integrity: sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=}
    engines: {node: '>=12'}

  clsx@2.1.1:
    resolution: {integrity: sha1-7tOXyf2L2IK/sY3qtxAgSaLzKZk=}
    engines: {node: '>=6'}

  color-convert@1.9.3:
    resolution: {integrity: sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=}

  color-convert@2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=}

  color-name@1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=}

  colorette@2.0.20:
    resolution: {integrity: sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=}

  combined-stream@1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=}
    engines: {node: '>= 0.8'}

  commander@13.1.0:
    resolution: {integrity: sha1-d2Fn22jHjzjczh+bjXuLmkiKv0Y=}
    engines: {node: '>=18'}

  commander@2.20.3:
    resolution: {integrity: sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=}

  common-tags@1.8.2:
    resolution: {integrity: sha1-lOuzwHbSYDJ0X9VPrOf2iO9aycY=}
    engines: {node: '>=4.0.0'}

  compare-func@2.0.0:
    resolution: {integrity: sha1-+2XnXtvd/S5WhVTotbBf/3pR/LM=}

  concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=}

  conventional-changelog-angular@7.0.0:
    resolution: {integrity: sha1-XuyO2/8VqpsWgKjc+9U+LX6yuno=}
    engines: {node: '>=16'}

  conventional-changelog-conventionalcommits@7.0.2:
    resolution: {integrity: sha1-ql2g8bJUMJSInoz3YW6+Go9ccNU=}
    engines: {node: '>=16'}

  conventional-commits-parser@5.0.0:
    resolution: {integrity: sha1-V/NZS4GtVNQMG0KA8EVU3yhifZo=}
    engines: {node: '>=16'}
    hasBin: true

  convert-source-map@2.0.0:
    resolution: {integrity: sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=}

  cookie@1.0.2:
    resolution: {integrity: sha1-JzYHAVMhFr0/H5QWkp0Xav4eRhA=}
    engines: {node: '>=18'}

  core-js-compat@3.41.0:
    resolution: {integrity: sha1-TN/OlfOajyd1m2Z89pPZbl3aPRc=}

  cosmiconfig-typescript-loader@6.1.0:
    resolution: {integrity: sha1-f2RFA+HCv/kK7S0ppjcAjyeWRrs=}
    engines: {node: '>=v18'}
    peerDependencies:
      '@types/node': '*'
      cosmiconfig: '>=9'
      typescript: '>=5'

  cosmiconfig@9.0.0:
    resolution: {integrity: sha1-NMP8WCh7kV866QWrbcPeJYtVrZ0=}
    engines: {node: '>=14'}
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true

  cross-env@7.0.3:
    resolution: {integrity: sha1-hlJkspZ33AFbqEGJGJZd0jL8VM8=}
    engines: {node: '>=10.14', npm: '>=6', yarn: '>=1'}
    hasBin: true

  cross-spawn@6.0.6:
    resolution: {integrity: sha1-MNDvoHEt2361p24ehyG/+vprXVc=}
    engines: {node: '>=4.8'}

  cross-spawn@7.0.6:
    resolution: {integrity: sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=}
    engines: {node: '>= 8'}

  crypto-random-string@2.0.0:
    resolution: {integrity: sha1-7yp6lm7BEIM4g2m6oC6+rSKbMNU=}
    engines: {node: '>=8'}

  css.escape@1.5.1:
    resolution: {integrity: sha1-QuJ9T6BK4y+TGktNQZH6nN3ul8s=}

  cssstyle@4.3.0:
    resolution: {integrity: sha1-g9si0a7I635ezYErTRShf7PdJD0=}
    engines: {node: '>=18'}

  csstype@3.1.3:
    resolution: {integrity: sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=}

  dargs@8.1.0:
    resolution: {integrity: sha1-o0hZ6lCcvORUheWqNW/vcL/McnI=}
    engines: {node: '>=12'}

  data-urls@5.0.0:
    resolution: {integrity: sha1-L3aQa84YJEKf/stpIPRaCzDwDd4=}
    engines: {node: '>=18'}

  data-view-buffer@1.0.2:
    resolution: {integrity: sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=}
    engines: {node: '>= 0.4'}

  data-view-byte-length@1.0.2:
    resolution: {integrity: sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=}
    engines: {node: '>= 0.4'}

  data-view-byte-offset@1.0.1:
    resolution: {integrity: sha1-BoMH+bcat2274QKROJ4CCFZgYZE=}
    engines: {node: '>= 0.4'}

  debug@4.4.0:
    resolution: {integrity: sha1-Kz8q6i/+t3ZHdGAmc3fchxD6uoo=}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decimal.js@10.5.0:
    resolution: {integrity: sha1-DzccfPbEiYzgr7CYNttzzYIBDyI=}

  deep-eql@5.0.2:
    resolution: {integrity: sha1-S3VtjXcKklcwCCXVKiws/5nDo0E=}
    engines: {node: '>=6'}

  deep-is@0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=}

  deepmerge@4.3.1:
    resolution: {integrity: sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=}
    engines: {node: '>=0.10.0'}

  define-data-property@1.1.4:
    resolution: {integrity: sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=}
    engines: {node: '>= 0.4'}

  define-properties@1.2.1:
    resolution: {integrity: sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=}
    engines: {node: '>= 0.4'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=}
    engines: {node: '>=0.4.0'}

  dequal@2.0.3:
    resolution: {integrity: sha1-JkQhTxmX057Q7g7OcjNUkKesZ74=}
    engines: {node: '>=6'}

  detect-libc@2.0.3:
    resolution: {integrity: sha1-8M1QO0D5k5uJRpfRmtUIleMM9wA=}
    engines: {node: '>=8'}

  detect-node-es@1.1.0:
    resolution: {integrity: sha1-FjrN9kMzDKoLTNfCHn7ndV1vpJM=}

  doctrine@2.1.0:
    resolution: {integrity: sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=}
    engines: {node: '>=0.10.0'}

  dom-accessibility-api@0.5.16:
    resolution: {integrity: sha1-WnQp5gZus2ZNkR4z+w5F3o6whFM=}

  dom-accessibility-api@0.6.3:
    resolution: {integrity: sha1-mT6SXMHXPyxmLn113VpURSWaj9g=}

  dot-prop@5.3.0:
    resolution: {integrity: sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=}
    engines: {node: '>=8'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha1-165mfh3INIL4tw/Q9u78UNow9Yo=}
    engines: {node: '>= 0.4'}

  ejs@3.1.10:
    resolution: {integrity: sha1-aauDWLFOiW+AzDnmIIe4hQDDrDs=}
    engines: {node: '>=0.10.0'}
    hasBin: true

  electron-to-chromium@1.5.136:
    resolution: {integrity: sha1-hUtF5qiSE3diywJu1ux3OR/FwHs=}

  emoji-regex@10.4.0:
    resolution: {integrity: sha1-A1U6/qgLOXV0nPyzb3dsomjkE9Q=}

  emoji-regex@8.0.0:
    resolution: {integrity: sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=}

  enhanced-resolve@5.18.1:
    resolution: {integrity: sha1-coqwgvi3toNt5R8WN6q107lWj68=}
    engines: {node: '>=10.13.0'}

  entities@4.5.0:
    resolution: {integrity: sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=}
    engines: {node: '>=0.12'}

  env-paths@2.2.1:
    resolution: {integrity: sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=}
    engines: {node: '>=6'}

  environment@1.1.0:
    resolution: {integrity: sha1-jobGaxgPNjx6sxF4fgJZZl9FqfE=}
    engines: {node: '>=18'}

  error-ex@1.3.2:
    resolution: {integrity: sha1-tKxAZIEH/c3PriQvQovqihTU8b8=}

  es-abstract@1.23.9:
    resolution: {integrity: sha1-W0WZS33nja2lwb6/E3lkazK51gY=}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.1:
    resolution: {integrity: sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=}
    engines: {node: '>= 0.4'}

  es-iterator-helpers@1.2.1:
    resolution: {integrity: sha1-0d0PWBKQVMCtki5qmh5l7vQ1/nU=}
    engines: {node: '>= 0.4'}

  es-module-lexer@1.6.0:
    resolution: {integrity: sha1-2kn1h/2eaO4kBP5OJWwMfTqBviE=}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha1-HE8sSDcydZfOadLKGQp/3RcjOME=}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha1-8x274MGDsAptJutjJcgQwP0YvU0=}
    engines: {node: '>= 0.4'}

  es-shim-unscopables@1.1.0:
    resolution: {integrity: sha1-Q43zVSDaxdEF85Q9knVJ6jsA9LU=}
    engines: {node: '>= 0.4'}

  es-to-primitive@1.3.0:
    resolution: {integrity: sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=}
    engines: {node: '>= 0.4'}

  esbuild@0.25.2:
    resolution: {integrity: sha1-VaHZ68s6ovlei7qekAwaUGG8Fos=}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=}
    engines: {node: '>=6'}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=}
    engines: {node: '>=10'}

  eslint-plugin-react@7.37.5:
    resolution: {integrity: sha1-KXVRFHK92hsnKzTXeTNcmw6HcGU=}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

  eslint-scope@8.3.0:
    resolution: {integrity: sha1-EM06kY/91yL18/e1uD25sjyHNA0=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.2.0:
    resolution: {integrity: sha1-aHussq+IT83aim59ZcYG9GoUzUU=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@9.24.0:
    resolution: {integrity: sha1-mn8ubLLegcQFqyRLAvRYTHnca+4=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.3.0:
    resolution: {integrity: sha1-KSZ89bDLmHNbZeZLoH4O1J0e7Yo=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  esquery@1.6.0:
    resolution: {integrity: sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=}
    engines: {node: '>=4.0'}

  estree-walker@1.0.1:
    resolution: {integrity: sha1-MbxdYSyWtwQQa0d+bdXYqhOMtwA=}

  estree-walker@2.0.2:
    resolution: {integrity: sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=}

  estree-walker@3.0.3:
    resolution: {integrity: sha1-Z8PlSexAKkh7T8GT0ZU6UkdSNA0=}

  esutils@2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=}
    engines: {node: '>=0.10.0'}

  eventemitter3@5.0.1:
    resolution: {integrity: sha1-U/X/0KSSrIAHIbtCxmuEHelkI8Q=}

  execa@8.0.1:
    resolution: {integrity: sha1-UfallDtYD5Y8PKnGMheW24zDm4w=}
    engines: {node: '>=16.17'}

  expect-type@1.2.1:
    resolution: {integrity: sha1-r3bYs1fPX6dsQcCdr7ecVJ519x8=}
    engines: {node: '>=12.0.0'}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=}

  fast-glob@3.3.3:
    resolution: {integrity: sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=}

  fast-uri@3.0.6:
    resolution: {integrity: sha1-iPEwt3z66iN41Wv5cN6iElemh0g=}

  fastq@1.19.1:
    resolution: {integrity: sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=}

  fdir@6.4.3:
    resolution: {integrity: sha1-ARzaz4N+ypuBHInbuQLfcUJz23I=}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  file-entry-cache@8.0.0:
    resolution: {integrity: sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=}
    engines: {node: '>=16.0.0'}

  filelist@1.0.4:
    resolution: {integrity: sha1-94l4oelEd1/55i50RCTyFeWDUrU=}

  fill-range@7.1.1:
    resolution: {integrity: sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=}
    engines: {node: '>=10'}

  find-up@7.0.0:
    resolution: {integrity: sha1-6N7BRV90942IitZb98oT3StOZvs=}
    engines: {node: '>=18'}

  flat-cache@4.0.1:
    resolution: {integrity: sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=}
    engines: {node: '>=16'}

  flatted@3.3.3:
    resolution: {integrity: sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=}

  follow-redirects@1.15.9:
    resolution: {integrity: sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  for-each@0.3.5:
    resolution: {integrity: sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=}
    engines: {node: '>= 0.4'}

  form-data@4.0.2:
    resolution: {integrity: sha1-Ncq73TDDznPessQtPI0+2cpReUw=}
    engines: {node: '>= 6'}

  fraction.js@4.3.7:
    resolution: {integrity: sha1-BsoAhRV+Qv2n+ecm55/vxAaIQPc=}

  framer-motion@12.12.1:
    resolution: {integrity: sha1-t1YHnMBQvn+mrg0JOrCQP9PxXKE=}
    peerDependencies:
      '@emotion/is-prop-valid': '*'
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/is-prop-valid':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true

  fs-extra@9.1.0:
    resolution: {integrity: sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=}
    engines: {node: '>=10'}

  fs.realpath@1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=}

  fsevents@2.3.2:
    resolution: {integrity: sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  fsevents@2.3.3:
    resolution: {integrity: sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=}

  function.prototype.name@1.1.8:
    resolution: {integrity: sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=}
    engines: {node: '>= 0.4'}

  functions-have-names@1.2.3:
    resolution: {integrity: sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-east-asian-width@1.3.0:
    resolution: {integrity: sha1-IbQHHuWO0E7g22UzcbVbQpmHU4k=}
    engines: {node: '>=18'}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=}
    engines: {node: '>= 0.4'}

  get-nonce@1.0.1:
    resolution: {integrity: sha1-/fPwJ4Bzgg0s6UJsGPB0gbHgzfM=}
    engines: {node: '>=6'}

  get-own-enumerable-property-symbols@3.0.2:
    resolution: {integrity: sha1-tf3nfyLL4185C04ImSLFC85u9mQ=}

  get-proto@1.0.1:
    resolution: {integrity: sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=}
    engines: {node: '>= 0.4'}

  get-stream@8.0.1:
    resolution: {integrity: sha1-3vnf1xdCzXdUp3Ye1DdJon0C7KI=}
    engines: {node: '>=16'}

  get-symbol-description@1.1.0:
    resolution: {integrity: sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=}
    engines: {node: '>= 0.4'}

  git-raw-commits@4.0.0:
    resolution: {integrity: sha1-shL9K/+XJtJ8EoOhFX6ClJBZMoU=}
    engines: {node: '>=16'}
    hasBin: true

  glob-parent@5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=}
    engines: {node: '>=10.13.0'}

  glob@7.2.3:
    resolution: {integrity: sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=}
    deprecated: Glob versions prior to v9 are no longer supported

  global-directory@4.0.1:
    resolution: {integrity: sha1-TXrHz9LLc/MExTuIEIkXSN9eNh4=}
    engines: {node: '>=18'}

  globals@11.12.0:
    resolution: {integrity: sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=}
    engines: {node: '>=4'}

  globals@14.0.0:
    resolution: {integrity: sha1-iY10E8Kbq89rr+Vvyt3thYrack4=}
    engines: {node: '>=18'}

  globals@15.15.0:
    resolution: {integrity: sha1-fEdhKZ1BwysHVxWkzh7eeJf/cqg=}
    engines: {node: '>=18'}

  globalthis@1.0.4:
    resolution: {integrity: sha1-dDDtOpddl7+1m8zkH1yruvplEjY=}
    engines: {node: '>= 0.4'}

  gopd@1.2.0:
    resolution: {integrity: sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=}

  graphemer@1.4.0:
    resolution: {integrity: sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=}

  has-bigints@1.1.0:
    resolution: {integrity: sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=}
    engines: {node: '>= 0.4'}

  has-flag@3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=}

  has-proto@1.2.0:
    resolution: {integrity: sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=}
    engines: {node: '>= 0.4'}

  has-symbols@1.1.0:
    resolution: {integrity: sha1-/JxqeDoISVHQuXH+EBjegTcHozg=}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha1-AD6vkb563DcuhOxZ3DclLO24AAM=}
    engines: {node: '>= 0.4'}

  hosted-git-info@2.8.9:
    resolution: {integrity: sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=}

  html-encoding-sniffer@4.0.0:
    resolution: {integrity: sha1-aW31KafP2CRGNp3FGT5ZCjc1tEg=}
    engines: {node: '>=18'}

  http-proxy-agent@7.0.2:
    resolution: {integrity: sha1-mosfJGhmwChQlIZYX2K48sGMJw4=}
    engines: {node: '>= 14'}

  https-proxy-agent@7.0.6:
    resolution: {integrity: sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=}
    engines: {node: '>= 14'}

  human-signals@5.0.0:
    resolution: {integrity: sha1-QmZaKE+a4NreO6QevDfrS4UvOig=}
    engines: {node: '>=16.17.0'}

  husky@9.1.7:
    resolution: {integrity: sha1-1Go4A10QG0anBFaoUP9CATRMCy0=}
    engines: {node: '>=18'}
    hasBin: true

  iconv-lite@0.6.3:
    resolution: {integrity: sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=}
    engines: {node: '>=0.10.0'}

  idb@7.1.1:
    resolution: {integrity: sha1-2RDe2GbTLHztm+/Fv9829XLO1ys=}

  ignore@5.3.2:
    resolution: {integrity: sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=}
    engines: {node: '>= 4'}

  immer@10.1.1:
    resolution: {integrity: sha1-IG80TqNy2OoXaJFUXuU8zAYtt7w=}

  import-fresh@3.3.1:
    resolution: {integrity: sha1-nOy1ZQPAraHydB271lRuSxO1fM8=}
    engines: {node: '>=6'}

  import-in-the-middle@1.13.1:
    resolution: {integrity: sha1-eJZR+ek92QKlowb0matR63KwOhI=}

  import-meta-resolve@4.1.0:
    resolution: {integrity: sha1-+duL6tn6+mGtuBHbd6K/IsU5lwY=}

  imurmurhash@0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=}
    engines: {node: '>=0.8.19'}

  indent-string@4.0.0:
    resolution: {integrity: sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=}
    engines: {node: '>=8'}

  inflight@1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.4:
    resolution: {integrity: sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=}

  ini@4.1.1:
    resolution: {integrity: sha1-2Vs9hDsekG5W1nR9VEeQT/UM56E=}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  inputmask@5.0.9:
    resolution: {integrity: sha1-e/ToP14ZnIjA7fKFRdwj+iCO9L4=}

  internal-slot@1.1.0:
    resolution: {integrity: sha1-HqyRdilH0vcFa8g42T4TsulgSWE=}
    engines: {node: '>= 0.4'}

  is-array-buffer@3.0.5:
    resolution: {integrity: sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=}
    engines: {node: '>= 0.4'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=}

  is-async-function@2.1.1:
    resolution: {integrity: sha1-PmkBjI4E5ztzh5PQIL/ohLn9NSM=}
    engines: {node: '>= 0.4'}

  is-bigint@1.1.0:
    resolution: {integrity: sha1-3aejRF31ekJYPbQihoLrp8QXBnI=}
    engines: {node: '>= 0.4'}

  is-boolean-object@1.2.2:
    resolution: {integrity: sha1-cGf0dwmAmjk8cf9bs+E12KkhXZ4=}
    engines: {node: '>= 0.4'}

  is-callable@1.2.7:
    resolution: {integrity: sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=}
    engines: {node: '>= 0.4'}

  is-core-module@2.16.1:
    resolution: {integrity: sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=}
    engines: {node: '>= 0.4'}

  is-data-view@1.0.2:
    resolution: {integrity: sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=}
    engines: {node: '>= 0.4'}

  is-date-object@1.1.0:
    resolution: {integrity: sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=}
    engines: {node: '>= 0.4'}

  is-extglob@2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=}
    engines: {node: '>=0.10.0'}

  is-finalizationregistry@1.1.1:
    resolution: {integrity: sha1-7v3NxslN3QZ02chYh7+T+USpfJA=}
    engines: {node: '>= 0.4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=}
    engines: {node: '>=8'}

  is-fullwidth-code-point@4.0.0:
    resolution: {integrity: sha1-+uMWfHKedGP4RhzlErCApJJoqog=}
    engines: {node: '>=12'}

  is-fullwidth-code-point@5.0.0:
    resolution: {integrity: sha1-lgnvztfC+X2ntgFF70gceHx7pwQ=}
    engines: {node: '>=18'}

  is-generator-function@1.1.0:
    resolution: {integrity: sha1-vz7tqTEgE5T1e126KAD5GiODCco=}
    engines: {node: '>= 0.4'}

  is-glob@4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=}
    engines: {node: '>=0.10.0'}

  is-map@2.0.3:
    resolution: {integrity: sha1-7elrf+HicLPERl46RlZYdkkm1i4=}
    engines: {node: '>= 0.4'}

  is-module@1.0.0:
    resolution: {integrity: sha1-Mlj7afeMFNW4FdZkM2tM/7ZEFZE=}

  is-number-object@1.1.1:
    resolution: {integrity: sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=}
    engines: {node: '>= 0.4'}

  is-number@7.0.0:
    resolution: {integrity: sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=}
    engines: {node: '>=0.12.0'}

  is-obj@1.0.1:
    resolution: {integrity: sha1-PkcprB9f3gJc19g6iW2rn09n2w8=}
    engines: {node: '>=0.10.0'}

  is-obj@2.0.0:
    resolution: {integrity: sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=}
    engines: {node: '>=8'}

  is-potential-custom-element-name@1.0.1:
    resolution: {integrity: sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=}

  is-regex@1.2.1:
    resolution: {integrity: sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=}
    engines: {node: '>= 0.4'}

  is-regexp@1.0.0:
    resolution: {integrity: sha1-/S2INUXEa6xaYz57mgnof6LLUGk=}
    engines: {node: '>=0.10.0'}

  is-set@2.0.3:
    resolution: {integrity: sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=}
    engines: {node: '>= 0.4'}

  is-shared-array-buffer@1.0.4:
    resolution: {integrity: sha1-m2eES9m38ka6BwjDqT40Jpx3T28=}
    engines: {node: '>= 0.4'}

  is-stream@2.0.1:
    resolution: {integrity: sha1-+sHj1TuXrVqdCunO8jifWBClwHc=}
    engines: {node: '>=8'}

  is-stream@3.0.0:
    resolution: {integrity: sha1-5r/XqmvvafT0cs6btoHj5XtDGaw=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  is-string@1.1.1:
    resolution: {integrity: sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=}
    engines: {node: '>= 0.4'}

  is-symbol@1.1.1:
    resolution: {integrity: sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=}
    engines: {node: '>= 0.4'}

  is-text-path@2.0.0:
    resolution: {integrity: sha1-skhOK3IKYz/rLoW2fcGT/3LHVjY=}
    engines: {node: '>=8'}

  is-typed-array@1.1.15:
    resolution: {integrity: sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=}
    engines: {node: '>= 0.4'}

  is-weakmap@2.0.2:
    resolution: {integrity: sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=}
    engines: {node: '>= 0.4'}

  is-weakref@1.1.1:
    resolution: {integrity: sha1-7qQwGCvo1kF0vZa/+8RvIb8/kpM=}
    engines: {node: '>= 0.4'}

  is-weakset@2.0.4:
    resolution: {integrity: sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=}
    engines: {node: '>= 0.4'}

  isarray@2.0.5:
    resolution: {integrity: sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=}

  isexe@2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=}

  iterator.prototype@1.1.5:
    resolution: {integrity: sha1-EslZop3jLeCqO7u4AfTXdwZtrjk=}
    engines: {node: '>= 0.4'}

  jake@10.9.2:
    resolution: {integrity: sha1-auSH5qaa/sOl4WdiiZa1nzWuK38=}
    engines: {node: '>=10'}
    hasBin: true

  jiti@2.4.2:
    resolution: {integrity: sha1-0Zt3Muu2EWsG4gONp0pVNm+u9WA=}
    hasBin: true

  js-cookie@3.0.5:
    resolution: {integrity: sha1-C34v0MAVUsWLqG4IQflNwlV9zbw=}
    engines: {node: '>=14'}

  js-tokens@4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=}

  js-yaml@4.1.0:
    resolution: {integrity: sha1-wftl+PUBeQHN0slRhkuhhFihBgI=}
    hasBin: true

  jsdom@26.0.0:
    resolution: {integrity: sha1-RG3RrYz8UN9+cU5Y8flywXY7NUw=}
    engines: {node: '>=18'}
    peerDependencies:
      canvas: ^3.0.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jsesc@3.0.2:
    resolution: {integrity: sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=}
    engines: {node: '>=6'}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=}

  json-parse-better-errors@1.0.2:
    resolution: {integrity: sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=}

  json-schema@0.4.0:
    resolution: {integrity: sha1-995M9u+rg4666zI2R0y7paGTCrU=}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=}

  json5@2.2.3:
    resolution: {integrity: sha1-eM1vGhm9wStz21rQxh79ZsHikoM=}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@6.1.0:
    resolution: {integrity: sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=}

  jsonparse@1.3.1:
    resolution: {integrity: sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA=}
    engines: {'0': node >= 0.2.0}

  jsonpointer@5.0.1:
    resolution: {integrity: sha1-IRDgrwkA/TdGe1kH7NE6eIShtVk=}
    engines: {node: '>=0.10.0'}

  jsx-ast-utils@3.3.5:
    resolution: {integrity: sha1-R2a9BajioRryIr7NGeFVdeUqhTo=}
    engines: {node: '>=4.0'}

  keyv@4.5.4:
    resolution: {integrity: sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=}

  leven@3.1.0:
    resolution: {integrity: sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=}
    engines: {node: '>=6'}

  levn@0.4.1:
    resolution: {integrity: sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=}
    engines: {node: '>= 0.8.0'}

  lightningcss-darwin-arm64@1.29.2:
    resolution: {integrity: sha1-bO/ziwETSvSOhZOU4coh5dSfquY=}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [darwin]

  lightningcss-darwin-x64@1.29.2:
    resolution: {integrity: sha1-iRtvnldoLXlCI8M0Y8pm068/sDg=}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [darwin]

  lightningcss-freebsd-x64@1.29.2:
    resolution: {integrity: sha1-ipX5q3OysrC+7+FZn6+osFiThJU=}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [freebsd]

  lightningcss-linux-arm-gnueabihf@1.29.2:
    resolution: {integrity: sha1-XGC7+Ss51+1R42P3uYpxEb9ZFKE=}
    engines: {node: '>= 12.0.0'}
    cpu: [arm]
    os: [linux]

  lightningcss-linux-arm64-gnu@1.29.2:
    resolution: {integrity: sha1-5z12CMTM4DTDZU5ei1O+dIRiJN4=}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  lightningcss-linux-arm64-musl@1.29.2:
    resolution: {integrity: sha1-qVoY1akJgxwJLgqNLeS5rBqNsVE=}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  lightningcss-linux-x64-gnu@1.29.2:
    resolution: {integrity: sha1-VRygflZTlJKGQu3ukqzAQuVGy3g=}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  lightningcss-linux-x64-musl@1.29.2:
    resolution: {integrity: sha1-L9FkVUNAgxvOUChbVxAYF4UN0lg=}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  lightningcss-win32-arm64-msvc@1.29.2:
    resolution: {integrity: sha1-2kPqSfr8XS3jjgFvGoU51e7Zgxg=}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [win32]

  lightningcss-win32-x64-msvc@1.29.2:
    resolution: {integrity: sha1-3e+qCZo5tyWy9bvcufxxhDXMl5c=}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [win32]

  lightningcss@1.29.2:
    resolution: {integrity: sha1-9fD9bmMpKiMml+b+cJ2ltHYk3vM=}
    engines: {node: '>= 12.0.0'}

  lilconfig@3.1.3:
    resolution: {integrity: sha1-obz9Ylf5WFv1rhTO7rt7VZAl5MQ=}
    engines: {node: '>=14'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=}

  lint-staged@15.5.0:
    resolution: {integrity: sha1-+mRkz7BuD69bsWf4MYbpUv9uVp4=}
    engines: {node: '>=18.12.0'}
    hasBin: true

  listr2@8.3.2:
    resolution: {integrity: sha1-wlLsmjM0lQv8qSOEV9CtLBpcyGc=}
    engines: {node: '>=18.0.0'}

  load-json-file@4.0.0:
    resolution: {integrity: sha1-L19Fq5HjMhYjT9U62rZo607AmTs=}
    engines: {node: '>=4'}

  locate-path@6.0.0:
    resolution: {integrity: sha1-VTIeswn+u8WcSAHZMackUqaB0oY=}
    engines: {node: '>=10'}

  locate-path@7.2.0:
    resolution: {integrity: sha1-acsXeb2Qs1qx53Hh8viaICwqioo=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  lodash.camelcase@4.3.0:
    resolution: {integrity: sha1-soqmKIorn8ZRA1x3EfZathkDMaY=}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha1-gteb/zCmfEAF/9XiUVMArZyk168=}

  lodash.isplainobject@4.0.6:
    resolution: {integrity: sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=}

  lodash.kebabcase@4.1.1:
    resolution: {integrity: sha1-hImxyw0p/4gZXM7KRI/21swpXDY=}

  lodash.merge@4.6.2:
    resolution: {integrity: sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=}

  lodash.mergewith@4.6.2:
    resolution: {integrity: sha1-YXEh+JrFX1kEfHrsHM1mVMZZD1U=}

  lodash.snakecase@4.1.1:
    resolution: {integrity: sha1-OdcUo1NXFHg3rv1ktdy7Fr7Nj40=}

  lodash.sortby@4.7.0:
    resolution: {integrity: sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=}

  lodash.startcase@4.4.0:
    resolution: {integrity: sha1-lDbjTtJgk+1/+uGTYUQ1CRXZrdg=}

  lodash.uniq@4.5.0:
    resolution: {integrity: sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=}

  lodash.upperfirst@4.3.1:
    resolution: {integrity: sha1-E2Xt9DFIBIHvDRxolXpe2Z1J984=}

  lodash@4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=}

  log-update@6.1.0:
    resolution: {integrity: sha1-GgT/OBZvlGR64a9WL0vWoVsbfNQ=}
    engines: {node: '>=18'}

  long@5.3.1:
    resolution: {integrity: sha1-nUIi0yE/OKXsgJZ0g04PCrIavpY=}

  loose-envify@1.4.0:
    resolution: {integrity: sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=}
    hasBin: true

  loupe@3.1.3:
    resolution: {integrity: sha1-BCqPeYbXfz0PmO95kKKy/vGLD9I=}

  lru-cache@10.4.3:
    resolution: {integrity: sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=}

  lru-cache@5.1.1:
    resolution: {integrity: sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=}

  lucide-react@0.476.0:
    resolution: {integrity: sha1-ozNUgy6oWyw3ZPrwnYXJqTMvf2A=}
    peerDependencies:
      react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0

  lz-string@1.5.0:
    resolution: {integrity: sha1-watQ93iHtxJiEgG6n9Tjpu0JmUE=}
    hasBin: true

  magic-string@0.25.9:
    resolution: {integrity: sha1-3n+fr5HvihyR0CwuUxTIJ3283Rw=}

  magic-string@0.30.17:
    resolution: {integrity: sha1-RQpElnPSRg5bvPupphkWoXFMdFM=}

  masonic@4.0.1:
    resolution: {integrity: sha1-34F+blRa7qQVYJoLFEvtCQIz+OQ=}
    peerDependencies:
      react: '>=16.8'

  math-intrinsics@1.1.0:
    resolution: {integrity: sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=}
    engines: {node: '>= 0.4'}

  memorystream@0.3.1:
    resolution: {integrity: sha1-htcJCzDORV1j+64S3aUaR93K+bI=}
    engines: {node: '>= 0.10.0'}

  meow@12.1.1:
    resolution: {integrity: sha1-5Vjd26sSR3tpsumicowyfxkbrOY=}
    engines: {node: '>=16.10'}

  merge-stream@2.0.0:
    resolution: {integrity: sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=}

  merge2@1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=}
    engines: {node: '>= 8'}

  micromatch@4.0.8:
    resolution: {integrity: sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha1-u6vNwChZ9JhzAchW4zh85exDv3A=}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=}
    engines: {node: '>= 0.6'}

  mimic-fn@4.0.0:
    resolution: {integrity: sha1-YKkFUNXLCyOcymXYk7GlOymHHsw=}
    engines: {node: '>=12'}

  mimic-function@5.0.1:
    resolution: {integrity: sha1-rL4rM0n5m53qyn+3Dki4PpTmcHY=}
    engines: {node: '>=18'}

  min-indent@1.0.1:
    resolution: {integrity: sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=}
    engines: {node: '>=4'}

  minimatch@3.1.2:
    resolution: {integrity: sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=}

  minimatch@5.1.6:
    resolution: {integrity: sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=}
    engines: {node: '>=10'}

  minimatch@9.0.5:
    resolution: {integrity: sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=}

  module-details-from-path@1.0.3:
    resolution: {integrity: sha1-EUyUlnPiqKNenTV4hSeqN7Z52is=}

  motion-dom@12.12.1:
    resolution: {integrity: sha1-EvUMd4yild4zfijztJN9qG6QmOo=}

  motion-utils@12.12.1:
    resolution: {integrity: sha1-Y+KHUTJcudHNaE88JzpXACKwAQ4=}

  ms@2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=}

  nanoid@3.3.11:
    resolution: {integrity: sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=}

  next-themes@0.4.6:
    resolution: {integrity: sha1-jX6S0DuP6mWCiSpQqSjJsjUC6LY=}
    peerDependencies:
      react: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc
      react-dom: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc

  nice-try@1.0.5:
    resolution: {integrity: sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=}

  node-releases@2.0.19:
    resolution: {integrity: sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=}

  normalize-package-data@2.5.0:
    resolution: {integrity: sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=}

  normalize-range@0.1.2:
    resolution: {integrity: sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=}
    engines: {node: '>=0.10.0'}

  npm-run-all@4.1.5:
    resolution: {integrity: sha1-BEdiAqFe4OLiFAgIYb/xKlHZj7o=}
    engines: {node: '>= 4'}
    hasBin: true

  npm-run-path@5.3.0:
    resolution: {integrity: sha1-4jNT0Ou5MX8XTpNBfkpNgtAknp8=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  nwsapi@2.2.20:
    resolution: {integrity: sha1-IuUyU8Yeew5+k870LIkRVLzKEe8=}

  object-assign@4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=}
    engines: {node: '>=0.10.0'}

  object-inspect@1.13.4:
    resolution: {integrity: sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha1-HEfyct8nfzsdrwYWd9nILiMixg4=}
    engines: {node: '>= 0.4'}

  object.assign@4.1.7:
    resolution: {integrity: sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=}
    engines: {node: '>= 0.4'}

  object.entries@1.1.9:
    resolution: {integrity: sha1-5HcKahREr7Yb05+YQBi1vt4l+LM=}
    engines: {node: '>= 0.4'}

  object.fromentries@2.0.8:
    resolution: {integrity: sha1-9xldipuXvZXLwZmeqTns0aKwDGU=}
    engines: {node: '>= 0.4'}

  object.values@1.2.1:
    resolution: {integrity: sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=}
    engines: {node: '>= 0.4'}

  once@1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=}

  onetime@6.0.0:
    resolution: {integrity: sha1-fCTBjtH9LpvKS9JoBqM2E8d9NLQ=}
    engines: {node: '>=12'}

  onetime@7.0.0:
    resolution: {integrity: sha1-nxbJLYye9RIOOs2d2ZV8zuzBq2A=}
    engines: {node: '>=18'}

  optionator@0.9.4:
    resolution: {integrity: sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=}
    engines: {node: '>= 0.8.0'}

  own-keys@1.0.1:
    resolution: {integrity: sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=}
    engines: {node: '>= 0.4'}

  p-limit@3.1.0:
    resolution: {integrity: sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=}
    engines: {node: '>=10'}

  p-limit@4.0.0:
    resolution: {integrity: sha1-kUr2VE7TK/pUZwsGHK/L0EmEtkQ=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  p-locate@5.0.0:
    resolution: {integrity: sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=}
    engines: {node: '>=10'}

  p-locate@6.0.0:
    resolution: {integrity: sha1-PamknUk0uQEIncozAvpl3FoFwE8=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  parent-module@1.0.1:
    resolution: {integrity: sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=}
    engines: {node: '>=6'}

  parse-json@4.0.0:
    resolution: {integrity: sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=}
    engines: {node: '>=4'}

  parse-json@5.2.0:
    resolution: {integrity: sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=}
    engines: {node: '>=8'}

  parse5@7.2.1:
    resolution: {integrity: sha1-iSj1WRXmEl9DDMRDCXZb8XVWozo=}

  path-exists@4.0.0:
    resolution: {integrity: sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=}
    engines: {node: '>=8'}

  path-exists@5.0.0:
    resolution: {integrity: sha1-pqrZSJIAsh+rMeSc8JJ35RFvuec=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=}
    engines: {node: '>=0.10.0'}

  path-key@2.0.1:
    resolution: {integrity: sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=}
    engines: {node: '>=4'}

  path-key@3.1.1:
    resolution: {integrity: sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=}
    engines: {node: '>=8'}

  path-key@4.0.0:
    resolution: {integrity: sha1-KVWI3DruZBVPh3rbnXgLgcVUvxg=}
    engines: {node: '>=12'}

  path-parse@1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=}

  path-type@3.0.0:
    resolution: {integrity: sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=}
    engines: {node: '>=4'}

  pathe@2.0.3:
    resolution: {integrity: sha1-PsvsVUIWhbcKnahyss/z4cvtFxY=}

  pathval@2.0.0:
    resolution: {integrity: sha1-fiVQtCJgHU9rjibxMBvI8Vp0GiU=}
    engines: {node: '>= 14.16'}

  picocolors@1.1.1:
    resolution: {integrity: sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=}

  picomatch@2.3.1:
    resolution: {integrity: sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=}
    engines: {node: '>=8.6'}

  picomatch@4.0.2:
    resolution: {integrity: sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=}
    engines: {node: '>=12'}

  pidtree@0.3.1:
    resolution: {integrity: sha1-7wmsLMBTPfHzJQzPLE02aw0SEUo=}
    engines: {node: '>=0.10'}
    hasBin: true

  pidtree@0.6.0:
    resolution: {integrity: sha1-kK17bULVhB5p4KJBnvOPiIOqBXw=}
    engines: {node: '>=0.10'}
    hasBin: true

  pify@3.0.0:
    resolution: {integrity: sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=}
    engines: {node: '>=4'}

  playwright-core@1.51.1:
    resolution: {integrity: sha1-1X8Dk+AkFvMqR8+CsnUzZWqKzOE=}
    engines: {node: '>=18'}
    hasBin: true

  playwright@1.51.1:
    resolution: {integrity: sha1-rhRn7jGAg5aK0o1pkNtZ9HpVOQ8=}
    engines: {node: '>=18'}
    hasBin: true

  possible-typed-array-names@1.1.0:
    resolution: {integrity: sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=}
    engines: {node: '>= 0.4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=}

  postcss@8.5.3:
    resolution: {integrity: sha1-FGO28cf7Fv4lhzbLopot41I36vs=}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.2.1:
    resolution: {integrity: sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=}
    engines: {node: '>= 0.8.0'}

  pretty-bytes@5.6.0:
    resolution: {integrity: sha1-NWJW9kOAR3PIL2RyP+eMksYr6us=}
    engines: {node: '>=6'}

  pretty-bytes@6.1.1:
    resolution: {integrity: sha1-OM1rtG9Hr79mfCAs/HVL/9IBajs=}
    engines: {node: ^14.13.1 || >=16.0.0}

  pretty-format@27.5.1:
    resolution: {integrity: sha1-IYGHn96lGnpYUfs52SD6pj8B2I4=}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  prop-types@15.8.1:
    resolution: {integrity: sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=}

  protobufjs@7.4.0:
    resolution: {integrity: sha1-fv4yTOmzthyCquXegQ0oe8CKJIo=}
    engines: {node: '>=12.0.0'}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=}

  punycode@2.3.1:
    resolution: {integrity: sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=}
    engines: {node: '>=6'}

  queue-microtask@1.2.3:
    resolution: {integrity: sha1-SSkii7xyTfrEPg77BYyve2z7YkM=}

  raf-schd@4.0.3:
    resolution: {integrity: sha1-XWw070b4sqDogKj823Q+/Fv9vBo=}

  randombytes@2.1.0:
    resolution: {integrity: sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=}

  react-dom@19.1.0:
    resolution: {integrity: sha1-EzVY3so3+h1oJwjfiQSyUYZ5NiM=}
    peerDependencies:
      react: ^19.1.0

  react-icons@5.5.0:
    resolution: {integrity: sha1-iqJdNUP/hCMWhdMzEWTAApnN+vI=}
    peerDependencies:
      react: '*'

  react-is@16.13.1:
    resolution: {integrity: sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=}

  react-is@17.0.2:
    resolution: {integrity: sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=}

  react-layout-masonry@1.2.0:
    resolution: {integrity: sha1-Z15AMsEXFaW0faLejlxZug/XKhI=}
    peerDependencies:
      react: ^18 || ^19

  react-photo-album@3.1.0:
    resolution: {integrity: sha1-kMpbEWNBVDztMCTFgOvrBjlMHP4=}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/react': ^18 || ^19
      react: ^18 || ^19
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-refresh@0.14.2:
    resolution: {integrity: sha1-ODPaAc4y2kcPH5NrnUd9pccCi/k=}
    engines: {node: '>=0.10.0'}

  react-remove-scroll-bar@2.3.8:
    resolution: {integrity: sha1-mcIPkI7kZ7OFtoo0abSj51ABIiM=}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-remove-scroll@2.6.3:
    resolution: {integrity: sha1-3wLN5W1fJzHgWFMfj/1/mt7JGsI=}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-responsive-masonry@2.7.1:
    resolution: {integrity: sha1-0ntzT+V5A0zyavEvQuQvKaMywLM=}

  react-router@7.5.0:
    resolution: {integrity: sha1-ZJVo5+O0Ez3uHc8zPYo6N7GWf1g=}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'
    peerDependenciesMeta:
      react-dom:
        optional: true

  react-style-singleton@2.2.3:
    resolution: {integrity: sha1-QmVgi+aaTXDP4wR/LGyIssOs44g=}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-use-websocket@4.13.0:
    resolution: {integrity: sha1-nbHbrG3Iui/cAqW7oGIF+/ZAZzY=}

  react@19.1.0:
    resolution: {integrity: sha1-kmhktsSNp2J/AEeV1szlDpB5O3U=}
    engines: {node: '>=0.10.0'}

  read-pkg@3.0.0:
    resolution: {integrity: sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=}
    engines: {node: '>=4'}

  redent@3.0.0:
    resolution: {integrity: sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=}
    engines: {node: '>=8'}

  reflect.getprototypeof@1.0.10:
    resolution: {integrity: sha1-xikhnnijMW2LYEx2XvaJlpZOe/k=}
    engines: {node: '>= 0.4'}

  regenerate-unicode-properties@10.2.0:
    resolution: {integrity: sha1-Ym4534w3Izjqm4Ao0fmdw/2cPbA=}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha1-NWreECY/aF3aElEAzYYsHbiVMn8=}

  regenerator-transform@0.15.2:
    resolution: {integrity: sha1-W7rli1IgmOvfCbyi+Dg4kpABx6Q=}

  regexp.prototype.flags@1.5.4:
    resolution: {integrity: sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=}
    engines: {node: '>= 0.4'}

  regexpu-core@6.2.0:
    resolution: {integrity: sha1-DlGQ155UK/KUlV3Mq64E08fVOCY=}
    engines: {node: '>=4'}

  regjsgen@0.8.0:
    resolution: {integrity: sha1-3yP/JuDFswCmRwytFgqdCQw6N6s=}

  regjsparser@0.12.0:
    resolution: {integrity: sha1-DoRt9sZTBYZCk3feVuBHVYOwiNw=}
    hasBin: true

  require-directory@2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=}
    engines: {node: '>=0.10.0'}

  require-in-the-middle@7.5.2:
    resolution: {integrity: sha1-3CWxSK/61C5XDPDkG6MNwA8XA+w=}
    engines: {node: '>=8.6.0'}

  resolve-from@4.0.0:
    resolution: {integrity: sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=}
    engines: {node: '>=8'}

  resolve@1.22.10:
    resolution: {integrity: sha1-tmPoP/sJu/I4aURza6roAwKbizk=}
    engines: {node: '>= 0.4'}
    hasBin: true

  resolve@2.0.0-next.5:
    resolution: {integrity: sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=}
    hasBin: true

  restore-cursor@5.1.0:
    resolution: {integrity: sha1-B2bZVpnvrLFBUJk/VbrwlT6h6+c=}
    engines: {node: '>=18'}

  reusify@1.1.0:
    resolution: {integrity: sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.4.1:
    resolution: {integrity: sha1-d492xPtzHZNBTo+SX77PZMzn9so=}

  rollup@2.79.2:
    resolution: {integrity: sha1-8VDkpdtLEhohp0fXYvcB5en0kJA=}
    engines: {node: '>=10.0.0'}
    hasBin: true

  rollup@4.39.0:
    resolution: {integrity: sha1-ncEBO3DA4stw7yg1AULpuBs/ZAw=}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  rrweb-cssom@0.8.0:
    resolution: {integrity: sha1-MCHRtDUvvzthSq7tC8DVc5q+C8I=}

  run-parallel@1.2.0:
    resolution: {integrity: sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=}

  safe-array-concat@1.1.3:
    resolution: {integrity: sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=}
    engines: {node: '>=0.4'}

  safe-buffer@5.2.1:
    resolution: {integrity: sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=}

  safe-push-apply@1.0.0:
    resolution: {integrity: sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=}
    engines: {node: '>= 0.4'}

  safe-regex-test@1.1.0:
    resolution: {integrity: sha1-f4fftnoxUHguqvGFg/9dFxGsEME=}
    engines: {node: '>= 0.4'}

  safer-buffer@2.1.2:
    resolution: {integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=}

  saxes@6.0.0:
    resolution: {integrity: sha1-/ltKR2jfTxSiAbG6amXB89mYjMU=}
    engines: {node: '>=v12.22.7'}

  scheduler@0.26.0:
    resolution: {integrity: sha1-TOiowqIJXxPqEb+aRFvlDFVdYzc=}

  semver@5.7.2:
    resolution: {integrity: sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=}
    hasBin: true

  semver@7.7.1:
    resolution: {integrity: sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=}
    engines: {node: '>=10'}
    hasBin: true

  serialize-javascript@6.0.2:
    resolution: {integrity: sha1-3voeBVyDv21Z6oBdjahiJU62psI=}

  set-cookie-parser@2.7.1:
    resolution: {integrity: sha1-MBbxUAciAt++kPre4FNXPMidKUM=}

  set-function-length@1.2.2:
    resolution: {integrity: sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha1-FqcFxaDcL15jjKltiozU4cK5CYU=}
    engines: {node: '>= 0.4'}

  set-proto@1.0.0:
    resolution: {integrity: sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=}
    engines: {node: '>= 0.4'}

  shebang-command@1.2.0:
    resolution: {integrity: sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=}
    engines: {node: '>=0.10.0'}

  shebang-command@2.0.0:
    resolution: {integrity: sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=}
    engines: {node: '>=8'}

  shebang-regex@1.0.0:
    resolution: {integrity: sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=}
    engines: {node: '>=0.10.0'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=}
    engines: {node: '>=8'}

  shell-quote@1.8.2:
    resolution: {integrity: sha1-0tg+BXlZ1T7CYTEenpuPUdyyk0o=}
    engines: {node: '>= 0.4'}

  shimmer@1.2.1:
    resolution: {integrity: sha1-YQhZ994ye1h+/r9QH7QxF/mv8zc=}

  side-channel-list@1.0.0:
    resolution: {integrity: sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=}
    engines: {node: '>= 0.4'}

  siginfo@2.0.0:
    resolution: {integrity: sha1-MudscLeXJOO7Vny51UPrhYzPrzA=}

  signal-exit@4.1.0:
    resolution: {integrity: sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=}
    engines: {node: '>=14'}

  slice-ansi@5.0.0:
    resolution: {integrity: sha1-tzBjxXqpb5zYgWVLFSlNldKFxCo=}
    engines: {node: '>=12'}

  slice-ansi@7.1.0:
    resolution: {integrity: sha1-zWtGVeKYqNG96wQlCkMwlLNHuak=}
    engines: {node: '>=18'}

  smob@1.5.0:
    resolution: {integrity: sha1-hdeaFAOr8SjSTT68HNxeGpVI06s=}

  sonner@2.0.3:
    resolution: {integrity: sha1-3nzbxLaiWsPwqeCu03SOCz1uCS4=}
    peerDependencies:
      react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc

  source-map-js@1.2.1:
    resolution: {integrity: sha1-HOVlD93YerwJnto33P8CTCZnrkY=}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.21:
    resolution: {integrity: sha1-BP58f54e0tZiIzwoyys1ufY/bk8=}

  source-map@0.6.1:
    resolution: {integrity: sha1-dHIq8y6WFOnCh6jQu95IteLxomM=}
    engines: {node: '>=0.10.0'}

  source-map@0.8.0-beta.0:
    resolution: {integrity: sha1-1MG7QsP37pJfAFknuhBwng0dHxE=}
    engines: {node: '>= 8'}

  sourcemap-codec@1.4.8:
    resolution: {integrity: sha1-6oBL2UhXQC5pktBaOO8a41qatMQ=}
    deprecated: Please use @jridgewell/sourcemap-codec instead

  spdx-correct@3.2.0:
    resolution: {integrity: sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=}

  spdx-exceptions@2.5.0:
    resolution: {integrity: sha1-XWB9J/yAb2bXtkp2ZlD6iQ8E7WY=}

  spdx-expression-parse@3.0.1:
    resolution: {integrity: sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=}

  spdx-license-ids@3.0.21:
    resolution: {integrity: sha1-bW6YDJ3ytvyQU0OjstcCpiOVNsM=}

  split2@4.2.0:
    resolution: {integrity: sha1-ycWSCQTRSLqwufZxRfJFqGqtv6Q=}
    engines: {node: '>= 10.x'}

  stackback@0.0.2:
    resolution: {integrity: sha1-Gsig2Ug4SNFpXkGLbQMaPDzmjjs=}

  std-env@3.9.0:
    resolution: {integrity: sha1-Gm9yQ7M53KTJ/VXhx1BMd+8j6PE=}

  string-argv@0.3.2:
    resolution: {integrity: sha1-K20O8ktlYnTZV9VOCku/YVPcArY=}
    engines: {node: '>=0.6.19'}

  string-width@4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=}
    engines: {node: '>=8'}

  string-width@7.2.0:
    resolution: {integrity: sha1-tbuOIWXOJ11NQ0dt0nAK2Qkdttw=}
    engines: {node: '>=18'}

  string.prototype.matchall@4.0.12:
    resolution: {integrity: sha1-bIh0DkmtSVaxMyqRHpSVg6J11MA=}
    engines: {node: '>= 0.4'}

  string.prototype.padend@3.1.6:
    resolution: {integrity: sha1-unnPiZJgmpHIctqkfGuxRO5/YqU=}
    engines: {node: '>= 0.4'}

  string.prototype.repeat@1.0.0:
    resolution: {integrity: sha1-6Qhy7gMIspQ1qiYnX24bdi2u4Bo=}

  string.prototype.trim@1.2.10:
    resolution: {integrity: sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.9:
    resolution: {integrity: sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=}
    engines: {node: '>= 0.4'}

  string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=}
    engines: {node: '>= 0.4'}

  stringify-object@3.3.0:
    resolution: {integrity: sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=}
    engines: {node: '>=4'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=}
    engines: {node: '>=12'}

  strip-bom@3.0.0:
    resolution: {integrity: sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=}
    engines: {node: '>=4'}

  strip-comments@2.0.1:
    resolution: {integrity: sha1-StEcP7ysF3pnpArCJMoznKHBups=}
    engines: {node: '>=10'}

  strip-final-newline@3.0.0:
    resolution: {integrity: sha1-UolMMT+/8xiDUoCu1g/3Hr8SuP0=}
    engines: {node: '>=12'}

  strip-indent@3.0.0:
    resolution: {integrity: sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=}
    engines: {node: '>=8'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=}
    engines: {node: '>=8'}

  supports-color@5.5.0:
    resolution: {integrity: sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=}
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha1-btpL00SjyUrqN21MwxvHcxEDngk=}
    engines: {node: '>= 0.4'}

  symbol-tree@3.2.4:
    resolution: {integrity: sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=}

  tailwind-merge@3.2.0:
    resolution: {integrity: sha1-vtz2pnqMmC2lkTr8upyFTzWruFc=}

  tailwindcss-animate@1.0.7:
    resolution: {integrity: sha1-MYtpLExCZ2zJ5nsZt4d1dCOIvvQ=}
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders'

  tailwindcss@4.1.3:
    resolution: {integrity: sha1-9aa0RRKVwG4hMBNpf3GTvhYw+kY=}

  tapable@2.2.1:
    resolution: {integrity: sha1-GWenPvQGCoLxKrlq+G1S/bdu7KA=}
    engines: {node: '>=6'}

  temp-dir@2.0.0:
    resolution: {integrity: sha1-vekrBb3+sVFugEycAK1FF38xMh4=}
    engines: {node: '>=8'}

  tempy@0.6.0:
    resolution: {integrity: sha1-ZeLDWrwG8RJKl/OHsIMDRCveWfM=}
    engines: {node: '>=10'}

  terser@5.39.0:
    resolution: {integrity: sha1-DoIDPtV7Pd8flnCNEjzKcX2Gyjo=}
    engines: {node: '>=10'}
    hasBin: true

  text-extensions@2.4.0:
    resolution: {integrity: sha1-oc/MUM802kG/0EfMdE+ATRaA6jQ=}
    engines: {node: '>=8'}

  through@2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=}

  tinybench@2.9.0:
    resolution: {integrity: sha1-EDyfi6bXI3pHq23R3P93JRhjQms=}

  tinyexec@0.3.2:
    resolution: {integrity: sha1-lBeU5leoXklld5lcbu9m9T9Cs9I=}

  tinyglobby@0.2.12:
    resolution: {integrity: sha1-rJQaQuDFdzvQtdCPMt6C50oaYbU=}
    engines: {node: '>=12.0.0'}

  tinypool@1.0.2:
    resolution: {integrity: sha1-cGGTzFMvTBAPZqoAsBxCFz2QUbI=}
    engines: {node: ^18.0.0 || >=20.0.0}

  tinyrainbow@2.0.0:
    resolution: {integrity: sha1-lQmyFiQ2MV6A4+7g/M5EdNJEQpQ=}
    engines: {node: '>=14.0.0'}

  tinyspy@3.0.2:
    resolution: {integrity: sha1-ht0889c3sVrc8X14h8hKdSAd8go=}
    engines: {node: '>=14.0.0'}

  tldts-core@6.1.85:
    resolution: {integrity: sha1-b2t5VGjAtfdmChHHMG/ydmzq6n4=}

  tldts@6.1.85:
    resolution: {integrity: sha1-x2Nq1ewvAlkSZDYNpRxrWE7297s=}
    hasBin: true

  to-regex-range@5.0.1:
    resolution: {integrity: sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=}
    engines: {node: '>=8.0'}

  tough-cookie@5.1.2:
    resolution: {integrity: sha1-Ztd0tKHZ4S3HUIlyWvOsdewxvtc=}
    engines: {node: '>=16'}

  tr46@1.0.1:
    resolution: {integrity: sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=}

  tr46@5.1.0:
    resolution: {integrity: sha1-Sgd5IjYK6Afhcgdc5b63mzbkoQE=}
    engines: {node: '>=18'}

  trie-memoize@1.2.0:
    resolution: {integrity: sha1-Oi6pMYcOteUaVHtCGlTVDKynizo=}

  ts-api-utils@2.1.0:
    resolution: {integrity: sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=}
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'

  tslib@2.8.1:
    resolution: {integrity: sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=}

  turbo-stream@2.4.0:
    resolution: {integrity: sha1-Hk/KZyXpD6FKxK23gvLTdZpWlfA=}

  type-check@0.4.0:
    resolution: {integrity: sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=}
    engines: {node: '>= 0.8.0'}

  type-fest@0.16.0:
    resolution: {integrity: sha1-MkC4kaeLDerpENvrhlU+VSoUiGA=}
    engines: {node: '>=10'}

  typed-array-buffer@1.0.3:
    resolution: {integrity: sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=}
    engines: {node: '>= 0.4'}

  typed-array-byte-length@1.0.3:
    resolution: {integrity: sha1-hAegT314aE89JSqhoUPSt3tBYM4=}
    engines: {node: '>= 0.4'}

  typed-array-byte-offset@1.0.4:
    resolution: {integrity: sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=}
    engines: {node: '>= 0.4'}

  typed-array-length@1.0.7:
    resolution: {integrity: sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=}
    engines: {node: '>= 0.4'}

  typescript-eslint@8.29.1:
    resolution: {integrity: sha1-wLIF5UKt4i+QJ8qqqcTsMaICAQ8=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  typescript@5.8.3:
    resolution: {integrity: sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=}
    engines: {node: '>=14.17'}
    hasBin: true

  ua-parser-js@1.0.40:
    resolution: {integrity: sha1-rGr/T9jqPnlKaqdD7Jwvwp51tnU=}
    hasBin: true

  unbox-primitive@1.1.0:
    resolution: {integrity: sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=}
    engines: {node: '>= 0.4'}

  undici-types@6.21.0:
    resolution: {integrity: sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=}

  unicode-canonical-property-names-ecmascript@2.0.1:
    resolution: {integrity: sha1-yzFz/kfKdD4ighbko93EyE1ijMI=}
    engines: {node: '>=4'}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.2.0:
    resolution: {integrity: sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=}
    engines: {node: '>=4'}

  unicorn-magic@0.1.0:
    resolution: {integrity: sha1-G7mlHII6r51zqL/NPRoj3elLDOQ=}
    engines: {node: '>=18'}

  unique-string@2.0.0:
    resolution: {integrity: sha1-OcZFH4GvsnSd4rIz4/fF6IQ72J0=}
    engines: {node: '>=8'}

  universalify@2.0.1:
    resolution: {integrity: sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=}
    engines: {node: '>= 10.0.0'}

  upath@1.2.0:
    resolution: {integrity: sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=}
    engines: {node: '>=4'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=}

  use-callback-ref@1.3.3:
    resolution: {integrity: sha1-mNn6sGcHWEHFssaFIJDV0P6r4r8=}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sidecar@1.1.3:
    resolution: {integrity: sha1-EOf9iX0TC4luLFRsY6XoIz0A79s=}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  validate-npm-package-license@3.0.4:
    resolution: {integrity: sha1-/JH2uce6FchX9MssXe/uw51PQQo=}

  vite-node@3.1.1:
    resolution: {integrity: sha1-rRhsB4Wabl/KfH9WPlX7EbFlV7w=}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true

  vite-plugin-pwa@0.21.2:
    resolution: {integrity: sha1-XGw39S6Wk5uomcXlccErf6zMbEk=}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@vite-pwa/assets-generator': ^0.2.6
      vite: ^3.1.0 || ^4.0.0 || ^5.0.0 || ^6.0.0
      workbox-build: ^7.3.0
      workbox-window: ^7.3.0
    peerDependenciesMeta:
      '@vite-pwa/assets-generator':
        optional: true

  vite-plugin-wasm@3.4.1:
    resolution: {integrity: sha1-YrVGrH4lzCoOexMx7VxLod1xt0M=}
    peerDependencies:
      vite: ^2 || ^3 || ^4 || ^5 || ^6

  vite@6.2.6:
    resolution: {integrity: sha1-fwzPL9wMHtoHnOJYUIco4kc9P2E=}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  vitest@3.1.1:
    resolution: {integrity: sha1-OfojVuUQUT/M3F0WRlqfwGbvH8Y=}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@edge-runtime/vm': '*'
      '@types/debug': ^4.1.12
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      '@vitest/browser': 3.1.1
      '@vitest/ui': 3.1.1
      happy-dom: '*'
      jsdom: '*'
    peerDependenciesMeta:
      '@edge-runtime/vm':
        optional: true
      '@types/debug':
        optional: true
      '@types/node':
        optional: true
      '@vitest/browser':
        optional: true
      '@vitest/ui':
        optional: true
      happy-dom:
        optional: true
      jsdom:
        optional: true

  w3c-xmlserializer@5.0.0:
    resolution: {integrity: sha1-+SW6JoVRWFlNkHMTzt0UdsWWf2w=}
    engines: {node: '>=18'}

  web-vitals@4.2.4:
    resolution: {integrity: sha1-HSC8hZCjd2m9CQKyiVUJNgaRhLc=}

  webidl-conversions@4.0.2:
    resolution: {integrity: sha1-qFWYCx8LazWbodXZ+zmulB+qY60=}

  webidl-conversions@7.0.0:
    resolution: {integrity: sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=}
    engines: {node: '>=12'}

  whatwg-encoding@3.1.1:
    resolution: {integrity: sha1-0PTvdpkF1CbhaI8+NDgambYLduU=}
    engines: {node: '>=18'}

  whatwg-mimetype@4.0.0:
    resolution: {integrity: sha1-vBv5SphdxQOI1UqSWKxAXDyi/Ao=}
    engines: {node: '>=18'}

  whatwg-url@14.2.0:
    resolution: {integrity: sha1-TuAtXXJRVdrgBPaulcc+fvXZVmM=}
    engines: {node: '>=18'}

  whatwg-url@7.1.0:
    resolution: {integrity: sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY=}

  which-boxed-primitive@1.1.1:
    resolution: {integrity: sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=}
    engines: {node: '>= 0.4'}

  which-builtin-type@1.2.1:
    resolution: {integrity: sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=}
    engines: {node: '>= 0.4'}

  which-collection@1.0.2:
    resolution: {integrity: sha1-Yn73YkOSChB+fOjpYZHevksWwqA=}
    engines: {node: '>= 0.4'}

  which-typed-array@1.1.19:
    resolution: {integrity: sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=}
    engines: {node: '>= 0.4'}

  which@1.3.1:
    resolution: {integrity: sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=}
    hasBin: true

  which@2.0.2:
    resolution: {integrity: sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=}
    engines: {node: '>= 8'}
    hasBin: true

  why-is-node-running@2.3.0:
    resolution: {integrity: sha1-o/aalxB/SUs83Dvd3Yg6fWXOvwQ=}
    engines: {node: '>=8'}
    hasBin: true

  word-wrap@1.2.5:
    resolution: {integrity: sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=}
    engines: {node: '>=0.10.0'}

  workbox-background-sync@7.3.0:
    resolution: {integrity: sha1-tjQHMajVtCuedaiofIgGko5uYwM=}

  workbox-broadcast-update@7.3.0:
    resolution: {integrity: sha1-v/hrkXlcS5+kanWNGnoVGChiMoA=}

  workbox-build@7.3.0:
    resolution: {integrity: sha1-q2iPMkGzKGIjau62KyQBlfH+S2I=}
    engines: {node: '>=16.0.0'}

  workbox-cacheable-response@7.3.0:
    resolution: {integrity: sha1-VXsPX9/Osi/iQ+PxmAfHagrmRuM=}

  workbox-core@7.3.0:
    resolution: {integrity: sha1-8k+5IEGgt0gv4t2FZUSqqfoQUkg=}

  workbox-expiration@7.3.0:
    resolution: {integrity: sha1-LB7h/a2jSqfnR09wbVQpyRS9ENI=}

  workbox-google-analytics@7.3.0:
    resolution: {integrity: sha1-PE1JVsCpgA37WH2C7IvA+c+WN5E=}

  workbox-navigation-preload@7.3.0:
    resolution: {integrity: sha1-nVRpO5F51RdeZq9e+aktG3zz5gU=}

  workbox-precaching@7.3.0:
    resolution: {integrity: sha1-qEZj1p79szTyXATboKcu0zkcTag=}

  workbox-range-requests@7.3.0:
    resolution: {integrity: sha1-Gz1cI1oP9ScUGMOnGDKB3BMczQ0=}

  workbox-recipes@7.3.0:
    resolution: {integrity: sha1-+kBxAejOUoUN+6jhelr8y3M6OUI=}

  workbox-routing@7.3.0:
    resolution: {integrity: sha1-/IYpa8EVXBEu4sFrMYCFNYbDAgg=}

  workbox-strategies@7.3.0:
    resolution: {integrity: sha1-uxUw8gWAaJWqzeo2OebPa/s6bLA=}

  workbox-streams@7.3.0:
    resolution: {integrity: sha1-pMCuUbZhIaKqb4kiniN6ym3CfrU=}

  workbox-sw@7.3.0:
    resolution: {integrity: sha1-OSFQF+ho18/mg1spYfVTadibPnM=}

  workbox-window@7.3.0:
    resolution: {integrity: sha1-5xuwtNiA0ilclr8cytts6g31HAc=}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=}
    engines: {node: '>=10'}

  wrap-ansi@9.0.0:
    resolution: {integrity: sha1-Gj3Itw2F7rg5jd+x5KAs0Ybliz4=}
    engines: {node: '>=18'}

  wrappy@1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=}

  ws@8.18.1:
    resolution: {integrity: sha1-6hMdN4Th39/5GtsKShFrEnUV48s=}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xml-name-validator@5.0.0:
    resolution: {integrity: sha1-gr6blX96/az5YeWYDxvyJ8C/dnM=}
    engines: {node: '>=18'}

  xmlchars@2.2.0:
    resolution: {integrity: sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=}

  y18n@5.0.8:
    resolution: {integrity: sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=}

  yaml@2.7.1:
    resolution: {integrity: sha1-RKJH0biFI4VWeax/p82m7X4TXPY=}
    engines: {node: '>= 14'}
    hasBin: true

  yargs-parser@21.1.1:
    resolution: {integrity: sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=}
    engines: {node: '>=12'}

  yargs@17.7.2:
    resolution: {integrity: sha1-mR3zmspnWhkrgW4eA2P5110qomk=}
    engines: {node: '>=12'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=}
    engines: {node: '>=10'}

  yocto-queue@1.2.1:
    resolution: {integrity: sha1-NtfEc593Wzy8KOYTbiGqBXrexBg=}
    engines: {node: '>=12.20'}

  zustand@5.0.3:
    resolution: {integrity: sha1-syNDW3PQayUS6Tx3I5Y0N0sOQH8=}
    engines: {node: '>=12.20.0'}
    peerDependencies:
      '@types/react': '>=18.0.0'
      immer: '>=9.0.6'
      react: '>=18.0.0'
      use-sync-external-store: '>=1.2.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      immer:
        optional: true
      react:
        optional: true
      use-sync-external-store:
        optional: true

snapshots:

  '@adobe/css-tools@4.4.2': {}

  '@alloc/quick-lru@5.2.0': {}

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@apideck/better-ajv-errors@0.3.6(ajv@8.17.1)':
    dependencies:
      ajv: 8.17.1
      json-schema: 0.4.0
      jsonpointer: 5.0.1
      leven: 3.1.0

  '@asamuzakjp/css-color@3.1.1':
    dependencies:
      '@csstools/css-calc': 2.1.2(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-color-parser': 3.0.8(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-parser-algorithms': 3.0.4(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-tokenizer': 3.0.3
      lru-cache: 10.4.3

  '@babel/code-frame@7.26.2':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.26.8': {}

  '@babel/core@7.26.10':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.27.0
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.10)
      '@babel/helpers': 7.27.0
      '@babel/parser': 7.27.0
      '@babel/template': 7.27.0
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
      convert-source-map: 2.0.0
      debug: 4.4.0
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.27.0':
    dependencies:
      '@babel/parser': 7.27.0
      '@babel/types': 7.27.0
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-annotate-as-pure@7.25.9':
    dependencies:
      '@babel/types': 7.27.0

  '@babel/helper-compilation-targets@7.27.0':
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/helper-validator-option': 7.25.9
      browserslist: 4.24.4
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.27.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/helper-replace-supers': 7.26.5(@babel/core@7.26.10)
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/traverse': 7.27.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-create-regexp-features-plugin@7.27.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-annotate-as-pure': 7.25.9
      regexpu-core: 6.2.0
      semver: 6.3.1

  '@babel/helper-define-polyfill-provider@0.6.4(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-plugin-utils': 7.26.5
      debug: 4.4.0
      lodash.debounce: 4.0.8
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-member-expression-to-functions@7.25.9':
    dependencies:
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.25.9':
    dependencies:
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.25.9':
    dependencies:
      '@babel/types': 7.27.0

  '@babel/helper-plugin-utils@7.26.5': {}

  '@babel/helper-remap-async-to-generator@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-wrap-function': 7.25.9
      '@babel/traverse': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-replace-supers@7.26.5(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/traverse': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    dependencies:
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.25.9': {}

  '@babel/helper-validator-identifier@7.25.9': {}

  '@babel/helper-validator-option@7.25.9': {}

  '@babel/helper-wrap-function@7.25.9':
    dependencies:
      '@babel/template': 7.27.0
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helpers@7.27.0':
    dependencies:
      '@babel/template': 7.27.0
      '@babel/types': 7.27.0

  '@babel/parser@7.27.0':
    dependencies:
      '@babel/types': 7.27.0

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/traverse': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/plugin-transform-optional-chaining': 7.25.9(@babel/core@7.26.10)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/traverse': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10

  '@babel/plugin-syntax-import-assertions@7.26.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-import-attributes@7.26.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-arrow-functions@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-async-generator-functions@7.26.8(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-remap-async-to-generator': 7.25.9(@babel/core@7.26.10)
      '@babel/traverse': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-async-to-generator@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-remap-async-to-generator': 7.25.9(@babel/core@7.26.10)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-block-scoped-functions@7.26.5(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-block-scoping@7.27.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-class-properties@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-class-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-class-static-block@7.26.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-class-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-classes@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-replace-supers': 7.26.5(@babel/core@7.26.10)
      '@babel/traverse': 7.27.0
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-computed-properties@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/template': 7.27.0

  '@babel/plugin-transform-destructuring@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-dotall-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-duplicate-keys@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-dynamic-import@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-exponentiation-operator@7.26.3(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-export-namespace-from@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-for-of@7.26.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-function-name@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/traverse': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-json-strings@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-literals@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-logical-assignment-operators@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-member-expression-literals@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-modules-amd@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-commonjs@7.26.3(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-systemjs@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-umd@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-new-target@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-nullish-coalescing-operator@7.26.6(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-numeric-separator@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-object-rest-spread@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/plugin-transform-parameters': 7.25.9(@babel/core@7.26.10)

  '@babel/plugin-transform-object-super@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-replace-supers': 7.26.5(@babel/core@7.26.10)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-optional-catch-binding@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-optional-chaining@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-parameters@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-private-methods@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-class-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-private-property-in-object@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-create-class-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-property-literals@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-react-jsx-self@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-react-jsx-source@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-regenerator@7.27.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      regenerator-transform: 0.15.2

  '@babel/plugin-transform-regexp-modifiers@7.26.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-reserved-words@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-shorthand-properties@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-spread@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-sticky-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-template-literals@7.26.8(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-typeof-symbol@7.27.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-unicode-escapes@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-unicode-property-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-unicode-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-unicode-sets-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/preset-env@7.26.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/core': 7.26.10
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-validator-option': 7.25.9
      '@babel/plugin-bugfix-firefox-class-in-computed-class-key': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-bugfix-safari-class-field-initializer-scope': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2(@babel/core@7.26.10)
      '@babel/plugin-syntax-import-assertions': 7.26.0(@babel/core@7.26.10)
      '@babel/plugin-syntax-import-attributes': 7.26.0(@babel/core@7.26.10)
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6(@babel/core@7.26.10)
      '@babel/plugin-transform-arrow-functions': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-async-generator-functions': 7.26.8(@babel/core@7.26.10)
      '@babel/plugin-transform-async-to-generator': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-block-scoped-functions': 7.26.5(@babel/core@7.26.10)
      '@babel/plugin-transform-block-scoping': 7.27.0(@babel/core@7.26.10)
      '@babel/plugin-transform-class-properties': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-class-static-block': 7.26.0(@babel/core@7.26.10)
      '@babel/plugin-transform-classes': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-computed-properties': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-destructuring': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-dotall-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-duplicate-keys': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-duplicate-named-capturing-groups-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-dynamic-import': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-exponentiation-operator': 7.26.3(@babel/core@7.26.10)
      '@babel/plugin-transform-export-namespace-from': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-for-of': 7.26.9(@babel/core@7.26.10)
      '@babel/plugin-transform-function-name': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-json-strings': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-literals': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-logical-assignment-operators': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-member-expression-literals': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-modules-amd': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-modules-commonjs': 7.26.3(@babel/core@7.26.10)
      '@babel/plugin-transform-modules-systemjs': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-modules-umd': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-new-target': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-nullish-coalescing-operator': 7.26.6(@babel/core@7.26.10)
      '@babel/plugin-transform-numeric-separator': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-object-rest-spread': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-object-super': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-optional-catch-binding': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-optional-chaining': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-parameters': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-private-methods': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-private-property-in-object': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-property-literals': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-regenerator': 7.27.0(@babel/core@7.26.10)
      '@babel/plugin-transform-regexp-modifiers': 7.26.0(@babel/core@7.26.10)
      '@babel/plugin-transform-reserved-words': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-shorthand-properties': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-spread': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-sticky-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-template-literals': 7.26.8(@babel/core@7.26.10)
      '@babel/plugin-transform-typeof-symbol': 7.27.0(@babel/core@7.26.10)
      '@babel/plugin-transform-unicode-escapes': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-unicode-property-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-unicode-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-unicode-sets-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/preset-modules': 0.1.6-no-external-plugins(@babel/core@7.26.10)
      babel-plugin-polyfill-corejs2: 0.4.13(@babel/core@7.26.10)
      babel-plugin-polyfill-corejs3: 0.11.1(@babel/core@7.26.10)
      babel-plugin-polyfill-regenerator: 0.6.4(@babel/core@7.26.10)
      core-js-compat: 3.41.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/types': 7.27.0
      esutils: 2.0.3

  '@babel/runtime@7.27.0':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/template@7.27.0':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.27.0
      '@babel/types': 7.27.0

  '@babel/traverse@7.27.0':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.27.0
      '@babel/parser': 7.27.0
      '@babel/template': 7.27.0
      '@babel/types': 7.27.0
      debug: 4.4.0
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.27.0':
    dependencies:
      '@babel/helper-string-parser': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9

  '@commitlint/cli@19.8.0(@types/node@22.14.0)(typescript@5.8.3)':
    dependencies:
      '@commitlint/format': 19.8.0
      '@commitlint/lint': 19.8.0
      '@commitlint/load': 19.8.0(@types/node@22.14.0)(typescript@5.8.3)
      '@commitlint/read': 19.8.0
      '@commitlint/types': 19.8.0
      tinyexec: 0.3.2
      yargs: 17.7.2
    transitivePeerDependencies:
      - '@types/node'
      - typescript

  '@commitlint/config-conventional@19.8.0':
    dependencies:
      '@commitlint/types': 19.8.0
      conventional-changelog-conventionalcommits: 7.0.2

  '@commitlint/config-validator@19.8.0':
    dependencies:
      '@commitlint/types': 19.8.0
      ajv: 8.17.1

  '@commitlint/ensure@19.8.0':
    dependencies:
      '@commitlint/types': 19.8.0
      lodash.camelcase: 4.3.0
      lodash.kebabcase: 4.1.1
      lodash.snakecase: 4.1.1
      lodash.startcase: 4.4.0
      lodash.upperfirst: 4.3.1

  '@commitlint/execute-rule@19.8.0': {}

  '@commitlint/format@19.8.0':
    dependencies:
      '@commitlint/types': 19.8.0
      chalk: 5.4.1

  '@commitlint/is-ignored@19.8.0':
    dependencies:
      '@commitlint/types': 19.8.0
      semver: 7.7.1

  '@commitlint/lint@19.8.0':
    dependencies:
      '@commitlint/is-ignored': 19.8.0
      '@commitlint/parse': 19.8.0
      '@commitlint/rules': 19.8.0
      '@commitlint/types': 19.8.0

  '@commitlint/load@19.8.0(@types/node@22.14.0)(typescript@5.8.3)':
    dependencies:
      '@commitlint/config-validator': 19.8.0
      '@commitlint/execute-rule': 19.8.0
      '@commitlint/resolve-extends': 19.8.0
      '@commitlint/types': 19.8.0
      chalk: 5.4.1
      cosmiconfig: 9.0.0(typescript@5.8.3)
      cosmiconfig-typescript-loader: 6.1.0(@types/node@22.14.0)(cosmiconfig@9.0.0(typescript@5.8.3))(typescript@5.8.3)
      lodash.isplainobject: 4.0.6
      lodash.merge: 4.6.2
      lodash.uniq: 4.5.0
    transitivePeerDependencies:
      - '@types/node'
      - typescript

  '@commitlint/message@19.8.0': {}

  '@commitlint/parse@19.8.0':
    dependencies:
      '@commitlint/types': 19.8.0
      conventional-changelog-angular: 7.0.0
      conventional-commits-parser: 5.0.0

  '@commitlint/read@19.8.0':
    dependencies:
      '@commitlint/top-level': 19.8.0
      '@commitlint/types': 19.8.0
      git-raw-commits: 4.0.0
      minimist: 1.2.8
      tinyexec: 0.3.2

  '@commitlint/resolve-extends@19.8.0':
    dependencies:
      '@commitlint/config-validator': 19.8.0
      '@commitlint/types': 19.8.0
      global-directory: 4.0.1
      import-meta-resolve: 4.1.0
      lodash.mergewith: 4.6.2
      resolve-from: 5.0.0

  '@commitlint/rules@19.8.0':
    dependencies:
      '@commitlint/ensure': 19.8.0
      '@commitlint/message': 19.8.0
      '@commitlint/to-lines': 19.8.0
      '@commitlint/types': 19.8.0

  '@commitlint/to-lines@19.8.0': {}

  '@commitlint/top-level@19.8.0':
    dependencies:
      find-up: 7.0.0

  '@commitlint/types@19.8.0':
    dependencies:
      '@types/conventional-commits-parser': 5.0.1
      chalk: 5.4.1

  '@csstools/color-helpers@5.0.2': {}

  '@csstools/css-calc@2.1.2(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    dependencies:
      '@csstools/css-parser-algorithms': 3.0.4(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-tokenizer': 3.0.3

  '@csstools/css-color-parser@3.0.8(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    dependencies:
      '@csstools/color-helpers': 5.0.2
      '@csstools/css-calc': 2.1.2(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-parser-algorithms': 3.0.4(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-tokenizer': 3.0.3

  '@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3)':
    dependencies:
      '@csstools/css-tokenizer': 3.0.3

  '@csstools/css-tokenizer@3.0.3': {}

  '@esbuild/aix-ppc64@0.25.2':
    optional: true

  '@esbuild/android-arm64@0.25.2':
    optional: true

  '@esbuild/android-arm@0.25.2':
    optional: true

  '@esbuild/android-x64@0.25.2':
    optional: true

  '@esbuild/darwin-arm64@0.25.2':
    optional: true

  '@esbuild/darwin-x64@0.25.2':
    optional: true

  '@esbuild/freebsd-arm64@0.25.2':
    optional: true

  '@esbuild/freebsd-x64@0.25.2':
    optional: true

  '@esbuild/linux-arm64@0.25.2':
    optional: true

  '@esbuild/linux-arm@0.25.2':
    optional: true

  '@esbuild/linux-ia32@0.25.2':
    optional: true

  '@esbuild/linux-loong64@0.25.2':
    optional: true

  '@esbuild/linux-mips64el@0.25.2':
    optional: true

  '@esbuild/linux-ppc64@0.25.2':
    optional: true

  '@esbuild/linux-riscv64@0.25.2':
    optional: true

  '@esbuild/linux-s390x@0.25.2':
    optional: true

  '@esbuild/linux-x64@0.25.2':
    optional: true

  '@esbuild/netbsd-arm64@0.25.2':
    optional: true

  '@esbuild/netbsd-x64@0.25.2':
    optional: true

  '@esbuild/openbsd-arm64@0.25.2':
    optional: true

  '@esbuild/openbsd-x64@0.25.2':
    optional: true

  '@esbuild/sunos-x64@0.25.2':
    optional: true

  '@esbuild/win32-arm64@0.25.2':
    optional: true

  '@esbuild/win32-ia32@0.25.2':
    optional: true

  '@esbuild/win32-x64@0.25.2':
    optional: true

  '@eslint-community/eslint-utils@4.5.1(eslint@9.24.0(jiti@2.4.2))':
    dependencies:
      eslint: 9.24.0(jiti@2.4.2)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/config-array@0.20.0':
    dependencies:
      '@eslint/object-schema': 2.1.6
      debug: 4.4.0
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/config-helpers@0.2.1': {}

  '@eslint/core@0.12.0':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/core@0.13.0':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.3.1':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.0
      espree: 10.3.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.24.0': {}

  '@eslint/object-schema@2.1.6': {}

  '@eslint/plugin-kit@0.2.8':
    dependencies:
      '@eslint/core': 0.13.0
      levn: 0.4.1

  '@essentials/memoize-one@1.1.0': {}

  '@essentials/one-key-map@1.2.0': {}

  '@essentials/raf@1.2.0': {}

  '@essentials/request-timeout@1.3.0':
    dependencies:
      '@essentials/raf': 1.2.0

  '@floating-ui/core@1.6.9':
    dependencies:
      '@floating-ui/utils': 0.2.9

  '@floating-ui/dom@1.6.13':
    dependencies:
      '@floating-ui/core': 1.6.9
      '@floating-ui/utils': 0.2.9

  '@floating-ui/react-dom@2.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@floating-ui/dom': 1.6.13
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@floating-ui/utils@0.2.9': {}

  '@grafana/faro-core@1.15.0':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/otlp-transformer': 0.57.2(@opentelemetry/api@1.9.0)

  '@grafana/faro-web-sdk@1.15.0':
    dependencies:
      '@grafana/faro-core': 1.15.0
      ua-parser-js: 1.0.40
      web-vitals: 4.2.4

  '@grafana/faro-web-tracing@1.15.0':
    dependencies:
      '@grafana/faro-web-sdk': 1.15.0
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.0(@opentelemetry/api@1.9.0)
      '@opentelemetry/exporter-trace-otlp-http': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-fetch': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-xml-http-request': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/otlp-transformer': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-web': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.32.0
    transitivePeerDependencies:
      - supports-color

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.2': {}

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/source-map@0.3.6':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@opentelemetry/api-logs@0.57.2':
    dependencies:
      '@opentelemetry/api': 1.9.0

  '@opentelemetry/api@1.9.0': {}

  '@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/semantic-conventions': 1.28.0

  '@opentelemetry/core@2.0.0(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/semantic-conventions': 1.32.0

  '@opentelemetry/exporter-trace-otlp-http@0.57.2(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/otlp-exporter-base': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/otlp-transformer': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-base': 1.30.1(@opentelemetry/api@1.9.0)

  '@opentelemetry/instrumentation-fetch@0.57.2(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-web': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.28.0
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-xml-http-request@0.57.2(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-web': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.28.0
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/api-logs': 0.57.2
      '@types/shimmer': 1.2.0
      import-in-the-middle: 1.13.1
      require-in-the-middle: 7.5.2
      semver: 7.7.1
      shimmer: 1.2.1
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/otlp-exporter-base@0.57.2(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/otlp-transformer': 0.57.2(@opentelemetry/api@1.9.0)

  '@opentelemetry/otlp-transformer@0.57.2(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/api-logs': 0.57.2
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-logs': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-metrics': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-base': 1.30.1(@opentelemetry/api@1.9.0)
      protobufjs: 7.4.0

  '@opentelemetry/resources@1.30.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.28.0

  '@opentelemetry/sdk-logs@0.57.2(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/api-logs': 0.57.2
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 1.30.1(@opentelemetry/api@1.9.0)

  '@opentelemetry/sdk-metrics@1.30.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 1.30.1(@opentelemetry/api@1.9.0)

  '@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.28.0

  '@opentelemetry/sdk-trace-web@1.30.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-base': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.28.0

  '@opentelemetry/semantic-conventions@1.28.0': {}

  '@opentelemetry/semantic-conventions@1.32.0': {}

  '@playwright/test@1.51.1':
    dependencies:
      playwright: 1.51.1

  '@protobufjs/aspromise@1.1.2': {}

  '@protobufjs/base64@1.1.2': {}

  '@protobufjs/codegen@2.0.4': {}

  '@protobufjs/eventemitter@1.1.0': {}

  '@protobufjs/fetch@1.1.0':
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/inquire': 1.1.0

  '@protobufjs/float@1.0.2': {}

  '@protobufjs/inquire@1.1.0': {}

  '@protobufjs/path@1.1.2': {}

  '@protobufjs/pool@1.1.0': {}

  '@protobufjs/utf8@1.1.0': {}

  '@radix-ui/number@1.1.1': {}

  '@radix-ui/primitive@1.1.2': {}

  '@radix-ui/react-accordion@1.2.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collapsible': 1.1.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-collection': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-arrow@1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-avatar@1.1.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-checkbox@1.3.2(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-previous': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-size': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-collapsible@1.1.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-presence': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-collection@1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.0(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-collection@1.1.6(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.2(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.2(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-context@1.1.2(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-dialog@1.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-dismissable-layer': 1.1.6(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-focus-guards': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-focus-scope': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-portal': 1.1.5(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-presence': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.0(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      aria-hidden: 1.2.4
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-remove-scroll: 2.6.3(@types/react@19.1.0)(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-direction@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-dismissable-layer@1.1.6(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-escape-keydown': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-dropdown-menu@2.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-menu': 2.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-focus-scope@1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-id@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-label@2.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-menu@2.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-dismissable-layer': 1.1.6(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-focus-guards': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-focus-scope': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-popper': 1.2.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-portal': 1.1.5(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-presence': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-roving-focus': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.0(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      aria-hidden: 1.2.4
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-remove-scroll: 2.6.3(@types/react@19.1.0)(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-popper@1.2.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@floating-ui/react-dom': 2.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-arrow': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-rect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-size': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/rect': 1.1.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-portal@1.1.5(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-presence@1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-primitive@2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-slot': 1.2.0(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-primitive@2.1.2(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-slot': 1.2.2(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-radio-group@1.3.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-roving-focus': 1.1.10(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-previous': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-size': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-roving-focus@1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-roving-focus@1.1.9(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.6(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.2(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-select@2.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/number': 1.1.1
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-dismissable-layer': 1.1.6(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-focus-guards': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-focus-scope': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-popper': 1.2.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-portal': 1.1.5(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.0(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-previous': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-visually-hidden': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      aria-hidden: 1.2.4
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-remove-scroll: 2.6.3(@types/react@19.1.0)(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-separator@1.1.6(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.2(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-slider@1.3.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/number': 1.1.1
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.6(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.2(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-previous': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-size': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-slot@1.2.0(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-slot@1.2.2(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-slot@1.2.3(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-tabs@1.1.11(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.2(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-roving-focus': 1.1.9(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-tooltip@1.2.0(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-dismissable-layer': 1.1.6(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-popper': 1.2.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-portal': 1.1.5(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-presence': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.0(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-visually-hidden': 1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-use-controllable-state@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-effect-event': 0.0.2(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      '@radix-ui/rect': 1.1.1
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.0)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  '@radix-ui/react-visually-hidden@1.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@radix-ui/rect@1.1.1': {}

  '@react-hook/debounce@3.0.0(react@19.1.0)':
    dependencies:
      '@react-hook/latest': 1.0.3(react@19.1.0)
      react: 19.1.0

  '@react-hook/event@1.2.6(react@19.1.0)':
    dependencies:
      react: 19.1.0

  '@react-hook/latest@1.0.3(react@19.1.0)':
    dependencies:
      react: 19.1.0

  '@react-hook/passive-layout-effect@1.2.1(react@19.1.0)':
    dependencies:
      react: 19.1.0

  '@react-hook/throttle@2.2.0(react@19.1.0)':
    dependencies:
      '@react-hook/latest': 1.0.3(react@19.1.0)
      react: 19.1.0

  '@react-hook/window-scroll@1.3.0(react@19.1.0)':
    dependencies:
      '@react-hook/event': 1.2.6(react@19.1.0)
      '@react-hook/throttle': 2.2.0(react@19.1.0)
      react: 19.1.0

  '@react-hook/window-size@3.1.1(react@19.1.0)':
    dependencies:
      '@react-hook/debounce': 3.0.0(react@19.1.0)
      '@react-hook/event': 1.2.6(react@19.1.0)
      '@react-hook/throttle': 2.2.0(react@19.1.0)
      react: 19.1.0

  '@rollup/plugin-babel@5.3.1(@babel/core@7.26.10)(@types/babel__core@7.20.5)(rollup@2.79.2)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-imports': 7.25.9
      '@rollup/pluginutils': 3.1.0(rollup@2.79.2)
      rollup: 2.79.2
    optionalDependencies:
      '@types/babel__core': 7.20.5
    transitivePeerDependencies:
      - supports-color

  '@rollup/plugin-node-resolve@15.3.1(rollup@2.79.2)':
    dependencies:
      '@rollup/pluginutils': 5.1.4(rollup@2.79.2)
      '@types/resolve': 1.20.2
      deepmerge: 4.3.1
      is-module: 1.0.0
      resolve: 1.22.10
    optionalDependencies:
      rollup: 2.79.2

  '@rollup/plugin-replace@2.4.2(rollup@2.79.2)':
    dependencies:
      '@rollup/pluginutils': 3.1.0(rollup@2.79.2)
      magic-string: 0.25.9
      rollup: 2.79.2

  '@rollup/plugin-terser@0.4.4(rollup@2.79.2)':
    dependencies:
      serialize-javascript: 6.0.2
      smob: 1.5.0
      terser: 5.39.0
    optionalDependencies:
      rollup: 2.79.2

  '@rollup/pluginutils@3.1.0(rollup@2.79.2)':
    dependencies:
      '@types/estree': 0.0.39
      estree-walker: 1.0.1
      picomatch: 2.3.1
      rollup: 2.79.2

  '@rollup/pluginutils@5.1.4(rollup@2.79.2)':
    dependencies:
      '@types/estree': 1.0.7
      estree-walker: 2.0.2
      picomatch: 4.0.2
    optionalDependencies:
      rollup: 2.79.2

  '@rollup/rollup-android-arm-eabi@4.39.0':
    optional: true

  '@rollup/rollup-android-arm64@4.39.0':
    optional: true

  '@rollup/rollup-darwin-arm64@4.39.0':
    optional: true

  '@rollup/rollup-darwin-x64@4.39.0':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.39.0':
    optional: true

  '@rollup/rollup-freebsd-x64@4.39.0':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.39.0':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.39.0':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.39.0':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.39.0':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.39.0':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.39.0':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.39.0':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.39.0':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.39.0':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.39.0':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.39.0':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.39.0':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.39.0':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.39.0':
    optional: true

  '@snap/design-system@1.2.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(immer@10.1.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(tailwindcss@4.1.3)':
    dependencies:
      '@radix-ui/react-accordion': 1.2.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-avatar': 1.1.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-checkbox': 1.3.2(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-collapsible': 1.1.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-dialog': 1.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-label': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-select': 2.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-separator': 1.1.6(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slider': 1.3.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.0)(react@19.1.0)
      '@radix-ui/react-tabs': 1.1.11(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@react-hook/window-size': 3.1.1(react@19.1.0)
      '@tailwindcss/postcss': 4.1.3
      class-variance-authority: 0.7.1
      clsx: 2.1.1
      framer-motion: 12.12.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      lucide-react: 0.476.0(react@19.1.0)
      masonic: 4.0.1(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-layout-masonry: 1.2.0(react@19.1.0)
      react-photo-album: 3.1.0(@types/react@19.1.0)(react@19.1.0)
      react-responsive-masonry: 2.7.1
      tailwind-merge: 3.2.0
      tailwindcss-animate: 1.0.7(tailwindcss@4.1.3)
      zustand: 5.0.3(@types/react@19.1.0)(immer@10.1.1)(react@19.1.0)
    transitivePeerDependencies:
      - '@emotion/is-prop-valid'
      - '@types/react'
      - '@types/react-dom'
      - immer
      - tailwindcss
      - use-sync-external-store

  '@surma/rollup-plugin-off-main-thread@2.2.3':
    dependencies:
      ejs: 3.1.10
      json5: 2.2.3
      magic-string: 0.25.9
      string.prototype.matchall: 4.0.12

  '@tailwindcss/node@4.1.3':
    dependencies:
      enhanced-resolve: 5.18.1
      jiti: 2.4.2
      lightningcss: 1.29.2
      tailwindcss: 4.1.3

  '@tailwindcss/oxide-android-arm64@4.1.3':
    optional: true

  '@tailwindcss/oxide-darwin-arm64@4.1.3':
    optional: true

  '@tailwindcss/oxide-darwin-x64@4.1.3':
    optional: true

  '@tailwindcss/oxide-freebsd-x64@4.1.3':
    optional: true

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.3':
    optional: true

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.3':
    optional: true

  '@tailwindcss/oxide-linux-arm64-musl@4.1.3':
    optional: true

  '@tailwindcss/oxide-linux-x64-gnu@4.1.3':
    optional: true

  '@tailwindcss/oxide-linux-x64-musl@4.1.3':
    optional: true

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.3':
    optional: true

  '@tailwindcss/oxide-win32-x64-msvc@4.1.3':
    optional: true

  '@tailwindcss/oxide@4.1.3':
    optionalDependencies:
      '@tailwindcss/oxide-android-arm64': 4.1.3
      '@tailwindcss/oxide-darwin-arm64': 4.1.3
      '@tailwindcss/oxide-darwin-x64': 4.1.3
      '@tailwindcss/oxide-freebsd-x64': 4.1.3
      '@tailwindcss/oxide-linux-arm-gnueabihf': 4.1.3
      '@tailwindcss/oxide-linux-arm64-gnu': 4.1.3
      '@tailwindcss/oxide-linux-arm64-musl': 4.1.3
      '@tailwindcss/oxide-linux-x64-gnu': 4.1.3
      '@tailwindcss/oxide-linux-x64-musl': 4.1.3
      '@tailwindcss/oxide-win32-arm64-msvc': 4.1.3
      '@tailwindcss/oxide-win32-x64-msvc': 4.1.3

  '@tailwindcss/postcss@4.1.3':
    dependencies:
      '@alloc/quick-lru': 5.2.0
      '@tailwindcss/node': 4.1.3
      '@tailwindcss/oxide': 4.1.3
      postcss: 8.5.3
      tailwindcss: 4.1.3

  '@tailwindcss/vite@4.1.3(vite@6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1))':
    dependencies:
      '@tailwindcss/node': 4.1.3
      '@tailwindcss/oxide': 4.1.3
      tailwindcss: 4.1.3
      vite: 6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1)

  '@tanstack/query-core@5.72.2': {}

  '@tanstack/query-devtools@5.72.2': {}

  '@tanstack/react-query-devtools@5.72.2(@tanstack/react-query@5.72.2(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@tanstack/query-devtools': 5.72.2
      '@tanstack/react-query': 5.72.2(react@19.1.0)
      react: 19.1.0

  '@tanstack/react-query@5.72.2(react@19.1.0)':
    dependencies:
      '@tanstack/query-core': 5.72.2
      react: 19.1.0

  '@testing-library/dom@10.4.0':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/runtime': 7.27.0
      '@types/aria-query': 5.0.4
      aria-query: 5.3.0
      chalk: 4.1.2
      dom-accessibility-api: 0.5.16
      lz-string: 1.5.0
      pretty-format: 27.5.1

  '@testing-library/jest-dom@6.6.3':
    dependencies:
      '@adobe/css-tools': 4.4.2
      aria-query: 5.3.2
      chalk: 3.0.0
      css.escape: 1.5.1
      dom-accessibility-api: 0.6.3
      lodash: 4.17.21
      redent: 3.0.0

  '@testing-library/react@16.3.0(@testing-library/dom@10.4.0)(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.0
      '@testing-library/dom': 10.4.0
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0
      '@types/react-dom': 19.1.2(@types/react@19.1.0)

  '@testing-library/user-event@14.6.1(@testing-library/dom@10.4.0)':
    dependencies:
      '@testing-library/dom': 10.4.0

  '@types/aria-query@5.0.4': {}

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.27.0
      '@babel/types': 7.27.0
      '@types/babel__generator': 7.27.0
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.7

  '@types/babel__generator@7.27.0':
    dependencies:
      '@babel/types': 7.27.0

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.27.0
      '@babel/types': 7.27.0

  '@types/babel__traverse@7.20.7':
    dependencies:
      '@babel/types': 7.27.0

  '@types/conventional-commits-parser@5.0.1':
    dependencies:
      '@types/node': 22.14.0

  '@types/cookie@0.6.0': {}

  '@types/estree@0.0.39': {}

  '@types/estree@1.0.7': {}

  '@types/inputmask@5.0.7': {}

  '@types/js-cookie@3.0.6': {}

  '@types/json-schema@7.0.15': {}

  '@types/node@22.14.0':
    dependencies:
      undici-types: 6.21.0

  '@types/react-dom@19.1.2(@types/react@19.1.0)':
    dependencies:
      '@types/react': 19.1.0

  '@types/react@19.1.0':
    dependencies:
      csstype: 3.1.3

  '@types/resolve@1.20.2': {}

  '@types/shimmer@1.2.0': {}

  '@types/trusted-types@2.0.7': {}

  '@typescript-eslint/eslint-plugin@8.29.1(@typescript-eslint/parser@8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/scope-manager': 8.29.1
      '@typescript-eslint/type-utils': 8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/utils': 8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.29.1
      eslint: 9.24.0(jiti@2.4.2)
      graphemer: 1.4.0
      ignore: 5.3.2
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.29.1
      '@typescript-eslint/types': 8.29.1
      '@typescript-eslint/typescript-estree': 8.29.1(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.29.1
      debug: 4.4.0
      eslint: 9.24.0(jiti@2.4.2)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@8.29.1':
    dependencies:
      '@typescript-eslint/types': 8.29.1
      '@typescript-eslint/visitor-keys': 8.29.1

  '@typescript-eslint/type-utils@8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 8.29.1(typescript@5.8.3)
      '@typescript-eslint/utils': 8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)
      debug: 4.4.0
      eslint: 9.24.0(jiti@2.4.2)
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@8.29.1': {}

  '@typescript-eslint/typescript-estree@8.29.1(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/types': 8.29.1
      '@typescript-eslint/visitor-keys': 8.29.1
      debug: 4.4.0
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.1
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.5.1(eslint@9.24.0(jiti@2.4.2))
      '@typescript-eslint/scope-manager': 8.29.1
      '@typescript-eslint/types': 8.29.1
      '@typescript-eslint/typescript-estree': 8.29.1(typescript@5.8.3)
      eslint: 9.24.0(jiti@2.4.2)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@8.29.1':
    dependencies:
      '@typescript-eslint/types': 8.29.1
      eslint-visitor-keys: 4.2.0

  '@vitejs/plugin-react@4.3.4(vite@6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1))':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/plugin-transform-react-jsx-self': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-react-jsx-source': 7.25.9(@babel/core@7.26.10)
      '@types/babel__core': 7.20.5
      react-refresh: 0.14.2
      vite: 6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1)
    transitivePeerDependencies:
      - supports-color

  '@vitest/expect@3.1.1':
    dependencies:
      '@vitest/spy': 3.1.1
      '@vitest/utils': 3.1.1
      chai: 5.2.0
      tinyrainbow: 2.0.0

  '@vitest/mocker@3.1.1(vite@6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1))':
    dependencies:
      '@vitest/spy': 3.1.1
      estree-walker: 3.0.3
      magic-string: 0.30.17
    optionalDependencies:
      vite: 6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1)

  '@vitest/pretty-format@3.1.1':
    dependencies:
      tinyrainbow: 2.0.0

  '@vitest/runner@3.1.1':
    dependencies:
      '@vitest/utils': 3.1.1
      pathe: 2.0.3

  '@vitest/snapshot@3.1.1':
    dependencies:
      '@vitest/pretty-format': 3.1.1
      magic-string: 0.30.17
      pathe: 2.0.3

  '@vitest/spy@3.1.1':
    dependencies:
      tinyspy: 3.0.2

  '@vitest/utils@3.1.1':
    dependencies:
      '@vitest/pretty-format': 3.1.1
      loupe: 3.1.3
      tinyrainbow: 2.0.0

  JSONStream@1.3.5:
    dependencies:
      jsonparse: 1.3.1
      through: 2.3.8

  acorn-import-attributes@1.9.5(acorn@8.14.1):
    dependencies:
      acorn: 8.14.1

  acorn-jsx@5.3.2(acorn@8.14.1):
    dependencies:
      acorn: 8.14.1

  acorn@8.14.1: {}

  agent-base@7.1.3: {}

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-escapes@7.0.0:
    dependencies:
      environment: 1.1.0

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  argparse@2.0.1: {}

  aria-hidden@1.2.4:
    dependencies:
      tslib: 2.8.1

  aria-query@5.3.0:
    dependencies:
      dequal: 2.0.3

  aria-query@5.3.2: {}

  array-buffer-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      is-array-buffer: 3.0.5

  array-ify@1.0.0: {}

  array-includes@3.1.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      is-string: 1.1.1

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0

  array.prototype.flat@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-shim-unscopables: 1.1.0

  array.prototype.flatmap@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-shim-unscopables: 1.1.0

  array.prototype.tosorted@1.1.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-shim-unscopables: 1.1.0

  arraybuffer.prototype.slice@1.0.4:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5

  assertion-error@2.0.1: {}

  async-function@1.0.0: {}

  async@3.2.6: {}

  asynckit@0.4.0: {}

  at-least-node@1.0.0: {}

  autoprefixer@10.4.21(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      caniuse-lite: 1.0.30001713
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.1
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.1.0

  axios@1.8.4:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.2
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-plugin-polyfill-corejs2@0.4.13(@babel/core@7.26.10):
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/core': 7.26.10
      '@babel/helper-define-polyfill-provider': 0.6.4(@babel/core@7.26.10)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.11.1(@babel/core@7.26.10):
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-define-polyfill-provider': 0.6.4(@babel/core@7.26.10)
      core-js-compat: 3.41.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.6.4(@babel/core@7.26.10):
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-define-polyfill-provider': 0.6.4(@babel/core@7.26.10)
    transitivePeerDependencies:
      - supports-color

  balanced-match@1.0.2: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.24.4:
    dependencies:
      caniuse-lite: 1.0.30001713
      electron-to-chromium: 1.5.136
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.24.4)

  buffer-from@1.1.2: {}

  cac@6.7.14: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  callsites@3.1.0: {}

  caniuse-lite@1.0.30001713: {}

  chai@5.2.0:
    dependencies:
      assertion-error: 2.0.1
      check-error: 2.1.1
      deep-eql: 5.0.2
      loupe: 3.1.3
      pathval: 2.0.0

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@3.0.0:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@5.4.1: {}

  check-error@2.1.1: {}

  cjs-module-lexer@1.4.3: {}

  class-variance-authority@0.7.1:
    dependencies:
      clsx: 2.1.1

  cli-cursor@5.0.0:
    dependencies:
      restore-cursor: 5.1.0

  cli-truncate@4.0.0:
    dependencies:
      slice-ansi: 5.0.0
      string-width: 7.2.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clsx@2.1.1: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  colorette@2.0.20: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@13.1.0: {}

  commander@2.20.3: {}

  common-tags@1.8.2: {}

  compare-func@2.0.0:
    dependencies:
      array-ify: 1.0.0
      dot-prop: 5.3.0

  concat-map@0.0.1: {}

  conventional-changelog-angular@7.0.0:
    dependencies:
      compare-func: 2.0.0

  conventional-changelog-conventionalcommits@7.0.2:
    dependencies:
      compare-func: 2.0.0

  conventional-commits-parser@5.0.0:
    dependencies:
      JSONStream: 1.3.5
      is-text-path: 2.0.0
      meow: 12.1.1
      split2: 4.2.0

  convert-source-map@2.0.0: {}

  cookie@1.0.2: {}

  core-js-compat@3.41.0:
    dependencies:
      browserslist: 4.24.4

  cosmiconfig-typescript-loader@6.1.0(@types/node@22.14.0)(cosmiconfig@9.0.0(typescript@5.8.3))(typescript@5.8.3):
    dependencies:
      '@types/node': 22.14.0
      cosmiconfig: 9.0.0(typescript@5.8.3)
      jiti: 2.4.2
      typescript: 5.8.3

  cosmiconfig@9.0.0(typescript@5.8.3):
    dependencies:
      env-paths: 2.2.1
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      parse-json: 5.2.0
    optionalDependencies:
      typescript: 5.8.3

  cross-env@7.0.3:
    dependencies:
      cross-spawn: 7.0.6

  cross-spawn@6.0.6:
    dependencies:
      nice-try: 1.0.5
      path-key: 2.0.1
      semver: 5.7.2
      shebang-command: 1.2.0
      which: 1.3.1

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypto-random-string@2.0.0: {}

  css.escape@1.5.1: {}

  cssstyle@4.3.0:
    dependencies:
      '@asamuzakjp/css-color': 3.1.1
      rrweb-cssom: 0.8.0

  csstype@3.1.3: {}

  dargs@8.1.0: {}

  data-urls@5.0.0:
    dependencies:
      whatwg-mimetype: 4.0.0
      whatwg-url: 14.2.0

  data-view-buffer@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-offset@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  debug@4.4.0:
    dependencies:
      ms: 2.1.3

  decimal.js@10.5.0: {}

  deep-eql@5.0.2: {}

  deep-is@0.1.4: {}

  deepmerge@4.3.1: {}

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  delayed-stream@1.0.0: {}

  dequal@2.0.3: {}

  detect-libc@2.0.3: {}

  detect-node-es@1.1.0: {}

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  dom-accessibility-api@0.5.16: {}

  dom-accessibility-api@0.6.3: {}

  dot-prop@5.3.0:
    dependencies:
      is-obj: 2.0.0

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  ejs@3.1.10:
    dependencies:
      jake: 10.9.2

  electron-to-chromium@1.5.136: {}

  emoji-regex@10.4.0: {}

  emoji-regex@8.0.0: {}

  enhanced-resolve@5.18.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1

  entities@4.5.0: {}

  env-paths@2.2.1: {}

  environment@1.1.0: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-abstract@1.23.9:
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-regex: 1.2.1
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.4
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.19

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-iterator-helpers@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-set-tostringtag: 2.1.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      iterator.prototype: 1.1.5
      safe-array-concat: 1.1.3

  es-module-lexer@1.6.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.1.0:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.3.0:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1

  esbuild@0.25.2:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.2
      '@esbuild/android-arm': 0.25.2
      '@esbuild/android-arm64': 0.25.2
      '@esbuild/android-x64': 0.25.2
      '@esbuild/darwin-arm64': 0.25.2
      '@esbuild/darwin-x64': 0.25.2
      '@esbuild/freebsd-arm64': 0.25.2
      '@esbuild/freebsd-x64': 0.25.2
      '@esbuild/linux-arm': 0.25.2
      '@esbuild/linux-arm64': 0.25.2
      '@esbuild/linux-ia32': 0.25.2
      '@esbuild/linux-loong64': 0.25.2
      '@esbuild/linux-mips64el': 0.25.2
      '@esbuild/linux-ppc64': 0.25.2
      '@esbuild/linux-riscv64': 0.25.2
      '@esbuild/linux-s390x': 0.25.2
      '@esbuild/linux-x64': 0.25.2
      '@esbuild/netbsd-arm64': 0.25.2
      '@esbuild/netbsd-x64': 0.25.2
      '@esbuild/openbsd-arm64': 0.25.2
      '@esbuild/openbsd-x64': 0.25.2
      '@esbuild/sunos-x64': 0.25.2
      '@esbuild/win32-arm64': 0.25.2
      '@esbuild/win32-ia32': 0.25.2
      '@esbuild/win32-x64': 0.25.2

  escalade@3.2.0: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@4.0.0: {}

  eslint-plugin-react@7.37.5(eslint@9.24.0(jiti@2.4.2)):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.3
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.2.1
      eslint: 9.24.0(jiti@2.4.2)
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.9
      object.fromentries: 2.0.8
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12
      string.prototype.repeat: 1.0.0

  eslint-scope@8.3.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.0: {}

  eslint@9.24.0(jiti@2.4.2):
    dependencies:
      '@eslint-community/eslint-utils': 4.5.1(eslint@9.24.0(jiti@2.4.2))
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.20.0
      '@eslint/config-helpers': 0.2.1
      '@eslint/core': 0.12.0
      '@eslint/eslintrc': 3.3.1
      '@eslint/js': 9.24.0
      '@eslint/plugin-kit': 0.2.8
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.2
      '@types/estree': 1.0.7
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.0
      escape-string-regexp: 4.0.0
      eslint-scope: 8.3.0
      eslint-visitor-keys: 4.2.0
      espree: 10.3.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    optionalDependencies:
      jiti: 2.4.2
    transitivePeerDependencies:
      - supports-color

  espree@10.3.0:
    dependencies:
      acorn: 8.14.1
      acorn-jsx: 5.3.2(acorn@8.14.1)
      eslint-visitor-keys: 4.2.0

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@5.3.0: {}

  estree-walker@1.0.1: {}

  estree-walker@2.0.2: {}

  estree-walker@3.0.3:
    dependencies:
      '@types/estree': 1.0.7

  esutils@2.0.3: {}

  eventemitter3@5.0.1: {}

  execa@8.0.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 8.0.1
      human-signals: 5.0.0
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 4.1.0
      strip-final-newline: 3.0.0

  expect-type@1.2.1: {}

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fdir@6.4.3(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  filelist@1.0.4:
    dependencies:
      minimatch: 5.1.6

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  find-up@7.0.0:
    dependencies:
      locate-path: 7.2.0
      path-exists: 5.0.0
      unicorn-magic: 0.1.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4

  flatted@3.3.3: {}

  follow-redirects@1.15.9: {}

  for-each@0.3.5:
    dependencies:
      is-callable: 1.2.7

  form-data@4.0.2:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35

  fraction.js@4.3.7: {}

  framer-motion@12.12.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      motion-dom: 12.12.1
      motion-utils: 12.12.1
      tslib: 2.8.1
    optionalDependencies:
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  fs-extra@9.1.0:
    dependencies:
      at-least-node: 1.0.0
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs.realpath@1.0.0: {}

  fsevents@2.3.2:
    optional: true

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-east-asian-width@1.3.0: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-nonce@1.0.1: {}

  get-own-enumerable-property-symbols@3.0.2: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@8.0.1: {}

  get-symbol-description@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0

  git-raw-commits@4.0.0:
    dependencies:
      dargs: 8.1.0
      meow: 12.1.1
      split2: 4.2.0

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  global-directory@4.0.1:
    dependencies:
      ini: 4.1.1

  globals@11.12.0: {}

  globals@14.0.0: {}

  globals@15.15.0: {}

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  has-bigints@1.1.0: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-proto@1.2.0:
    dependencies:
      dunder-proto: 1.0.1

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  hosted-git-info@2.8.9: {}

  html-encoding-sniffer@4.0.0:
    dependencies:
      whatwg-encoding: 3.1.1

  http-proxy-agent@7.0.2:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color

  human-signals@5.0.0: {}

  husky@9.1.7: {}

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  idb@7.1.1: {}

  ignore@5.3.2: {}

  immer@10.1.1: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-in-the-middle@1.13.1:
    dependencies:
      acorn: 8.14.1
      acorn-import-attributes: 1.9.5(acorn@8.14.1)
      cjs-module-lexer: 1.4.3
      module-details-from-path: 1.0.3

  import-meta-resolve@4.1.0: {}

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  ini@4.1.1: {}

  inputmask@5.0.9: {}

  internal-slot@1.1.0:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0

  is-array-buffer@3.0.5:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-arrayish@0.2.1: {}

  is-async-function@2.1.1:
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-bigint@1.1.0:
    dependencies:
      has-bigints: 1.1.0

  is-boolean-object@1.2.2:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-callable@1.2.7: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.2:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15

  is-date-object@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-fullwidth-code-point@3.0.0: {}

  is-fullwidth-code-point@4.0.0: {}

  is-fullwidth-code-point@5.0.0:
    dependencies:
      get-east-asian-width: 1.3.0

  is-generator-function@1.1.0:
    dependencies:
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-map@2.0.3: {}

  is-module@1.0.0: {}

  is-number-object@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-obj@1.0.1: {}

  is-obj@2.0.0: {}

  is-potential-custom-element-name@1.0.1: {}

  is-regex@1.2.1:
    dependencies:
      call-bound: 1.0.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  is-regexp@1.0.0: {}

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.4:
    dependencies:
      call-bound: 1.0.4

  is-stream@2.0.1: {}

  is-stream@3.0.0: {}

  is-string@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-symbol@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0

  is-text-path@2.0.0:
    dependencies:
      text-extensions: 2.4.0

  is-typed-array@1.1.15:
    dependencies:
      which-typed-array: 1.1.19

  is-weakmap@2.0.2: {}

  is-weakref@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-weakset@2.0.4:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  iterator.prototype@1.1.5:
    dependencies:
      define-data-property: 1.1.4
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      has-symbols: 1.1.0
      set-function-name: 2.0.2

  jake@10.9.2:
    dependencies:
      async: 3.2.6
      chalk: 4.1.2
      filelist: 1.0.4
      minimatch: 3.1.2

  jiti@2.4.2: {}

  js-cookie@3.0.5: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsdom@26.0.0:
    dependencies:
      cssstyle: 4.3.0
      data-urls: 5.0.0
      decimal.js: 10.5.0
      form-data: 4.0.2
      html-encoding-sniffer: 4.0.0
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.20
      parse5: 7.2.1
      rrweb-cssom: 0.8.0
      saxes: 6.0.0
      symbol-tree: 3.2.4
      tough-cookie: 5.1.2
      w3c-xmlserializer: 5.0.0
      webidl-conversions: 7.0.0
      whatwg-encoding: 3.1.1
      whatwg-mimetype: 4.0.0
      whatwg-url: 14.2.0
      ws: 8.18.1
      xml-name-validator: 5.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsesc@3.0.2: {}

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-better-errors@1.0.2: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-schema@0.4.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@2.2.3: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonparse@1.3.1: {}

  jsonpointer@5.0.1: {}

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.8
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  leven@3.1.0: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lightningcss-darwin-arm64@1.29.2:
    optional: true

  lightningcss-darwin-x64@1.29.2:
    optional: true

  lightningcss-freebsd-x64@1.29.2:
    optional: true

  lightningcss-linux-arm-gnueabihf@1.29.2:
    optional: true

  lightningcss-linux-arm64-gnu@1.29.2:
    optional: true

  lightningcss-linux-arm64-musl@1.29.2:
    optional: true

  lightningcss-linux-x64-gnu@1.29.2:
    optional: true

  lightningcss-linux-x64-musl@1.29.2:
    optional: true

  lightningcss-win32-arm64-msvc@1.29.2:
    optional: true

  lightningcss-win32-x64-msvc@1.29.2:
    optional: true

  lightningcss@1.29.2:
    dependencies:
      detect-libc: 2.0.3
    optionalDependencies:
      lightningcss-darwin-arm64: 1.29.2
      lightningcss-darwin-x64: 1.29.2
      lightningcss-freebsd-x64: 1.29.2
      lightningcss-linux-arm-gnueabihf: 1.29.2
      lightningcss-linux-arm64-gnu: 1.29.2
      lightningcss-linux-arm64-musl: 1.29.2
      lightningcss-linux-x64-gnu: 1.29.2
      lightningcss-linux-x64-musl: 1.29.2
      lightningcss-win32-arm64-msvc: 1.29.2
      lightningcss-win32-x64-msvc: 1.29.2

  lilconfig@3.1.3: {}

  lines-and-columns@1.2.4: {}

  lint-staged@15.5.0:
    dependencies:
      chalk: 5.4.1
      commander: 13.1.0
      debug: 4.4.0
      execa: 8.0.1
      lilconfig: 3.1.3
      listr2: 8.3.2
      micromatch: 4.0.8
      pidtree: 0.6.0
      string-argv: 0.3.2
      yaml: 2.7.1
    transitivePeerDependencies:
      - supports-color

  listr2@8.3.2:
    dependencies:
      cli-truncate: 4.0.0
      colorette: 2.0.20
      eventemitter3: 5.0.1
      log-update: 6.1.0
      rfdc: 1.4.1
      wrap-ansi: 9.0.0

  load-json-file@4.0.0:
    dependencies:
      graceful-fs: 4.2.11
      parse-json: 4.0.0
      pify: 3.0.0
      strip-bom: 3.0.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  locate-path@7.2.0:
    dependencies:
      p-locate: 6.0.0

  lodash.camelcase@4.3.0: {}

  lodash.debounce@4.0.8: {}

  lodash.isplainobject@4.0.6: {}

  lodash.kebabcase@4.1.1: {}

  lodash.merge@4.6.2: {}

  lodash.mergewith@4.6.2: {}

  lodash.snakecase@4.1.1: {}

  lodash.sortby@4.7.0: {}

  lodash.startcase@4.4.0: {}

  lodash.uniq@4.5.0: {}

  lodash.upperfirst@4.3.1: {}

  lodash@4.17.21: {}

  log-update@6.1.0:
    dependencies:
      ansi-escapes: 7.0.0
      cli-cursor: 5.0.0
      slice-ansi: 7.1.0
      strip-ansi: 7.1.0
      wrap-ansi: 9.0.0

  long@5.3.1: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  loupe@3.1.3: {}

  lru-cache@10.4.3: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lucide-react@0.476.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  lz-string@1.5.0: {}

  magic-string@0.25.9:
    dependencies:
      sourcemap-codec: 1.4.8

  magic-string@0.30.17:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  masonic@4.0.1(react@19.1.0):
    dependencies:
      '@essentials/memoize-one': 1.1.0
      '@essentials/one-key-map': 1.2.0
      '@essentials/request-timeout': 1.3.0
      '@react-hook/event': 1.2.6(react@19.1.0)
      '@react-hook/latest': 1.0.3(react@19.1.0)
      '@react-hook/passive-layout-effect': 1.2.1(react@19.1.0)
      '@react-hook/throttle': 2.2.0(react@19.1.0)
      '@react-hook/window-scroll': 1.3.0(react@19.1.0)
      '@react-hook/window-size': 3.1.1(react@19.1.0)
      raf-schd: 4.0.3
      react: 19.1.0
      trie-memoize: 1.2.0

  math-intrinsics@1.1.0: {}

  memorystream@0.3.1: {}

  meow@12.1.1: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mimic-fn@4.0.0: {}

  mimic-function@5.0.1: {}

  min-indent@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  module-details-from-path@1.0.3: {}

  motion-dom@12.12.1:
    dependencies:
      motion-utils: 12.12.1

  motion-utils@12.12.1: {}

  ms@2.1.3: {}

  nanoid@3.3.11: {}

  natural-compare@1.4.0: {}

  next-themes@0.4.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  nice-try@1.0.5: {}

  node-releases@2.0.19: {}

  normalize-package-data@2.5.0:
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.10
      semver: 5.7.2
      validate-npm-package-license: 3.0.4

  normalize-range@0.1.2: {}

  npm-run-all@4.1.5:
    dependencies:
      ansi-styles: 3.2.1
      chalk: 2.4.2
      cross-spawn: 6.0.6
      memorystream: 0.3.1
      minimatch: 3.1.2
      pidtree: 0.3.1
      read-pkg: 3.0.0
      shell-quote: 1.8.2
      string.prototype.padend: 3.1.6

  npm-run-path@5.3.0:
    dependencies:
      path-key: 4.0.0

  nwsapi@2.2.20: {}

  object-assign@4.1.1: {}

  object-inspect@1.13.4: {}

  object-keys@1.1.1: {}

  object.assign@4.1.7:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1

  object.entries@1.1.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1

  object.values@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@6.0.0:
    dependencies:
      mimic-fn: 4.0.0

  onetime@7.0.0:
    dependencies:
      mimic-function: 5.0.1

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  own-keys@1.0.1:
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-limit@4.0.0:
    dependencies:
      yocto-queue: 1.2.1

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-locate@6.0.0:
    dependencies:
      p-limit: 4.0.0

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@4.0.0:
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.26.2
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse5@7.2.1:
    dependencies:
      entities: 4.5.0

  path-exists@4.0.0: {}

  path-exists@5.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@2.0.1: {}

  path-key@3.1.1: {}

  path-key@4.0.0: {}

  path-parse@1.0.7: {}

  path-type@3.0.0:
    dependencies:
      pify: 3.0.0

  pathe@2.0.3: {}

  pathval@2.0.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  pidtree@0.3.1: {}

  pidtree@0.6.0: {}

  pify@3.0.0: {}

  playwright-core@1.51.1: {}

  playwright@1.51.1:
    dependencies:
      playwright-core: 1.51.1
    optionalDependencies:
      fsevents: 2.3.2

  possible-typed-array-names@1.1.0: {}

  postcss-value-parser@4.2.0: {}

  postcss@8.5.3:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prelude-ls@1.2.1: {}

  pretty-bytes@5.6.0: {}

  pretty-bytes@6.1.1: {}

  pretty-format@27.5.1:
    dependencies:
      ansi-regex: 5.0.1
      ansi-styles: 5.2.0
      react-is: 17.0.2

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  protobufjs@7.4.0:
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/base64': 1.1.2
      '@protobufjs/codegen': 2.0.4
      '@protobufjs/eventemitter': 1.1.0
      '@protobufjs/fetch': 1.1.0
      '@protobufjs/float': 1.0.2
      '@protobufjs/inquire': 1.1.0
      '@protobufjs/path': 1.1.2
      '@protobufjs/pool': 1.1.0
      '@protobufjs/utf8': 1.1.0
      '@types/node': 22.14.0
      long: 5.3.1

  proxy-from-env@1.1.0: {}

  punycode@2.3.1: {}

  queue-microtask@1.2.3: {}

  raf-schd@4.0.3: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  react-dom@19.1.0(react@19.1.0):
    dependencies:
      react: 19.1.0
      scheduler: 0.26.0

  react-icons@5.5.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  react-is@16.13.1: {}

  react-is@17.0.2: {}

  react-layout-masonry@1.2.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  react-photo-album@3.1.0(@types/react@19.1.0)(react@19.1.0):
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.0

  react-refresh@0.14.2: {}

  react-remove-scroll-bar@2.3.8(@types/react@19.1.0)(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-style-singleton: 2.2.3(@types/react@19.1.0)(react@19.1.0)
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.0

  react-remove-scroll@2.6.3(@types/react@19.1.0)(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-remove-scroll-bar: 2.3.8(@types/react@19.1.0)(react@19.1.0)
      react-style-singleton: 2.2.3(@types/react@19.1.0)(react@19.1.0)
      tslib: 2.8.1
      use-callback-ref: 1.3.3(@types/react@19.1.0)(react@19.1.0)
      use-sidecar: 1.1.3(@types/react@19.1.0)(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.0

  react-responsive-masonry@2.7.1: {}

  react-router@7.5.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      '@types/cookie': 0.6.0
      cookie: 1.0.2
      react: 19.1.0
      set-cookie-parser: 2.7.1
      turbo-stream: 2.4.0
    optionalDependencies:
      react-dom: 19.1.0(react@19.1.0)

  react-style-singleton@2.2.3(@types/react@19.1.0)(react@19.1.0):
    dependencies:
      get-nonce: 1.0.1
      react: 19.1.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.0

  react-use-websocket@4.13.0: {}

  react@19.1.0: {}

  read-pkg@3.0.0:
    dependencies:
      load-json-file: 4.0.0
      normalize-package-data: 2.5.0
      path-type: 3.0.0

  redent@3.0.0:
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0

  reflect.getprototypeof@1.0.10:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1

  regenerate-unicode-properties@10.2.0:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.14.1: {}

  regenerator-transform@0.15.2:
    dependencies:
      '@babel/runtime': 7.27.0

  regexp.prototype.flags@1.5.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2

  regexpu-core@6.2.0:
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.2.0
      regjsgen: 0.8.0
      regjsparser: 0.12.0
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0

  regjsgen@0.8.0: {}

  regjsparser@0.12.0:
    dependencies:
      jsesc: 3.0.2

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  require-in-the-middle@7.5.2:
    dependencies:
      debug: 4.4.0
      module-details-from-path: 1.0.3
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@5.1.0:
    dependencies:
      onetime: 7.0.0
      signal-exit: 4.1.0

  reusify@1.1.0: {}

  rfdc@1.4.1: {}

  rollup@2.79.2:
    optionalDependencies:
      fsevents: 2.3.3

  rollup@4.39.0:
    dependencies:
      '@types/estree': 1.0.7
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.39.0
      '@rollup/rollup-android-arm64': 4.39.0
      '@rollup/rollup-darwin-arm64': 4.39.0
      '@rollup/rollup-darwin-x64': 4.39.0
      '@rollup/rollup-freebsd-arm64': 4.39.0
      '@rollup/rollup-freebsd-x64': 4.39.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.39.0
      '@rollup/rollup-linux-arm-musleabihf': 4.39.0
      '@rollup/rollup-linux-arm64-gnu': 4.39.0
      '@rollup/rollup-linux-arm64-musl': 4.39.0
      '@rollup/rollup-linux-loongarch64-gnu': 4.39.0
      '@rollup/rollup-linux-powerpc64le-gnu': 4.39.0
      '@rollup/rollup-linux-riscv64-gnu': 4.39.0
      '@rollup/rollup-linux-riscv64-musl': 4.39.0
      '@rollup/rollup-linux-s390x-gnu': 4.39.0
      '@rollup/rollup-linux-x64-gnu': 4.39.0
      '@rollup/rollup-linux-x64-musl': 4.39.0
      '@rollup/rollup-win32-arm64-msvc': 4.39.0
      '@rollup/rollup-win32-ia32-msvc': 4.39.0
      '@rollup/rollup-win32-x64-msvc': 4.39.0
      fsevents: 2.3.3

  rrweb-cssom@0.8.0: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safe-array-concat@1.1.3:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5

  safe-buffer@5.2.1: {}

  safe-push-apply@1.0.0:
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5

  safe-regex-test@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-regex: 1.2.1

  safer-buffer@2.1.2: {}

  saxes@6.0.0:
    dependencies:
      xmlchars: 2.2.0

  scheduler@0.26.0: {}

  semver@5.7.2: {}

  semver@6.3.1: {}

  semver@7.7.1: {}

  serialize-javascript@6.0.2:
    dependencies:
      randombytes: 2.1.0

  set-cookie-parser@2.7.1: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-proto@1.0.0:
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1

  shebang-command@1.2.0:
    dependencies:
      shebang-regex: 1.0.0

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@1.0.0: {}

  shebang-regex@3.0.0: {}

  shell-quote@1.8.2: {}

  shimmer@1.2.1: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  siginfo@2.0.0: {}

  signal-exit@4.1.0: {}

  slice-ansi@5.0.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 4.0.0

  slice-ansi@7.1.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 5.0.0

  smob@1.5.0: {}

  sonner@2.0.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  source-map-js@1.2.1: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  source-map@0.8.0-beta.0:
    dependencies:
      whatwg-url: 7.1.0

  sourcemap-codec@1.4.8: {}

  spdx-correct@3.2.0:
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.21

  spdx-exceptions@2.5.0: {}

  spdx-expression-parse@3.0.1:
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.21

  spdx-license-ids@3.0.21: {}

  split2@4.2.0: {}

  stackback@0.0.2: {}

  std-env@3.9.0: {}

  string-argv@0.3.2: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@7.2.0:
    dependencies:
      emoji-regex: 10.4.0
      get-east-asian-width: 1.3.0
      strip-ansi: 7.1.0

  string.prototype.matchall@4.0.12:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.4
      set-function-name: 2.0.2
      side-channel: 1.1.0

  string.prototype.padend@3.1.6:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1

  string.prototype.repeat@1.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.9

  string.prototype.trim@1.2.10:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2

  string.prototype.trimend@1.0.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  stringify-object@3.3.0:
    dependencies:
      get-own-enumerable-property-symbols: 3.0.2
      is-obj: 1.0.1
      is-regexp: 1.0.0

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-bom@3.0.0: {}

  strip-comments@2.0.1: {}

  strip-final-newline@3.0.0: {}

  strip-indent@3.0.0:
    dependencies:
      min-indent: 1.0.1

  strip-json-comments@3.1.1: {}

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  symbol-tree@3.2.4: {}

  tailwind-merge@3.2.0: {}

  tailwindcss-animate@1.0.7(tailwindcss@4.1.3):
    dependencies:
      tailwindcss: 4.1.3

  tailwindcss@4.1.3: {}

  tapable@2.2.1: {}

  temp-dir@2.0.0: {}

  tempy@0.6.0:
    dependencies:
      is-stream: 2.0.1
      temp-dir: 2.0.0
      type-fest: 0.16.0
      unique-string: 2.0.0

  terser@5.39.0:
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.14.1
      commander: 2.20.3
      source-map-support: 0.5.21

  text-extensions@2.4.0: {}

  through@2.3.8: {}

  tinybench@2.9.0: {}

  tinyexec@0.3.2: {}

  tinyglobby@0.2.12:
    dependencies:
      fdir: 6.4.3(picomatch@4.0.2)
      picomatch: 4.0.2

  tinypool@1.0.2: {}

  tinyrainbow@2.0.0: {}

  tinyspy@3.0.2: {}

  tldts-core@6.1.85: {}

  tldts@6.1.85:
    dependencies:
      tldts-core: 6.1.85

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  tough-cookie@5.1.2:
    dependencies:
      tldts: 6.1.85

  tr46@1.0.1:
    dependencies:
      punycode: 2.3.1

  tr46@5.1.0:
    dependencies:
      punycode: 2.3.1

  trie-memoize@1.2.0: {}

  ts-api-utils@2.1.0(typescript@5.8.3):
    dependencies:
      typescript: 5.8.3

  tslib@2.8.1: {}

  turbo-stream@2.4.0: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.16.0: {}

  typed-array-buffer@1.0.3:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-typed-array: 1.1.15

  typed-array-byte-length@1.0.3:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15

  typed-array-byte-offset@1.0.4:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10

  typed-array-length@1.0.7:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10

  typescript-eslint@8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3):
    dependencies:
      '@typescript-eslint/eslint-plugin': 8.29.1(@typescript-eslint/parser@8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/parser': 8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/utils': 8.29.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)
      eslint: 9.24.0(jiti@2.4.2)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  typescript@5.8.3: {}

  ua-parser-js@1.0.40: {}

  unbox-primitive@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1

  undici-types@6.21.0: {}

  unicode-canonical-property-names-ecmascript@2.0.1: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.2.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  unicorn-magic@0.1.0: {}

  unique-string@2.0.0:
    dependencies:
      crypto-random-string: 2.0.0

  universalify@2.0.1: {}

  upath@1.2.0: {}

  update-browserslist-db@1.1.3(browserslist@4.24.4):
    dependencies:
      browserslist: 4.24.4
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  use-callback-ref@1.3.3(@types/react@19.1.0)(react@19.1.0):
    dependencies:
      react: 19.1.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.0

  use-sidecar@1.1.3(@types/react@19.1.0)(react@19.1.0):
    dependencies:
      detect-node-es: 1.1.0
      react: 19.1.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.0

  validate-npm-package-license@3.0.4:
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1

  vite-node@3.1.1(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1):
    dependencies:
      cac: 6.7.14
      debug: 4.4.0
      es-module-lexer: 1.6.0
      pathe: 2.0.3
      vite: 6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1)
    transitivePeerDependencies:
      - '@types/node'
      - jiti
      - less
      - lightningcss
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  vite-plugin-pwa@0.21.2(vite@6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1))(workbox-build@7.3.0(@types/babel__core@7.20.5))(workbox-window@7.3.0):
    dependencies:
      debug: 4.4.0
      pretty-bytes: 6.1.1
      tinyglobby: 0.2.12
      vite: 6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1)
      workbox-build: 7.3.0(@types/babel__core@7.20.5)
      workbox-window: 7.3.0
    transitivePeerDependencies:
      - supports-color

  vite-plugin-wasm@3.4.1(vite@6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1)):
    dependencies:
      vite: 6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1)

  vite@6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1):
    dependencies:
      esbuild: 0.25.2
      postcss: 8.5.3
      rollup: 4.39.0
    optionalDependencies:
      '@types/node': 22.14.0
      fsevents: 2.3.3
      jiti: 2.4.2
      lightningcss: 1.29.2
      terser: 5.39.0
      yaml: 2.7.1

  vitest@3.1.1(@types/node@22.14.0)(jiti@2.4.2)(jsdom@26.0.0)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1):
    dependencies:
      '@vitest/expect': 3.1.1
      '@vitest/mocker': 3.1.1(vite@6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1))
      '@vitest/pretty-format': 3.1.1
      '@vitest/runner': 3.1.1
      '@vitest/snapshot': 3.1.1
      '@vitest/spy': 3.1.1
      '@vitest/utils': 3.1.1
      chai: 5.2.0
      debug: 4.4.0
      expect-type: 1.2.1
      magic-string: 0.30.17
      pathe: 2.0.3
      std-env: 3.9.0
      tinybench: 2.9.0
      tinyexec: 0.3.2
      tinypool: 1.0.2
      tinyrainbow: 2.0.0
      vite: 6.2.6(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1)
      vite-node: 3.1.1(@types/node@22.14.0)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.0)(yaml@2.7.1)
      why-is-node-running: 2.3.0
    optionalDependencies:
      '@types/node': 22.14.0
      jsdom: 26.0.0
    transitivePeerDependencies:
      - jiti
      - less
      - lightningcss
      - msw
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  w3c-xmlserializer@5.0.0:
    dependencies:
      xml-name-validator: 5.0.0

  web-vitals@4.2.4: {}

  webidl-conversions@4.0.2: {}

  webidl-conversions@7.0.0: {}

  whatwg-encoding@3.1.1:
    dependencies:
      iconv-lite: 0.6.3

  whatwg-mimetype@4.0.0: {}

  whatwg-url@14.2.0:
    dependencies:
      tr46: 5.1.0
      webidl-conversions: 7.0.0

  whatwg-url@7.1.0:
    dependencies:
      lodash.sortby: 4.7.0
      tr46: 1.0.1
      webidl-conversions: 4.0.2

  which-boxed-primitive@1.1.1:
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1

  which-builtin-type@1.2.1:
    dependencies:
      call-bound: 1.0.4
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.19

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4

  which-typed-array@1.1.19:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      for-each: 0.3.5
      get-proto: 1.0.1
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  which@1.3.1:
    dependencies:
      isexe: 2.0.0

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  why-is-node-running@2.3.0:
    dependencies:
      siginfo: 2.0.0
      stackback: 0.0.2

  word-wrap@1.2.5: {}

  workbox-background-sync@7.3.0:
    dependencies:
      idb: 7.1.1
      workbox-core: 7.3.0

  workbox-broadcast-update@7.3.0:
    dependencies:
      workbox-core: 7.3.0

  workbox-build@7.3.0(@types/babel__core@7.20.5):
    dependencies:
      '@apideck/better-ajv-errors': 0.3.6(ajv@8.17.1)
      '@babel/core': 7.26.10
      '@babel/preset-env': 7.26.9(@babel/core@7.26.10)
      '@babel/runtime': 7.27.0
      '@rollup/plugin-babel': 5.3.1(@babel/core@7.26.10)(@types/babel__core@7.20.5)(rollup@2.79.2)
      '@rollup/plugin-node-resolve': 15.3.1(rollup@2.79.2)
      '@rollup/plugin-replace': 2.4.2(rollup@2.79.2)
      '@rollup/plugin-terser': 0.4.4(rollup@2.79.2)
      '@surma/rollup-plugin-off-main-thread': 2.2.3
      ajv: 8.17.1
      common-tags: 1.8.2
      fast-json-stable-stringify: 2.1.0
      fs-extra: 9.1.0
      glob: 7.2.3
      lodash: 4.17.21
      pretty-bytes: 5.6.0
      rollup: 2.79.2
      source-map: 0.8.0-beta.0
      stringify-object: 3.3.0
      strip-comments: 2.0.1
      tempy: 0.6.0
      upath: 1.2.0
      workbox-background-sync: 7.3.0
      workbox-broadcast-update: 7.3.0
      workbox-cacheable-response: 7.3.0
      workbox-core: 7.3.0
      workbox-expiration: 7.3.0
      workbox-google-analytics: 7.3.0
      workbox-navigation-preload: 7.3.0
      workbox-precaching: 7.3.0
      workbox-range-requests: 7.3.0
      workbox-recipes: 7.3.0
      workbox-routing: 7.3.0
      workbox-strategies: 7.3.0
      workbox-streams: 7.3.0
      workbox-sw: 7.3.0
      workbox-window: 7.3.0
    transitivePeerDependencies:
      - '@types/babel__core'
      - supports-color

  workbox-cacheable-response@7.3.0:
    dependencies:
      workbox-core: 7.3.0

  workbox-core@7.3.0: {}

  workbox-expiration@7.3.0:
    dependencies:
      idb: 7.1.1
      workbox-core: 7.3.0

  workbox-google-analytics@7.3.0:
    dependencies:
      workbox-background-sync: 7.3.0
      workbox-core: 7.3.0
      workbox-routing: 7.3.0
      workbox-strategies: 7.3.0

  workbox-navigation-preload@7.3.0:
    dependencies:
      workbox-core: 7.3.0

  workbox-precaching@7.3.0:
    dependencies:
      workbox-core: 7.3.0
      workbox-routing: 7.3.0
      workbox-strategies: 7.3.0

  workbox-range-requests@7.3.0:
    dependencies:
      workbox-core: 7.3.0

  workbox-recipes@7.3.0:
    dependencies:
      workbox-cacheable-response: 7.3.0
      workbox-core: 7.3.0
      workbox-expiration: 7.3.0
      workbox-precaching: 7.3.0
      workbox-routing: 7.3.0
      workbox-strategies: 7.3.0

  workbox-routing@7.3.0:
    dependencies:
      workbox-core: 7.3.0

  workbox-strategies@7.3.0:
    dependencies:
      workbox-core: 7.3.0

  workbox-streams@7.3.0:
    dependencies:
      workbox-core: 7.3.0
      workbox-routing: 7.3.0

  workbox-sw@7.3.0: {}

  workbox-window@7.3.0:
    dependencies:
      '@types/trusted-types': 2.0.7
      workbox-core: 7.3.0

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@9.0.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 7.2.0
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  ws@8.18.1: {}

  xml-name-validator@5.0.0: {}

  xmlchars@2.2.0: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yaml@2.7.1: {}

  yargs-parser@21.1.1: {}

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yocto-queue@0.1.0: {}

  yocto-queue@1.2.1: {}

  zustand@5.0.3(@types/react@19.1.0)(immer@10.1.1)(react@19.1.0):
    optionalDependencies:
      '@types/react': 19.1.0
      immer: 10.1.1
      react: 19.1.0
