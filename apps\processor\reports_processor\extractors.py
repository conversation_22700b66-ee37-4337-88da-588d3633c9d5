import json
from typing import Dict, Tuple, List, Any

from reports_processor.EntityExtractor import <PERSON><PERSON><PERSON><PERSON>x<PERSON>or, VinculoConfig
from reports_processor.FormatFrontFormat import front_vinculo_pessoais, VinculoSection
from reports_processor.constants import *
from reports_processor.formatters.formatters import (
    merge_entity_lists, final_front_format
)

from reports_processor.reports.ReportProcessorFactory import ReportProcessorFactory
from reports_processor.utils import (
    get_dtype, is_a_match, get_constant, basic_property_dict, collect_sources
)
from reports_processor.dutils.PreProcessEntity import pre_process_entities

from datetime import datetime
from copy import deepcopy

def extract_entities_from_dataframe(data, report_type: ReportType, search_value: str) -> tuple[List[Dict], Any]:
    """
    Main function to extract entities from DataFrame with improved performance and maintainability
    """
    extractor = EntityExtractor()

    # Extract basic entity data
    logger.debug(f'[timne] extracting basic entity data {datetime.now()}')
    main_entity_data, other_result_data, main_entity_sources = _extract_basic_entity_data(
        data, report_type, search_value
    )
    logger.debug(f'[timne] back extracting basic entity data - starting extraction configs {datetime.now()}')

    # Configure extraction operations
    extraction_configs, processor = _get_extraction_configs(extractor, report_type, search_value, other_result_data, bool(main_entity_data))

    # Execute all extractions
    extraction_results = {}
    for key, config in extraction_configs.items():
        try:
            if hasattr(config, 'extract_func'):
                result = config.extract_func(other_result_data, report_type.main_entity, search_value, config)

                if not main_entity_data and key is VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS:
                    rp2 = ReportType.POSSIVEIS_PESSOAS
                    ex2 = EntityExtractor()
                    __p = ReportProcessorFactory.create_processor(ex2, rp2)
                    __p.get_config()

                extraction_results[key] = final_front_format(config.front_format, result.sources, result.data)

                # TODO: verificar com empresas - obter dados
                if not main_entity_data and key is VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS:
                    for idx, p in enumerate(result.data):

                        rp2 = ReportType.POSSIVEIS_PESSOAS
                        ex2 = EntityExtractor()
                        sv2 = p.get('cpf', '')
                        ex_configs2, p2 = _get_extraction_configs(ex2, rp2, sv2, p, True)
                        er = {}
                        for k2, c2 in ex_configs2.items():
                            if hasattr(c2, 'extract_func'):
                                r2 = c2.extract_func(p, rp2.main_entity, sv2, c2)
                                er[k2] = final_front_format(c2.front_format, r2.sources, r2.data)

                        extraction_results[key]['data'][idx] = {k: v for k, v in extraction_results[key]['data'][idx].items() if not isinstance(v, list) or k == ReportKeys.VINCULO}

                        cur_main = extraction_results[key]['data'][idx]['detalhes']
                        p_main_form = final_front_format(front_vinculo_pessoais, collect_sources(cur_main),
                                                        [cur_main])
                        del extraction_results[key]['data'][idx]['detalhes']
                        extraction_results[key]['data'][idx]['sections'] = [p_main_form] + [x for x in er.values() if x]


            else:
                raise NotImplementedError(f'extract_func not implemented for {key}')
        except Exception as e:
            logger.error(f"Error extracting {key}: {e}")
            extraction_results[key] = final_front_format(config.front_format, [], [])

    # processor.get_config()
    logger.warning(f"processor = {processor}, main front = {front_vinculo_pessoais.title}")
    # Format main entity data
    logger.debug(f'[timne] finished extraction configs, starting final front {datetime.now()}')
    main_form = final_front_format(front_vinculo_pessoais, main_entity_sources, [main_entity_data])

    # Combine all results
    final_data = [main_form] + [result for result in extraction_results.values() if result]
    logger.debug(f'[timne] finished final front {datetime.now()}')

    return final_data, processor


def _extract_basic_entity_data(
        data,
        report_type,
        search_value: str
) -> Tuple[Dict, Dict, set]:
    """Extract basic entity data from DataFrame"""
    main_entity_data = {}
    possible_main_entity_data = []
    failed_single_match = False
    main_entity_sources = set()
    other_result_data = {}

    # def explode_and_select(df: DataFrame, column: str) -> DataFrame:
    #     try:
    #         exploded_df = df.withColumn(column, explode(col(column)))
    #
    #         # Get the data type of the exploded column
    #         field_type = [f for f in exploded_df.schema.fields if f.name == column][0].dataType
    #
    #         if isinstance(field_type, StructType):
    #             # If it's a struct, we can use `column.*`
    #             return exploded_df.select(f"{column}.*")
    #         else:
    #             # Otherwise just return the exploded column
    #             return exploded_df.select(column)
    #     except Exception as e:
    #         logging.error(f"failing to expand df ===> Before explode_and_select {e}")
    #         df.printSchema()
    #         df.select(column).show(truncate=False)
    #         raise

    for base_column in list(data):

        if base_column == ReportKeys.METADATA:
            continue

        df_entity = data.get(base_column)

        if not df_entity:
            continue

        if API_ERROR_MESSAGE_KEY in df_entity[0]:
            has_no_results = df_entity[0][API_ERROR_MESSAGE_KEY] == NO_RESULTS_API_MESSAGE
            if has_no_results:
                logger.info(f"{base_column} has no results.")
            else:
                logger.warning(f"Skipping data from {base_column} due to error.")
            continue

        failed_single_match = _process_dataframe_column(
            df_entity, base_column, report_type, search_value,
            possible_main_entity_data, failed_single_match, main_entity_data, main_entity_sources, other_result_data
        )

    other_res = {OtherResultDataTypes.other: other_result_data,
                 OtherResultDataTypes.main: {}}

    for p_main_b_column, p_main_sub_column, p_main_entity in possible_main_entity_data:
        _process_main_entity(p_main_entity, p_main_b_column, main_entity_data, other_res['main'])
        main_entity_sources.add(p_main_b_column)

    return main_entity_data, other_res, main_entity_sources


def _process_dataframe_column(
        df_entity,
        base_column: str,
        report_type,
        search_value: str,
        possible_main_entity_data: List,
        failed_single_match: bool,
        main_entity_data: Dict,
        main_entity_sources: set,
        other_result_data: Dict
):
    """Process a single DataFrame column"""

    if report_type is ReportType.RELACOES:
        df_entity = [{base_column: df_entity}]
        base_column = "SNAP"

    for a_list in df_entity:
        for sub_column in a_list:

            if not isinstance(a_list[sub_column], list):
                continue

            entities = a_list[sub_column]

            pre_process_entities(sub_column, entities, base_column)

            if (report_type is ReportType.PHONE or report_type is ReportType.EMAIL) and sub_column in report_type.main_entity:
                for entity in entities:
                    cur_search_value = None
                    cur_search_value_list = set()

                    for p_main in possible_main_entity_data:
                        cur_search_value = p_main[2].get(main_entity_fields[sub_column], None)
                        if cur_search_value and 'value' in cur_search_value:
                            cur_search_value_list.add(cur_search_value['value'])
                            cur_search_value = cur_search_value['value']
                        elif cur_search_value:
                            cur_search_value_list.add(cur_search_value)

                    if len(cur_search_value_list) > 1:
                        cur_search_value = '111'

                    if not failed_single_match and (not cur_search_value or is_a_match(entity, sub_column, cur_search_value)):
                        possible_main_entity_data.append((base_column, sub_column, entity))
                    else:
                        failed_single_match = True
                        for ent in possible_main_entity_data:
                            _add_other_entity(*ent, other_result_data)
                        possible_main_entity_data.clear()
                        main_entity_data.clear()
                        main_entity_sources.clear()
                        _add_other_entity(base_column, sub_column, entity, other_result_data)

            elif report_type is ReportType.RELACOES:
                for entity in entities:
                    _add_other_entity(base_column, sub_column, entity, other_result_data)
            else:
                for entity in entities:

                    if sub_column == report_type.main_entity and is_a_match(entity, sub_column, search_value):
                        possible_main_entity_data.append((base_column, sub_column, entity))
                        # _process_main_entity(entity, base_column, main_entity_data, other_result_data)
                        # main_entity_sources.add(base_column)
                    else:
                        _add_other_entity(base_column, sub_column, entity, other_result_data)

    return failed_single_match



def _process_main_entity(
        entity: Dict,
        base_column: str,
        main_entity_data: Dict,
        other_result_data: Dict
):
    """Process main entity data"""
    for k, v in entity.items():
        if k in skip_fields:
            continue
        elif isinstance(v, str):
            existing = main_entity_data.get(k)
            if existing:
                if existing["value"] == v:
                    existing["source"].add(base_column)
                else:
                    variant_key = f"{k}_{base_column}"
                    main_entity_data[variant_key] = basic_property_dict(
                        v, get_constant(Constants.PropToLabel, k, k), {base_column}
                    )
            else:
                main_entity_data[k] = basic_property_dict(
                    v, get_constant(Constants.PropToLabel, k, k), {base_column}
                )
        else:
            new_base = other_result_data.setdefault(base_column, {})
            if k in new_base:
                if isinstance(v, list) and isinstance(new_base[k], list):
                    new_base[k].extend(deepcopy(v))
                else:
                    logger.warning(f"Losing unmapped property {k}, value: {v}")
            else:
                new_base[k] = deepcopy(v)


def _add_other_entity(
        base_column: str,
        sub_column: str,
        entity: Dict,
        other_result_data: Dict
):
    """Add other entity to result data"""
    new_base = other_result_data.setdefault(base_column, {})
    if sub_column in new_base:
        new_base[sub_column] = merge_entity_lists(new_base[sub_column], [entity])
    else:
        new_base[sub_column] = [entity]

def _get_extraction_configs(
        extractor: EntityExtractor,
        report_type: ReportType,
        search_value: str,
        other_result_data: Dict,
        has_main_data: bool = True,
        skip_custom_vinculos: bool = False
) -> tuple[Dict[str, VinculoConfig], Any]:
    """Get extraction configurations for all relationship types"""

    # Create the appropriate processor
    processor = ReportProcessorFactory.create_processor(extractor, report_type)
    entity_type = report_type.main_entity
    return processor.get_section_vinculo_config(other_result_data, entity_type, search_value, has_main_data, skip_custom_vinculos), processor