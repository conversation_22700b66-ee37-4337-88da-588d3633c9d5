import os
import time
import requests
import docker
import logging

# Logging config
logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s: %(message)s")
logger = logging.getLogger(__name__)

# REPLICA PRESERVATION CONFIGURATION:
# This autoscaler is configured to preserve existing running containers when scaling up.
# Key settings:
# - order='start-first': Start new replicas before stopping old ones
# - parallelism=1: Update one replica at a time for maximum safety
# - delay=30s: Wait 30 seconds between updates for stability
# - monitor=300s: Monitor each update for 5 minutes before proceeding
# - failure_action='pause': Pause on any failure to prevent replica loss
# - max_failure_ratio=0.0: Zero tolerance for failures
# - preserve_previous_spec=True: Preserve all existing service configuration

# Environment variables
PROMETHEUS_URL = os.getenv("PROMETHEUS_URL", "http://prometheus:9090")
PROM_QUERY = os.getenv(
    "PROM_QUERY",
    'sum(clamp_min(kafka_consumergroup_lag{consumergroup=~"pdf-service.*", topic="%s"}, 0))' % (
        os.getenv("TOPIC", "pdf-generation-requests")
    )
)
SERVICE_NAME = os.getenv("SERVICE_NAME_AUTOSCALER", "mystack_pdf")
USE_ROLLING_UPDATE = os.getenv("USE_ROLLING_UPDATE", "true").lower() == "true"

POLL_INTERVAL = int(os.getenv("POLL_INTERVAL_MS", "5000")) / 1000  # seconds
HIGH_LAG_THRESHOLD = int(os.getenv("HIGH_LAG_THRESHOLD", "4"))
LOW_LAG_THRESHOLD = int(os.getenv("LOW_LAG_THRESHOLD", "3"))
DOWNSCALE_STABLE_COUNT = int(os.getenv("DOWNSCALE_STABLE_COUNT", "1"))
IDLE_TIMEOUT_SECONDS = int(os.getenv("IDLE_TIMEOUT_SECONDS", "600"))  # 10 minutes

MIN_REPLICAS = int(os.getenv("MIN_REPLICAS", "1"))
MAX_REPLICAS = int(os.getenv("MAX_REPLICAS", "3"))
SCALE_UP_REPLICAS = int(os.getenv("SCALE_UP_REPLICAS", "1"))

COOLDOWN_SECONDS = int(os.getenv("COOLDOWN_SECONDS", "10"))

# Enhanced rolling update configuration for maximum replica preservation
ROLLING_UPDATE_PARALLELISM = int(os.getenv("ROLLING_UPDATE_PARALLELISM", "1"))
ROLLING_UPDATE_DELAY = int(os.getenv("ROLLING_UPDATE_DELAY", "30"))  # Increased from 5 to 30 seconds
ROLLING_UPDATE_MONITOR = int(os.getenv("ROLLING_UPDATE_MONITOR", "300"))  # Increased from 20 to 300 seconds
ROLLING_UPDATE_FAILURE_RATIO = float(os.getenv("ROLLING_UPDATE_FAILURE_RATIO", "0.0"))

# State
stable_low_count = 0
last_action_time = 0
previous_lag = 0  # Track previous lag value to detect decreases
scaling_in_progress = False  # Prevent multiple scaling operations from running simultaneously

# Docker client
docker_client = docker.from_env()


def get_total_lag():
    """Query Prometheus for Kafka lag."""
    try:
        logger.info(f"Querying Prometheus at {PROMETHEUS_URL} with query: {PROM_QUERY}")
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", params={"query": PROM_QUERY}, timeout=5)
        resp.raise_for_status()
        data = resp.json()
        logger.info(f"Prometheus response status: {data.get('status')}")
        if data["status"] != "success":
            logger.warning("Prometheus query failed: %s", data)
            return 0
        result = data["data"]["result"]
        if not result:
            logger.info("No results from Prometheus query")
            return 0
        lag_value = int(float(result[0]["value"][1]))
        logger.info(f"Retrieved lag value: {lag_value}")
        return lag_value
    except requests.exceptions.ConnectionError as e:
        logger.error(f"Failed to connect to Prometheus at {PROMETHEUS_URL}: {e}")
        logger.info("Returning 0 lag due to Prometheus connectivity issues (conservative approach)")
        return 0
    except Exception as e:
        logger.error("Error querying Prometheus: %s", e)
        logger.info("Returning 0 lag due to Prometheus query error (conservative approach)")
        return 0


def get_current_replicas():
    """Get current replica count of the service."""
    try:
        logger.info(f"Getting current replicas for service: {SERVICE_NAME}")
        service = docker_client.services.get(SERVICE_NAME)
        spec = service.attrs["Spec"]
        replicas = spec["Mode"]["Replicated"]["Replicas"]
        logger.info(f"Current replicas for {SERVICE_NAME}: {replicas}")
        return replicas
    except Exception as e:
        logger.error(f"Failed to get current replicas for {SERVICE_NAME}: {e}")
        raise


def scale_service(count):
    """Wrapper function to scale service using the configured method."""
    if USE_ROLLING_UPDATE:
        logger.info("Using rolling update method for scaling")
        return set_replicas_with_rolling_update(count)
    else:
        logger.info("Using standard scaling method")
        return set_replicas(count)


def set_replicas(count):
    """Scale the service to the given replica count while preserving existing replicas."""
    global scaling_in_progress
    
    if scaling_in_progress:
        logger.warning("Scaling operation already in progress, skipping this request")
        return
    
    count = max(MIN_REPLICAS, min(MAX_REPLICAS, count))
    logger.info(f"Attempting to scale {SERVICE_NAME} to {count} replicas with enhanced replica preservation")
    
    try:
        scaling_in_progress = True
        service = docker_client.services.get(SERVICE_NAME)
        
        # Get current replica count and service spec
        current_replicas = service.attrs["Spec"]["Mode"]["Replicated"]["Replicas"]
        logger.info(f"Current replicas: {current_replicas}, Target replicas: {count}")
        
        if count > current_replicas:
            # Scaling up - use enhanced replica preservation strategy
            logger.info(f"Scaling UP from {current_replicas} to {count} replicas - ENHANCED replica preservation")
            
            # Verify current replicas before scaling
            replicas_before = verify_replica_preservation()
            logger.info(f"Verified {replicas_before} replicas running before scaling up")
            
            # Perform detailed verification before scaling
            logger.info("Performing detailed replica state analysis before scaling...")
            detailed_state_before = verify_replica_preservation_detailed()
            logger.info(f"Detailed state before scaling: {detailed_state_before['running']} running, {detailed_state_before['starting']} starting, {detailed_state_before['stopping']} stopping, {detailed_state_before['failed']} failed")
            
            # Ensure service supports rolling updates before scaling
            logger.info("Ensuring service supports rolling updates...")
            ensure_service_rolling_update_support()
            
            # Get current service spec to preserve all existing settings
            current_spec = service.attrs["Spec"]
            logger.info("Preserving existing service configuration during scale up")
            
            # ENHANCED rolling update configuration for maximum replica preservation
            update_config = docker.types.UpdateConfig(
                parallelism=1,  # ALWAYS update one at a time for maximum safety
                delay=ROLLING_UPDATE_DELAY,  # Use configured delay (30s default)
                failure_action='pause',  # Pause on any failure
                monitor=ROLLING_UPDATE_MONITOR,  # Use configured monitor time (300s default)
                max_failure_ratio=0.0,  # Zero tolerance for failures
                order='start-first'  # Start new replicas before stopping old ones
            )
            
            # Create rollback configuration for additional safety
            rollback_config = docker.types.UpdateConfig(
                parallelism=1,
                delay=ROLLING_UPDATE_DELAY,
                failure_action='pause',
                monitor=ROLLING_UPDATE_MONITOR,
                max_failure_ratio=0.0,
                order='start-first'
            )
            
            logger.info(f"Using ENHANCED replica preservation config: parallelism=1, delay={ROLLING_UPDATE_DELAY}s, monitor={ROLLING_UPDATE_MONITOR}s, failure_ratio=0.0, order=start-first")
            
            # Update service with enhanced rolling update config
            service.update(
                mode=docker.types.ServiceMode('replicated', replicas=count),
                update_config=update_config,
                rollback_config=rollback_config,
                preserve_previous_spec=True  # Preserve all existing configuration
            )
            
            logger.info(f"Enhanced rolling update initiated - will scale to {count} replicas gradually while preserving existing ones")
            
            # Wait for the operation to stabilize and verify replicas are being preserved
            logger.info("Waiting for scaling operation to stabilize...")
            time.sleep(10)  # Wait 10 seconds for initial stabilization
            
            # Perform detailed verification after initial stabilization
            logger.info("Performing detailed replica state analysis after initial stabilization...")
            detailed_state_after = verify_replica_preservation_detailed()
            logger.info(f"Detailed state after initial stabilization: {detailed_state_after['running']} running, {detailed_state_after['starting']} starting, {detailed_state_after['stopping']} stopping, {detailed_state_after['failed']} failed")
            
            replicas_after = verify_replica_preservation()
            logger.info(f"After scaling operation: {replicas_after} replicas still running")
            
            # Additional verification after a longer wait
            logger.info("Waiting additional time for operation to complete...")
            time.sleep(20)  # Wait 20 more seconds
            
            # Perform final detailed verification
            logger.info("Performing final detailed replica state analysis...")
            detailed_state_final = verify_replica_preservation_detailed()
            logger.info(f"Final detailed state: {detailed_state_final['running']} running, {detailed_state_final['starting']} starting, {detailed_state_final['stopping']} stopping, {detailed_state_final['failed']} failed")
            
            replicas_final = verify_replica_preservation()
            logger.info(f"Final verification after scaling: {replicas_final} replicas still running")
            
            # Enhanced verification logic
            if replicas_final < replicas_before:
                logger.error(f"CRITICAL: Replica preservation failed! Had {replicas_before}, now have {replicas_final}")
                logger.error("This indicates Docker is still killing existing replicas during scale up")
                
                # Log detailed state comparison
                logger.error(f"State comparison - Before: {detailed_state_before}, After: {detailed_state_final}")
                
                # Try alternative scaling approach
                logger.info("Attempting alternative scaling approach...")
                if try_alternative_scaling_approach(count):
                    logger.info("Alternative scaling approach successful")
                    # Wait and verify again
                    time.sleep(15)
                    replicas_alternative = verify_replica_preservation()
                    logger.info(f"After alternative scaling: {replicas_alternative} replicas running")
                else:
                    logger.error("All scaling approaches failed - replicas are being killed")
            else:
                logger.info(f"✓ Replica preservation successful: maintained {replicas_final} replicas")
                
                # Check for any suspicious state changes
                if detailed_state_final['stopping'] > 0:
                    logger.warning(f"⚠️  {detailed_state_final['stopping']} replicas are still stopping - monitoring closely")
                
                if detailed_state_final['failed'] > 0:
                    logger.warning(f"⚠️  {detailed_state_final['failed']} replicas have failed - investigating")
                
                if detailed_state_final['starting'] > 0:
                    logger.info(f"🔄 {detailed_state_final['starting']} replicas are still starting - this is normal during scale up")
                
        else:
            # Scaling down - normal update is fine
            logger.info(f"Scaling DOWN from {current_replicas} to {count} replicas")
            service.update(mode=docker.types.ServiceMode('replicated', replicas=count))
        
        logger.info(f"Successfully scaled {SERVICE_NAME} to {count} replicas")
        
    except Exception as e:
        logger.error(f"Failed to scale {SERVICE_NAME} to {count} replicas: {e}")
        raise
    finally:
        scaling_in_progress = False


def set_replicas_with_rolling_update(count):
    """Scale the service using rolling update to preserve existing replicas."""
    count = max(MIN_REPLICAS, min(MAX_REPLICAS, count))
    logger.info(f"Attempting to scale {SERVICE_NAME} to {count} replicas using rolling update")
    
    try:
        service = docker_client.services.get(SERVICE_NAME)
        
        # Get current replica count and service spec
        current_replicas = service.attrs["Spec"]["Mode"]["Replicated"]["Replicas"]
        logger.info(f"Current replicas: {current_replicas}, Target replicas: {count}")
        
        if count > current_replicas:
            # Scaling up - use rolling update configuration to preserve existing replicas
            logger.info(f"Scaling UP from {current_replicas} to {count} replicas - using rolling update to preserve existing replicas")
            
            # Verify current replicas before scaling
            replicas_before = verify_replica_preservation()
            logger.info(f"Verified {replicas_before} replicas running before rolling update")
            
            # Get current service spec to preserve other settings
            current_spec = service.attrs["Spec"]
            logger.info(f"Preserving existing service configuration during scale up")
            
            # Create ENHANCED rolling update configuration for maximum replica preservation
            update_config = docker.types.UpdateConfig(
                parallelism=1,  # ALWAYS update one at a time for safety
                delay=ROLLING_UPDATE_DELAY,  # Use configured delay (30s default)
                failure_action='pause',  # Pause on any failure
                monitor=ROLLING_UPDATE_MONITOR,  # Use configured monitor time (300s default)
                max_failure_ratio=0.0,  # Zero tolerance for failures
                order='start-first'  # Start new replicas before stopping old ones
            )
            
            # Create rollback configuration for additional safety
            rollback_config = docker.types.UpdateConfig(
                parallelism=1,
                delay=ROLLING_UPDATE_DELAY,
                failure_action='pause',
                monitor=ROLLING_UPDATE_MONITOR,
                max_failure_ratio=0.0,
                order='start-first'
            )
            
            logger.info(f"Applying ENHANCED rolling update config: parallelism=1, delay={ROLLING_UPDATE_DELAY}s, monitor={ROLLING_UPDATE_MONITOR}s, failure_ratio=0.0, order=start-first")
            
            # Update service with rolling update config
            service.update(
                mode=docker.types.ServiceMode('replicated', replicas=count),
                update_config=update_config,
                rollback_config=rollback_config
            )
            
            logger.info(f"Rolling update initiated - will scale to {count} replicas gradually while preserving existing ones")
            
            # Wait a moment and verify replicas are being preserved
            import time
            time.sleep(2)
            replicas_after = verify_replica_preservation()
            logger.info(f"After rolling update operation: {replicas_after} replicas still running")
        else:
            # Scaling down - normal update is fine
            logger.info(f"Scaling DOWN from {current_replicas} to {count} replicas")
            service.update(mode=docker.types.ServiceMode('replicated', replicas=count))
        
        logger.info(f"Successfully initiated scaling of {SERVICE_NAME} to {count} replicas")
        
    except Exception as e:
        logger.error(f"Failed to scale {SERVICE_NAME} to {count} replicas: {e}")
        raise


def get_service_tasks():
    """Get all running tasks for the service with their creation timestamps."""
    try:
        service = docker_client.services.get(SERVICE_NAME)
        tasks = service.tasks()
        
        task_info = []
        for task in tasks:
            # Handle both task objects and dict responses
            if hasattr(task, 'attrs'):
                task_attrs = task.attrs
            elif isinstance(task, dict):
                task_attrs = task
            else:
                logger.warning(f"Unexpected task type: {type(task)}")
                continue
            
            # Only consider running tasks
            task_status = task_attrs.get('Status', {}).get('State', '')
            if task_status != 'running':
                logger.debug(f"Skipping task {task_attrs.get('ID', '')[:12]} with status: {task_status}")
                continue
            
            created_at = task_attrs.get('CreatedAt', '')
            task_id = task_attrs.get('ID', '')
            slot = task_attrs.get('Slot', 0)
            
            # Convert timestamp to seconds since epoch
            if created_at:
                # Docker timestamps are in format like "2024-01-01T12:00:00.123456789Z"
                import datetime
                try:
                    # Parse the timestamp
                    dt = datetime.datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    created_timestamp = dt.timestamp()
                except:
                    created_timestamp = 0
            else:
                created_timestamp = 0
            
            task_info.append({
                'task_id': task_id,
                'slot': slot,
                'created_at': created_at,
                'created_timestamp': created_timestamp,
                'status': task_status
            })
        
        # Sort by creation timestamp (newest first)
        task_info.sort(key=lambda x: x['created_timestamp'], reverse=True)
        
        logger.info(f"Found {len(task_info)} running tasks for {SERVICE_NAME}")
        for task in task_info:
            logger.info(f"Task {task['task_id'][:12]} (slot {task['slot']}) created at {task['created_at']} - Status: {task['status']}")
        
        return task_info
    except Exception as e:
        logger.error(f"Failed to get service tasks for {SERVICE_NAME}: {e}")
        return []


def scale_down_keeping_newest(target_replicas):
    """Simplified: directly scale to target replicas (no keep-newest logic)."""
    try:
        scale_service(target_replicas)
        logger.info(f"Scaled to {target_replicas} replicas")
    except Exception as e:
        logger.error(f"Failed to scale to {target_replicas} replicas: {e}")


def verify_replica_preservation():
    """Verify that existing replicas are being preserved during scaling operations."""
    try:
        service = docker_client.services.get(SERVICE_NAME)
        tasks = service.tasks()
        
        # Get running tasks before scaling
        running_tasks_before = [task for task in tasks if task.get('Status', {}).get('State') == 'running']
        logger.info(f"Current running tasks before scaling: {len(running_tasks_before)}")
        
        # Log task details for debugging
        for task in running_tasks_before:
            task_id = task.get('ID', '')[:12]
            slot = task.get('Slot', 'N/A')
            created_at = task.get('CreatedAt', 'N/A')
            logger.info(f"Task {task_id} (slot {slot}) created at {created_at}")
        
        return len(running_tasks_before)
        
    except Exception as e:
        logger.error(f"Failed to verify replica preservation: {e}")
        return 0


def verify_replica_preservation_detailed():
    """Enhanced verification that provides detailed information about replica states during scaling operations."""
    try:
        service = docker_client.services.get(SERVICE_NAME)
        tasks = service.tasks()
        
        # Get all task states
        task_states = {}
        running_count = 0
        starting_count = 0
        stopping_count = 0
        failed_count = 0
        
        for task in tasks:
            task_id = task.get('ID', '')[:12]
            slot = task.get('Slot', 'N/A')
            state = task.get('Status', {}).get('State', 'unknown')
            created_at = task.get('CreatedAt', 'N/A')
            started_at = task.get('Status', {}).get('StartedAt', 'N/A')
            
            task_states[task_id] = {
                'slot': slot,
                'state': state,
                'created_at': created_at,
                'started_at': started_at
            }
            
            if state == 'running':
                running_count += 1
            elif state == 'starting':
                starting_count += 1
            elif state == 'stopping':
                stopping_count += 1
            elif state == 'failed':
                failed_count += 1
        
        # Log detailed state information
        logger.info(f"=== Detailed Replica State Analysis ===")
        logger.info(f"Total tasks: {len(tasks)}")
        logger.info(f"Running tasks: {running_count}")
        logger.info(f"Starting tasks: {starting_count}")
        logger.info(f"Stopping tasks: {stopping_count}")
        logger.info(f"Failed tasks: {failed_count}")
        
        # Log individual task details
        for task_id, task_info in task_states.items():
            logger.info(f"Task {task_id} (slot {task_info['slot']}): {task_info['state']} - Created: {task_info['created_at']}, Started: {task_info['started_at']}")
        
        # Check for any suspicious state changes
        if stopping_count > 0:
            logger.warning(f"⚠️  {stopping_count} tasks are stopping - this may indicate replica killing during scale up")
        
        if failed_count > 0:
            logger.error(f"❌ {failed_count} tasks have failed - this may indicate scaling issues")
        
        if starting_count > 0:
            logger.info(f"🔄 {starting_count} tasks are starting - this is normal during scale up")
        
        logger.info(f"=== End Replica State Analysis ===")
        
        return {
            'running': running_count,
            'starting': starting_count,
            'stopping': stopping_count,
            'failed': failed_count,
            'total': len(tasks),
            'task_states': task_states
        }
        
    except Exception as e:
        logger.error(f"Failed to verify replica preservation in detail: {e}")
        return {
            'running': 0,
            'starting': 0,
            'stopping': 0,
            'failed': 0,
            'total': 0,
            'task_states': {}
        }


def wait_for_scaling_completion(target_replicas, max_wait_time=300):
    """Wait for scaling operation to complete and verify replica preservation."""
    logger.info(f"Waiting for scaling operation to complete (target: {target_replicas} replicas)")
    
    start_time = time.time()
    last_check_time = start_time
    check_interval = 10  # Check every 10 seconds
    
    while time.time() - start_time < max_wait_time:
        try:
            current_time = time.time()
            
            # Get current replica count
            current_replicas = get_current_replicas()
            logger.info(f"Current replicas: {current_replicas}, Target: {target_replicas}")
            
            # Check if scaling is complete
            if current_replicas == target_replicas:
                logger.info(f"✓ Scaling completed successfully - reached target of {target_replicas} replicas")
                
                # Final verification
                final_state = verify_replica_preservation_detailed()
                if final_state['running'] >= target_replicas:
                    logger.info(f"✓ Final verification passed: {final_state['running']} replicas running")
                    return True
                else:
                    logger.warning(f"⚠️  Final verification failed: expected {target_replicas} running, got {final_state['running']}")
                    return False
            
            # Check replica preservation every 30 seconds
            if current_time - last_check_time >= 30:
                logger.info("Performing intermediate replica preservation check...")
                state = verify_replica_preservation_detailed()
                
                if state['stopping'] > 0:
                    logger.warning(f"⚠️  {state['stopping']} replicas are stopping - monitoring closely")
                
                if state['failed'] > 0:
                    logger.error(f"❌ {state['failed']} replicas have failed - scaling may be problematic")
                
                last_check_time = current_time
            
            # Wait before next check
            time.sleep(check_interval)
            
        except Exception as e:
            logger.error(f"Error during scaling completion check: {e}")
            time.sleep(check_interval)
    
    logger.error(f"Scaling operation did not complete within {max_wait_time} seconds")
    return False


def ensure_service_rolling_update_support():
    """Ensure the service is configured to support rolling updates properly."""
    try:
        service = docker_client.services.get(SERVICE_NAME)
        spec = service.attrs["Spec"]
        
        # Check if service already has proper update config
        has_update_config = "UpdateConfig" in spec
        has_rollback_config = "RollbackConfig" in spec
        
        logger.info(f"Service {SERVICE_NAME} update config status:")
        logger.info(f"  - Has UpdateConfig: {has_update_config}")
        logger.info(f"  - Has RollbackConfig: {has_rollback_config}")
        
        if not has_update_config:
            logger.warning(f"Service {SERVICE_NAME} does not have UpdateConfig - this may cause replica killing during scaling")
            logger.info("Attempting to add UpdateConfig to service...")
            
            # Try to add update config to the service
            try:
                logger.info("Adding UpdateConfig with replica preservation settings...")
                update_config = docker.types.UpdateConfig(
                    parallelism=1,
                    delay=ROLLING_UPDATE_DELAY,
                    failure_action='pause',
                    monitor=ROLLING_UPDATE_MONITOR,
                    max_failure_ratio=0.0,
                    order='start-first'
                )
                
                service.update(update_config=update_config)
                logger.info("✓ Successfully added UpdateConfig to service")
                return True
            except Exception as e:
                logger.error(f"Failed to add UpdateConfig to service: {e}")
                logger.info("Trying alternative method using Docker CLI...")
                
                # Try using Docker CLI as fallback
                try:
                    import subprocess
                    cmd = [
                        "docker", "service", "update",
                        "--update-parallelism", "1",
                        "--update-delay", f"{ROLLING_UPDATE_DELAY}s",
                        "--update-failure-action", "pause",
                        "--update-monitor", f"{ROLLING_UPDATE_MONITOR}s",
                        "--update-max-failure-ratio", "0.0",
                        "--update-order", "start-first",
                        SERVICE_NAME
                    ]
                    
                    logger.info(f"Executing: {' '.join(cmd)}")
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                    
                    if result.returncode == 0:
                        logger.info("✓ Successfully added UpdateConfig using Docker CLI")
                        return True
                    else:
                        logger.error(f"Failed to add UpdateConfig using Docker CLI: {result.stderr}")
                        return False
                        
                except Exception as cli_error:
                    logger.error(f"Failed to use Docker CLI: {cli_error}")
                    return False
        
        # Check if existing config needs improvement
        if has_update_config:
            update_config = spec["UpdateConfig"]
            current_order = update_config.get("Order", "stop-first")
            current_parallelism = update_config.get("Parallelism", 0)
            
            needs_update = False
            
            if current_order != "start-first":
                logger.warning(f"Update Order is '{current_order}' but should be 'start-first' for replica preservation")
                needs_update = True
                
            if current_parallelism > 1:
                logger.warning(f"Update Parallelism is {current_parallelism} but should be 1 for safe replica preservation")
                needs_update = True
            
            if needs_update:
                logger.info("Updating existing UpdateConfig for better replica preservation...")
                try:
                    improved_config = docker.types.UpdateConfig(
                        parallelism=1,
                        delay=ROLLING_UPDATE_DELAY,
                        failure_action='pause',
                        monitor=ROLLING_UPDATE_MONITOR,
                        max_failure_ratio=0.0,
                        order='start-first'
                    )
                    
                    service.update(update_config=improved_config)
                    logger.info("✓ Successfully updated UpdateConfig for replica preservation")
                    return True
                except Exception as e:
                    logger.error(f"Failed to update UpdateConfig: {e}")
                    return False
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to check service rolling update support: {e}")
        return False


def try_alternative_scaling_approach(count):
    """Try alternative scaling approaches if the main one fails."""
    logger.info("Attempting alternative scaling approach...")
    
    try:
        service = docker_client.services.get(SERVICE_NAME)
        
        # Method 1: Try using Docker CLI directly
        logger.info("Method 1: Trying Docker CLI...")
        import subprocess
        
        try:
            # Use docker service update with rolling update configuration
            cmd = [
                "docker", "service", "update", 
                "--replicas", str(count),
                "--update-parallelism", "1",
                "--update-delay", f"{ROLLING_UPDATE_DELAY}s",
                "--update-failure-action", "pause",
                "--update-monitor", f"{ROLLING_UPDATE_MONITOR}s",
                "--update-max-failure-ratio", "0.0",
                "--update-order", "start-first",
                SERVICE_NAME
            ]
            
            logger.info(f"Executing command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                logger.info("✓ Alternative scaling method 1 successful!")
                logger.info(f"Output: {result.stdout}")
                return True
            else:
                logger.warning(f"Alternative scaling method 1 failed: {result.stderr}")
                
        except Exception as e:
            logger.warning(f"Alternative scaling method 1 failed: {e}")
        
        # Method 2: Try using docker-py with different approach
        logger.info("Method 2: Trying docker-py with different approach...")
        try:
            # Try to update just the replica count without other changes
            service.update(
                mode=docker.types.ServiceMode('replicated', replicas=count)
            )
            logger.info("✓ Alternative scaling method 2 successful!")
            return True
            
        except Exception as e:
            logger.warning(f"Alternative scaling method 2 failed: {e}")
        
        logger.error("All alternative scaling methods failed")
        return False
        
    except Exception as e:
        logger.error(f"Alternative scaling approach failed: {e}")
        return False


def scale_up_preserving_existing(target_replicas):
    """Alternative scaling approach that focuses on preserving existing replicas."""
    logger.info(f"Using alternative scaling approach to scale up to {target_replicas} replicas while preserving existing ones")
    
    try:
        import subprocess
        
        # Use docker service update with ultra-conservative settings
        # This approach focuses on adding replicas without affecting existing ones
        cmd = [
            "docker", "service", "update",
            "--replicas", str(target_replicas),
            "--update-parallelism", "1",
            "--update-delay", f"{ROLLING_UPDATE_DELAY * 2}s",  # Double the configured delay for ultra-safety
            "--update-failure-action", "pause",
            "--update-monitor", f"{ROLLING_UPDATE_MONITOR * 2}s",  # Double the configured monitor time
            "--update-max-failure-ratio", "0.0",
            "--update-order", "start-first",
            "--rollback-parallelism", "1",
            "--rollback-delay", f"{ROLLING_UPDATE_DELAY * 2}s",
            "--rollback-failure-action", "pause",
            "--rollback-monitor", f"{ROLLING_UPDATE_MONITOR * 2}s",
            "--rollback-max-failure-ratio", "0.0",
            SERVICE_NAME
        ]
        
        logger.info(f"Executing ultra-conservative scaling command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            logger.info("✓ Ultra-conservative scaling successful!")
            logger.info(f"Output: {result.stdout}")
            
            # Wait and verify replicas are preserved
            import time
            time.sleep(15)  # Wait for the operation to stabilize
            replicas_after = verify_replica_preservation()
            logger.info(f"After ultra-conservative scaling: {replicas_after} replicas running")
            
            return True
        else:
            logger.error(f"Ultra-conservative scaling failed: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Ultra-conservative scaling approach failed: {e}")
        return False


def test_scaling_behavior():
    """Test function to verify scaling behavior without affecting the main loop."""
    logger.info("=== Testing Scaling Behavior ===")
    
    try:
        current_replicas = get_current_replicas()
        logger.info(f"Current replicas: {current_replicas}")
        
        if current_replicas < MAX_REPLICAS:
            # Test scaling up
            target_replicas = current_replicas + 1
            logger.info(f"Testing scale up from {current_replicas} to {target_replicas}")
            
            # Verify replicas before
            replicas_before = verify_replica_preservation()
            logger.info(f"Replicas before scaling: {replicas_before}")
            
            # Scale up
            scale_service(target_replicas)
            
            # Wait and verify
            import time
            time.sleep(5)
            
            replicas_after = verify_replica_preservation()
            logger.info(f"Replicas after scaling: {replicas_after}")
            
            if replicas_after >= replicas_before:
                logger.info("✓ Scale up test PASSED - existing replicas preserved")
            else:
                logger.warning("⚠ Scale up test FAILED - existing replicas were killed")
                
        else:
            logger.info("Already at max replicas, cannot test scale up")
            
    except Exception as e:
        logger.error(f"Scale up test failed: {e}")
    
    logger.info("=== End Scaling Test ===")


def check_active_work():
    """Check if there are any active tasks/jobs running in the containers."""
    try:
        # Since PDF service doesn't expose Prometheus metrics, we'll use Docker container metrics
        # and be very conservative to avoid killing active containers
        
        # Check if any PDF containers have recent CPU activity (last 2 minutes)
        recent_cpu_query = 'sum(rate(container_cpu_usage_seconds_total{container_name=~".*pdf.*"}[2m]))'
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": recent_cpu_query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                recent_cpu = float(data["data"]["result"][0]["value"][1])
                if recent_cpu > 0.001:  # Very low threshold - any recent activity
                    logger.info(f"Active work detected: recent CPU activity {recent_cpu:.6f}")
                    return True
                else:
                    logger.info(f"No recent CPU activity: {recent_cpu:.6f}")
            else:
                logger.warning("No CPU metrics data available from Prometheus")
        else:
            logger.warning(f"Failed to query CPU metrics: status {resp.status_code}")
        
        # Check if there are any PDF containers running at all
        running_containers_query = 'count(container_cpu_usage_seconds_total{container_name=~".*pdf.*"})'
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": running_containers_query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                container_count = int(float(data["data"]["result"][0]["value"][1]))
                if container_count == 0:
                    logger.info("No PDF containers running - no active work")
                    return False
                else:
                    logger.info(f"Found {container_count} PDF containers running")
        
        # Conservative approach: If we can't determine with certainty, assume there might be active work
        logger.info("Cannot determine with certainty if work is active - being conservative")
        return True
        
    except Exception as e:
        logger.warning(f"Failed to check active work: {e}")
        # If we can't check, assume there might be active work (safer)
        return True


def is_any_pdf_processing():
    """Return True if any container is currently processing PDFs (based on Docker container metrics)."""
    try:
        # Since PDF service doesn't expose Prometheus metrics, we'll use Docker container metrics
        # and be very conservative to avoid killing active containers
        
        # Check if any PDF containers have recent CPU activity (last 2 minutes)
        # Using Prometheus container metrics directly
        recent_cpu_query = 'sum(rate(container_cpu_usage_seconds_total{container_name=~".*pdf.*"}[2m]))'
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": recent_cpu_query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                recent_cpu = float(data["data"]["result"][0]["value"][1])
                if recent_cpu > 0.001:  # Very low threshold - any recent activity
                    logger.info(f"Recent CPU activity detected in PDF containers: {recent_cpu:.6f}")
                    return True
                else:
                    logger.info(f"No recent CPU activity in PDF containers: {recent_cpu:.6f}")
            else:
                logger.warning("No CPU metrics data available from Prometheus")
        else:
            logger.warning(f"Failed to query CPU metrics: status {resp.status_code}")
        
        # Additional check: Look for any containers that have been running for less than 5 minutes
        # New containers might still be starting up and shouldn't be killed
        container_uptime_query = 'container_start_time_seconds{container_name=~".*pdf.*"}'
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": container_uptime_query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                current_time = time.time()
                for result in data["data"]["result"]:
                    start_time = float(result["value"][1])
                    uptime_seconds = current_time - start_time
                    if uptime_seconds < 300:  # Less than 5 minutes
                        logger.info(f"PDF container {result['metric'].get('container_name', 'unknown')} started {uptime_seconds:.1f}s ago - too new to kill")
                        return True
        
        # Check if there are any PDF containers running at all
        # If no containers are running, we can't have active work
        running_containers_query = 'count(container_cpu_usage_seconds_total{container_name=~".*pdf.*"})'
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": running_containers_query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                container_count = int(float(data["data"]["result"][0]["value"][1]))
                if container_count == 0:
                    logger.info("No PDF containers running - safe to scale")
                    return False
                else:
                    logger.info(f"Found {container_count} PDF containers running")
        
        # Conservative approach: If we can't determine with certainty, assume there might be active work
        logger.info("Cannot determine with certainty if PDF processing is active - being conservative")
        return True
        
    except Exception as e:
        logger.warning(f"Failed to query PDF processing metrics: {e}")
        # Be conservative if we can't check - assume there might be active work
        logger.info("Returning True due to exception (conservative approach)")
        return True

def check_individual_container_activity():
    """Check which individual containers are active and which are idle."""
    try:
        # Get individual container metrics
        container_cpu_query = 'rate(container_cpu_usage_seconds_total{container_name=~".*pdf.*"}[1m]) by (container_name)'
        container_requests_query = 'rate(http_requests_total{service="pdf-service"}[1m]) by (container_name)'
        
        active_containers = []
        idle_containers = []
        
        # Check CPU usage per container
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": container_cpu_query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                for result in data["data"]["result"]:
                    container_name = result["metric"]["container_name"]
                    cpu_usage = float(result["value"][1])
                    
                    if cpu_usage > 0.05:  # 5% CPU threshold
                        active_containers.append(container_name)
                        logger.debug(f"Container {container_name} is active (CPU: {cpu_usage:.2%})")
                    else:
                        idle_containers.append(container_name)
                        logger.debug(f"Container {container_name} is idle (CPU: {cpu_usage:.2%})")
        
        # Check HTTP requests per container
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": container_requests_query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                for result in data["data"]["result"]:
                    container_name = result["metric"]["container_name"]
                    requests_rate = float(result["value"][1])
                    
                    if requests_rate > 0 and container_name not in active_containers:
                        active_containers.append(container_name)
                        logger.debug(f"Container {container_name} is active (requests: {requests_rate})")
        
        logger.info(f"Active containers: {len(active_containers)}, Idle containers: {len(idle_containers)}")
        return active_containers, idle_containers
        
    except Exception as e:
        logger.warning(f"Failed to check individual container activity: {e}")
        return [], []


def can_partially_scale_down():
    """Check if we can scale down some containers while keeping busy ones."""
    active_containers, idle_containers = check_individual_container_activity()
    
    # If we have idle containers and at least some active ones, we can scale down
    if idle_containers and active_containers:
        logger.info(f"Can scale down {len(idle_containers)} idle containers while keeping {len(active_containers)} active ones")
        return True, idle_containers
    
    # If all containers are idle, we can scale down to minimum
    elif idle_containers and not active_containers:
        logger.info(f"All containers are idle, can scale down to minimum")
        return True, idle_containers
    
    # If all containers are active, don't scale down
    else:
        logger.info("All containers are active, cannot scale down")
        return False, []


def can_safely_scale_down():
    """Check if it's safe to scale down (no active work)."""
    return not check_active_work()


def get_queue_item_age():
    """Get the age of the oldest item in the queue."""
    try:
        # Query for the oldest message timestamp in the queue
        # This requires Kafka metrics that track message timestamps
        oldest_message_query = f'''
        min(
            kafka_log_log_logendoffset{{topic="{os.getenv("TOPIC", "pdf-generation-requests")}"}} * 
            on(partition) group_left 
            kafka_log_log_logstartoffset{{topic="{os.getenv("TOPIC", "pdf-generation-requests")}"}}
        )
        '''
        
        # Alternative: Check consumer lag age
        # This gives us the age of the oldest unprocessed message
        consumer_lag_age_query = f'''
        max(
            kafka_consumergroup_lag_sum{{consumergroup=~"pdf-service.*", topic="{os.getenv("TOPIC", "pdf-generation-requests")}"}} * 
            on(partition) group_left 
            kafka_log_log_logendoffset{{topic="{os.getenv("TOPIC", "pdf-generation-requests")}"}}
        )
        '''
        
        # For now, let's use a simpler approach: check if lag has been stable for too long
        # This indicates items are waiting without being processed
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": consumer_lag_age_query}, timeout=5)
        
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                # This is a simplified approach - in practice you'd need more sophisticated metrics
                # For now, we'll use the lag value as a proxy for wait time
                lag_value = float(data["data"]["result"][0]["value"][1])
                
                # If lag is high and stable, items are likely waiting
                if lag_value > 0:
                    logger.info(f"Queue has {lag_value} items waiting")
                    return lag_value
        
        # Fallback: check if there are any items in the queue at all
        total_lag = get_total_lag()
        if total_lag > 0:
            logger.info(f"Queue has {total_lag} items, checking if they're waiting too long")
            return total_lag
        
        return 0
        
    except Exception as e:
        logger.warning(f"Failed to get queue item age: {e}")
        return 0


def check_queue_wait_time():
    """Check if items in the queue have been waiting too long."""
    queue_age = get_queue_item_age()
    
    if queue_age > 0:
        logger.info(f"Queue items have been waiting (age: {queue_age})")
        # If we have items and they've been waiting, consider scaling up
        return True
    
    return False


def test_prometheus_connectivity():
    """Test if Prometheus is reachable and responding."""
    try:
        logger.info(f"Testing Prometheus connectivity to {PROMETHEUS_URL}")
        
        # Test basic connectivity
        test_resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", params={"query": "up"}, timeout=5)
        logger.info(f"Prometheus connectivity test status: {test_resp.status_code}")
        
        if test_resp.status_code == 200:
            data = test_resp.json()
            logger.info(f"Prometheus response: {data}")
            return True
        else:
            logger.warning(f"Prometheus returned status {test_resp.status_code}")
            return False
            
    except requests.exceptions.ConnectionError as e:
        logger.error(f"Failed to connect to Prometheus: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error testing Prometheus connectivity: {e}")
        return False


def log_current_state():
    """Log the current state of all monitoring metrics for debugging."""
    try:
        logger.info("=== Current State Check ===")
        
        # Check PDF processing
        pdf_processing = is_any_pdf_processing()
        logger.info(f"PDF processing check: {pdf_processing}")
        
        # Check active work
        active_work = check_active_work()
        logger.info(f"Active work check: {active_work}")
        
        # Check individual container activity
        active_containers, idle_containers = check_individual_container_activity()
        logger.info(f"Active containers: {active_containers}")
        logger.info(f"Idle containers: {idle_containers}")
        
        # Check current lag
        total_lag = get_total_lag()
        logger.info(f"Current lag: {total_lag}")
        
        # Check current replicas
        try:
            current_replicas = get_current_replicas()
            logger.info(f"Current replicas: {current_replicas}")
        except Exception as e:
            logger.error(f"Failed to get current replicas: {e}")
        
        logger.info("=== End State Check ===")
        
    except Exception as e:
        logger.error(f"Failed to log current state: {e}")


def log_service_configuration():
    """Log the current Docker service configuration for debugging."""
    try:
        service = docker_client.services.get(SERVICE_NAME)
        spec = service.attrs["Spec"]
        
        logger.info("=== Current Service Configuration ===")
        logger.info(f"Service: {SERVICE_NAME}")
        logger.info(f"Current replicas: {spec['Mode']['Replicated']['Replicas']}")
        
        # Log UpdateConfig if it exists
        if "UpdateConfig" in spec:
            update_config = spec["UpdateConfig"]
            logger.info("UpdateConfig:")
            logger.info(f"  Parallelism: {update_config.get('Parallelism', 'Not set')}")
            logger.info(f"  Delay: {update_config.get('Delay', 'Not set')}")
            logger.info(f"  Failure Action: {update_config.get('FailureAction', 'Not set')}")
            logger.info(f"  Monitor: {update_config.get('Monitor', 'Not set')}")
            logger.info(f"  Max Failure Ratio: {update_config.get('MaxFailureRatio', 'Not set')}")
            logger.info(f"  Order: {update_config.get('Order', 'Not set')}")
        else:
            logger.info("UpdateConfig: Not configured")
        
        # Log RollbackConfig if it exists
        if "RollbackConfig" in spec:
            rollback_config = spec["RollbackConfig"]
            logger.info("RollbackConfig:")
            logger.info(f"  Parallelism: {rollback_config.get('Parallelism', 'Not set')}")
            logger.info(f"  Delay: {rollback_config.get('Delay', 'Not set')}")
            logger.info(f"  Failure Action: {rollback_config.get('FailureAction', 'Not set')}")
            logger.info(f"  Monitor: {rollback_config.get('Monitor', 'Not set')}")
            logger.info(f"  Max Failure Ratio: {rollback_config.get('MaxFailureRatio', 'Not set')}")
            logger.info(f"  Order: {rollback_config.get('Order', 'Not set')}")
        else:
            logger.info("RollbackConfig: Not configured")
        
        logger.info("=" * 50)
        
    except Exception as e:
        logger.error(f"Failed to log service configuration: {e}")


def main():
    global stable_low_count, last_action_time, previous_lag, scale_up_wait_start, scale_up_waiting

    logger.info(f"Starting autoscaler for {SERVICE_NAME}")
    logger.info(f"Prometheus URL: {PROMETHEUS_URL}")
    logger.info(f"Query: {PROM_QUERY}")
    logger.info(f"High lag threshold: {HIGH_LAG_THRESHOLD}")
    logger.info(f"Low lag threshold: {LOW_LAG_THRESHOLD}")
    logger.info(f"Idle timeout: {IDLE_TIMEOUT_SECONDS} seconds")
    # No wait time needed since we scale up immediately
    logger.info(f"Min replicas: {MIN_REPLICAS}, Max replicas: {MAX_REPLICAS}")
    logger.info(f"Using rolling update: {USE_ROLLING_UPDATE}")
    logger.info(f"Rolling update config: parallelism={ROLLING_UPDATE_PARALLELISM}, delay={ROLLING_UPDATE_DELAY}s, monitor={ROLLING_UPDATE_MONITOR}s, failure_ratio={ROLLING_UPDATE_FAILURE_RATIO}")

    # Test Prometheus connectivity on startup
    if not test_prometheus_connectivity():
        logger.error("Prometheus connectivity test failed on startup. Autoscaler will run in conservative mode.")
    else:
        logger.info("Prometheus connectivity test passed on startup.")
    
    # Check and ensure service rolling update support on startup
    logger.info("Checking service rolling update support on startup...")
    ensure_service_rolling_update_support()
    
    # Log current service configuration for debugging
    logger.info("Logging current service configuration for debugging...")
    log_service_configuration()

    while True:
        # Test Prometheus connectivity before each iteration
        prometheus_available = test_prometheus_connectivity()
        if not prometheus_available:
            logger.warning("Prometheus is not available, running in conservative mode")
            logger.info("Skipping scaling decisions due to Prometheus unavailability")
            time.sleep(POLL_INTERVAL)
            continue
            
        total_lag = get_total_lag()
        logger.info(f"Total lag: {total_lag}")

        # Log current state for debugging
        log_current_state()

        now = time.time()
        if now - last_action_time < COOLDOWN_SECONDS:
            logger.debug(f"In cooldown period, skipping scaling decision")
            time.sleep(POLL_INTERVAL)
            continue

        try:
            current_replicas = get_current_replicas()
        except Exception as e:
            logger.error(f"Failed to get current replicas: {e}")
            time.sleep(POLL_INTERVAL)
            continue

        # Scale up due to high lag
        if total_lag > HIGH_LAG_THRESHOLD:
            logger.info(f"High lag detected ({total_lag} > {HIGH_LAG_THRESHOLD}), proceeding with scale up")
            
            # Since CPU metrics are not available, we'll be less conservative and scale up immediately
            # when high lag is detected, rather than waiting for active work to complete
            
            desired = max(current_replicas + 1, SCALE_UP_REPLICAS)
            if desired > current_replicas:
                logger.info(f"Scaling up from {current_replicas} to {desired} replicas due to high lag")
                scale_service(desired)
                
                # Wait for scaling to complete and verify replica preservation
                logger.info("Waiting for scaling operation to complete and verifying replica preservation...")
                if wait_for_scaling_completion(desired):
                    logger.info(f"✓ Scaling to {desired} replicas completed successfully with replica preservation")
                else:
                    logger.warning(f"⚠️  Scaling to {desired} replicas completed but replica preservation verification failed")
                
                last_action_time = now
                stable_low_count = 0
            time.sleep(POLL_INTERVAL)
            continue


        if LOW_LAG_THRESHOLD <= total_lag <= HIGH_LAG_THRESHOLD:
            logger.debug(f"In hysteresis zone ({LOW_LAG_THRESHOLD} <= {total_lag} <= {HIGH_LAG_THRESHOLD}), no action")
            stable_low_count = 0
                    # No scale up waiting logic needed since we scale up immediately
            time.sleep(POLL_INTERVAL)
            continue

        # Scale down logic (simplified): if lag stays low for a few intervals, step down by 1
        if total_lag < LOW_LAG_THRESHOLD:
            stable_low_count += 1
            # No scale up waiting logic needed
            logger.info(f"Low lag detected ({total_lag} < {LOW_LAG_THRESHOLD}), stable count: {stable_low_count}/{DOWNSCALE_STABLE_COUNT}")
            if stable_low_count >= DOWNSCALE_STABLE_COUNT and current_replicas > MIN_REPLICAS:
                # Test Prometheus connectivity before making scaling decisions
                try:
                    test_resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", params={"query": "up"}, timeout=5)
                    if test_resp.status_code != 200:
                        logger.warning(f"Prometheus connectivity test failed with status {test_resp.status_code}")
                        logger.info("Skipping scale down due to Prometheus connectivity issues (conservative approach)")
                        stable_low_count = 0
                        time.sleep(POLL_INTERVAL)
                        continue
                except Exception as e:
                    logger.warning(f"Prometheus connectivity test failed: {e}")
                    logger.info("Skipping scale down due to Prometheus connectivity issues (conservative approach)")
                    stable_low_count = 0
                    time.sleep(POLL_INTERVAL)
                    continue
                
                # Double-check: Only scale down if no containers are actively processing PDFs
                pdf_processing = is_any_pdf_processing()
                active_work = check_active_work()
                
                if not pdf_processing and not active_work:
                    logger.info("No active work detected by both checks, proceeding with scale down")
                    desired = current_replicas - 1
                    scale_service(desired)
                    last_action_time = now
                    stable_low_count = 0
                else:
                    if pdf_processing:
                        logger.info("Active PDF processing detected, skipping scale down")
                    if active_work:
                        logger.info("Active work detected by general check, skipping scale down")
                    stable_low_count = 0

        # Update previous lag for next iteration
        previous_lag = total_lag
        logger.info(f"Previous lag: {previous_lag}")

        time.sleep(POLL_INTERVAL)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        logger.info("Running in test mode")
        test_scaling_behavior()
    else:
        main()
