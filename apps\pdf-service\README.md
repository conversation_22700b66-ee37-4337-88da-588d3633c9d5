# PDF Service - Serviço de Geração de Relatórios em PDF

## 📋 Visão Geral

Este é um serviço especializado em geração de relatórios em PDF utilizando **Puppeteer** e **React Server-Side Rendering (SSR)**. O serviço converte dados estruturados de relatórios em documentos PDF profissionais com cabeçalho, rodapé e formatação personalizada.

## 🏗️ Arquitetura da Aplicação

### Estrutura de Diretórios

```
pdf-service/
├── assets/                    # Recursos estáticos (imagens, ícones)
├── components/               # Componentes React para renderização
│   ├── pdf-components/      # Componentes base para PDF
│   ├── strategies/          # Estratégias de renderização por seção
│   ├── Header.tsx           # Cabeçalho do PDF
│   ├── Footer.tsx           # Rodapé do PDF
├── config/                  # Configurações da aplicação
├── controllers/             # Controladores da API
├── helpers/                 # Funções auxiliares
├── middlewares/             # Middlewares do Express
├── service/                 # Serviços de geração HTML/PDF
├── utils/                   # Utilitários (logger)
├── __tests__/              # Testes e dados de exemplo
└── dist/                   # Código compilado
```

## 🔧 Tecnologias e Dependências

### Dependências Principais

| Dependência | Versão | Função |
|-------------|--------|---------|
| **Express** | ^5.1.0 | Framework web para API REST |
| **Puppeteer** | ^24.11.2 | Automação do Chrome para geração de PDF |
| **React** | ^19.1.0 | Renderização de componentes |
| **React-DOM** | ^19.1.0 | Server-Side Rendering (SSR) |
| **CORS** | ^2.8.5 | Controle de acesso entre origens |
| **dotenv** | ^16.4.7 | Gerenciamento de variáveis de ambiente |

### Dependências de Desenvolvimento

- **TypeScript** ^5.8.3 - Tipagem estática
- **@types/** - Definições de tipos
- **copyfiles** ^2.4.1 - Cópia de arquivos estáticos
- **ts-node** ^10.9.2 - Execução direta de TypeScript

## 🚀 Funcionamento da Aplicação

### 1. Inicialização (main.ts)

```typescript
// Inicializa o browser Puppeteer
browser = await puppeteer.launch({
  args: ['--no-sandbox', '--disable-setuid-sandbox']
});

// Inicia o servidor Express com a instância do browser
initializeServer(browser, serverConfig);
```

### 2. Servidor Express (server.ts)

- **Porta**: 1127 (configurável)
- **Endpoints**:
  - `GET /health` - Health check
  - `POST /pdf/reports` - Geração de PDF

### 3. Fluxo de Geração de PDF

#### Etapa 1: Recepção da Requisição
O controlador `ReportsPDFController` recebe:
- `sections[]` - Array de seções do relatório
- `metadata` - Metadados do relatório
- `browserRef` - Instância do browser Puppeteer

#### Etapa 2: Geração de HTML (`GenerateReportsHtml`)
1. **Validação** dos dados de entrada
2. **Renderização React SSR** usando `renderToStaticMarkup()`
3. **Geração separada** de:
   - Cabeçalho com logo e informações do relatório
   - Conteúdo principal com seções
   - Rodapé com numeração de páginas

#### Etapa 3: Conversão para PDF (`GenerateReportsPdf`)
1. **Criação de nova página** no browser
2. **Configuração do viewport** (794x1123 - A4)
3. **Injeção do HTML** com `page.setContent()`
4. **Geração do PDF** com configurações otimizadas:
   - Formato A4
   - Margens personalizadas
   - Cabeçalho e rodapé fixos
   - Background de impressão habilitado

## 📊 Sistema de Estratégias de Renderização

### Padrão Strategy Pattern

O sistema utiliza o padrão Strategy para renderizar diferentes tipos de seções baseado no tipo de relatório:

```typescript
type ReportType = 'cpf' | 'telefone' | 'email' | 'cnpj';
type PrintRenderer = (section: ReportSection) => React.ReactNode;
```

### Mapeamento de Estratégias

#### Seções Comuns (todos os tipos)
- **Emails** - `RenderPrintEmail`
- **Telefones** - `RenderPrintTelefone`
- **Endereços** - `RenderPrintEndereco`

#### Seções CPF/Telefone/Email
- **Dados Pessoais** - `RenderPrintDadosPessoais`
- **Parentes** - `RenderPrintParentes`
- **Possíveis Contatos** - `RenderPrintPossiveisContatos`
- **Redes Sociais** - `RenderPrintRedesSociais`

#### Seções CPF/CNPJ
- **Processos** - `RenderPrintProcessos`
- **Sócios** - `RenderPrintSocios`
- **Recursos Públicos** - `RenderPrintRecursosPublicos`
- **Diários Oficiais** - `RenderPrintDiariosOficiais`

#### Seções Específicas CPF
- **Mandados de Prisão** - `RenderPrintMandados`
- **Vínculos Empregatícios** - `RenderPrintVinculosEmpregaticios`
- **Serviço Público** - `RenderPrintServicosPublicos`
- **Filiação Partidária** - `RenderPrintFiliacaoPartidaria`

#### Seções Específicas CNPJ
- **Juntas Comerciais** - `RenderPrintJuntasComerciais`

## 🎨 Sistema de Estilos e Layout

### Configuração de Página
- **Formato**: A4 (210 × 297 mm)
- **Viewport**: 794 × 1123 pixels (96 DPI)
- **Margens**:
  - Superior: 80px (espaço para cabeçalho)
  - Inferior: 70px (espaço para rodapé)
  - Laterais: 20px

### Componentes Visuais

#### Cabeçalho (`Header.tsx`)
- Logo da aplicação
- Título do relatório
- Ícone do tipo de relatório (CPF/CNPJ)
- Valor pesquisado
- Grafismo decorativo

#### Rodapé (`Footer.tsx`)
- Numeração de páginas ("X de Y")
- Grafismo decorativo em branco
- Background colorido (#889EA3)

## 🔄 Gerenciamento de Memória

### Otimizações Implementadas
- **Detecção de erro de heap**: Monitora erros de memória
- **Restart automático**: Container reinicia em caso de estouro de memória
- **Fechamento de páginas**: Páginas do Puppeteer são fechadas após uso
- **Graceful shutdown**: Fechamento adequado do browser em SIGINT/SIGTERM

### Configurações Docker
```yaml
environment:
  NODE_OPTIONS: "--max-old-space-size=1024"
deploy:
  resources:
    limits:
      memory: "1280M"
    reservations:
      memory: 256M
```

## 📝 Estrutura de Dados

### ReportSection
```typescript
interface ReportSection {
  title: string;           // Título da seção
  subtitle: string;        // Subtítulo
  subsection: string;      // Subseção
  source: string[];        // Fontes dos dados
  data_count: number;      // Quantidade de registros
  is_deleted?: boolean;    // Flag de exclusão
  data: Array<Record<string, any>>; // Dados da seção
}
```

### ReportMetadata
```typescript
interface ReportMetadata {
  report_id: string;
  report_type: string;     // 'cpf' | 'cnpj' | 'email' | 'telefone'
  report_name: string;
  subject_name: string;
  creation_at: string;
  // ... outros metadados
}
```

## 🐳 Containerização

### Dockerfile
- **Base**: Node.js 24
- **Gerenciador**: pnpm 10.5.1
- **Chrome**: Instalação do Google Chrome estável
- **Usuário**: nodejs (não-root) para segurança
- **Fontes**: Suporte a múltiplos charsets (CJK, emoji, tailandês)

### Docker Compose
- **Porta**: 3002:1127 (externa:interna)
- **Restart**: always
- **Recursos limitados**: CPU e memória controlados

## 🚀 Como Executar

### Desenvolvimento
```bash
# Instalar dependências
pnpm install

# Compilar TypeScript
pnpm build

# Executar em modo desenvolvimento
pnpm dev
```

### Produção com Docker
```bash
# Construir e executar
docker-compose up --build

# Apenas executar
docker-compose up
```

## 📊 Monitoramento e Logs

### Sistema de Logging
- **Biblioteca**: Logger customizado (`utils/logger.ts`)
- **Níveis**: info, debug, error
- **Métricas**: Tempo de processamento, tamanho dos arquivos
- **Contexto**: Detalhes da requisição e geração

### Health Check
- **Endpoint**: `GET /health`
- **Resposta**: Status 200 "OK"
- **Uso**: Monitoramento de containers

## 🔒 Segurança

### Configurações Puppeteer
- `--no-sandbox`: Desabilita sandbox (necessário em containers)
- `--disable-setuid-sandbox`: Desabilita setuid sandbox
- **Usuário não-root**: Execução com usuário nodejs

### CORS
- **Origem**: Permitida para todas (`*`)
- **Métodos**: GET, POST, OPTIONS
- **Headers**: Content-Type, Connect-Protocol-Version

## 🎯 Casos de Uso

### Tipos de Relatórios Suportados
- **SNAP**: Snap Reports