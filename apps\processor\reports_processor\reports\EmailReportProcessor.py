from typing import Dict

from .BaseReportProcessor import BaseReportProcessor, ReportTypeConfig
from ..FormatFrontFormat import VinculoSection
from ..EntityExtractor import VinculoConfig
from ..constants import OtherResultDataTypes


class EmailReportProcessor(BaseReportProcessor):
    """Processor for Email reports"""

    def get_config(self) -> ReportTypeConfig:
        return ReportTypeConfig(
            do_filter_doc='Email',
            do_filter_name='Email',
            do_doc = {'title': 'Diários Oficiais - Email'},  # TODO: pode dar merda
            dados_pessoais={'title': 'Dados Pessoais'}, # TODO: pode dar merda - alterar conforme main data?
            possiveis_pessoas_relacionadas={'title': 'Possíveis Pessoas Relacionadas'},
            possiveis_empresas_relacionadas={'title': 'Empresas Relacionadas'},
            enabled_sections=[
                VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS, VinculoSection.POSSIVEIS_EMPRESAS_RELACIONADAS,
                VinculoSection.PHONES, VinculoSection.EMAILS, VinculoSection.ENDERECOS,
                VinculoSection.PARENTES, VinculoSection.OUTROS_CONTATOS, VinculoSection.IMAGENS, VinculoSection.NOMES_USUARIO,
                VinculoSection.OUTRAS_URLS, VinculoSection.REDES_SOCIAIS, VinculoSection.PAIS

            ]
        )

    def get_custom_vinculos(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[
        str, VinculoConfig]:
        # Email reports might have simpler vinculo requirements
        return {}

    def adjust_metadata(self, processed_result, metadata):
        self._adjust_metadata_personal_data(processed_result, metadata)
        self._adjust_metadata_age(processed_result, metadata)
        self._adjust_metadata_for_multiple_results(processed_result, metadata)


    def make_vinculo_possiveis_empresas(self) -> VinculoConfig:
        cfg = super().make_vinculo_possiveis_empresas()
        cfg.other_result_keys = [OtherResultDataTypes.other]
        return cfg
