import MasonryLayout from "react-layout-masonry"
import { AnimatedFilledButtonSkeleton, ReportCardSkeleton } from "~/components/ReportCardSkeleton"

interface ReportListSkeletonProps {
  count?: number
}

export function ReportListSkeleton({ count = 11 }: ReportListSkeletonProps) {
  const columnsBreakpoints = {
    346: 1,
    628: 2,
    930: 3,
    1232: 4,
    1534: 5,
    1836: 6,
    2138: 7,
    2440: 8,
    2742: 9,
  }

  return (
    <section className="flex-1 w-full overflow-y-auto overflow-x-hidden min-h-0 max-h-[calc(100vh-232px)] px-8 pt-2 pb-8">
      <MasonryLayout columns={columnsBreakpoints} gap={20} className="relative">
        <AnimatedFilledButtonSkeleton />
        {Array.from({ length: count }).map((_, index) => (
          <div key={index} className="z-[1]">
            <ReportCardSkeleton />
          </div>
        ))}
      </MasonryLayout>
    </section>
  )
}