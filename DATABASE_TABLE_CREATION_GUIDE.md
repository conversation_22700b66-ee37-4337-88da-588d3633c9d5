# Database Table Creation & Column Addition Guide

## Overview

This guide provides comprehensive instructions for creating new tables and adding columns in the **reports schema** following established patterns and best practices. All tables in this application must be created in the `reports` schema to maintain organization and avoid conflicts with other systems (like Keycloak).

**📖 Related Guides:**
- For adding columns to existing tables: See `COLUMN_ADDITION_GUIDE.md`
- For migration troubleshooting: See database helper scripts in `database/`

## Quick Reference

### Essential Requirements
- ✅ **Schema**: All tables MUST use `schema='reports'`
- ✅ **Primary Key**: Use UUID with `{table_name}_id` naming convention
- ✅ **Timestamps**: Include `created_at` (required), `modified_at` (recommended)
- ✅ **Foreign Keys**: Reference `reports.{table}.{column}_id`
- ✅ **Indexes**: Add appropriate indexes for performance
- ✅ **Migration**: Create Alembic migration for schema changes

## Step-by-Step Process

### 1. Planning Phase

Before creating a table, consider:

```python
# Questions to ask:
# 1. What is the table's primary purpose?
# 2. How does it relate to existing tables?
# 3. What are the key columns and their types?
# 4. What queries will be common?
# 5. What indexes are needed for performance?
```

**Use the table creation checklist:**

```python
from database.table_creation_guide import get_table_creation_checklist

checklist = get_table_creation_checklist()
for category, items in checklist.items():
    print(f"\n{category.upper()}:")
    for item in items:
        print(f"  □ {item}")
```

### 2. Model Creation

#### Standard Table Pattern

```python
# apps/backend/models/example_model.py
from sqlalchemy import Column, DateTime, Text, BigInteger, Index, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from models.base import Base

class ExampleTable(Base):
    __tablename__ = 'example_table'
    __table_args__ = (
        # Indexes
        Index("idx_example_table_user_id", "user_id"),
        Index("idx_example_table_status", "status"),
        Index("idx_example_table_data", "data", postgresql_using="gin"),  # For JSONB
        {"schema": "reports"}  # REQUIRED: Must be last in tuple
    )

    # Primary Key (UUID with standard naming)
    example_table_id = Column(UUID, primary_key=True, server_default=func.gen_random_uuid())

    # Foreign Keys
    user_id = Column(UUID, ForeignKey("reports.users.user_id"), nullable=False, index=True)
    organization_id = Column(UUID, ForeignKey("reports.organizations.organization_id"), nullable=True)

    # Standard Timestamps
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.timezone('UTC', func.now()))
    modified_at = Column(DateTime(timezone=True), nullable=False, server_default=func.timezone('UTC', func.now()))

    # Business Columns
    name = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    status = Column(Text, nullable=False, server_default='active')
    data = Column(JSONB, nullable=True)  # For flexible JSON storage

    # Relationships
    user = relationship("Users", back_populates="example_tables")
    organization = relationship("Organizations", back_populates="example_tables")

    def __repr__(self):
        return f"<ExampleTable(example_table_id={self.example_table_id}, name={self.name})>"
```

#### Junction Table Pattern

```python
# For many-to-many relationships
class UserExampleTable(Base):
    __tablename__ = 'user_example_table'
    __table_args__ = (
        {"schema": "reports"}
    )

    user_id = Column(UUID, ForeignKey("reports.users.user_id"), primary_key=True)
    example_table_id = Column(UUID, ForeignKey("reports.example_table.example_table_id"), primary_key=True)
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.timezone('UTC', func.now()))
    role = Column(Text, nullable=False, server_default='member')  # Additional data

    # Relationships
    user = relationship("Users")
    example_table = relationship("ExampleTable")
```

#### Lookup Table Pattern

```python
# For reference/lookup data
class StatusLookup(Base):
    __tablename__ = 'status_lookup'
    __table_args__ = (
        Index("idx_status_lookup_code", "code", unique=True),
        Index("idx_status_lookup_active", "is_active"),
        {"schema": "reports"}
    )

    id = Column(Integer, primary_key=True)
    code = Column(String(50), nullable=False, unique=True)
    name = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, nullable=False, server_default='true')
    sort_order = Column(Integer, nullable=True)
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.timezone('UTC', func.now()))
```

### 3. Update Model Imports

Add your new model to `models/__init__.py`:

```python
# models/__init__.py
# Add to appropriate dependency level
from models.example_model import ExampleTable

# Add to __all__ list
__all__ = [
    # ... existing models ...
    "ExampleTable"
]
```

### 4. Create Migration

#### Option A: Using Templates (Recommended)

```python
from database.migration_templates import generate_migration_file

# Generate migration file
migration_content = generate_migration_file(
    table_name="example_table",
    template_type="standard",  # or "lookup", "junction", "log"
    previous_revision="001"  # Get from latest migration
)

# Save to alembic/versions/002_add_example_table.py
```

#### Option B: Manual Migration

Create file `alembic/versions/002_add_example_table.py`:

```python
"""Add example_table

Revision ID: 002
Revises: 001
Create Date: 2025-09-25 10:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None

def upgrade() -> None:
    op.create_table('example_table',
        sa.Column('example_table_id', postgresql.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('user_id', postgresql.UUID(), nullable=False),
        sa.Column('organization_id', postgresql.UUID(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=False),
        sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=False),
        sa.Column('name', sa.Text(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('status', sa.Text(), nullable=False, server_default='active'),
        sa.Column('data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['organization_id'], ['reports.organizations.organization_id']),
        sa.ForeignKeyConstraint(['user_id'], ['reports.users.user_id']),
        sa.PrimaryKeyConstraint('example_table_id'),
        schema='reports'
    )

    # Create indexes
    op.create_index('idx_example_table_user_id', 'example_table', ['user_id'], unique=False, schema='reports')
    op.create_index('idx_example_table_status', 'example_table', ['status'], unique=False, schema='reports')
    op.create_index('idx_example_table_data', 'example_table', ['data'], unique=False, schema='reports', postgresql_using='gin')

def downgrade() -> None:
    op.drop_table('example_table', schema='reports')
```

### 5. Create Pydantic Schemas

Create corresponding schemas in `schemas/example_schema.py`:

```python
from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel

class ExampleTableBase(BaseModel):
    name: str
    description: Optional[str] = None
    status: str = 'active'
    data: Optional[dict] = None

class ExampleTableCreate(ExampleTableBase):
    user_id: UUID
    organization_id: Optional[UUID] = None

class ExampleTableUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    data: Optional[dict] = None

class ExampleTableInDB(ExampleTableBase):
    example_table_id: UUID
    user_id: UUID
    organization_id: Optional[UUID]
    created_at: datetime
    modified_at: datetime

    class Config:
        from_attributes = True

class ExampleTableResponse(ExampleTableInDB):
    pass
```

### 6. Validation and Testing

#### Validate Schema Design

```python
from database.schema_validator import SchemaValidator
from database.db import engine

validator = SchemaValidator(engine)
result = validator.validate_table("example_table")

if not result.is_valid:
    print("❌ Validation failed:")
    for error in result.errors:
        print(f"  - {error}")
else:
    print("✅ Table design is valid!")
```

#### Test Migration

```bash
# Test migration (if you have alembic CLI set up)
alembic upgrade head  # Apply migration
alembic downgrade -1  # Test rollback
alembic upgrade head  # Reapply
```

### 7. Performance Considerations

#### Index Strategy

```python
# Common index patterns:

# 1. Foreign key indexes (automatic with our pattern)
Index("idx_table_user_id", "user_id")

# 2. Query columns
Index("idx_table_status", "status")
Index("idx_table_created_at", "created_at")

# 3. Composite indexes for common queries
Index("idx_table_user_status", "user_id", "status")
Index("idx_table_user_created", "user_id", "created_at")

# 4. JSONB columns (use GIN)
Index("idx_table_data", "data", postgresql_using="gin")

# 5. Text search (if needed)
Index("idx_table_name_search", "name", postgresql_using="gin",
      postgresql_ops={"name": "gin_trgm_ops"})
```

## Common Patterns Reference

### 1. User-Related Tables

Tables that store user-specific data:

```python
# Always include
user_id = Column(UUID, ForeignKey("reports.users.user_id"), nullable=False)

# Often include
organization_id = Column(UUID, ForeignKey("reports.organizations.organization_id"), nullable=True)

# Common indexes
Index("idx_table_user_id", "user_id")
Index("idx_table_user_org", "user_id", "organization_id")
```

### 2. Audit/Log Tables

Tables for tracking changes:

```python
# Standard audit columns
user_id = Column(UUID, ForeignKey("reports.users.user_id"), nullable=True)
action = Column(Text, nullable=False)  # 'create', 'update', 'delete'
entity_type = Column(Text, nullable=False)  # 'user', 'report', etc.
entity_id = Column(Text, nullable=False)  # UUID as string
old_values = Column(JSONB, nullable=True)
new_values = Column(JSONB, nullable=True)
ip_address = Column(String(45), nullable=True)
user_agent = Column(Text, nullable=True)
```

### 3. Configuration Tables

Static or semi-static configuration data:

```python
# Use integer primary keys for lookup tables
id = Column(Integer, primary_key=True)
code = Column(String(50), nullable=False, unique=True)
name = Column(Text, nullable=False)
is_active = Column(Boolean, nullable=False, server_default='true')
sort_order = Column(Integer, nullable=True)
```

### 4. Junction Tables

Many-to-many relationships:

```python
# Composite primary key
table1_id = Column(UUID, ForeignKey("reports.table1.table1_id"), primary_key=True)
table2_id = Column(UUID, ForeignKey("reports.table2.table2_id"), primary_key=True)

# Additional relationship data
role = Column(Text, nullable=False, server_default='member')
created_at = Column(DateTime(timezone=True), server_default=func.timezone('UTC', func.now()))
```

## Advanced Patterns

### 1. Enum Types

```python
# Define enum in models/enums.py
from enum import Enum
class StatusType(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"

# Use in model
from sqlalchemy.dialects.postgresql import ENUM as PGEnum
from models.enums import StatusType

status = Column(
    PGEnum(StatusType, name="status_type", create_type=True),
    nullable=False,
    server_default='active'
)
```

### 2. JSONB with Structure

```python
# For structured JSON data
data = Column(JSONB, nullable=True)

# Add GIN index for queries
Index("idx_table_data", "data", postgresql_using="gin")

# Example queries:
# session.query(Table).filter(Table.data['key'].astext == 'value')
# session.query(Table).filter(Table.data.contains({'key': 'value'}))
```

### 3. Soft Deletes

```python
# Instead of hard deletes
deleted_at = Column(DateTime(timezone=True), nullable=True)
is_deleted = Column(Boolean, nullable=False, server_default='false')

# Add to queries
def get_active_records():
    return session.query(Table).filter(Table.is_deleted == False)
```

## Testing Your Table

### 1. Basic CRUD Operations

```python
async def test_table_operations():
    from database.db import get_db

    async with get_db() as db:
        # Create
        new_record = ExampleTable(
            user_id=user_uuid,
            name="Test Record",
            status="active"
        )
        db.add(new_record)
        await db.commit()

        # Read
        record = await db.get(ExampleTable, new_record.example_table_id)
        assert record.name == "Test Record"

        # Update
        record.name = "Updated Record"
        await db.commit()

        # Delete
        await db.delete(record)
        await db.commit()
```

### 2. Relationship Testing

```python
async def test_relationships():
    async with get_db() as db:
        # Test foreign key constraints
        # Test relationship loading
        user = await db.get(Users, user_id)
        example_tables = user.example_tables  # Should work if relationship defined
```

## Troubleshooting

### Common Issues

1. **Schema not specified**: Always include `{"schema": "reports"}` in `__table_args__`

2. **Foreign key errors**: Ensure referenced tables exist and use correct schema prefix:
   ```python
   # Correct
   ForeignKey("reports.users.user_id")
   # Incorrect
   ForeignKey("users.user_id")
   ```

3. **Index naming conflicts**: Use consistent naming:
   ```python
   # Pattern: idx_{table}_{column(s)}
   Index("idx_example_table_user_id", "user_id")
   ```

4. **Migration failures**: Test migrations in development:
   ```bash
   # Always test both directions
   alembic upgrade head
   alembic downgrade -1
   alembic upgrade head
   ```

### Validation Tools

Use the provided utilities to validate your table design:

```python
from database.schema_validator import validate_model_definition
from models.example_model import ExampleTable

result = validate_model_definition(ExampleTable)
if not result.is_valid:
    for error in result.errors:
        print(f"❌ {error}")
    for warning in result.warnings:
        print(f"⚠️  {warning}")
```

## Best Practices Summary

### ✅ DO:
- Use `reports` schema for all application tables
- Use UUID primary keys with `{table_name}_id` naming
- Include `created_at` timestamp
- Add appropriate indexes for common queries
- Use foreign key constraints
- Create Alembic migrations
- Test migrations up and down
- Validate schema design

### ❌ DON'T:
- Create tables without schema specification
- Use auto-incrementing integers for primary keys (except lookup tables)
- Skip foreign key constraints
- Ignore indexing strategy
- Make direct database changes without migrations
- Use reserved words as column names
- Skip relationship definitions in models

### 🔧 TOOLS AVAILABLE:

- `database/migration_templates.py` - Templates for common table types
- `database/schema_validator.py` - Validate table design
- `database/table_creation_guide.py` - Utilities and patterns
- This guide - Comprehensive documentation

## Quick Commands

```bash
# Create new model file
touch apps/backend/models/new_table_model.py

# Add model to imports
# Edit apps/backend/models/__init__.py

# Create migration
# Use templates or create manually in alembic/versions/

# Test migration
alembic upgrade head
alembic downgrade -1
alembic upgrade head

# Validate schema
python -c "
from database.schema_validator import SchemaValidator
from database.db import engine
validator = SchemaValidator(engine)
print(validator.generate_validation_report())
"
```

This completes the comprehensive guide for creating tables in your reports schema. Follow these patterns to ensure consistency and maintainability across your database schema.