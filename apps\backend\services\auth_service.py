import logging
import asyncio
import time

logger = logging.getLogger(__name__)
from jose import jwt
from urllib.parse import quote
from fastapi import Request, Response
import httpx

from core.jwt_utils import verify_jwt, refresh_access_token
from core.config import settings
from services.redis_service import redis_service
from exceptions.business_exceptions import UserNotAuthenticatedError, FailRefreshTokenError, TokenExchangeFailedError
from core.jwt_utils import TokenVerificationFailedError

# Lock timeout settings
LOCK_ACQUIRE_TIMEOUT = 30  # Maximum seconds to wait for lock acquisition



# Extended outage protection settings
EXTENDED_OUTAGE_THRESHOLD = 10  # After 10 failures, consider it an extended outage
EXTENDED_OUTAGE_GRACE_PERIOD = 3600  # 1 hour - how long to keep users connected during outage


def _extract_user_id_from_token(token: str) -> str:
    """
    Extract user ID from JWT token to create user-specific cache keys.
    
    Args:
        token: JWT token (access or refresh token)
        
    Returns:
        User ID string or 'unknown' if extraction fails
        
    Raises:
        None - always returns a string to prevent cache key errors
    """
    try:
        # Decode token without verification to extract claims
        unverified_claims = jwt.get_unverified_claims(token)
        user_id = unverified_claims.get('sub') or unverified_claims.get('user_id') or 'unknown'
        logger.info("[_extract_user_id_from_token] Successfully extracted user_id: %s from token", user_id)
        return str(user_id)
    except Exception as e:
        logger.warning("[_extract_user_id_from_token] Failed to extract user_id from token: %s. Using 'unknown'", str(e))
        return 'unknown'





def _validate_cached_tokens_belong_to_user(cached_access_token: str, expected_user_id: str) -> bool:
    """
    Validate that cached tokens belong to the expected user.
    
    Args:
        cached_access_token: The cached access token to validate
        expected_user_id: The user ID that should own the token
        
    Returns:
        True if tokens belong to the expected user, False otherwise
    """
    try:
        # Extract user ID from the cached access token
        cached_user_id = _extract_user_id_from_token(cached_access_token)
        
        # Compare with expected user ID
        is_valid = cached_user_id == expected_user_id
        
        if is_valid:
            logger.info("[_validate_cached_tokens_belong_to_user] Cached tokens validated for user: %s", expected_user_id)
        else:
            logger.warning("[_validate_cached_tokens_belong_to_user] Cached tokens do not belong to expected user. Expected: %s, Found: %s", 
                          expected_user_id, cached_user_id)
        
        return is_valid
    except Exception as e:
        logger.error("[_validate_cached_tokens_belong_to_user] Failed to validate cached tokens: %s", str(e))
        return False


async def _get_cached_tokens(user_id: str):
    """Get cached tokens if they exist and are still valid for the specific user."""
    logger.info("[_get_cached_tokens] Checking Redis cache for user: %s", user_id)
    
    try:
        cached_tokens = await redis_service.get_cached_tokens(user_id)
        
        if cached_tokens:
            access_token, refresh_token = cached_tokens
            
            # Validate that cached tokens belong to the expected user
            if user_id and user_id != 'unknown':
                if not _validate_cached_tokens_belong_to_user(access_token, user_id):
                    logger.warning("[_get_cached_tokens] Cached tokens do not belong to expected user. Removing from cache.")
                    # Clear the invalid cache entry
                    await redis_service.clear_user_cache(user_id)
                    return None
            
            logger.info("[_get_cached_tokens] Returning valid cached tokens from Redis for user. access_token_length: %s, refresh_token_length: %s",
                        len(access_token) if access_token else 0, len(refresh_token) if refresh_token else 0)
            return access_token, refresh_token
        else:
            logger.info("[_get_cached_tokens] No cached tokens found in Redis for user: %s", user_id)
            return None
            
    except Exception as e:
        logger.error("[_get_cached_tokens] Error retrieving cached tokens from Redis for user %s: %s", user_id, str(e))
        return None


async def _update_cached_tokens(user_id: str, new_access_token: str, new_refresh_token: str, 
                               ttl_seconds: int = None) -> bool:
    """Update cached tokens by replacing the existing cache entry with new tokens."""
    logger.info("[_update_cached_tokens] Updating cached tokens in Redis. user_id: %s, new_access_token_length: %s, new_refresh_token_length: %s", 
                user_id, len(new_access_token) if new_access_token else 0, len(new_refresh_token) if new_refresh_token else 0)
    
    try:
        logger.info("[_update_cached_tokens] Calling redis_service.update_user_tokens...")
        success = await redis_service.update_user_tokens(
            user_id, 
            new_access_token, 
            new_refresh_token, 
            ttl_seconds
        )
        
        if success:
            logger.info("[_update_cached_tokens] Successfully updated tokens in Redis for user: %s", user_id)
            logger.info("[_update_cached_tokens] Updated token details - new_access_token_length: %s, new_refresh_token_length: %s",
                        len(new_access_token) if new_access_token else 0, len(new_refresh_token) if new_refresh_token else 0)
            
            # Verify the tokens were actually stored by trying to retrieve them
            logger.info("[_update_cached_tokens] Verifying tokens were stored by retrieving from Redis...")
            verification_tokens = await redis_service.get_cached_tokens(user_id)
            if verification_tokens:
                logger.info("[_update_cached_tokens] ✅ Verification successful - tokens found in Redis after update")
                return True
            else:
                logger.error("[_update_cached_tokens] ❌ Verification failed - tokens NOT found in Redis after update")
                return False
        else:
            logger.error("[_update_cached_tokens] Failed to update tokens in Redis for user: %s", user_id)
            return False
            
    except Exception as e:
        logger.error("[_update_cached_tokens] Error updating tokens in Redis for user %s: %s", user_id, str(e))
        logger.error("[_update_cached_tokens] Exception type: %s", type(e).__name__)
        import traceback
        logger.error("[_update_cached_tokens] Full traceback: %s", traceback.format_exc())
        return False



async def _acquire_refresh_lock(user_id: str) -> bool:
    """Acquire a distributed lock for the given user to prevent concurrent refresh attempts."""
    logger.info("[_acquire_refresh_lock] Requesting distributed lock for user: %s", user_id)
    
    try:
        lock_acquired = await redis_service.acquire_lock(user_id, LOCK_ACQUIRE_TIMEOUT)
        
        if lock_acquired:
            logger.info("[_acquire_refresh_lock] Successfully acquired distributed lock for user: %s", user_id)
        else:
            logger.info("[_acquire_refresh_lock] Failed to acquire distributed lock for user: %s (already locked by another worker)", user_id)
        
        return lock_acquired
        
    except Exception as e:
        logger.error("[_acquire_refresh_lock] Error acquiring distributed lock for user %s: %s", user_id, str(e))
        return False


async def _release_refresh_lock(user_id: str) -> bool:
    """Release the distributed lock for the given user."""
    logger.info("[_release_refresh_lock] Releasing distributed lock for user: %s", user_id)
    
    try:
        lock_released = await redis_service.release_lock(user_id)
        
        if lock_released:
            logger.info("[_release_refresh_lock] Successfully released distributed lock for user: %s", user_id)
        else:
            logger.warning("[_release_refresh_lock] Failed to release distributed lock for user: %s", user_id)
        
        return lock_released
        
    except Exception as e:
        logger.error("[_release_refresh_lock] Error releasing distributed lock for user %s: %s", user_id, str(e))
        return False


async def _is_refresh_failing(user_id: str) -> bool:
    """Check if refresh token has failed recently to avoid hammering for the specific user."""
    try:
        is_failing = await redis_service.is_refresh_failing(user_id)
        
        if is_failing:
            logger.warning("[_is_refresh_failing] Refresh token is failing for user: %s", user_id)
        else:
            logger.info("[_is_refresh_failing] Refresh token is not failing for user: %s", user_id)
        
        return is_failing
        
    except Exception as e:
        logger.error("[_is_refresh_failing] Error checking refresh failure for user %s: %s", user_id, str(e))
        return False


async def _record_refresh_failure(user_id: str):
    """Record a refresh failure to prevent repeated attempts for the specific user."""
    try:
        await redis_service.record_refresh_failure(user_id)
        logger.warning("[_record_refresh_failure] Recorded refresh failure for user: %s", user_id)
        
    except Exception as e:
        logger.error("[_record_refresh_failure] Error recording refresh failure for user %s: %s", user_id, str(e))


async def _clear_refresh_failure(user_id: str):
    """Clear failure record when refresh succeeds for the specific user."""
    try:
        await redis_service.clear_refresh_failure(user_id)
        logger.info("[_clear_refresh_failure] Cleared failure record for successful refresh for user: %s", user_id)
        
    except Exception as e:
        logger.error("[_clear_refresh_failure] Error clearing refresh failure for user %s: %s", user_id, str(e))


async def _log_redis_cache_status():
    """Log current status of Redis cache for monitoring purposes."""
    try:
        stats = await redis_service.get_cache_stats()
        logger.info("[_log_redis_cache_status] Redis cache stats: %s", stats)
        return stats
    except Exception as e:
        logger.error("[_log_redis_cache_status] Error getting Redis cache stats: %s", str(e))
        return {}

async def _debug_redis_state(user_id: str):
    """Debug function to check Redis state for a specific user."""
    try:
        logger.info("[_debug_redis_state] Checking Redis state for user: %s", user_id)
        
        # Check if tokens exist in Redis
        cached_tokens = await redis_service.get_cached_tokens(user_id)
        if cached_tokens:
            access_token, refresh_token = cached_tokens
            logger.info("[_debug_redis_state] ✅ Found tokens in Redis for user: %s", user_id)
            logger.info("[_debug_redis_state] Access token length: %s, Refresh token length: %s", 
                       len(access_token) if access_token else 0,
                       len(refresh_token) if refresh_token else 0)
        else:
            logger.info("[_debug_redis_state] ❌ No tokens found in Redis for user: %s", user_id)
        
        # Check if lock exists
        lock_key = f"user_refresh_lock:{user_id}"
        lock_exists = await redis_service._redis.get(lock_key)
        logger.info("[_debug_redis_state] Lock exists for user %s: %s", user_id, "Yes" if lock_exists else "No")
        
        # Check if failure record exists
        failure_key = f"user_refresh_failure:{user_id}"
        failure_exists = await redis_service._redis.get(failure_key)
        logger.info("[_debug_redis_state] Failure record exists for user %s: %s", user_id, "Yes" if failure_exists else "No")
        
    except Exception as e:
        logger.error("[_debug_redis_state] Error checking Redis state for user %s: %s", user_id, str(e))


def _log_request_details(request: Request, response: Response = None):
    """Log detailed request and response information for debugging."""
    logger.info("[_log_request_details] === REQUEST DETAILS ===")
    logger.info("[_log_request_details] Method: %s, URL: %s", request.method, str(request.url))
    logger.info("[_log_request_details] Client IP: %s, User Agent: %s", 
               request.client.host if request.client else "unknown",
               request.headers.get("user-agent", "unknown"))
    
    # Log all headers
    logger.info("[_log_request_details] Request headers:")
    for header_name, header_value in request.headers.items():
        if header_name.lower() in ["authorization", "cookie"]:
            # Log sensitive headers with length only
            logger.info("[_log_request_details]   %s: [LENGTH: %s]", header_name, len(header_value))
        else:
            logger.info("[_log_request_details]   %s: %s", header_name, header_value)
    
    # Log cookies
    all_cookies = dict(request.cookies)
    logger.info("[_log_request_details] Request cookies: %s", list(all_cookies.keys()))
    for cookie_name, cookie_value in all_cookies.items():
        logger.info("[_log_request_details]   %s: [LENGTH: %s]", cookie_name, len(cookie_value))
    
    if response:
        logger.info("[_log_request_details] === RESPONSE DETAILS ===")
        logger.info("[_log_request_details] Status code: %s", response.status_code)
        
        # Log response headers
        logger.info("[_log_request_details] Response headers:")
        for header_name, header_value in response.headers.items():
            if header_name.lower() == "set-cookie":
                # Log Set-Cookie headers with length only
                logger.info("[_log_request_details]   %s: [LENGTH: %s]", header_name, len(header_value))
            else:
                logger.info("[_log_request_details]   %s: %s", header_name, header_value)
        
        # Count Set-Cookie headers
        try:
            set_cookie_headers = [
                h for (k, h) in ((k.decode().lower(), v.decode()) for k, v in getattr(response, "raw_headers", []))
                if k == "set-cookie"
            ]
            logger.info("[_log_request_details] Total Set-Cookie headers: %s", len(set_cookie_headers))
        except Exception as header_err:
            logger.warning("[_log_request_details] Could not count Set-Cookie headers: %s", header_err)
    
    logger.info("[_log_request_details] === END DETAILS ===")


async def auth_guard(request: Request, response: Response) -> dict:
    """
    Authentication guard that verifies JWT tokens and handles token refresh with distributed locking using Redis.
    
    This function implements a robust distributed locking mechanism using Redis to prevent multiple concurrent
    token refresh attempts for the same refresh token across all workers. If one request fails to verify
    the JWT, it will acquire a distributed lock and other requests will wait until the refresh
    is complete and new tokens are set in cookies.
    """
    logger.info("[auth_guard] Starting authentication guard check with Redis-based distributed locking...")
    
    # Log detailed request information for debugging
    _log_request_details(request)
    
    # Log detailed request information including authorization header
    auth_header = request.headers.get("authorization") or request.headers.get("Authorization")
    logger.info("[auth_guard] Request details - method: %s, url: %s, client_ip: %s", 
                request.method, str(request.url), request.client.host if request.client else "unknown")
    logger.info("[auth_guard] Authorization header present: %s, header_length: %s", 
                bool(auth_header), len(auth_header) if auth_header else 0)
    if auth_header:
        if auth_header.lower().startswith("bearer "):
            bearer_token = auth_header.split(" ", 1)[1].strip()
            logger.info("[auth_guard] Bearer token found - token_length: %s, token_preview: %s...", 
                        len(bearer_token), bearer_token[:20] if len(bearer_token) > 20 else bearer_token)
        else:
            logger.info("[auth_guard] Authorization header format: %s", auth_header[:50] + "..." if len(auth_header) > 50 else auth_header)
    
    # Log all cookies present
    all_cookies = dict(request.cookies)
    logger.info("[auth_guard] All cookies present: %s", list(all_cookies.keys()))
    if "access_token" in all_cookies:
        logger.info("[auth_guard] access_token cookie - length: %s, preview: %s...", 
                    len(all_cookies["access_token"]), all_cookies["access_token"][:20])
    if "refresh_token" in all_cookies:
        logger.info("[auth_guard] refresh_token cookie - length: %s, preview: %s...", 
                    len(all_cookies["refresh_token"]), all_cookies["refresh_token"][:20])
    
    # Extract tokens from cookies
    access_token = request.cookies.get("access_token")
    refresh_token = request.cookies.get("refresh_token")

    if not access_token or not refresh_token:
        logger.error("[auth_guard] Missing access_token or refresh_token in cookies")
        raise UserNotAuthenticatedError()

    logger.info("[auth_guard] Found tokens in cookies. access_token length: %s, refresh_token length: %s", 
                len(access_token) if access_token else 0, 
                len(refresh_token) if refresh_token else 0)

    try:
        # First attempt: try to verify the current access token
        logger.info("[auth_guard] Attempting to verify current access token...")
        return await verify_jwt(access_token)
        
    except TokenVerificationFailedError as e:
        logger.warning("[auth_guard] Access token verification failed: %s", str(e))
        logger.info("[auth_guard] Token expired or invalid. Initiating token refresh process with Redis-based distributed locking...")
        
        # Extract user ID from the refresh token for user-specific locking and caching
        user_id = _extract_user_id_from_token(refresh_token)
        logger.info("[auth_guard] Extracted user_id: %s from refresh token for Redis lock and cache operations", user_id)
        
        # Try to acquire distributed lock for this user to prevent concurrent refresh attempts
        lock_acquired = await _acquire_refresh_lock(user_id)
        
        if not lock_acquired:
            logger.info("[auth_guard] Failed to acquire distributed lock. Another worker is handling refresh for user: %s", user_id)
            logger.info("[auth_guard] Waiting for other worker to complete refresh and checking cache periodically...")
            
            # Wait for other worker to complete refresh by checking cache periodically
            max_wait_time = LOCK_ACQUIRE_TIMEOUT
            check_interval = 0.5  # Check every 500ms
            waited_time = 0
            
            while waited_time < max_wait_time:
                await asyncio.sleep(check_interval)
                waited_time += check_interval
                
                # Check if cached tokens are now available
                cached = await _get_cached_tokens(user_id)
                if cached:
                    new_access_token, new_refresh_token = cached
                    logger.info("[auth_guard] Found cached tokens from another worker. Verifying if access token is still valid...")
                    logger.info("[auth_guard] Cached tokens - access_token length: %s, refresh_token length: %s",
                              len(new_access_token) if new_access_token else 0,
                              len(new_refresh_token) if new_refresh_token else 0)
                    
                    # Verify if the cached access token is still valid
                    try:
                        await verify_jwt(new_access_token)
                        logger.info("[auth_guard] Cached access token is still valid. Using cached tokens.")
                        
                        # Set cookies with the new tokens
                        await _set_cookies_with_tokens(response, new_access_token, new_refresh_token)
                        
                        # Get user data from the verified token
                        user_data = await verify_jwt(new_access_token)
                        logger.info("[auth_guard] Cached access token verification successful. Authentication complete.")
                        
                        # Log final response details
                        _log_request_details(request, response)
                        
                        return user_data
                    except TokenVerificationFailedError as verify_error:
                        logger.warning("[auth_guard] Cached access token has expired: %s. Continuing to wait for fresh refresh.", str(verify_error))
                        # Continue waiting for fresh refresh
                        continue
            
            # If we've waited too long, raise an error
            logger.error("[auth_guard] Timeout waiting for other worker to complete refresh after %s seconds", max_wait_time)
            clear_user_cookies(response)
            raise FailRefreshTokenError()
        
        logger.info("[auth_guard] Successfully acquired distributed lock for refresh token. Checking cache for existing refresh...")
        
        # Debug Redis state before starting
        await _debug_redis_state(user_id)
        
        try:
            # Check if refresh has been failing recently to avoid hammering
            if await _is_refresh_failing(user_id):
                logger.warning("[auth_guard] Refresh token has failed multiple times recently. Blocking refresh attempt.")
                logger.warning("[auth_guard] Consider checking network connectivity or Keycloak service status.")
                raise FailRefreshTokenError()
            
            # Check if another request has already refreshed the tokens using the current refresh token for this specific user
            cached = await _get_cached_tokens(user_id)
            new_access_token = None
            new_refresh_token = None
            
            if cached:
                new_access_token, new_refresh_token = cached
                logger.info("[auth_guard] Found cached tokens. Verifying if access token is still valid...")
                logger.info("[auth_guard] Cached tokens - access_token length: %s, refresh_token length: %s",
                          len(new_access_token) if new_access_token else 0,
                          len(new_refresh_token) if new_refresh_token else 0)
                
                # Verify if the cached access token is still valid
                try:
                    await verify_jwt(new_access_token)
                    logger.info("[auth_guard] Cached access token is still valid. Using cached tokens.")
                except TokenVerificationFailedError:
                    logger.warning("[auth_guard] Cached access token has expired. Will perform fresh refresh.")
                    new_access_token = None
                    new_refresh_token = None
            
            # If we don't have valid cached tokens, perform fresh refresh
            if not new_access_token or not new_refresh_token:
                logger.info("[auth_guard] Performing fresh token refresh...")
                logger.info("[auth_guard] Refresh process details - refresh_token_length: %s, client_id: %s, keycloak_url: %s", 
                           len(refresh_token) if refresh_token else 0, settings.CLIENT_ID_KEYCLOAK, settings.KEYCLOAK_URL)
                
                try:
                    # Perform the actual token refresh
                    logger.info("[auth_guard] Calling refresh_access_token function...")
                    new_access_token, new_refresh_token = await refresh_access_token(refresh_token)
                    
                    logger.info("[auth_guard] Token refresh successful. New tokens obtained.")
                    logger.info("[auth_guard] New tokens - access_token length: %s, refresh_token length: %s",
                              len(new_access_token) if new_access_token else 0,
                              len(new_refresh_token) if new_refresh_token else 0)
                    
                    # Cache the new tokens in Redis for other concurrent requests
                    # This ensures that subsequent requests can find the cached tokens
                    logger.info("[auth_guard] Caching new tokens in Redis for concurrent requests...")
                    logger.info("[auth_guard] About to call _update_cached_tokens with user_id: %s", user_id)
                    logger.info("[auth_guard] New access token length: %s, new refresh token length: %s", 
                               len(new_access_token) if new_access_token else 0,
                               len(new_refresh_token) if new_refresh_token else 0)
                    
                    update_success = await _update_cached_tokens(user_id, new_access_token, new_refresh_token)
                    
                    if update_success:
                        logger.info("[auth_guard] ✅ Successfully cached new tokens in Redis for user: %s", user_id)
                    else:
                        logger.error("[auth_guard] ❌ Failed to cache new tokens in Redis for user: %s", user_id)
                    
                    # Debug Redis state after update
                    await _debug_redis_state(user_id)
                    
                    # Clear any failure records since refresh succeeded
                    logger.info("[auth_guard] Clearing failure records due to successful refresh...")
                    await _clear_refresh_failure(user_id)
                    
                except Exception as refresh_error:
                    logger.error("[auth_guard] Token refresh failed: %s", str(refresh_error))
                    logger.error("[auth_guard] Refresh process encountered an error. Recording failure and raising FailRefreshTokenError")
                    logger.error("[auth_guard] Error details - exception_type: %s, exception_message: %s", 
                               type(refresh_error).__name__, str(refresh_error))
                    
                    # Record the failure to prevent hammering
                    logger.info("[auth_guard] Recording refresh failure to prevent hammering...")
                    await _record_refresh_failure(user_id)
                    raise FailRefreshTokenError()

            # Validate that we have valid tokens before setting cookies
            if not new_access_token or not new_refresh_token:
                logger.error("[auth_guard] Cannot set cookies: tokens are invalid or missing")
                logger.error("[auth_guard] new_access_token: %s, new_refresh_token: %s", 
                           "None" if new_access_token is None else f"length={len(new_access_token)}",
                           "None" if new_refresh_token is None else f"length={len(new_refresh_token)}")
                raise FailRefreshTokenError("Invalid tokens received from refresh")
            
            # Set cookies with the new tokens
            await _set_cookies_with_tokens(response, new_access_token, new_refresh_token)

            # Verify the new access token before returning
            logger.info("[auth_guard] Verifying newly refreshed access token...")
            try:
                user_data = await verify_jwt(new_access_token)
                logger.info("[auth_guard] New access token verification successful. Authentication complete.")
                
                # Log final response details
                _log_request_details(request, response)
                
                return user_data
            except TokenVerificationFailedError as verify_error:
                logger.error("[auth_guard] New access token verification failed: %s", str(verify_error))
                logger.error("[auth_guard] This indicates a critical issue with the refresh process")
                raise FailRefreshTokenError()
                
        finally:
            # Always release the distributed lock
            await _release_refresh_lock(user_id)
            logger.info("[auth_guard] Released distributed lock for user: %s", user_id)
            
            # Log current Redis cache status for monitoring
            await _log_redis_cache_status()


async def _set_cookies_with_tokens(response: Response, new_access_token: str, new_refresh_token: str):
    """Set cookies with the new tokens."""
    logger.info("[_set_cookies_with_tokens] Setting new tokens in cookies...")
    logger.info("[_set_cookies_with_tokens] Cookie setting details - new_access_token_length: %s, new_refresh_token_length: %s, frontend_url: %s", 
               len(new_access_token) if new_access_token else 0, 
               len(new_refresh_token) if new_refresh_token else 0,
               settings.FRONTEND_REDIRECT_URL)
    domain = None
    # Determine cookie security settings based on environment
    if "localhost" in settings.FRONTEND_REDIRECT_URL:
        is_secure = True
        same_site = "none"
    elif "189" in settings.FRONTEND_REDIRECT_URL or "reportsbeta" in settings.FRONTEND_REDIRECT_URL:
        is_secure = True
        same_site = "lax"
        domain=".reportsbeta.snapforensics.com"
    else:
        is_secure = False
        same_site = "lax"


    logger.info("[_set_cookies_with_tokens] Cookie settings - secure: %s, same_site: %s, frontend_url: %s", is_secure, same_site, settings.FRONTEND_REDIRECT_URL)

    # Set the new access token cookie
    try:
        response.set_cookie(
            "access_token",
            new_access_token,
            httponly=True,
            secure=is_secure,
            samesite=same_site,
            path="/",
            domain=domain,
        )
        logger.info("[_set_cookies_with_tokens] Successfully set access_token cookie with settings - httponly: True, secure: %s, samesite: %s, path: /", is_secure, same_site)
    except Exception as cookie_error:
        logger.error("[_set_cookies_with_tokens] Failed to set access_token cookie: %s", str(cookie_error))
        logger.error("[_set_cookies_with_tokens] Cookie setting failed - access_token_length: %s, secure: %s, same_site: %s", 
                   len(new_access_token) if new_access_token else 0, is_secure, same_site)
        logger.error("[_set_cookies_with_tokens] Cookie error details - error_type: %s, error_message: %s", 
                   type(cookie_error).__name__, str(cookie_error))
        raise FailRefreshTokenError(f"Failed to set access token cookie: {str(cookie_error)}")

    # Set the new refresh token cookie
    try:
        response.set_cookie(
            "refresh_token",
            new_refresh_token,
            httponly=True,
            secure=is_secure,
            samesite=same_site,
            path="/",
            domain=domain,
        )
        logger.info("[_set_cookies_with_tokens] Successfully set refresh_token cookie with settings - httponly: True, secure: %s, samesite: %s, path: /, domain: %s", is_secure, same_site, domain)
    except Exception as cookie_error:
        logger.error("[_set_cookies_with_tokens] Failed to set refresh_token cookie: %s", str(cookie_error))
        logger.error("[_set_cookies_with_tokens] Cookie setting failed - refresh_token_length: %s, secure: %s, same_site: %s", 
                   len(new_refresh_token) if new_refresh_token else 0, is_secure, same_site)
        logger.error("[_set_cookies_with_tokens] Cookie error details - error_type: %s, error_message: %s", 
                   type(cookie_error).__name__, str(cookie_error))
        raise FailRefreshTokenError(f"Failed to set refresh token cookie: {str(cookie_error)}")

    # Log cookie header count for debugging
    try:
        set_cookie_headers = [
            h for (k, h) in ((k.decode().lower(), v.decode()) for k, v in getattr(response, "raw_headers", []))
            if k == "set-cookie"
        ]
        logger.info("[_set_cookies_with_tokens] Total Set-Cookie headers after setting tokens: %s", len(set_cookie_headers))
        
        # Log details of each Set-Cookie header for debugging
        for i, header in enumerate(set_cookie_headers):
            logger.info("[_set_cookies_with_tokens] Set-Cookie header %s: %s", i+1, header[:200] + "..." if len(header) > 200 else header)
            
    except Exception as header_err:
        logger.warning("[_set_cookies_with_tokens] Could not count Set-Cookie headers: %s", header_err)
        logger.warning("[_set_cookies_with_tokens] Response object type: %s, has raw_headers: %s", 
                     type(response), hasattr(response, "raw_headers"))


def clear_user_cookies(response: Response):
    """
    Clear authentication cookies to force re-authentication.
    This should be called when tokens are invalid or expired.
    
    Args:
        response: The FastAPI response object to set cookies on
    """
    try:
        # Clear access token cookie
        response.delete_cookie("access_token", path="/")
        logger.info("[clear_user_cookies] Cleared access_token cookie")
        
        # Clear refresh token cookie
        response.delete_cookie("refresh_token", path="/")
        logger.info("[clear_user_cookies] Cleared refresh_token cookie")
        
    except Exception as e:
        logger.error("[clear_user_cookies] Failed to clear cookies: %s", str(e))


async def clear_user_token_cache(user_id: str) -> dict:
    """
    Public function to clear all cached data for a specific user from Redis.
    This can be called from logout endpoints or other parts of the application.
    
    Args:
        user_id: The user ID whose cached data should be cleared
        
    Returns:
        Dictionary with counts of removed entries for each cache type
    """
    logger.info("[clear_user_token_cache] Clearing all cached data from Redis for user: %s", user_id)
    
    try:
        removed_count = await redis_service.clear_user_cache(user_id)
        
        result = {
            "cache_entries_removed": removed_count,
            "total_entries_removed": removed_count
        }
        
        logger.info("[clear_user_token_cache] Cleared data from Redis for user %s: %s", user_id, result)
        return result
        
    except Exception as e:
        logger.error("[clear_user_token_cache] Error clearing Redis cache for user %s: %s", user_id, str(e))
        return {
            "cache_entries_removed": 0,
            "total_entries_removed": 0,
            "error": str(e)
        }


async def exchange_code_for_tokens(code: str, frontend):
    logger.info("[exchange_code_for_tokens] Received code: %s", code)
    token_endpoint = "%s/realms/%s/protocol/openid-connect/token" % (settings.KEYCLOAK_URL, settings.REALM_NAME)
    logger.info("[exchange_code_for_tokens] Token endpoint URL: %s", token_endpoint)

    logger.info("[exchange_code_for_tokens] Received frontend: %s", frontend)
    # frontend="http://localhost"
    redirect_uri = f"{settings.REDIRECT_URI_KEYCLOAK}?frontend={quote(frontend)}"
    # redirect_uri = f"{settings.REDIRECT_URI_KEYCLOAK}"
    data = {
        "grant_type": "authorization_code",
        "code": code,
        "redirect_uri": redirect_uri,
        "client_id": settings.CLIENT_ID_KEYCLOAK,
        "client_secret": settings.CLIENT_SECRET_KEYCLOAK,
    }

    max_attempts = 3
    for attempt in range(1, max_attempts + 1):
        try:
            async with httpx.AsyncClient(verify=settings.KEYCLOAK_VERIFY_SSL, timeout=10.0) as client:
                logger.info("[exchange_code_for_tokens] Sending POST request to Keycloak... (attempt %s/%s)", attempt, max_attempts)
                response = await client.post(
                    token_endpoint,
                    data=data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
            break
        except httpx.RequestError as e:
            if attempt == max_attempts:
                logger.error("[exchange_code_for_tokens] Network error on last attempt: %s", e)
                raise TokenExchangeFailedError(0, str(e))
            backoff = 0.5 * (2 ** (attempt - 1))
            logger.warning("[exchange_code_for_tokens] Network error (attempt %s/%s): %s. Retrying in %.1fs", attempt, max_attempts, e, backoff)
            await asyncio.sleep(backoff)

    logger.info("[exchange_code_for_tokens] Response status code: %s", response.status_code)

    if response.status_code != 200:
        try:
            error_text = response.text
            logger.error("[exchange_code_for_tokens] Error response from Keycloak: %s", error_text)
        except Exception as e:
            logger.error("[exchange_code_for_tokens] Error reading response: %s", e)
        raise TokenExchangeFailedError(response.status_code, response.text)

    logger.info("[exchange_code_for_tokens] Successfully received tokens.")
    return response.json()

