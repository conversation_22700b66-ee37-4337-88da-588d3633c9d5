import { Tabs } from '@snap/design-system'
import DefaultUserAccount from "./DefaultUserAccount";
import GeneralAccount from './GeneralAccount';
import InviteUserAccount from './InviteUserAccount';
import AdminUserAccount from './AdminUserAccount';
import { usePermissionCheck } from '~/components/router/PermissionGuard';
import { Permission } from '~/helpers/permissions.helper';
import NotAllowed from '~/components/NotAllowed';

// Configuração das abas com suas respectivas permissões
const TAB_CONFIG = [
  {
    value: 'geral',
    label: 'Geral',
    children: <GeneralAccount />,
    permission: Permission.ADD_API_KEY, // aba geral visível se pode adicionar API key
    order: 1
  },
  {
    value: 'convidar_usuarios',
    label: 'Convidar Usuários',
    children: <InviteUserAccount />,
    permission: Permission.INVITE_USER,
    order: 2
  },
  {
    value: 'gerenciar_usuarios',
    label: 'Gerenciar Usuários',
    children: <AdminUserAccount />,
    permission: Permission.GET_ORGANIZATION_INVITE,
    order: 3
  },
  {
    value: 'relatorios',
    label: 'RELATÓRIOS',
    children: <DefaultUserAccount />,
    permission: Permission.GET_USER_LOGS,
    order: 4
  }
];

const TabContainerAccount = () => {
  const { checkPermission } = usePermissionCheck();

  const renderTabList = () => {
    return TAB_CONFIG
      .filter(tab => checkPermission(tab.permission))
      .sort((a, b) => a.order - b.order)
      .map(({ value, label, children }) => ({
        value,
        label,
        children
      }));
  }

  const tabList = renderTabList();

  // Fallback caso nenhuma aba esteja disponível (não deveria acontecer na prática)
  if (tabList.length === 0) {
    return (
      <div className="flex align-center justify-center w-full shadow-md bg-foreground/5 rounded-md ">
        <NotAllowed />
      </div>
    );
  }

  return (
    <Tabs items={tabList} className='[&_[role=tab]]:cursor-pointer' />
  );
};

export default TabContainerAccount;
