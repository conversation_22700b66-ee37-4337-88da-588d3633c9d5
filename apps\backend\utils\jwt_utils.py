import logging
from core.constants import JW<PERSON>ields

logger = logging.getLogger(__name__)

class JWTUtils:

    def __init__(self, user_jwt: dict) -> None:
        logger.info("[JWTUtils.__init__] Initializing JWTUtils with user_jwt: %s", user_jwt)
        self.user_jwt = user_jwt
        self.user_id = self.user_jwt.get(JWTFields.sub)
        self.user_realm_access = self.user_jwt.get(JWTFields.realm_access)
        self.user_roles = self.user_realm_access.get(JWTFields.roles)
        self.user_email = self.user_jwt.get(JWTFields.email)
        logger.info("[JWTUtils.__init__] user_id: %s, user_realm_access: %s, user_roles: %s", self.user_id, self.user_realm_access, self.user_roles)

    def get_user_id(self):
        logger.info("[JWTUtils.get_user_id] Returning user_id: %s", self.user_id)
        return self.user_id
    

    def get_user_realm_access(self):
        logger.info("[JWTUtils.get_user_realm_access] Returning user_realm_access: %s", self.user_realm_access)
        return self.user_realm_access
    

    def get_user_roles(self):
        logger.info("[JWTUtils.get_user_roles] Returning user_roles: %s", self.user_roles)
        return self.user_roles
    
    def get_user_email(self):
        logger.info("[JWTUtils.get_user_roles] Returning user_email: %s", self.user_email)
        return self.user_email