#!/usr/bin/env python3
"""
Debug script to analyze JWT tokens and their expiration times.
This helps diagnose time synchronization and token expiration issues.
"""

import asyncio
import logging
import time
from jose import jwt
from core.config import settings
from core.jwt_utils import refresh_access_token, verify_jwt

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_jwt_token(token: str, token_name: str = "Token"):
    """Debug a JWT token by decoding its claims without verification."""
    try:
        logger.info(f"=== {token_name} Analysis ===")
        
        # Decode without verification to see the claims
        unverified_claims = jwt.get_unverified_claims(token)
        
        current_time = time.time()
        exp_time = unverified_claims.get("exp")
        iat_time = unverified_claims.get("iat")  # issued at
        nbf_time = unverified_claims.get("nbf")  # not before
        
        logger.info(f"Token length: {len(token)}")
        logger.info(f"Current server time: {current_time}")
        
        if exp_time:
            time_until_expiry = exp_time - current_time
            logger.info(f"Expiration time (exp): {exp_time}")
            logger.info(f"Time until expiry: {time_until_expiry:.2f} seconds")
            
            if time_until_expiry <= 0:
                logger.warning(f"❌ Token is EXPIRED! Expired by {abs(time_until_expiry):.2f} seconds")
            elif time_until_expiry < 60:
                logger.warning(f"⚠️  Token expires very soon! Expires in {time_until_expiry:.2f} seconds")
            else:
                logger.info(f"✅ Token is valid for {time_until_expiry:.2f} seconds")
        
        if iat_time:
            token_age = current_time - iat_time
            logger.info(f"Issued at (iat): {iat_time}")
            logger.info(f"Token age: {token_age:.2f} seconds")
        
        if nbf_time:
            if current_time < nbf_time:
                logger.warning(f"⚠️  Token not yet valid! Valid from {nbf_time} (in {nbf_time - current_time:.2f} seconds)")
            else:
                logger.info(f"✅ Token is valid (nbf: {nbf_time})")
        
        # Show other important claims
        logger.info(f"Subject (sub): {unverified_claims.get('sub', 'Not present')}")
        logger.info(f"Audience (aud): {unverified_claims.get('aud', 'Not present')}")
        logger.info(f"Issuer (iss): {unverified_claims.get('iss', 'Not present')}")
        
        # Check if token matches expected values
        expected_audience = settings.CLIENT_ID_KEYCLOAK
        expected_issuer = f"{settings.KEYCLOAK_URL}/realms/{settings.REALM_NAME}"
        
        if unverified_claims.get('aud') != expected_audience:
            logger.warning(f"⚠️  Audience mismatch! Expected: {expected_audience}, Got: {unverified_claims.get('aud')}")
        
        if unverified_claims.get('iss') != expected_issuer:
            logger.warning(f"⚠️  Issuer mismatch! Expected: {expected_issuer}, Got: {unverified_claims.get('iss')}")
        
        logger.info("")
        
    except Exception as e:
        logger.error(f"Error analyzing {token_name}: {e}")

async def test_token_refresh_and_verification():
    """Test the complete token refresh and verification flow."""
    logger.info("=== Testing Token Refresh and Verification Flow ===")
    
    # You'll need to provide a valid refresh token for testing
    # This is just a placeholder - replace with actual refresh token
    refresh_token = "YOUR_REFRESH_TOKEN_HERE"
    
    if refresh_token == "YOUR_REFRESH_TOKEN_HERE":
        logger.error("Please replace the placeholder refresh token with a real one for testing")
        return
    
    try:
        # Step 1: Analyze the refresh token
        await debug_jwt_token(refresh_token, "Refresh Token")
        
        # Step 2: Attempt token refresh
        logger.info("Attempting token refresh...")
        new_access_token, new_refresh_token = await refresh_access_token(refresh_token)
        
        # Step 3: Analyze the new access token
        await debug_jwt_token(new_access_token, "New Access Token")
        
        # Step 4: Analyze the new refresh token
        await debug_jwt_token(new_refresh_token, "New Refresh Token")
        
        # Step 5: Try to verify the new access token
        logger.info("Attempting to verify the new access token...")
        try:
            user_data = await verify_jwt(new_access_token)
            logger.info("✅ Token verification successful!")
            logger.info(f"User data: {user_data}")
        except Exception as verify_error:
            logger.error(f"❌ Token verification failed: {verify_error}")
            
            # Try to decode the failed token for debugging
            await debug_jwt_token(new_access_token, "Failed Access Token")
        
    except Exception as e:
        logger.error(f"Error during token refresh test: {e}")

async def check_server_time_synchronization():
    """Check if there might be time synchronization issues."""
    logger.info("=== Server Time Synchronization Check ===")
    
    current_time = time.time()
    current_utc = time.gmtime(current_time)
    current_local = time.localtime(current_time)
    
    logger.info(f"Current server time (Unix timestamp): {current_time}")
    logger.info(f"Current UTC time: {time.strftime('%Y-%m-%d %H:%M:%S UTC', current_utc)}")
    logger.info(f"Current local time: {time.strftime('%Y-%m-%d %H:%M:%S', current_local)}")
    
    # Check if the time seems reasonable (not too far from expected)
    import datetime
    expected_time = datetime.datetime.now().timestamp()
    time_diff = abs(current_time - expected_time)
    
    if time_diff > 60:  # More than 1 minute difference
        logger.warning(f"⚠️  Large time difference detected: {time_diff:.2f} seconds")
        logger.warning("This may indicate a time synchronization issue")
    else:
        logger.info(f"✅ Time synchronization appears normal (difference: {time_diff:.2f} seconds)")
    
    logger.info("")

async def main():
    """Main function to run all debug checks."""
    logger.info("Starting JWT Token Debug Analysis")
    logger.info("=" * 50)
    
    # Check server time
    await check_server_time_synchronization()
    
    # Test token refresh flow (if you have a refresh token)
    # await test_token_refresh_and_verification()
    
    logger.info("Debug analysis complete!")
    logger.info("=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
