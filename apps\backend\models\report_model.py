from sqlalchemy import Column, ForeignKey, Text, DateTime, UniqueConstraint, Index, BigInteger
from sqlalchemy.dialects.postgresql import UUID, JSONB, DATE
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from models.base import Base


class UserReports(Base):
    __tablename__ = 'user_reports'
    __table_args__ = (
        UniqueConstraint("user_reports_id", name="user_reports_id_unique"),
        Index("idx_user_reports_report_status_status_report", "report_status", postgresql_using="gin"),
        Index("idx_user_reports_report_type", "report_type"),
        Index("ix_user_reports_user_type", "user_id", "report_type", postgresql_using="btree"),
        Index("ix_user_reports_user_org_type", "user_id", "organization_id", "report_type", postgresql_using="btree"),
        Index("ix_user_reports_user_folder", "user_id", "folder_id"),
        Index("ix_user_reports_user_org", "user_id", "organization_id"),
        Index('ix_user_reports_user_created_at_desc', "user_id", "created_at", postgresql_using="btree"),
        Index('ix_user_reports_user_modified_at_desc', "user_id", "modified_at", postgresql_using="btree"),
        Index('ix_user_reports_user_folder_created_at_desc', "user_id", "folder_id", "created_at", postgresql_using="btree"),
        Index('ix_user_reports_user_folder_modified_at_desc', "user_id", "folder_id", "modified_at", postgresql_using="btree"),
        Index('ix_user_reports_user_org_created_at_desc', "user_id", "organization_id", "created_at", postgresql_using="btree"),
        Index('ix_user_reports_user_org_modified_at_desc', "user_id", "organization_id", "modified_at", postgresql_using="btree"),
        Index('ix_user_reports_user_org_folder_created_desc',
        "user_id", "organization_id", "folder_id", "created_at", postgresql_using="btree"),
        Index('ix_user_reports_user_org_folder_modified_desc',
        "user_id", "organization_id", "folder_id", "modified_at", postgresql_using="btree"),
        {"schema": "reports"}
    )

    # Primary key columns
    user_id = Column(
        UUID,
        ForeignKey("reports.users.user_id"),
        primary_key=True
    )
    user_reports_id = Column(
        UUID,
        ForeignKey("reports.user_report_ledger.user_reports_id"),
        primary_key=True,
        server_default=func.gen_random_uuid()
    )

    # Foreign keys
    organization_id = Column(
        UUID,
        ForeignKey("reports.organizations.organization_id"),
        nullable=True,
        index=True
    )
    folder_id = Column(
        UUID,
        ForeignKey("reports.folders.folder_id", ondelete="CASCADE"),
        nullable=True,
        index=True
    )

    # Data columns
    data = Column(JSONB, nullable=True)
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.timezone('UTC', func.now()))
    modified_at = Column(DateTime(timezone=True), nullable=False, server_default=func.timezone('UTC', func.now()))
    report_name = Column(JSONB, nullable=True)
    report_status = Column(JSONB, nullable=True)
    report_type = Column(Text, nullable=True, index=True)
    subject_name = Column(JSONB, nullable=True)
    subject_mother_name = Column(JSONB, nullable=True)
    subject_age = Column(DATE, nullable=True, index=True)  # Changed to DATE type
    subject_sex = Column(JSONB, nullable=True)
    subject_person_count = Column(JSONB, nullable=True)
    subject_company_count = Column(JSONB, nullable=True)
    report_search_args = Column(JSONB, nullable=True)
    subject_name_related = Column(JSONB, nullable=True)
    number_of_relations = Column(JSONB, nullable=True)

    # Relationships
    user_report_ledger = relationship("UserReportLedger", back_populates="user_report", foreign_keys=[user_reports_id])
    folder = relationship("Folder", back_populates="reports", passive_deletes=True)

    def __repr__(self):
        return (
            f"<UserReports(user_id={self.user_id}, user_reports_id={self.user_reports_id}, "
            f"data={self.data}, "
            f"created_at={self.created_at}, modified_at={self.modified_at}, "
            f"report_name={self.report_name}, report_status={self.report_status}, "
            f"report_type={self.report_type}, subject_name={self.subject_name}, "
            f"subject_mother_name={self.subject_mother_name}, subject_age={self.subject_age}, "
            f"subject_sex={self.subject_sex}, subject_person_count={self.subject_person_count}, "
            f"subject_company_count={self.subject_company_count}, report_search_args={self.report_search_args}, "
            f"folder_id={self.folder_id}>"
        )
