import React from "react";
import { BsFilePersonFill, BsBuilding } from "react-icons/bs";
import { MdLocalPhone, MdEmail } from "react-icons/md";
import { TbCirclesRelation } from "react-icons/tb";


export function getTypeIcon(
  type: string,
  size: number = 16,
  color: string = "text-primary"
): React.ReactElement {
  const reportTypeIconMap: Record<string, (size?: number) => React.ReactElement> = {
    cpf: (size = 16) =>
      React.createElement(BsFilePersonFill, { size, className: `${color}` }),
    cnpj: (size = 16) => React.createElement(BsBuilding, { size, className: `${color}` }),
    telefone: (size = 16) =>
      React.createElement(MdLocalPhone, { size, className: `${color}` }),
    email: (size = 16) => React.createElement(MdEmail, { size, className: `${color}` }),
    relacoes: (size = 16) =>
      React.createElement(TbCirclesRelation, { size, className: `${color}` }),
  };
  const creator = reportTypeIconMap[type.toLowerCase()];

  return creator
    ? creator(size)
    : React.createElement("div", { style: { width: size, height: size }, className: "bg-border" });
}