import React from "react";
import { useLocation, useParams } from "react-router";
import { Home, ChevronRight } from "lucide-react";
import { BreadcrumbItem } from "~/types/global";
import { useFolderPath, useReportListActions } from "~/store/reportListStore";
import { REPORT_DETAIL_STORE_INSTANCE } from "~/store/details";
import { useFolderNavigation } from "~/hooks/useFolderNavigation";
import { REPORT_CONSTANTS } from "~/helpers/constants";

interface BreadcrumbNavigationProps {
  breadcrumbs?: BreadcrumbItem[];
  reportName?: string;
}

const baseLinkClasses =
  "flex items-center gap-4 font-mono uppercase tracking-wide transition-opacity hover:opacity-80";

const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({
  breadcrumbs = [],
  reportName,
}) => {
  const location = useLocation();
  const { folderId } = useParams<{ folderId?: string }>();
  const folderPath = useFolderPath();
  const {
    selectors: { useReportMetadata }
  } = REPORT_DETAIL_STORE_INSTANCE;
  const reportMetadata = useReportMetadata();
  const { removeFolderFromPath } = useReportListActions();
  const { navigateToFolder, navigateToRoot } = useFolderNavigation();

  const handleNavigateToRoot = () => {
    navigateToRoot({ shouldInvalidateFilters: false });
  };

  const isReportListPage = location.pathname === "/" || location.pathname.startsWith("/pasta/");
  const isReportDetailsPage = location.pathname.startsWith("/report/");

  const homeLink: BreadcrumbItem = {
    title: "dashboard",
    icon: <Home />,
    onClick: handleNavigateToRoot,
  };

  const handleNavigateToFolder = (targetFolderId: string, index: number) => {
    // Remove folders from path that come after the clicked folder
    if (index < folderPath.length - 1) {
      for (let i = folderPath.length - 1; i > index; i--) {
        removeFolderFromPath(folderPath[i].id);
      }
    }
    navigateToFolder(targetFolderId);
  };

  const buildBreadcrumbs = (): BreadcrumbItem[] => {
    const breadcrumbItems: BreadcrumbItem[] = [homeLink];

    if (isReportListPage) {
      if (folderId && folderPath.length === 0) {
        breadcrumbItems.push({
          title: "PASTA",
        });
      }
      // For regular navigation (via buttons), use the full folderPath
      else {
        folderPath.forEach((folder, index) => {
          breadcrumbItems.push({
            title: folder.name,
            onClick: () => handleNavigateToFolder(folder.id, index),
          });
        });
      }
    } else if (isReportDetailsPage && reportMetadata) {
      const parentFolderId = reportMetadata[REPORT_CONSTANTS.new_report.parent_folder_id];
      const reportName = reportMetadata[REPORT_CONSTANTS.new_report.report_name] as string;
      const folderName = reportMetadata[REPORT_CONSTANTS.new_report.folder_name] as string;

      if (folderPath.length > 0) {
        folderPath.forEach((folder, index) => {
          breadcrumbItems.push({
            title: folder.name,
            onClick: () => handleNavigateToFolder(folder.id, index),
          });
        });
      } else if (parentFolderId && folderName) {
        breadcrumbItems.push({
          title: folderName,
          onClick: () => {
            navigateToFolder(parentFolderId as string);
          },
        });
      }

      if (reportName) {
        breadcrumbItems.push({
          title: reportName,
        });
      }
    } else {
      breadcrumbItems.push(...breadcrumbs);
    }

    return breadcrumbItems;
  };

  const defaultBreadcrumbs = buildBreadcrumbs();
  const totalBreadcrumbs = defaultBreadcrumbs.length;

  return (
    <div className="flex items-center w-full">
      {defaultBreadcrumbs.map((crumb, index) => {
        const isLast = index === totalBreadcrumbs - 1;
        const isClickable = !isLast && crumb.onClick;

        return (
          <React.Fragment key={index}>
            {index > 0 && <ChevronRight className="mx-2" size={16} />}
            {isClickable ? (
              <span
                className={baseLinkClasses}
                onClick={(e) => {
                  e.preventDefault();
                  crumb.onClick?.();
                }}
                style={{ cursor: 'pointer' }}
              >
                {crumb.icon && crumb.icon}
                <p>{crumb.title}</p>
              </span>
            ) : (
              <span className={`${baseLinkClasses} ${isLast ? 'text-brand-primary' : ''}`}>
                {crumb.icon && crumb.icon}
                <p>{crumb.title}</p>
              </span>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default BreadcrumbNavigation;
