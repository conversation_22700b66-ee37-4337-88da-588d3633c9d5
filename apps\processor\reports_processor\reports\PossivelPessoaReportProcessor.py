from typing import Dict, Any

from .BaseReportProcessor import BaseReportProcessor, ReportTypeConfig
from ..constants import ReportKeys
from ..FormatFrontFormat import VinculoSection
from ..EntityExtractor import VinculoConfig, ExtractionResult
from ..CallbackProcessors import CallbackProcessors

class PossivelPessoaReportProcessor(BaseReportProcessor):
    """Processor for CPF reports"""

    def get_config(self) -> ReportTypeConfig:
        return ReportTypeConfig(
            do_filter_doc='CPF',
            do_filter_name='Nome',
            do_doc = {'title': 'Diários Oficiais - CPF'},  # TODO: pode dar merda
            dados_pessoais={'title': 'Dados Pessoais'},  # TODO: pode dar merda
            possiveis_pessoas_relacionadas={'title': "Múltiplos Registros Encontrados"},
            possiveis_empresas_relacionadas={'title': 'Empresas Relacionadas'},
            enabled_sections=[
                VinculoSection.PHONES, VinculoSection.EMAILS,
                VinculoSection.ENDERECOS, VinculoSection.PARENTES, VinculoSection.VINCULOS_EMPREGATICIOS,
                VinculoSection.SERVICO_PUBLICO, VinculoSection.OUTROS_CONTATOS,
                VinculoSection.IMAGENS, VinculoSection.NOMES_USUARIO,
                VinculoSection.OUTRAS_URLS, VinculoSection.REDES_SOCIAIS, VinculoSection.VINCULOS_EDUCACIONAIS
            ],

        )

    def _get_custom_societario(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[
        VinculoSection, VinculoConfig]:
        # Extract societario data for CPF
        societario = self.extractor.extract_vinculos_genericos(
            other_result_data, entity_type, search_value,
            VinculoConfig(extract_type=ReportKeys.EMPRESA,
                          filter_base_data_callback=CallbackProcessors.filter_for_sociedades_vinculos,
                          replace_data_processor=CallbackProcessors.sociedade_data_processor_callback)
        )

        if not societario.data:
            return {}

        return {
            VinculoSection.SOCIEDADES: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.SOCIEDADES),
                extract_func=lambda *args, **kwargs: ExtractionResult(societario.data.get('sociedades', []), societario.sources),
                extract_type=ReportKeys.EMPRESA
            ),
            VinculoSection.SOCIOS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.SOCIOS),
                extract_func=lambda *args, **kwargs: ExtractionResult(societario.data.get('socios_p', []) + societario.data.get('socios_e', []),
                                         societario.sources),
                extract_type=ReportKeys.EMPRESA
            )
        }

    def get_custom_vinculos(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[
        Any, VinculoConfig]:

        custom_dict = self._get_custom_societario(other_result_data, entity_type, search_value)
        custom_dict.update(self._get_custom_processos(other_result_data, entity_type, search_value))
        custom_dict.update(self._get_custom_fornecimentos_campanha(other_result_data, entity_type, search_value))
        custom_dict.update(self._get_custom_doacoes_campanha(other_result_data, entity_type, search_value))
        return custom_dict

    def adjust_metadata(self, processed_result, metadata):
        self._adjust_metadata_personal_data(processed_result, metadata)
        self._adjust_metadata_age(processed_result, metadata)


    def make_vinculo_empregaticios(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.VINCULOS_EMPREGATICIOS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.EMPRESA,
            filter_base_data_callback=CallbackProcessors.filter_for_vinculos_empregaticio,
            #item_callback=CallbackProcessors.vinculos_educacionais_item_callback,
        )