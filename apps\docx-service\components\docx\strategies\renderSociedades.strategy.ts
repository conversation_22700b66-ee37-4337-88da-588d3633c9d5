import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, getSingular, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintSociedadesProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      razao_social?: ValueWithSource;
      detalhes?: Record<string, ValueWithSource>;
      enderecos?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      telefones?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      emails?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      [key: string]: any;
    }>;
  };
}

export const renderSociedades = ({ section }: RenderPrintSociedadesProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((sociedade) => {
    if (sociedade.is_deleted) return;

    if (sociedade.razao_social && !sociedade.razao_social.is_deleted) {
      children.push(new Paragraph({
        children: [
          new TextRun({ text: `${(sociedade.razao_social.label || "Razão Social").toUpperCase()}: `, bold: true, color: "FE473C" }),
          new TextRun(String(sociedade.razao_social.value)),
          new TextRun({ text: ` | ${sociedade.razao_social.source?.map(translateSource).join(', ')}`, size: 16, color: "FE473C" }),
        ],
        spacing: { after: 200 }
      }));
    }

    if (sociedade.detalhes) {
      const tableRows = Object.entries(sociedade.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => {
          return new TableRow({
            children: [
              new TableCell({ children: [new Paragraph(translatePropToLabel(field.label || key).toUpperCase())] }),
              new TableCell({ children: [new Paragraph(String(field.value))] }),
            ],
          });
        });
      if (tableRows.length > 0) {
        children.push(new Table({ rows: tableRows, width: { size: 100, type: WidthType.PERCENTAGE } }));
      }
    }

    // Logic for enderecos, telefones, emails

  });

  return { children };
};
