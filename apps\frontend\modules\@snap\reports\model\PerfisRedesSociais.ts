import { ValueWithSource } from "./ValueWithSource";

export interface PerfilRedeSocial {
    [key: string]: Array<ValueWithSource>;
}

export type RedesSociaisSection = { data: PerfilRedeSocial[]; data_count?: number };
export type UpdaterFunction = (entry: PerfilRedeSocial, index?: number) => void;
export type TestFunction = (entry: PerfilRedeSocial) => boolean;
export type SectionTestFunction = (section: RedesSociaisSection) => boolean;
export type CalculateFunction = (section: RedesSociaisSection) => number;


