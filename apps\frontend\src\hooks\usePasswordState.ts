import { useCredentialsStore } from "~/store/credentials/store";
import { AUTH_STORE_INSTANCE } from "~/store/auth";

/**
 * Custom hook to get fresh password state without relying on potentially stale React hook values.
 * This is particularly useful when password expiration needs to be checked in real-time,
 * especially for cases where user selects "Não armazenar" (expires in 500ms).
 */
export const usePasswordState = () => {
    const {
        selectors: { useIsAuthorized },
    } = AUTH_STORE_INSTANCE;
    const isUserAuthorized = useIsAuthorized();

    /**
     * Gets fresh password state directly from the Zustand store to avoid stale values.
     * @returns {boolean} True if the user needs to provide their password/secret key
     */
    const getFreshPasswordState = (): boolean => {
        // Get fresh state directly from the store
        const store = useCredentialsStore.getState();
        const validThrough = store.password.validThrough;
        const persistedValue = store.password.persistedValue;

        // Recalculate expiration status with fresh values
        const freshIsPasswordExpired = !validThrough || new Date() > new Date(validThrough);
        const freshIsUsingTemporaryKey = freshIsPasswordExpired && persistedValue !== "";
        const freshIsUserAuthorized = isUserAuthorized;

        return (!freshIsUserAuthorized || freshIsPasswordExpired) || freshIsUsingTemporaryKey;
    };

    /**
     * Gets detailed fresh password state for debugging purposes.
     * @returns {object} Detailed password state information
     */
    const getFreshPasswordStateDetailed = () => {
        const store = useCredentialsStore.getState();
        const validThrough = store.password.validThrough;
        const persistedValue = store.password.persistedValue;

        const freshIsPasswordExpired = !validThrough || new Date() > new Date(validThrough);
        const freshIsUsingTemporaryKey = freshIsPasswordExpired && persistedValue !== "";
        const freshIsUserAuthorized = isUserAuthorized;
        const needsSecretKey = (!freshIsUserAuthorized || freshIsPasswordExpired) || freshIsUsingTemporaryKey;

        return {
            validThrough,
            persistedValue: persistedValue ? "***" : null, // Hide actual password value
            isUserAuthorized: freshIsUserAuthorized,
            isPasswordExpired: freshIsPasswordExpired,
            isUsingTemporaryKey: freshIsUsingTemporaryKey,
            needsSecretKey,
        };
    };

    return {
        getFreshPasswordState,
        getFreshPasswordStateDetailed,
    };
};
