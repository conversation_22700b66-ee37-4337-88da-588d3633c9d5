import reportTypes from "./word-map/report_types.json";
import reportLabels from "./word-map/labels.json";
import reportSections from "./word-map/ui_group_titles.json";
import translatedLabels from "./word-map/translate_prop_to_label.json"
import parsedValues from "./word-map/parsed_values.json";
import genericConstants from "./word-map/nomes_genericos.json";
import sourceLabels from "./word-map/source_labels.json";
import pluralWords from "./word-map/nomes_genericos.json";
import relacoesVinculos from "./word-map/relacoes_vinculos.json";
import multiplosRegistros from "./word-map/multiplos_registros.json";

export const REPORT_CONSTANTS = reportTypes;
export const REPORT_LABELS = reportLabels;
export const REPORT_SECTIONS = reportSections;
export const GENERIC_CONSTANTS = genericConstants;
export const SOURCE_LABELS = sourceLabels;
export const TRANSLATED_LABELS = translatedLabels;
export const PARSED_VALUES = parsedValues;
export const PLURAL_WORDS = pluralWords.getPluralWord;
export const RELACOES_VINCULOS = relacoesVinculos;
export const MULTIPLOS_REGISTROS_DETALHES = multiplosRegistros.detalhes;

export const FIELD_KEYS = {
    CPF: MULTIPLOS_REGISTROS_DETALHES.cpf,
    DATA_NASCIMENTO: MULTIPLOS_REGISTROS_DETALHES['data de nascimento'],
    IDADE: MULTIPLOS_REGISTROS_DETALHES.idade,
    SEXO: MULTIPLOS_REGISTROS_DETALHES.sexo,
    NOME_DA_MAE: MULTIPLOS_REGISTROS_DETALHES['nome_da_mae'],
} as const;

export const MAX_PAGE_HEIGHT = "max-h-[calc(100vh-212px)]"
