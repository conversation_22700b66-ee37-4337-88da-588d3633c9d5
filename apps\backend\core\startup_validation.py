"""
Startup Validation System

This module provides comprehensive validation checks that run during application startup
to ensure all systems are properly configured and operational.
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from database.db import check_db_connection
from database.auto_init import DatabaseInitializer
from core.config import settings

logger = logging.getLogger(__name__)

class ValidationLevel(Enum):
    """Validation severity levels"""
    CRITICAL = "critical"    # Will prevent startup
    WARNING = "warning"      # Will log warning but continue
    INFO = "info"           # Informational only

@dataclass
class ValidationResult:
    """Result of a validation check"""
    name: str
    level: ValidationLevel
    passed: bool
    message: str
    details: Optional[Dict[str, Any]] = None

class StartupValidator:
    """Performs comprehensive startup validation"""

    def __init__(self):
        self.results: List[ValidationResult] = []

    async def run_all_validations(self) -> Dict[str, Any]:
        """
        Run all startup validations.

        Returns:
            Summary of validation results
        """
        logger.info("🔍 Starting comprehensive startup validation...")

        self.results = []

        # Run all validation checks
        await self._validate_database_connectivity()
        await self._validate_database_schema()
        await self._validate_environment_configuration()
        await self._validate_external_services()
        await self._validate_security_configuration()

        # Generate summary
        summary = self._generate_summary()
        self._log_validation_results(summary)

        return summary

    async def _validate_database_connectivity(self):
        """Validate database connectivity and basic functionality"""
        logger.info("[Validation] Checking database connectivity...")

        try:
            if await check_db_connection():
                self.results.append(ValidationResult(
                    name="Database Connectivity",
                    level=ValidationLevel.CRITICAL,
                    passed=True,
                    message="Database connection successful"
                ))
            else:
                self.results.append(ValidationResult(
                    name="Database Connectivity",
                    level=ValidationLevel.CRITICAL,
                    passed=False,
                    message="Database connection failed"
                ))
        except Exception as e:
            self.results.append(ValidationResult(
                name="Database Connectivity",
                level=ValidationLevel.CRITICAL,
                passed=False,
                message=f"Database connectivity check failed: {str(e)}"
            ))

    async def _validate_database_schema(self):
        """Validate database schema state"""
        logger.info("[Validation] Checking database schema...")

        try:
            initializer = DatabaseInitializer()

            # Check if schema exists and is properly set up
            async with initializer.engine.begin() as conn:
                schema_exists = await initializer._check_schema_exists(conn)
                table_count = await initializer._count_application_tables()

            if not schema_exists:
                self.results.append(ValidationResult(
                    name="Database Schema",
                    level=ValidationLevel.CRITICAL,
                    passed=False,
                    message="Reports schema does not exist",
                    details={"schema_name": "reports"}
                ))
            elif table_count == 0:
                self.results.append(ValidationResult(
                    name="Database Schema",
                    level=ValidationLevel.WARNING,
                    passed=False,
                    message="Reports schema exists but no application tables found",
                    details={"table_count": table_count}
                ))
            else:
                self.results.append(ValidationResult(
                    name="Database Schema",
                    level=ValidationLevel.CRITICAL,
                    passed=True,
                    message=f"Database schema is properly configured with {table_count} tables",
                    details={"table_count": table_count, "schema_name": "reports"}
                ))

        except Exception as e:
            self.results.append(ValidationResult(
                name="Database Schema",
                level=ValidationLevel.CRITICAL,
                passed=False,
                message=f"Schema validation failed: {str(e)}"
            ))

    async def _validate_environment_configuration(self):
        """Validate environment configuration and required settings"""
        logger.info("[Validation] Checking environment configuration...")

        # Critical settings that must be present
        critical_settings = {
            'DATABASE_URL': settings.DATABASE_URL,
            'KEYCLOAK_URL': settings.KEYCLOAK_URL,
            'REALM_NAME': settings.REALM_NAME,
        }

        missing_critical = []
        for key, value in critical_settings.items():
            if not value:
                missing_critical.append(key)

        if missing_critical:
            self.results.append(ValidationResult(
                name="Critical Configuration",
                level=ValidationLevel.CRITICAL,
                passed=False,
                message=f"Missing critical configuration: {', '.join(missing_critical)}",
                details={"missing_settings": missing_critical}
            ))
        else:
            self.results.append(ValidationResult(
                name="Critical Configuration",
                level=ValidationLevel.CRITICAL,
                passed=True,
                message="All critical configuration settings are present"
            ))

        # Warning-level settings
        warning_settings = {
            'REDIS_HOST': settings.REDIS_HOST,
            'MINIO_ROOT_USER': settings.MINIO_ROOT_USER,
            'KAFKA_EXTERNAL_URI': settings.KAFKA_EXTERNAL_URI,
        }

        missing_warnings = []
        for key, value in warning_settings.items():
            if not value:
                missing_warnings.append(key)

        if missing_warnings:
            self.results.append(ValidationResult(
                name="Optional Configuration",
                level=ValidationLevel.WARNING,
                passed=False,
                message=f"Missing optional configuration: {', '.join(missing_warnings)}",
                details={"missing_settings": missing_warnings}
            ))

        # Database connection string validation
        if settings.DATABASE_URL:
            self.results.append(ValidationResult(
                name="Database Schema Configuration",
                level=ValidationLevel.WARNING,
                passed=False,
                message="Database URL does not specify reports schema",
                details={"database_url": settings.DATABASE_URL.split('@')[1] if '@' in settings.DATABASE_URL else 'Unknown'}
            ))

    async def _validate_external_services(self):
        """Validate external service connectivity"""
        logger.info("[Validation] Checking external service configuration...")

        # Redis validation
        try:
            if settings.REDIS_HOST:
                # Basic Redis connectivity would be checked here
                # For now, just validate configuration
                self.results.append(ValidationResult(
                    name="Redis Configuration",
                    level=ValidationLevel.WARNING,
                    passed=True,
                    message=f"Redis configured at {settings.REDIS_HOST}:{settings.REDIS_PORT}",
                    details={"host": settings.REDIS_HOST, "port": settings.REDIS_PORT}
                ))
            else:
                self.results.append(ValidationResult(
                    name="Redis Configuration",
                    level=ValidationLevel.WARNING,
                    passed=False,
                    message="Redis not configured"
                ))
        except Exception as e:
            self.results.append(ValidationResult(
                name="Redis Configuration",
                level=ValidationLevel.WARNING,
                passed=False,
                message=f"Redis validation error: {str(e)}"
            ))

        # Keycloak validation
        if settings.KEYCLOAK_URL and settings.REALM_NAME:
            self.results.append(ValidationResult(
                name="Keycloak Configuration",
                level=ValidationLevel.CRITICAL,
                passed=True,
                message="Keycloak configuration present",
                details={
                    "keycloak_url": settings.KEYCLOAK_URL,
                    "realm": settings.REALM_NAME,
                    "issuer_url": settings.ISSUER_URL
                }
            ))
        else:
            self.results.append(ValidationResult(
                name="Keycloak Configuration",
                level=ValidationLevel.CRITICAL,
                passed=False,
                message="Keycloak configuration incomplete"
            ))

    async def _validate_security_configuration(self):
        """Validate security-related configuration"""
        logger.info("[Validation] Checking security configuration...")

        # Check if running in secure mode for production
        is_secure = 'https' in settings.SERVER_URL.lower()
        is_production = settings.KC_ENV.lower() in ['production', 'prod']

        if is_production and not is_secure:
            self.results.append(ValidationResult(
                name="Production Security",
                level=ValidationLevel.WARNING,
                passed=False,
                message="Production environment detected but not using HTTPS",
                details={"server_url": settings.SERVER_URL, "environment": settings.KC_ENV}
            ))

        # Check secret configuration
        secrets_configured = bool(settings.CLIENT_SECRET_KEYCLOAK and settings.CAPTCHA_KEY)

        if secrets_configured:
            self.results.append(ValidationResult(
                name="Secret Configuration",
                level=ValidationLevel.CRITICAL,
                passed=True,
                message="Required secrets are configured"
            ))
        else:
            self.results.append(ValidationResult(
                name="Secret Configuration",
                level=ValidationLevel.CRITICAL,
                passed=False,
                message="Some required secrets are missing"
            ))

    def _generate_summary(self) -> Dict[str, Any]:
        """Generate validation summary"""
        total = len(self.results)
        passed = sum(1 for r in self.results if r.passed)
        failed = total - passed

        critical_failed = sum(1 for r in self.results if not r.passed and r.level == ValidationLevel.CRITICAL)
        warnings = sum(1 for r in self.results if not r.passed and r.level == ValidationLevel.WARNING)

        startup_safe = critical_failed == 0

        return {
            'startup_safe': startup_safe,
            'total_checks': total,
            'passed': passed,
            'failed': failed,
            'critical_failures': critical_failed,
            'warnings': warnings,
            'results': self.results
        }

    def _log_validation_results(self, summary: Dict[str, Any]):
        """Log validation results"""
        logger.info("=" * 60)
        logger.info("🔍 STARTUP VALIDATION SUMMARY")
        logger.info("=" * 60)

        logger.info(f"Total Checks: {summary['total_checks']}")
        logger.info(f"Passed: {summary['passed']}")
        logger.info(f"Failed: {summary['failed']}")
        logger.info(f"Critical Failures: {summary['critical_failures']}")
        logger.info(f"Warnings: {summary['warnings']}")

        # Log individual results
        for result in self.results:
            if result.passed:
                logger.info(f"✅ {result.name}: {result.message}")
            elif result.level == ValidationLevel.CRITICAL:
                logger.error(f"❌ {result.name}: {result.message}")
            elif result.level == ValidationLevel.WARNING:
                logger.warning(f"⚠️  {result.name}: {result.message}")
            else:
                logger.info(f"ℹ️  {result.name}: {result.message}")

        # Final status
        if summary['startup_safe']:
            logger.info("🟢 STARTUP VALIDATION: PASSED - Application can start safely")
        else:
            logger.error("🔴 STARTUP VALIDATION: FAILED - Critical issues detected")

        logger.info("=" * 60)

async def validate_startup() -> Dict[str, Any]:
    """
    Convenience function to run startup validation.

    Returns:
        Validation summary dictionary
    """
    validator = StartupValidator()
    return await validator.run_all_validations()

async def validate_startup_or_fail():
    """
    Run startup validation and raise exception if critical issues are found.

    Raises:
        RuntimeError: If critical validation failures are detected
    """
    summary = await validate_startup()

    if not summary['startup_safe']:
        critical_messages = [
            result.message for result in summary['results']
            if not result.passed and result.level == ValidationLevel.CRITICAL
        ]

        error_msg = f"Startup validation failed with {summary['critical_failures']} critical issues:\n" + \
                   "\n".join(f"- {msg}" for msg in critical_messages)

        raise RuntimeError(error_msg)

    return summary