import { toast } from "sonner";

/**
 * Interface para definir opções de tratamento de erro
 */
export interface ErrorHandlingOptions {
  /** Mensagem personalizada para erros 500 */
  serverErrorMessage?: string;
  /** Título personalizado para o toast de erro */
  title?: string;
  /** Se deve mostrar o toast ou apenas retornar a mensagem */
  showToast?: boolean;
  /** Callback adicional para tratamento customizado */
  onError?: (error: any, message: string) => void;
}

/**
 * Interface para o resultado do tratamento de erro
 */
export interface ErrorHandlingResult {
  /** Mensagem de erro processada */
  message: string;
  /** Código de status HTTP se disponível */
  statusCode?: number;
  /** Indica se é um erro de servidor (500) */
  isServerError: boolean;
  /** Erro original */
  originalError: any;
}

/**
 * Extrai a mensagem de erro de diferentes estruturas de resposta de API
 */
const extractErrorMessage = (error: any): string => {
  // Verifica estruturas comuns de resposta de API primeiro
  if (error?.response?.data) {
    const data = error.response.data;

    // Estrutura: { detail: { message: "..." } } - Prioridade alta para APIs REST
    if (data?.detail?.message) {
      return data.detail.message;
    }

    // Estrutura: { detail: "..." } - quando detail é string direta
    if (data?.detail && typeof data.detail === "string") {
      return data.detail;
    }

    // Estrutura: { error: { message: "..." } }
    if (data?.error?.message) {
      return data.error.message;
    }

    // Estrutura: { message: "..." }
    if (data?.message) {
      return data.message;
    }
  }

  // Verifica se há uma mensagem no error.message (para erros de rede, etc.)
  if (error?.message && typeof error.message === "string") {
    return error.message;
  }

  // Fallback para error como string
  if (typeof error === "string") {
    return error;
  }

  return "Ocorreu um erro inesperado";
};

/**
 * Obtém o código de status HTTP do erro
 */
const getStatusCode = (error: any): number | undefined => {
  return error?.response?.status || error?.status || error?.statusCode;
};

/**
 * Processa um erro e retorna informações estruturadas
 */
export const processError = (
  error: any,
  options: ErrorHandlingOptions = {}
): ErrorHandlingResult => {
  const statusCode = getStatusCode(error);
  const isServerError = statusCode === 500;

  let message: string;

  if (isServerError && options.serverErrorMessage) {
    // Usa a mensagem personalizada para erros 500
    message = options.serverErrorMessage;
  } else {
    // Extrai a mensagem do erro para outros casos
    message = extractErrorMessage(error);
  }

  return {
    message,
    statusCode,
    isServerError,
    originalError: error,
  };
};

/**
 * Função principal para tratamento centralizado de erros em onError do TanStack Query
 */
export const handleQueryError = (
  error: any,
  options: ErrorHandlingOptions = {}
): ErrorHandlingResult => {
  const {
    serverErrorMessage,
    title = "Erro",
    showToast = true,
    onError,
  } = options;

  // Processa o erro
  const result = processError(error, { serverErrorMessage });

  // IMPORTANTE: Não processa erros 401 aqui - deixa para o RootApi emitir UNAUTHORIZED_ERROR
  if (result.statusCode === 401) {
    console.log("[handleQueryError] 401 error detected - skipping toast, letting HTTP interceptors handle it", {
      statusCode: result.statusCode,
      originalError: error,
      url: error?.config?.url,
      method: error?.config?.method
    });
    return result;
  }

  // Log do erro para debug
  console.error("Query Error:", {
    message: result.message,
    statusCode: result.statusCode,
    isServerError: result.isServerError,
    originalError: error,
  });

  // Mostra o toast se solicitado
  if (showToast) {
    toast.error(title, {
      description: result.message,
    });
  }

  // Executa callback personalizado se fornecido
  if (onError) {
    onError(error, result.message);
  }

  return result;
};

/**
 * Função de conveniência para criar handlers de erro padronizados
 * SEMPRE prioriza mensagens da API, usa fallbackMessage apenas quando não há mensagem da API
 */
export const createErrorHandler = (
  fallbackMessage: string,
  title?: string
) => {
  return (error: any) => {
    const result = processError(error);

    // IMPORTANTE: Não processa erros 401 aqui - deixa para o RootApi emitir UNAUTHORIZED_ERROR
    if (result.statusCode === 401) {
      console.log("401 error detected in createErrorHandler - skipping toast, letting HTTP interceptors handle it");
      return result;
    }

    // Prioriza mensagem da API SEMPRE, usa fallback apenas se não conseguir extrair
    // Mesmo para erros 500, se a API retornou uma mensagem, usa ela
    const finalMessage = result.message === "Ocorreu um erro inesperado"
      ? fallbackMessage
      : result.message;

    console.error("Query Error:", {
      message: finalMessage,
      statusCode: result.statusCode,
      isServerError: result.isServerError,
      originalError: error,
    });

    // Mostra o toast com a mensagem final
    toast.error(title || "Erro", {
      description: finalMessage,
    });

    return result;
  };
};

/**
 * Função de conveniência para criar handlers de erro sem toast
 */
export const createSilentErrorHandler = (
  serverErrorMessage: string,
  onError?: (error: any, message: string) => void
) => {
  return (error: any) => handleQueryError(error, {
    serverErrorMessage,
    showToast: false,
    onError
  });
};

/**
 * Função que SEMPRE prioriza mensagens da API sobre mensagens personalizadas
 * Usa fallbackMessage apenas quando não consegue extrair nenhuma mensagem da API
 */
export const createApiFirstErrorHandler = (
  fallbackMessage: string,
  title?: string
) => {
  return (error: any) => {
    const result = processError(error);

    // IMPORTANTE: Não processa erros 401 aqui - deixa para o RootApi emitir UNAUTHORIZED_ERROR
    if (result.statusCode === 401) {
      console.log("401 error detected in createApiFirstErrorHandler - skipping toast, letting HTTP interceptors handle it");
      return result;
    }

    // SEMPRE prioriza mensagem da API, mesmo para erros 500
    const finalMessage = result.message === "Ocorreu um erro inesperado"
      ? fallbackMessage
      : result.message;

    console.error("Query Error:", {
      message: finalMessage,
      statusCode: result.statusCode,
      isServerError: result.isServerError,
      originalError: error,
    });

    // Mostra o toast com a mensagem final
    toast.error(title || "Erro", {
      description: finalMessage,
    });

    return result;
  };
};

/**
 * Handlers pré-configurados para casos comuns
 * Exemplo de uso:
 * onError: commonErrorHandlers.auth
 */
export const commonErrorHandlers = {
  /** Handler para erros de autenticação/autorização */
  auth: createErrorHandler("Erro de autenticação. Tente fazer login novamente.", "Erro de Autenticação"),

  /** Handler para erros de validação */
  validation: createErrorHandler("Dados inválidos. Verifique os campos e tente novamente.", "Erro de Validação"),

  /** Handler para erros de rede */
  network: createErrorHandler("Erro de conexão. Verifique sua internet e tente novamente.", "Erro de Conexão"),

  /** Handler genérico */
  generic: createErrorHandler("Ocorreu um erro inesperado. Tente novamente mais tarde.", "Erro"),
};
