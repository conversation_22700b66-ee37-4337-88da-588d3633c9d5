import { Image, Text, View } from "./pdf-components";
import { PdfStyles } from "./styles";
import { LOGO_COMPLETA_HEADER } from "../config/assets";
import { IReportsPdfHeaderProps } from "../global";

export const ReportsPdfHeader = ({ title, organization_logo, should_print_snap_logo = true }: IReportsPdfHeaderProps) => {
  const headerHeight = organization_logo ? 100 : 'auto';
  const headerStyle = { ...PdfStyles.header, height: headerHeight };
  
  return (
    <View style={headerStyle} fixed className={"header"}>
      <View style={PdfStyles.logoContainerHeader} className={"logoContainerHeader"}>
        {organization_logo && <Image src={organization_logo} style={PdfStyles.organizationLogo} className="organizationLogo" />}
        {should_print_snap_logo && <Image src={LOGO_COMPLETA_HEADER} style={PdfStyles.reportsLogo} className="reportsLogo" />}
      </View>

      <View className="headerContent" style={PdfStyles.headerContent}>
        <Text style={PdfStyles.reportNameTitle} className={"reportNameTitle"}>{title}</Text>
      </View>
    </View>
  )
}

export const wrapHeaderWithInlineCSS = (html: string): string => {
  return `
    <style>${
    Object.entries(PdfStyles)
      .map(([key, value]) => `.${key} { ${Object.entries(value).map(([k, v]) => `${k}: ${v};`)
        .join(' ')} }`).join('\n')}
    }</style>
    ${html}
  `;
}