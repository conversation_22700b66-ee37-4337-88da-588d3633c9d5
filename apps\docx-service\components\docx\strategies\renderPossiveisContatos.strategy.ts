import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, getSingular, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintPossiveisContatosProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      nome_completo?: ValueWithSource;
      detalhes?: Record<string, ValueWithSource>;
      pessoa?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      telefones?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      enderecos?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      [key: string]: any;
    }>;
  };
}

export const renderPossiveisContatos = ({ section }: RenderPrintPossiveisContatosProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((contato) => {
    if (contato.nome_completo && !contato.nome_completo.is_deleted) {
      children.push(new Paragraph({
        children: [
          new TextRun({ text: `${(contato.nome_completo.label || "Nome").toUpperCase()}: `, bold: true, color: "FE473C" }),
          new TextRun(String(contato.nome_completo.value)),
          new TextRun({ text: ` | ${contato.nome_completo.source?.map(translateSource).join(', ')}`, size: 16, color: "FE473C" }),
        ],
        spacing: { after: 200 }
      }));
    }

    if (contato.detalhes) {
      const tableRows = Object.entries(contato.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => {
          return new TableRow({
            children: [
              new TableCell({
                children: [new Paragraph(translatePropToLabel(field.label || key).toUpperCase())],
                shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
              }),
              new TableCell({
                children: [new Paragraph(String(field.value))],
                shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
              }),
            ],
          });
        });

      if (tableRows.length > 0) {
        children.push(new Table({
          rows: tableRows,
          width: { size: 100, type: WidthType.PERCENTAGE },
          columnWidths: [3500, 5500],
        }));
      }
    }

    // Logic for pessoa, telefones, enderecos

  });

  return { children };
};
