import logging
import asyncio
import math
import copy
from collections import Counter
from datetime import datetime, timezone
import httpx
from fastapi import HTTPException, FastAPI
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, distinct, desc, asc, case, update, delete, literal, or_, union_all, String
from sqlalchemy.dialects.postgresql import insert, JSONB
from sqlalchemy.exc import SQLAlchemyError
from typing import List, Optional, Tuple
from uuid import UUID
import pytz

from database.db import async_session

from core.constants import (Fields,
                            Endpoints, UserFields,
                            DefaultPageLogs, SummaryReportStatus)

from models.report_model import UserReports
from models.user_columns_hmac import UserColumnsHmac
from models.user_model import Users
from models.folder_model import Folder
from models.user_report_ledger_model import UserReportLedger

from schemas.report_schema import SnapApiRequest, InsertReport, RenameReport
from schemas.user_schema import PaginatedResponse, PaginationMetadata
from schemas.user_report_ledger_schema import UserReportLedgerSchema

from services.base_service import BaseService
from services.SnapStatusTracker import SnapStatusTracker
from services.apikey_service import ApikeyService
from services.credits_service import CreditsService
from services.user_service import UserStandaloneService
from services.organization_users_service import OrganizationUsersService
from services.websocket import try_send_websocket
from services.folder_service import FolderService
from services.user_report_ledger_service import UserReportLedgerService
from services.report_executions_service import ReportExecutionsService

from utils.db_utils import retry_db_operation
from exceptions.business_exceptions import (
    ReportPermissionDeniedError,
    ReportApiKeyMissingError,
    ReportInsufficientCreditsError,
    FailToAccessUsersReportsError,
    InputValueSnapWrongError,
    ProblemsWithSnapApiError,
    SnapApiFailedError,
    SnapApiNoIdError,
    MaxRetriesExceededError,
    FailCreateEmptyReportError,
    FailUpdateErrorReportError,
    FailToAccessDataToGetPendingReportsError,
    FailToDeleteReportError,
    FailToDeleteHmacsError,
    FailToRenameReportError
)

logger = logging.getLogger(__name__)



class UserReportsService(BaseService):
    def __init__(self, db: AsyncSession, user_id: str = None, user_reports_id: str = None, organization_id: str = None) -> None:
        super().__init__(db)
        
        self.user_data = None
        self.user_id = user_id
        self.organization_id = organization_id
        self.user_reports_id = user_reports_id
        self.api_key = None


    def set_user_data(self, user_data: dict):
        self.user_data=user_data


    def set_api_key(self, api_key: str):
        return super().set_api_key(api_key)
    
    def set_organization_id(self, organization_id: str):
        return super().set_organization_id(organization_id)


    async def _apply_contextual_filters_to_query(self, query, is_user_scope: bool, model_class=None):
        """
        Applies contextual filters to a query based on the scope.
        Overrides the base method to add organization-specific logic for UserReports.

        :param query: The SQLAlchemy query object to modify.
        :param is_user_scope: If True, filters for the current user's context (personal or within their org).
                              If False, filters for the entire organization's context.
        :param model_class: The model class to use for filtering (defaults to UserReports)
        """
        # Use UserReports as default model class if none provided
        if model_class is None:
            model_class = UserReports
            
        # Call the base method first
        query = await super()._apply_contextual_filters_to_query(query, is_user_scope, model_class)
        
        # Add UserReports-specific organization logic
        if is_user_scope:
            organization_user_service = OrganizationUsersService(db=self.db, user_id=self.user_id)
            user_organization_data = await organization_user_service.get_organization_user()
            if not user_organization_data:
                # Standalone user: only reports with no organization_id
                logger.info("[_apply_contextual_filters_to_query] User %s is standalone. Filtering for reports with no organization_id.", self.user_id)
                query = query.where(UserReports.organization_id.is_(None))
            else:
                # User in an org: only reports for that user within that org
                organization_id = user_organization_data.organization_id
                self.organization_id = organization_id
                logger.info("[_apply_contextual_filters_to_query] User %s is in organization %s. Applying organization_id filter.", self.user_id, organization_id)
                query = query.where(UserReports.organization_id == organization_id)
        
        return query


    async def _apply_contextual_filters_to_user_report_ledger_query(self, query, is_user_scope: bool):
        """
        Applies contextual filters to a UserReportLedger query based on the scope.
        This method is specifically designed for UserReportLedger filtering.

        :param query: The SQLAlchemy query object to modify.
        :param is_user_scope: If True, filters for the current user's context (personal or within their org).
                              If False, filters for the entire organization's context.
        """
        logger.info(
            "[_apply_contextual_filters_to_user_report_ledger_query] Applying UserReportLedger filters for user %s with scope: %s (user_id type: %s, organization_id type: %s)",
            self.user_id, "user" if is_user_scope else "organization",
            type(self.user_id).__name__ if self.user_id else "None",
            type(self.organization_id).__name__ if self.organization_id else "None"
        )
        
        if is_user_scope:
            # Personal scope: filter by user_id
            if self.user_id:
                # Check if user_id is already a UUID object or needs conversion
                if hasattr(self.user_id, 'hex'):  # Already a UUID object
                    user_id_uuid = self.user_id
                    logger.debug("[_apply_contextual_filters_to_user_report_ledger_query] Applied user_id filter (UUID, already converted): %s", user_id_uuid)
                else:
                    user_id_uuid = UUID(str(self.user_id)) if self.user_id else None
                    logger.debug("[_apply_contextual_filters_to_user_report_ledger_query] Applied user_id filter (UUID, converted from string): %s", user_id_uuid)
                
                query = query.where(UserReportLedger.user_id == user_id_uuid)
                logger.info("[_apply_contextual_filters_to_user_report_ledger_query] Applied user_id filter for UserReportLedger: %s", user_id_uuid)
            else:
                logger.warning("[_apply_contextual_filters_to_user_report_ledger_query] User scope requested but no user_id is set for the service.")
                return query.where(literal(False))  # Return no results if user ID is missing
        else:
            # Organization scope: filter by organization_id
            if not self.organization_id:
                logger.warning("[_apply_contextual_filters_to_user_report_ledger_query] Organization scope requested but no organization_id is set for the service.")

                return query.where(literal(False))  # Return no results if org ID is missing
            
            logger.info("[_apply_contextual_filters_to_user_report_ledger_query] Applying organization scope filter for organization_id: %s", self.organization_id)
            
            # Check if organization_id is already a UUID object or needs conversion
            if hasattr(self.organization_id, 'hex'):  # Already a UUID object
                org_id_uuid = self.organization_id
                logger.debug("[_apply_contextual_filters_to_user_report_ledger_query] Applied organization_id filter (UUID, already converted): %s", org_id_uuid)
            else:
                org_id_uuid = UUID(str(self.organization_id)) if self.organization_id else None
                logger.debug("[_apply_contextual_filters_to_user_report_ledger_query] Applied organization_id filter (UUID, converted from string): %s", org_id_uuid)
            
            query = query.where(UserReportLedger.organization_id == org_id_uuid)
            logger.info("[_apply_contextual_filters_to_user_report_ledger_query] Applied organization_id filter for UserReportLedger: %s", org_id_uuid)
        
        return query


    async def get_all_logs(self,
                           column_order: str,
                           report_type: str = None,
                           limit:int = DefaultPageLogs.pagedefault , 
                           page:int = 1, order:str = "desc",
                            is_user: bool = True, created_start_at: datetime = None,
                            created_end_at:datetime = None):
        """
        Id can be from the organization or from the user
        Returns paginated response with data and pagination metadata
        """

        # Base query for data
        base_query = select(
            UserReportLedger.created_at,
            UserReportLedger.user_id,
            UserReportLedger.report_type,
            UserReportLedger.user_reports_id,
            Users.name,
            Users.role,
            Users.email
        ).join(Users, UserReportLedger.user_id == Users.user_id)

        # Count query for total items
        count_query = select(func.count(UserReportLedger.user_reports_id)).join(Users, UserReportLedger.user_id == Users.user_id)

        # Apply contextual filters based on user or organization scope
        base_query = await self._apply_contextual_filters_to_user_report_ledger_query(base_query, is_user_scope=is_user)
        count_query = await self._apply_contextual_filters_to_user_report_ledger_query(count_query, is_user_scope=is_user)

        # Apply common filters to both queries
        common_filters = []
        if report_type:
            common_filters.append(UserReportLedger.report_type == report_type)
        if created_start_at:
            common_filters.append(UserReportLedger.created_at >= created_start_at)
        if created_end_at:
            common_filters.append(UserReportLedger.created_at <= created_end_at)

        if common_filters:
            base_query = base_query.where(and_(*common_filters))
            count_query = count_query.where(and_(*common_filters))

        page = max(1, page)
        offset = (page - 1) * limit if limit else None

        column_map = {
            "created_at": UserReportLedger.created_at,
            "updated_at": UserReportLedger.updated_at,
        }

        user_or_org_id = self.user_id if is_user else self.organization_id
        user_or_org = "user" if is_user else "organization"
        if column_order not in column_map:
            logger.warning("[get_all_logs][%s(%s)] Invalid column_order: '%s', defaulting to 'created_at'", user_or_org, user_or_org_id, column_order)

        sort_column = column_map.get(column_order, UserReportLedger.created_at)
        sort_func = asc if order.lower() == "asc" else desc

        base_query = base_query.order_by(sort_func(sort_column))

        if limit:
            base_query = base_query.limit(limit)
            if offset:
                base_query = base_query.offset(offset)
            logger.info("[get_all_logs][%s(%s)] Limit: %s | Page: %s | Offset: %s", user_or_org, user_or_org_id, limit, page, offset)

        try:
            # Execute both queries
            result = await self.db.execute(base_query)
            rows = result.mappings().all()

            count_result = await self.db.execute(count_query)
            total_items = count_result.scalar()

            # Calculate pagination metadata
            total_pages = math.ceil(total_items / limit) if limit > 0 else 1
            has_next = page < total_pages
            has_previous = page > 1

            data = [dict(r) for r in rows]

            pagination_metadata = PaginationMetadata(
                current_page=page,
                total_pages=total_pages,
                total_items=total_items,
                page_size=limit,
                has_next=has_next,
                has_previous=has_previous
            )

            logger.info("[get_all_logs][%s(%s)] Found %s reports. Total: %s, Page: %s/%s",
                       user_or_org, user_or_org_id, len(rows), total_items, page, total_pages)

            return PaginatedResponse(data=data, pagination=pagination_metadata)
        except Exception as e:
            logger.error("[get_all_logs][%s(%s)] Error executing query: %s", user_or_org, user_or_org_id, e)
            raise


    async def get_reports_by_folder(self, folder_id: str, limit: int, page: int, 
                                    order: str, column_order: str, 
                                    hmac_filter: Optional[List[str]] = None, 
                                    hmac_column: Optional[str] = "report_name"):
        """
        Get reports by folder
        """
        logger.info(f"[get_reports_by_folder] Fetching reports for folder_id={folder_id}")

        base_query = select(
            UserReports.report_name,
            UserReports.subject_name,
            UserReports.user_reports_id,
            UserReports.created_at,
            UserReports.modified_at,
            Folder.folder_name
        ).outerjoin(Folder, UserReports.folder_id == Folder.folder_id).where(UserReports.user_id == self.user_id)

        count_query = select(func.count(UserReports.user_reports_id)).where(UserReports.user_id == self.user_id)

        common_filters = []
        
        if hmac_filter:
            user_reports_id_hmac = await self.filter_using_hmac(hmac_filter=hmac_filter, hmac_column=hmac_column)
            common_filters.append(UserReports.user_reports_id.in_(user_reports_id_hmac))

        if folder_id:
            folder_service = FolderService(db=self.db, user_id=self.user_id)
            folder_ids = await folder_service.get_folders_except(folder_id=folder_id)
            logger.info(f"[get_reports_by_folder] folder_ids: {folder_ids}")
            common_filters.append(UserReports.folder_id.in_(folder_ids))

        organization_users_service = OrganizationUsersService(db=self.db, user_id=self.user_id)
        org_data = await organization_users_service.get_organization_user()
        if org_data:
            logger.info(f"[get_reports_by_folder] Active organization found for user: {self.user_id}")
            common_filters.append(UserReports.organization_id == org_data.organization_id)

        if common_filters:
            base_query = base_query.where(and_(*common_filters))
            count_query = count_query.where(and_(*common_filters))

        page = max(1, page)
        offset = (page - 1) * limit if limit else None

        column_map = {
            "created_at": UserReports.created_at,
            "modified_at": UserReports.modified_at,
        }

        sort_column = column_map.get(column_order, UserReports.modified_at)
        sort_func = asc if order.lower() == "asc" else desc

        base_query = base_query.order_by(sort_func(sort_column))

        if limit:
            base_query = base_query.limit(limit)
            if offset:
                base_query = base_query.offset(offset)

        try:
            # Execute both queries
            result = await self.db.execute(base_query)
            rows = result.mappings().all()

            count_result = await self.db.execute(count_query)
            total_items = count_result.scalar()

            # Calculate pagination metadata
            total_pages = math.ceil(total_items / limit) if limit > 0 else 1
            has_next = page < total_pages
            has_previous = page > 1

            data = [dict(r) for r in rows]

            pagination_metadata = PaginationMetadata(
                current_page=page,
                total_pages=total_pages,
                total_items=total_items,
                page_size=limit,
                has_next=has_next,
                has_previous=has_previous
            )

            return PaginatedResponse(data=data, pagination=pagination_metadata)
        except Exception as e:
            logger.error("[get_reports_by_folder][user(%s)] Error executing query: %s", self.user_id, e)
            raise


    async def validate_access_to_report(self, report_type: str):

        user_service = UserStandaloneService(db=self.db, user_id=self.user_id)
        user_data = await user_service.get_user_data()

        user_report_types = user_data.report_types
        if report_type not in user_report_types:
            logger.warning("[validate_access_to_report] No permission of type %s for user: %s", 
                        report_type , self.user_id)
            raise ReportPermissionDeniedError()
            

        api_key_service = ApikeyService(db=self.db, user_id=self.user_id)
        self.api_key = await api_key_service.get_api_key()

        if self.api_key is None:
            logger.warning("[validate_access_to_report] No api_key for user: %s", 
                            self.user_id)
            raise ReportApiKeyMissingError()

        credits_service = CreditsService(db=self.db, user_id=self.user_id)
        credits_service.set_api_key(api_key=self.api_key)

        user_data_dict = {Fields.total_credits: user_data.credits,
                          UserFields.credits_monthly: user_data.credits_monthly,
                          UserFields.next_reset_credits: user_data.next_reset_credits
                          }

        minimun_credits, db_credits = await credits_service.compare_user_credits_with_api_credits(user_data_dict)


        if minimun_credits<=0:
            logger.warning("[validate_access_to_report] No credits for user: %s", self.user_id)
            raise ReportInsufficientCreditsError()
        if db_credits:
            await credits_service.change_user_credits(credit_delta=-1)
        
        return self.api_key
    

    async def get_saved_reports_handler(self, 
                                        limit: int,  page: int,
                                        order: str, column_order: str, 
                                        hmac_filter: List, hmac_column: str, 
                                        folder_id: str = None, get_data_child_folders: bool = False):
        
        logger.info(f"[get_saved_reports_handler] Called with limit={limit}, page={page}, "
                    f"order={order}, column_order={column_order}, hmac_filter={hmac_filter}, "
                    f"hmac_column={hmac_column}, folder_id={folder_id}, get_data_child_folders={get_data_child_folders}")
        page = max(1, page)
        try:
            #get the 25 reports from the main folder
            logger.info(f"[get_saved_reports_handler] Fetching saved reports for folder_id={folder_id}")
            saved_reports = await retry_db_operation(
                lambda: self.list_user_reports(
                    limit=limit,
                    page=page,
                    order=order,
                    column_order=column_order, 
                    hmac_filter=hmac_filter,
                    hmac_column=hmac_column,
                    folder_id=folder_id,
                    get_data_child_folders=get_data_child_folders
                )
            )
            logger.info(f"[get_saved_reports_handler] Got {len(saved_reports)} saved reports for folder_id={folder_id}")

            #get the folders from the main folder
            folder_service = FolderService(db=self.db, user_id=self.user_id)
            logger.info(f"[get_saved_reports_handler] Fetching subfolders for folder_id={folder_id}")
            subfolders = await folder_service.list_user_folders(limit=limit, page=page, order=order, 
                                                                column_order=column_order, hmac_filter=hmac_filter,
                                                                folder_id=folder_id)
            logger.info(f"[get_saved_reports_handler] Got {len(subfolders)} subfolders for folder_id={folder_id}")
            folders_in_saved_reports = set()
            #check on subfolders if the subfolder is already there if is already there remove from subfolders
            logger.info(f"[get_saved_reports_handler] Removing subfolders that are already present in saved_reports")
            for subfolder in subfolders[:]:
                for saved_report in saved_reports:
                    if subfolder.get("folder_id") == saved_report.get("folder_id"):
                        logger.info(f"[get_saved_reports_handler] Removing subfolder {subfolder.get('folder_id')} as it is already present in saved_reports")
                        subfolders.remove(subfolder)
                        folders_in_saved_reports.add(subfolder.get("folder_id"))
                        break

            logger.info(f"[get_saved_reports_handler] Merging subfolders and saved_reports into a new list with limit={limit}")
            if subfolders:
                for folder in subfolders:

                    if not saved_reports:
                        logger.info(f"[get_saved_reports_handler] No saved_reports, adding folder {folder['folder_id']} directly to saved_reports")
                        saved_reports.append(folder)
                        folders_in_saved_reports.add(folder.get("folder_id"))
                        continue
                    
                    for idx, saved_report in enumerate(saved_reports):
                        logger.info(f"[get_saved_reports_handler] Comparing folder.modified_at={folder['modified_at']} with saved_report.modified_at={saved_report['modified_at']}")
                        if folder["modified_at"] > saved_report["modified_at"]:
                            logger.info(f"[get_saved_reports_handler] Adding folder {folder['folder_id']} to saved_reports")
                            saved_reports.insert(idx, folder)
                            folders_in_saved_reports.add(folder.get("folder_id"))
                            break
                        elif folder["modified_at"] < saved_report["modified_at"] and idx == len(saved_reports) - 1:
                            logger.info(f"[get_saved_reports_handler] Adding folder {folder['folder_id']} to saved_reports")
                            saved_reports.append(folder)
                            folders_in_saved_reports.add(folder.get("folder_id"))

            elif not hmac_filter:
                logger.info(f"[get_saved_reports_handler] No subfolders, sending saved_reports")
                return saved_reports
            user_reports_ids = None
            folder_ids_to_add = None
            if hmac_filter:
                folder_data = None
                folder_path = None
                if folder_id:
                    folder_data = await folder_service.get_folder_by_id(folder_id=folder_id)
                    logger.info(f"[get_saved_reports_handler] folder_data: {folder_data}")
                    folder_path = folder_data.folder_path
                
                logger.info(f"[get_saved_reports_handler] folder_data: {folder_data}")
                user_reports_ids = await self.list_user_reports_deeper(hmac_filter=hmac_filter, hmac_column=hmac_column)
                logger.info(f"[get_saved_reports_handler] user_reports_ids: {user_reports_ids}")
                folder_ids_to_add = await self.list_folders_deeper(hmac_filter=hmac_filter)
                logger.info(f"[get_saved_reports_handler] folder_ids_to_add: {folder_ids_to_add}")

                report_folder_map, folder_path_map = await self.get_report_and_folder_paths(user_reports_ids, folder_ids_to_add, folder_id_base= folder_id)
                logger.info(f"[get_saved_reports_handler] folder_path_map: {folder_path_map}")

                set_of_folders_to_add = self.get_next_folders_to_add(folder_path, folder_path_map)
                logger.info(f"[get_saved_reports_handler] set_of_folders_to_add (before filter): {set_of_folders_to_add}")

                set_of_folders_to_add = self.filter_existing_folders(set_of_folders_to_add, folders_in_saved_reports)
                logger.info(f"[get_saved_reports_handler] set_of_folders_to_add (after filter): {set_of_folders_to_add}")

                await self.insert_folders_by_modified_at(set_of_folders_to_add, saved_reports, folder_service, folders_in_saved_reports, limit*page)


            if get_data_child_folders:
                logger.info(f"[get_saved_reports_handler] get_data_child_folders is True, fetching data for child folders in saved_reports")
                #will check on new list if the item is a folder or a report, if is a folder will find the child folders
                if saved_reports:
                    for item in saved_reports:
                        if item.get("folder_id"):
                            logger.info(f"[get_saved_reports_handler] Recursively fetching data for child folder {item.get('folder_id')}")
                            data_on_folder = await self.get_saved_reports_handler(limit=limit, page=page, order=order, 
                                                                                    column_order=column_order, hmac_filter=hmac_filter,
                                                                                    hmac_column=hmac_column, folder_id=item.get("folder_id"))
                            number_default_of_mini_items=11
                            item["data"] = data_on_folder[:number_default_of_mini_items]
                            item["number_of_items"]= await self.number_of_items_subfolder(
                                folder_id=item.get("folder_id"),
                                user_reports_id_hmac=user_reports_ids,
                                folder_reports_id_hmac=folder_ids_to_add,
                                number_default_of_mini_items=number_default_of_mini_items
                            )
            logger.info(f"[get_saved_reports_handler] Returning new_list with {len(saved_reports)} items")


            offset = (page - 1) * limit
            saved_reports = saved_reports[offset:offset + limit:1]
            logger.info(f"[get_saved_reports_handler] Applying limit {limit} and offset {offset}")



            logger.info(f"[get_saved_reports_handler][user({self.user_id})] Limit: {limit} | Page: {page} | Offset: {offset}")

            return saved_reports
                
        except Exception as e:
            logger.error(f"[get_saved_reports_handler] Failed: {e}")
            raise FailToAccessUsersReportsError()


    async def number_of_items_subfolder(self, folder_id, user_reports_id_hmac: List[str] = None, folder_reports_id_hmac: List[str] = None, number_default_of_mini_items: int = 11):
        """
        Get the number of items in a subfolder.
        """
        logger.info(f"[number_of_items_subfolder] Called with folder_id={folder_id}")
        try:
            number_of_reports = await self.number_of_report_in_subfolder(folder_id, user_reports_id_hmac)
            logger.info(f"[number_of_items_subfolder] Number of reports in folder {folder_id}: {number_of_reports}")
            number_of_folders = await self.number_of_folder_in_subfolder(folder_id, folder_reports_id_hmac) 
            logger.info(f"[number_of_items_subfolder] Number of folders in folder {folder_id}: {number_of_folders}")
            total_items = number_of_reports + number_of_folders
            logger.info(f"[number_of_items_subfolder] Total items in folder {folder_id}: {total_items}")
            if total_items - number_default_of_mini_items<=0:
                logger.info(f"[number_of_items_subfolder] Total items in folder {folder_id} is less than or equal to 0, returning 0")
                return None
            return total_items-number_default_of_mini_items
        except Exception as e:
            logger.error(f"[number_of_items_subfolder] Error counting items in folder {folder_id}: {e}")
            raise FailToAccessUsersReportsError()            

        
    async def number_of_report_in_subfolder(self, folder_id: str, user_reports_id_hmac: List[str] = None):
        try:
            query = select(func.count(UserReports.user_reports_id)).where(UserReports.folder_id == folder_id)
            if user_reports_id_hmac:
                logger.info(f"[number_of_report_in_subfolder] Filtering by user_reports_id_hmac: {user_reports_id_hmac}")
                query = query.where(UserReports.user_reports_id.in_(user_reports_id_hmac))
            result = await self.db.execute(query)
            count = result.scalar()
            logger.info(f"[number_of_report_in_subfolder] Found {count} reports in folder {folder_id}")
            return count
        except Exception as e:
            logger.error(f"[number_of_report_in_subfolder] Error counting items in folder {folder_id}: {e}")
            raise FailToAccessUsersReportsError()


    async def number_of_folder_in_subfolder(self, folder_id: str, folder_reports_id_hmac: List[str] = None):
        try:
            logger.info(f"[number_of_folder_in_subfolder] Counting folders in subfolder: folder_id={folder_id}, folder_reports_id_hmac={folder_reports_id_hmac}")
            
            # Start with base query to count folders that are direct children of the specified folder
            query = select(func.count(Folder.folder_id)).where(Folder.parent_folder_id == folder_id)
            
            # Apply user context filter
            query = query.where(Folder.user_folder_id == self.user_id)
            logger.info(f"[number_of_folder_in_subfolder] Applied user filter: user_id={self.user_id}")
            
            # Apply organization context filter if user belongs to an organization
            organization_users_service = OrganizationUsersService(db=self.db, user_id=self.user_id)
            org_data = await organization_users_service.get_organization_user()
            if org_data:
                query = query.where(Folder.organization_id == org_data.organization_id)
                logger.info(f"[number_of_folder_in_subfolder] Applied organization filter: organization_id={org_data.organization_id}")
            
            # Apply HMAC filter if provided
            if folder_reports_id_hmac:
                logger.info(f"[number_of_folder_in_subfolder] Filtering by folder_reports_id_hmac: {folder_reports_id_hmac}")
                query = query.where(Folder.folder_id.in_(folder_reports_id_hmac))
            
            result = await self.db.execute(query)
            count = result.scalar()
            logger.info(f"[number_of_folder_in_subfolder] Found {count} folders in subfolder {folder_id}")
            return count
        except Exception as e:
            logger.error(f"[number_of_folder_in_subfolder] Error counting folders in folder {folder_id}: {e}")
            raise FailToAccessUsersReportsError()
        

    async def get_report_and_folder_paths(self, user_reports_ids, extra_folder_ids, folder_id_base=None):
        """Get mapping from user_reports_id to folder_id and from folder_id to folder_path."""
        stmt = select(UserReports.user_reports_id, UserReports.folder_id).where(
            UserReports.user_reports_id.in_(user_reports_ids)
        )

        organization_users_service = OrganizationUsersService(db=self.db, user_id=self.user_id)
        org_data = await organization_users_service.get_organization_user()
        if org_data:
            logger.info(f"[get_report_and_folder_paths] Active organization found for user: {self.user_id}")
            stmt = stmt.where(UserReports.organization_id == org_data.organization_id)

        result = await self.db.execute(stmt)
        report_folder_map = {str(row[0]): str(row[1]) for row in result.fetchall() if row[1]}

        # Get all unique folder_ids
        folder_ids = list(set(report_folder_map.values()) | set(extra_folder_ids))

        # Get mapping from folder_id to folder_path
        stmt = select(Folder.folder_id, Folder.folder_path).where(Folder.folder_id.in_(folder_ids))
        if folder_id_base:
            stmt = stmt.where(Folder.folder_path.ilike(f"%{folder_id_base}%"))

        if org_data:
            logger.info(f"[get_report_and_folder_paths] Active organization found for user: {self.user_id}")
            stmt = stmt.where(Folder.organization_id == org_data.organization_id)

        result = await self.db.execute(stmt)
        folder_path_map = {str(row[0]): row[1] for row in result.fetchall()}
        return report_folder_map, folder_path_map


    def get_next_folders_to_add(self, base_folder_path, folder_path_map):
        """Get the set of next folder UUIDs after base_folder_path in each folder_path."""
        logger.info(f"[get_next_folders_to_add] Called with base_folder_path='{base_folder_path}', folder_path_map={folder_path_map}")
        set_of_folders_to_add = set()
        for report_folder_path in folder_path_map.values():
            logger.info(f"[get_next_folders_to_add] Processing report_folder_path='{report_folder_path}'")
            next_folder = self.get_next_uuid_after_path(base_folder_path, report_folder_path)
            logger.info(f"[get_next_folders_to_add] Next folder after base_folder_path: '{next_folder}'")
            if next_folder:
                set_of_folders_to_add.add(next_folder)
        logger.info(f"[get_next_folders_to_add] Resulting set_of_folders_to_add: {set_of_folders_to_add}")
        return set_of_folders_to_add


    def filter_existing_folders(self, set_of_folders_to_add, folders_in_saved_reports):
        """Remove folders already present in saved_reports from the set to add."""
        logger.info(f"[filter_existing_folders] Called with set_of_folders_to_add={set_of_folders_to_add}, folders_in_saved_reports={folders_in_saved_reports}")
        filtered_set = set_of_folders_to_add - folders_in_saved_reports
        logger.info(f"[filter_existing_folders] Resulting filtered_set: {filtered_set}")
        return filtered_set


    async def insert_folders_by_modified_at(self, set_of_folders_to_add, saved_reports, folder_service, folders_in_saved_reports, limit):
        """Insert folders into saved_reports in order by modified_at, avoiding duplicates."""
        logger.info(f"[insert_folders_by_modified_at] Called with set_of_folders_to_add={set_of_folders_to_add}, saved_reports_count={len(saved_reports)}, folders_in_saved_reports={folders_in_saved_reports}, limit={limit}")
        for folder_id in set_of_folders_to_add:
            logger.info(f"[insert_folders_by_modified_at] Processing folder_id={folder_id}")
            folder = await folder_service.get_folder_by_id(folder_id=folder_id)
            logger.info(f"[insert_folders_by_modified_at] Folder data: {folder}")
            if saved_reports:
                for idx, saved_report in enumerate(saved_reports):
                    logger.info(f"[insert_folders_by_modified_at] Comparing folder.modified_at={folder.modified_at} with saved_report[{idx}].modified_at={saved_report['modified_at']}")
                    if folder.modified_at > saved_report["modified_at"]:
                        logger.info(f"[insert_folders_by_modified_at] Inserting folder at position {idx}")
                        folder_dict ={
                            "folder_id": str(folder.folder_id),
                            "folder_name": folder.folder_name,
                            "parent_folder_id": str(folder.parent_folder_id) if folder.parent_folder_id else None,
                            "folder_path": folder.folder_path,
                            "depth_level": folder.depth_level,
                            "created_at": folder.created_at,
                            "modified_at": folder.modified_at,
                            "item_type": "folder"
                        }
                        saved_reports.insert(idx, folder_dict)
                        folders_in_saved_reports.add(folder.folder_id)
                        break
                    elif folder.modified_at < saved_report["modified_at"] and idx == len(saved_reports) - 1 and len(saved_reports) < limit:
                        logger.info(f"[insert_folders_by_modified_at] Appending folder at the end of saved_reports")
                        folder_dict ={
                            "folder_id": str(folder.folder_id),
                            "folder_name": folder.folder_name,
                            "parent_folder_id": str(folder.parent_folder_id) if folder.parent_folder_id else None,
                            "folder_path": folder.folder_path,
                            "depth_level": folder.depth_level,
                            "created_at": folder.created_at,
                            "modified_at": folder.modified_at,
                            "item_type": "folder"
                        }
                        saved_reports.append(folder_dict)
                        folders_in_saved_reports.add(folder.folder_id)
                        break
            else:
                logger.info(f"[insert_folders_by_modified_at] No saved_reports, inserting folder at the end")
                folder_dict ={
                            "folder_id": str(folder.folder_id),
                            "folder_name": folder.folder_name,
                            "parent_folder_id": str(folder.parent_folder_id) if folder.parent_folder_id else None,
                            "folder_path": folder.folder_path,
                            "depth_level": folder.depth_level,
                            "created_at": folder.created_at,
                            "modified_at": folder.modified_at,
                            "item_type": "folder"
                        }
                saved_reports.append(folder_dict)
                folders_in_saved_reports.add(folder.folder_id)
        logger.info(f"[insert_folders_by_modified_at] Final saved_reports_count={len(saved_reports)}, folders_in_saved_reports={folders_in_saved_reports}")


    async def list_user_reports(
        self,
        limit: int = 25,
        page: int = 1,
        order: str = "desc",
        column_order: str = "modified_at",
        hmac_filter: Optional[List] = None,
        hmac_column: Optional[str] = None,
        offset: Optional[int] = None,
        folder_id: str = None,
        get_data_child_folders: bool = False
    ) -> List[dict]:
        """
        List user reports with pagination and optional HMAC filtering.
        """
        logger.info(f"[list_user_reports] Called with limit={limit}, page={page}, "
                    f"order={order}, column_order={column_order}, hmac_filter={hmac_filter}, "
                    f"hmac_column={hmac_column}, folder_id={folder_id}, get_data_child_folders={get_data_child_folders}")
        user_reports_ids = None

        if hmac_filter:
            try:
                logger.info(f"[list_user_reports] Filtering using HMAC: {hmac_filter}, column: {hmac_column}")
                user_reports_ids = await self.filter_using_hmac(hmac_filter=hmac_filter, hmac_column=hmac_column)
                logger.info(f"[list_user_reports] Found {len(user_reports_ids)} user_reports_ids matching HMAC filter")
            except Exception as e:
                logger.error(f"[list_user_reports][user({self.user_id})] Error while filtering with HMAC: {e}")
                raise
        if get_data_child_folders:
            logger.info(f"[list_user_reports] get_data_child_folders is True, selecting all report fields")
            query = select(
                UserReports.report_name,
                UserReports.report_status,
                UserReports.modified_at,
                UserReports.created_at,
                UserReports.report_type,
                UserReports.report_search_args,
                UserReports.user_reports_id,
                UserReports.subject_age,
                UserReports.subject_mother_name,
                UserReports.subject_name,
                UserReports.subject_sex,
            UserReports.subject_person_count,
            UserReports.subject_company_count,
            UserReports.folder_id
            )
        else:
            logger.info(f"[list_user_reports] get_data_child_folders is False, selecting basic report fields")
            query = select(
                UserReports.report_name,
                UserReports.report_status,
                UserReports.modified_at,
                UserReports.created_at,
                UserReports.folder_id,
                UserReports.subject_name
            )

        # if folder_id:
        logger.info(f"[list_user_reports] Filtering by folder_id: {folder_id}")
        # 1. Get the base folder_path for the given folder_id
        query = query.where(UserReports.folder_id==folder_id)




        if user_reports_ids:
            logger.info(f"[list_user_reports] Filtering by user_reports_ids: {user_reports_ids}")
            query = query.where(UserReports.user_reports_id.in_(user_reports_ids))
        elif hmac_filter: # If a filter was provided but no IDs were found
            logger.info(f"[list_user_reports] HMAC filter provided but no matching user_reports_ids found, returning empty list")
            return []

        logger.info(f"[list_user_reports] Applying contextual filters to query")
        query = await self._apply_contextual_filters_to_query(query, is_user_scope=True, model_class=UserReports)



        column_map = {
            "created_at": UserReports.created_at,
            "modified_at": UserReports.modified_at,
            "report_name": UserReports.report_name,
        }
        if column_order not in column_map:
            logger.warning(f"[list_user_reports][user({self.user_id})] Invalid column_order: '{column_order}', defaulting to 'created_at'")

        sort_column = column_map.get(column_order, UserReports.created_at)
        sort_func = asc if order.lower() == "asc" else desc

        # Use CASE WHEN to prioritize 'pending' report_status (when it's a valid JSON)
        pending_first = case(
            (UserReports.report_status["status_report"].astext == "pending", 0),
            else_=1
        )

        logger.info(f"[list_user_reports] Ordering query by pending_first and {column_order} {order}")
        query = query.order_by(pending_first, sort_func(sort_column))

        query = query.limit(limit*page)
        logger.info(f"[list_user_reports] Limiting the results by limit ({limit}) and page ({page})")
        try:
            logger.info(f"[list_user_reports] Executing query")
            result = await self.db.execute(query)
            rows = result.mappings().all()
            logger.info(f"[list_user_reports] Query returned {len(rows)} rows")
            final_results = []
            for r in rows:
                report = dict(r)
                folder_id_to_fetch = report.get("folder_id")
                report["parent_folder_id"] = report.pop("folder_id", None)
                if folder_id_to_fetch is None:
                    # If folder_id is None, set folder_name as None
                    report["folder_name"] = None
                    final_results.append(report)
                    continue
                if str(folder_id_to_fetch) != str(folder_id):
                    logger.info(f"[list_user_reports] Report folder_id {folder_id_to_fetch} != requested folder_id {folder_id}, fetching folder info from DB")
                    folder_id_to_fetch = report.get("parent_folder_id")
                    if folder_id_to_fetch is None:
                        logger.info(f"[list_user_reports] Report has no folder_id (None), skipping folder info append.")
                        continue 
                    folder = await self.db.get(Folder, folder_id_to_fetch)
                    folder_dict = {
                        "folder_id": str(folder.folder_id),
                        "folder_name": folder.folder_name,
                        "parent_folder_id": str(folder.parent_folder_id) if folder.parent_folder_id else None,
                        "folder_path": folder.folder_path,
                        "depth_level": folder.depth_level,
                        "created_at": folder.created_at,
                        "modified_at": folder.modified_at
                    }
                    logger.info(f"[list_user_reports] Appending folder_dict for folder_id {folder.folder_id} to final_results")
                    final_results.append(folder_dict)
                else:
                    logger.info(f"[list_user_reports] Report belongs to requested folder_id {folder_id}, appending report info to final_results")
                    # Remove any folder-related keys that might be present
                    for key in [
                        "folder_name", "folder_path", "folder_depth_level", "next_segment", "row_number"
                    ]:
                        report.pop(key, None)
                    final_results.append(report)
            logger.info(f"[list_user_reports] Returning final_results with {len(final_results)} items")
            return final_results
        except Exception as e:
            logger.error(f"[list_user_reports][user({self.user_id})] Error executing query: {e}")
            raise


    async def list_user_reports_deeper(self, hmac_filter: str, hmac_column: str):
        logger.info(f"[list_user_reports_deeper] Called with hmac_filter={hmac_filter}, hmac_column={hmac_column}")
        user_reports_ids = await self.filter_using_hmac(hmac_filter=hmac_filter, hmac_column=hmac_column)
        logger.info(f"[list_user_reports_deeper] Found {len(user_reports_ids)} user_reports_ids matching HMAC filter")
        return user_reports_ids


    async def list_folders_deeper(self, hmac_filter: List):
        logger.info(f"[list_folders_deeper] Called with hmac_filter={hmac_filter}")
        folder_service = FolderService(db=self.db, user_id=self.user_id)
        folder_ids = await folder_service.filter_using_hmac_folder(hmac_filter=hmac_filter)
        logger.info(f"[list_folders_deeper] Found {len(folder_ids)} folder_ids matching HMAC filter")
        return folder_ids


    async def populate_report_handler(self, body: InsertReport):

        logger.info("[populate_report_handler][user(%s)] Populating report with data", self.user_id)

        await self._populate_report_data(
            report_name=body.report_name,
            report_type=body.report_type,
            report_status=body.report_status,
            report_search_args=body.report_search_args,
            subject_name=body.subject_name,
            subject_mother_name=body.subject_mother_name,
            subject_age=body.subject_age,
            subject_sex=body.subject_sex,
            subject_person_count=body.subject_person_count,
            subject_company_count=body.subject_company_count,
            number_of_relations=body.number_of_relations,
            data=body.data
        )

        if body.hmac: #TODO REMOVE THIS CONDITIONAL WHEN WE START TO USE
            await self.insert_hmacs(hmac=body.hmac)


    async def _populate_report_data(self,
                                report_name: dict, report_type: str, report_status: dict, 
                                report_search_args: dict,
                                subject_name: dict, subject_mother_name: dict, 
                                subject_age: int, subject_sex: dict,
                                subject_person_count: dict,
                                subject_company_count: dict, number_of_relations: dict,
                                data: dict):
    
        logger.info("[_populate_report_data][user(%s)] Populating report with full data.", self.user_id)
        try:
            modified_at = datetime.now(pytz.timezone("UTC"))
            
            update_dict = {
                Fields.report_name: report_name,
                Fields.report_type: report_type,
                Fields.report_status: report_status,
                Fields.report_search_args: report_search_args,
                Fields.subject_name: subject_name,
                Fields.subject_mother_name: subject_mother_name,
                Fields.subject_age: subject_age,
                Fields.subject_sex: subject_sex,
                Fields.modified_at: modified_at,
                Fields.subject_person_count: subject_person_count,
                Fields.subject_company_count: subject_company_count,
                Fields.number_of_relations: number_of_relations,
                Fields.data: data
            }

            stmt = (
                update(UserReports)
                .where(UserReports.user_reports_id == self.user_reports_id)
                .where(UserReports.user_id == self.user_id)
                .values(**update_dict)
                .returning(UserReports.folder_id)
                )

            result = await self.db.execute(stmt)
            folder_id = result.scalar_one()
            await self.db.commit()
            logger.info("[update_blank_report][user(%s)] Blank report updated successfully.", self.user_id)


            if folder_id:
                logger.info(f"[populate_report_handler] folder_id provided: {folder_id}, fetching folder data")
                folder_service = FolderService(db=self.db, user_id=self.user_id)
                folder_data = await folder_service.get_folder_by_id(folder_id=folder_id)
                logger.info(f"[populate_report_handler] Fetched folder data for folder_id={folder_id}, folder_path={folder_data.folder_path}")
                logger.info(f"[populate_report_handler] Updating parent folders' modified_at for folder_path={folder_data.folder_path}")
                await folder_service.update_parent_folders_modified_at(folder_path=folder_data.folder_path)
                logger.info(f"[populate_report_handler] Updated parent folders' modified_at for folder_path={folder_data.folder_path}")



            report_executions_service = ReportExecutionsService(self.db, self.user_id)
            await report_executions_service.update_columns(
                user_reports_id=self.user_reports_id,
                updates={"dt_salvo_pelo_frontend": modified_at, "status_tentativa": SummaryReportStatus.success}
            )

            logger.info("[update_blank_report][user(%s)] ReportExecutions entry updated for user_reports_id=%s.", self.user_id, self.user_reports_id)

            user_report_ledger_service = UserReportLedgerService(self.db, self.user_id) 
            await user_report_ledger_service.update(
                user_reports_id=self.user_reports_id,
                status= SummaryReportStatus.success,
                updated_at=modified_at
            )

            logger.info("[update_blank_report][user(%s)] UserReportLedger entry updated for user_reports_id=%s.", self.user_id, self.user_reports_id)
        except Exception as e:
            logger.error("[update_blank_report][user(%s)] Error: %s", self.user_id, e)
            await self.db.rollback()


    async def get_one_report_handler(self):
        logger.info("[get_one_report_handler][user(%s)] Fetching one report", self.user_id)
        return await self.db.get(UserReports, (self.user_id, self.user_reports_id))


    async def get_data_from_snap_api_handler(self, body: SnapApiRequest) -> JSONResponse:
        logger.info("[get_data_from_snap_api_handler][user(%s)] Fetching data from SNAP API", self.user_id)

        headers = {
            'Ocp-Apim-Subscription-Key': self.api_key,
            'Accept': 'application/json'
        }

        #extract from user the group and access allowed)
        report_type = body.report_type

        request_id = ''
        if report_type == "relacoes":
            request_payload = {
                                "documento_1": body.report_input_value.get("cpf_1", body.report_input_value.get("cnpj_1")),
                                "documento_2": body.report_input_value.get("cpf_2", body.report_input_value.get("cnpj_2"))}

        else:
            request_payload = {report_type: body.report_input_value}

        # Log the outgoing request details
        logger.info("[get_data_from_snap_api_handler][user(%s)] Sending request to SNAP API endpoint: %s", self.user_id, Endpoints.snap_report_enpoint + "/" + report_type)
        logger.info("[get_data_from_snap_api_handler][user(%s)] Request headers: %s", self.user_id, headers)
        logger.info("[get_data_from_snap_api_handler][user(%s)] Request payload: %s", self.user_id, request_payload)

        async with httpx.AsyncClient() as client:
            if report_type == "relacoes":
                response = await client.post(Endpoints.relacoes_endpoint, headers=headers, json=request_payload)
            else:
                response = await client.post(Endpoints.snap_report_enpoint + "/" + report_type, headers=headers, json=request_payload)

        if response.status_code==422:
            logger.error("[get_data_from_snap_api_handler][user(%s)] Input value format wrong", self.user_id)
            raise InputValueSnapWrongError()
        
        if response.status_code==500:
            logger.error("[get_data_from_snap_api_handler][user(%s)] Problems with snap api", self.user_id)
            raise ProblemsWithSnapApiError()

        if response.status_code not in [200, 202]:
            logger.error("[get_data_from_snap_api_handler][user(%s)] Snap API failed", self.user_id)
            raise SnapApiFailedError(response.status_code)

        request_id = response.json().get('id')
        if not request_id:
            logger.error("[get_data_from_snap_api_handler][user(%s)] Snap API returned no ID", self.user_id)
            raise SnapApiNoIdError()

        return {"id": request_id}


    async def snap_status_ws(self, app: FastAPI, request_id: str, report_type: str,
                            report_number: str, report_search_args: dict,
                            api_key: str) -> None:
        """
        Track SNAP status for a report request.

        Args:
            app: FastAPI application instance
            request_id: SNAP API request identifier
            user_id: User identifier
            report_type: Type of report being processed
            report_number: Report number
            report_search_args: Search arguments for the report
            reports_id: Internal report identifier
        """
        tracker = SnapStatusTracker(app, self.user_id, self.user_reports_id, api_key)
        await tracker.track_status(request_id, report_type, report_number, report_search_args)


    async def handle_max_retries_exceeded(self,
                                        app, client_id, user_reports_id, 
                                        parsed_body, exception):
            
        """Handle case when max retries are exceeded during Snap API requests."""
        logger.error(
            f"[get_data_from_snap_api][user({self.user_id})] All retry attempts failed: {str(exception)}"
        )

        # Handle server errors from snap API differently
        if isinstance(exception, HTTPException) and exception.status_code == 500:
            # Create report with error status for server-side failures
            report_status = {"status_report": SummaryReportStatus.error}
            user_reports_id = await self.create_blank_report_with_status(
                snap_request_id=client_id,
                status_report=report_status,
                report_type=parsed_body.report_type,
                report_input_encrypted=parsed_body.report_input_encrypted,
                folder_id=parsed_body.folder_id
            )
        else:
            # Update existing report to error state
            if user_reports_id:
                await self.update_error_report(
                    snap_request_id=client_id,
                    error_detail=str(exception)
                )
                logger.info(
                    f"[get_data_from_snap_api][user({self.user_id})] Updated status to error for report {user_reports_id}"
                )

        # Send websocket error notification if possible
        try:
            connection_manager = app.state.connection_manager
            await try_send_websocket(
                connection_manager,
                self.user_id,
                user_reports_id,
                {
                    "id": user_reports_id,
                    "status_code": SummaryReportStatus.error
                }
            )
            logger.info(
                f"[get_data_from_snap_api][user({self.user_id})] Sent error via WebSocket for report {user_reports_id}"
            )
        except Exception as ws_error:
            logger.error(
                f"[get_data_from_snap_api][user({self.user_id})] Failed to send WebSocket error: {str(ws_error)}"
            )

        # Raise appropriate exception to caller
        raise MaxRetriesExceededError()


    async def create_blank_report_with_status(self, status_report: dict, report_type: str, 
                                              report_input_encrypted: dict = None, folder_id: str = None):
        """Create a blank report with status"""
        logger.info("[create_blank_report_with_status][user(%s)] Creating blank report.", self.user_id)
        
        try:

            created_at = datetime.now(pytz.timezone("UTC"))

            user_report_ledger_service = UserReportLedgerService(self.db, self.user_id)
            log_data = UserReportLedgerSchema(
                created_at=created_at,
                status_final=SummaryReportStatus.pending,
                updated_at=created_at,
                organization_id=self.organization_id,
                report_type=report_type,
                user_id=self.user_id
            )
            user_reports_id = await user_report_ledger_service.create(log_data)

            logger.info(
                "[create_blank_report_with_status][user(%s)] Creating UserReportLedger entry with: user_reports_id=%s, created_at=%s, status_final=%s, updated_at=%s, organization_id=%s, report_type=%s, user_id=%s, folder_id=%s",
                self.user_id,
                user_reports_id,
                created_at,
                SummaryReportStatus.pending,
                created_at,
                self.organization_id,
                report_type,
                self.user_id,
                folder_id
            )


            new_report = UserReports(
                user_id=self.user_id,
                user_reports_id = user_reports_id,
                created_at=created_at,
                modified_at=created_at,
                report_status=status_report,
                report_type=report_type,
                report_search_args=report_input_encrypted,
                organization_id=self.organization_id,
                folder_id=folder_id
            )
            self.db.add(new_report)
            await self.db.commit()
            await self.db.refresh(new_report)
            
            # Update the service's user_reports_id with the new report
            self.user_reports_id = new_report.user_reports_id

            if folder_id:
                logger.info(f"[create_blank_report_with_status] folder_id provided: {folder_id}, fetching folder data")
                folder_service = FolderService(db=self.db, user_id=self.user_id)
                folder_data = await folder_service.get_folder_by_id(folder_id=folder_id)
                logger.info(f"[create_blank_report_with_status] Fetched folder data for folder_id={folder_id}, folder_path={folder_data.folder_path}")
                logger.info(f"[create_blank_report_with_status] Updating parent folders' modified_at for folder_path={folder_data.folder_path}")
                await folder_service.update_parent_folders_modified_at(folder_path=folder_data.folder_path)
                logger.info(f"[create_blank_report_with_status] Updated parent folders' modified_at for folder_path={folder_data.folder_path}")
            
            logger.info("[create_blank_report_with_status][user(%s)] Blank report created successfully.", self.user_id)


            logger.info(
                "[create_blank_report_with_status][user(%s)] Creating ReportExecutions entry with: user_reports_id=%s, dt_inicio_tentativa=%s, status_tentativa=%s, error_detail=%s, dt_deleted=%s, dt_aquisicao_snap=%s, dt_chegada_minio_reports=%s, dt_proc_spark=%s, dt_chegada_minio_processed_reports=%s, dt_envio_websocket=%s",
                self.user_id,
                new_report.user_reports_id,
                created_at,
                SummaryReportStatus.pending,
                None,
                None,
                None,
                None,
                None,
                None,
                None
            )

            report_executions_service = ReportExecutionsService(self.db, self.user_id)
            await report_executions_service.create_if_no_pending(
                user_reports_id=new_report.user_reports_id,
                user_id=self.user_id,
                dt_inicio_tentativa=created_at,
                status_tentativa=SummaryReportStatus.pending,
                detalhe_erro=None,
                dt_deleted=None,
                dt_aquisicao_snap=None,
                dt_chegada_minio_reports=None,
                dt_proc_spark=None,
                dt_chegada_minio_processed_reports=None,
                dt_envio_websocket=None,
                dt_salvo_pelo_frontend = None,
            )
            logger.info(
                "[create_blank_report_with_status][user(%s)] ReportExecutions entry created for user_reports_id=%s.",
                self.user_id,
                new_report.user_reports_id
            )


            return new_report.user_reports_id
        except SQLAlchemyError as e:
            logger.error("[create_blank_report_with_status][user(%s)] Database error: %s", self.user_id, e)
            await self.db.rollback()
            raise FailCreateEmptyReportError()


    async def update_error_report_to_pending(self, status_report: dict):
        """Update error report to pending status"""
        logger.info("[update_error_report_to_pending][user(%s)] Updating error report to pending.", self.user_id)
        
        if not self.user_reports_id:
            logger.error("[update_error_report_to_pending][user(%s)] No report ID provided", self.user_id)
            raise ValueError("Report ID is required")
            
        try:
            modified_at = datetime.now(pytz.timezone("UTC"))

            report_executions_service = ReportExecutionsService(self.db, self.user_id)
            await report_executions_service.create_if_no_pending(
                user_reports_id=self.user_reports_id,
                user_id=self.user_id,
                dt_inicio_tentativa=modified_at,
                status_tentativa=SummaryReportStatus.pending,
                detalhe_erro=None,
                dt_deleted=None,
                dt_aquisicao_snap=None,
                dt_chegada_minio_reports=None,
                dt_proc_spark=None,
                dt_chegada_minio_processed_reports=None,
                dt_envio_websocket=None,
                dt_salvo_pelo_frontend = None,
            )
            logger.info("[update_error_report_to_pending][user(%s)] ReportExecutions entry created for user_reports_id=%s.", self.user_id, self.user_reports_id)


            update_query = (
                update(UserReports)
                .where(UserReports.user_reports_id == self.user_reports_id)
                .where(UserReports.user_id == self.user_id)
                .values({Fields.report_status: literal(status_report, type_=JSONB), 
                         Fields.modified_at: modified_at})
                .returning(UserReports.folder_id)
            )
            
            result = await self.db.execute(update_query)
            logger.info(f"[update_error_report_to_pending] Result: {result}")
            folder_id = result.scalar()
            logger.info(f"[update_error_report_to_pending] folder_id: {folder_id}")
            await self.db.commit()
            logger.info("[update_error_report_to_pending][user(%s)] Updated error report to pending.", self.user_id)


            user_report_ledger_service = UserReportLedgerService(self.db, self.user_id)
            await user_report_ledger_service.update(
                user_reports_id=self.user_reports_id,
                status= SummaryReportStatus.pending,
                updated_at=modified_at
            )
            logger.info("[update_error_report_to_pending][user(%s)] Updated user_report_ledger to pending.", self.user_id)


            if folder_id:
                folder_service = FolderService(db=self.db, user_id=self.user_id)
                folder_data = await folder_service.get_folder_by_id(folder_id=folder_id)
                logger.info(f"[update_error_report_to_pending] Fetched folder data for folder_id={folder_id}, folder_path={folder_data.folder_path}")
                logger.info(f"[update_error_report_to_pending] Updating parent folders' modified_at for folder_path={folder_data.folder_path}")
                await folder_service.update_parent_folders_modified_at(folder_path=folder_data.folder_path)
                logger.info(f"[update_error_report_to_pending] Updated parent folders' modified_at for folder_path={folder_data.folder_path}")

        except SQLAlchemyError as e:
            logger.error("[update_error_report_to_pending][user(%s)] Database error: %s", self.user_id, e)
            await self.db.rollback()
            raise FailCreateEmptyReportError()


    async def update_error_report(self, snap_request_id: str, error_detail: str = None):
        """Update report status to error"""
        logger.info("[update_error_report][user(%s)] Updating report to error status.", self.user_id)
        
        if not self.user_reports_id:
            logger.error("[update_error_report][user(%s)] No report ID provided", self.user_id)
            raise ValueError("Report ID is required")

        try:
            modified_at = datetime.now(pytz.timezone("UTC"))

            report_status = {
                "status_report": SummaryReportStatus.error,
                "snap_request_id": snap_request_id
            }

            result = await self.db.execute(
                update(UserReports)
                .where(UserReports.user_reports_id == self.user_reports_id)
                .where(UserReports.user_id == self.user_id)
                .values({Fields.report_status: literal(report_status, type_=JSONB), 
                         Fields.modified_at: modified_at})
                .returning(UserReports.folder_id)
            )
            folder_id = result.scalar()
            logger.info(f"[update_error_report] folder_id: {folder_id}")
            await self.db.commit()

            logger.info("[update_error_report][user(%s)] Report updated to error.", self.user_id)

            user_report_ledger_service = UserReportLedgerService(self.db, self.user_id)
            await user_report_ledger_service.update(
                user_reports_id=self.user_reports_id,
                status= SummaryReportStatus.error,
                updated_at=modified_at
            )
            logger.info("[update_error_report][user(%s)] Updated user_report_ledger to error.", self.user_id)

            report_executions_service = ReportExecutionsService(self.db, self.user_id)
            updates = {"status_tentativa": SummaryReportStatus.error, "detalhe_erro": error_detail}

            await report_executions_service.update_columns(
                user_reports_id=self.user_reports_id,
                updates=updates
            )
            logger.info("[update_error_report][user(%s)] Updated report_executions to error.", self.user_id)

            if folder_id:
                folder_service = FolderService(db=self.db, user_id=self.user_id)
                folder_data = await folder_service.get_folder_by_id(folder_id=folder_id)
                logger.info(f"[update_error_report] Fetched folder data for folder_id={folder_id}, folder_path={folder_data.folder_path}")
                logger.info(f"[update_error_report] Updating parent folders' modified_at for folder_path={folder_data.folder_path}")
                await folder_service.update_parent_folders_modified_at(folder_path=folder_data.folder_path)
                logger.info(f"[update_error_report] Updated parent folders' modified_at for folder_path={folder_data.folder_path}")
                
        except Exception as e:
            logger.error("[update_error_report][user(%s)] Failed to update report: %s", self.user_id, e)
            raise FailUpdateErrorReportError()
        

    async def get_pending_reports(self) -> List[Tuple[str, str, dict]]:
        """Get pending reports for a user"""
        logger.info(f"[get_pending_reports][user({self.user_id})] Fetching pending reports...")

        try:
            report_status = {"status_report": SummaryReportStatus.pending}

            result = await self.db.execute(
                select(
                    UserReports.user_reports_id,
                    UserReports.report_type,
                    UserReports.report_status
                )
                .where(UserReports.user_id == self.user_id)
                .where(UserReports.report_status.contains(report_status))
            )

            reports = result.fetchall()

            if not reports:
                logger.info(f"[get_pending_reports][user({self.user_id})] No pending reports found.")
                return []

            logger.info(f"[get_pending_reports][user({self.user_id})] Found {len(reports)} pending reports.")
            return reports
        except Exception as e:
            logger.error(f"[get_pending_reports][user({self.user_id})] Error: {e}")
            raise FailToAccessDataToGetPendingReportsError()


    async def get_number_of_report_type(self, report_type: str) -> str:
        """Get number of reports by type"""
        logger.info("[get_number_of_report_type][user(%s)] Getting number of types of a report for a user.", self.user_id)
        
        stmt = select(func.count()).select_from(UserReports).where(
                        UserReports.user_id == self.user_id,
                        UserReports.report_type == report_type)

        organization_users_service = OrganizationUsersService(db=self.db, user_id=self.user_id)
        org_data = await organization_users_service.get_organization_user()
        if org_data:
            logger.info(f"[get_number_of_report_type][user({self.user_id})] Active organization found for user: {self.user_id}")
            stmt = stmt.where(UserReports.organization_id == org_data.organization_id)



        result = await self.db.execute(stmt)
        
        count = result.scalar_one_or_none() or 0
        logger.info(f"[get_number_of_report_type][user({self.user_id})] Number of report types: {count}")
        return str(count)


    async def insert_hmacs(self, hmac: dict):
        """Insert HMACs for a report"""
        if not self.user_reports_id:
            logger.error("[insert_hmacs] No user_reports_id provided")
            raise ValueError("user_reports_id is required")
            
        try:
            logger.info("[insert_hmacs] Inserting hmacs for user_reports_id: %s", self.user_reports_id)

            rows_to_insert = []
            for column_name, hmac_list in hmac['n-grams'].items():
                for hmac_value in set(hmac_list):
                    rows_to_insert.append({
                        'hmac': hmac_value,
                        'column_name': column_name,
                        'user_reports_id': self.user_reports_id
                    })

            if rows_to_insert:
                stmt = insert(UserColumnsHmac).values(rows_to_insert).on_conflict_do_nothing()
                await self.db.execute(stmt)
                await self.db.commit()

            logger.info("[insert_hmacs] Successfully inserted hmacs for user_reports_id: %s", self.user_reports_id)

        except Exception as e:
            logger.error("[insert_hmacs] Error inserting hmacs for user_reports_id %s: %s", self.user_reports_id, e)
            await self.db.rollback()
            raise


    async def filter_using_hmac(self, hmac_filter: List[str], hmac_column: str = None) -> List[str]:
        """Filter reports using HMAC - must match ALL hmacs in hmac_filter."""
        user_reports_ids = []

        if hmac_filter:
            filters = []
            if hmac_column:
                filters.append(UserColumnsHmac.column_name == hmac_column)
            filters.append(UserColumnsHmac.hmac.in_(hmac_filter))

            try:
                # Group by user_reports_id and count distinct hmacs matched
                stmt = (
                    select(UserColumnsHmac.user_reports_id)
                    .where(and_(*filters))
                    .group_by(UserColumnsHmac.user_reports_id)
                    .having(func.count(distinct(UserColumnsHmac.hmac)) == len(hmac_filter))
                )
                result = await self.db.execute(stmt)
                user_reports_ids = [row[0] for row in result.fetchall()]
                logger.info("[filter_using_hmac][ALL] Fetched report_ids using hmac filter: %s | column: %s => %s", 
                            hmac_filter, hmac_column, user_reports_ids)

                if not user_reports_ids:
                    logger.warning("[filter_using_hmac][ALL] No user_reports_id found with given filters: hmac=%s, column=%s", 
                                  hmac_filter, hmac_column)
                    return []

            except Exception as e:
                logger.error("[filter_using_hmac][ALL] Error executing HMAC filter query: %s", e)
                raise

        return user_reports_ids


    def get_next_uuid_after_path(self, current_path: str, target_path: str) -> str:
        """
        Extract the next UUID after the current_path in the target_path.
        Helper method to navigate folder hierarchy.
        """
        logger.info(f"[get_next_uuid_after_path] Called with current_path='{current_path}', target_path='{target_path}'")
        if not target_path:
            logger.info(f"[get_next_uuid_after_path] target_path is empty. Returning empty string.")
            return ""
        if not current_path:
            logger.info(f"[get_next_uuid_after_path] current_path is None or empty (root). Returning first non-empty segment of target_path as next UUID.")
            segments = [seg for seg in target_path.split('/') if seg]
            first_segment = segments[0] if segments else ''
            logger.info(f"[get_next_uuid_after_path] First non-empty segment (UUID) from target_path: '{first_segment}'")
            return first_segment
        if target_path.startswith(current_path):
            remaining = target_path[len(current_path):]
            logger.info(f"[get_next_uuid_after_path] Remaining path after current_path: '{remaining}'")
            # Find the next UUID (before the next slash)
            next_slash = remaining.find('/')
            logger.info(f"[get_next_uuid_after_path] Position of next slash: {next_slash}")
            if next_slash > 0:
                next_uuid = remaining[:next_slash]
                logger.info(f"[get_next_uuid_after_path] Next UUID found before slash: '{next_uuid}'")
                return next_uuid
            elif remaining:
                logger.info(f"[get_next_uuid_after_path] No slash found, remaining is the next UUID: '{remaining}'")
                return remaining
        logger.info(f"[get_next_uuid_after_path] target_path does not start with current_path or no next UUID found. Returning empty string.")
        return ""


    async def delete_report_handler(self):
        logger.info("[delete_report_handler] Deleting report %s.", self.user_reports_id)

        try:
            # Fetch the folder_id before deleting the report
            stmt = select(UserReports.folder_id).where(UserReports.user_reports_id == self.user_reports_id, UserReports.user_id == self.user_id)
            result = await self.db.execute(stmt)
            folder_id = result.scalar_one_or_none()
            logger.info(f"[delete_report_handler] Folder_id before deletion: {folder_id}")

            #delete from hmac
            await self.delete_hmacs()

            # Delete the report from the database
            await self.delete_report()
            updated_time = datetime.now(timezone.utc)
            user_report_ledger_service = UserReportLedgerService(db=self.db, user_id=self.user_id)
            await user_report_ledger_service.update(user_reports_id=self.user_reports_id, status="deleted", updated_at=updated_time)

            report_executions_service = ReportExecutionsService(db=self.db, user_id=self.user_id)
            update_data = {
                "dt_deleted": updated_time
            }
            await report_executions_service.update_columns_to_delete(user_reports_id=self.user_reports_id, updates=update_data)

            if folder_id:
                folder_service = FolderService(db=self.db, user_id=self.user_id)
                folder = await folder_service.get_folder_by_id(folder_id)
                logger.info(f"[delete_report_handler] Folder before deletion: {folder}")
                await folder_service.update_parent_folders_modified_at(folder.folder_path)
                logger.info(f"[delete_report_handler] Folder modified_at after deletion: {folder.modified_at}")

        except Exception as e:
            logger.error("[delete_report_handler] Error deleting report: %s", e)
            raise FailToDeleteReportError()


    async def delete_report(self):
        logger.info("[delete_report] Deleting report %s.", self.user_reports_id)

        try:
            await self.db.execute(delete(UserReports).where(UserReports.user_reports_id == self.user_reports_id).where(UserReports.user_id == self.user_id))
            await self.db.commit()
        except Exception as e:
            logger.error("[delete_report] Error deleting report: %s", e)
            raise FailToDeleteReportError()


    async def delete_hmacs(self):
        logger.info("[delete_hmacs] Deleting hmacs for report %s.", self.user_reports_id)

        try:
            await self.db.execute(delete(UserColumnsHmac).where(UserColumnsHmac.user_reports_id == self.user_reports_id))
            await self.db.commit()
        except Exception as e:
            logger.error("[delete_hmacs] Error deleting hmacs: %s", e)
            raise FailToDeleteHmacsError()
        

    async def rename_report_handler(self, body: RenameReport):
        logger.info("[rename_report_handler] Renaming report %s.", self.user_reports_id)

        try:
            await self.db.execute(update(UserReports)
            .where(UserReports.user_reports_id == self.user_reports_id)
            .where(UserReports.user_id == self.user_id)
            .values(report_name=body.report_name, modified_at=datetime.now(timezone.utc)))
            await self.db.commit()
        except Exception as e:
            logger.error("[rename_report_handler] Error renaming report: %s", e)
            raise FailToRenameReportError()


        await self.delete_hmac_report_name()
        await self.insert_hmacs(hmac=body.hmac)
        

    async def delete_hmac_report_name(self):
        logger.info("[delete_hmac_report_name] Deleting hmac report name for report %s.", self.user_reports_id)

        try:
            await self.db.execute(delete(UserColumnsHmac).where(UserColumnsHmac.user_reports_id == self.user_reports_id).where(UserColumnsHmac.column_name == "report_name"))
            await self.db.commit()
        except Exception as e:
            logger.error("[delete_hmac_report_name] Error deleting hmac report name: %s", e)
            raise FailToDeleteHmacsError()


    async def list_user_items_unified(self, 
                                     limit: int, 
                                     page: int,
                                     order: str, 
                                     column_order: str, 
                                     hmac_filter: List = None, 
                                     hmac_column: str = None, 
                                     folder_id: str = None,
                                     get_data_child_folders: bool = False) -> Tuple[List[dict], int]:
        """
        Unified query that merges reports and folders at the database level using UNION ALL.
        Returns a single paginated, ordered list with total count.
        """

        folders_in_saved_reports = set()
        logger.info("[list_user_items_unified] Fetching unified items with limit=%s, page=%s, order=%s, column_order=%s, folder_id=%s", 
                   limit, page, order, column_order, folder_id)
        
        # Build reports query with conditional column selection
        if get_data_child_folders:
            logger.info("[list_user_items_unified] get_data_child_folders is True, selecting all report fields")
            report_q = (
                select(
                    UserReports.user_reports_id.label("user_reports_id"),
                    UserReports.folder_id.label("folder_id"),
                    UserReports.modified_at.label("modified_at"),
                    literal("report").label("item_type"),
                    UserReports.report_name.label("report_name"),
                    UserReports.report_type.label("report_type"),
                    UserReports.report_status.label("report_status"),
                    UserReports.report_search_args.label("report_search_args"),
                    UserReports.subject_age.label("subject_age"),
                    UserReports.subject_mother_name.label("subject_mother_name"),
                    UserReports.subject_name.label("subject_name"),
                    UserReports.subject_sex.label("subject_sex"),
                    UserReports.subject_person_count.label("subject_person_count"),
                    UserReports.subject_company_count.label("subject_company_count"),
                    UserReports.created_at.label("created_at"),
                    literal(None, type_=UserReports.folder_id.type).label("parent_folder_id"),
                    literal(None).label("folder_name"),
                    literal(None).label("folder_path"),
                    literal(None).label("depth_level"),
                )
            )
        else:
            logger.info("[list_user_items_unified] get_data_child_folders is False, selecting basic report fields")
            report_q = (
                select(
                    UserReports.user_reports_id.label("user_reports_id"),
                    UserReports.folder_id.label("folder_id"),
                    UserReports.modified_at.label("modified_at"),
                    literal("report").label("item_type"),
                    UserReports.report_name.label("report_name"),
                    UserReports.report_type.label("report_type"),
                    UserReports.report_status.label("report_status"),
                    literal(None).label("report_search_args"),
                    literal(None).label("subject_age"),
                    literal(None).label("subject_mother_name"),
                    UserReports.subject_name.label("subject_name"),
                    literal(None).label("subject_sex"),
                    literal(None).label("subject_person_count"),
                    literal(None).label("subject_company_count"),
                    UserReports.created_at.label("created_at"),
                    literal(None, type_=UserReports.folder_id.type).label("parent_folder_id"),
                    literal(None).label("folder_name"),
                    literal(None).label("folder_path"),
                    literal(None).label("depth_level"),
                )
            )
        
        # Apply contextual filters for reports (includes organization filtering)
        report_q = await self._apply_contextual_filters_to_query(report_q, is_user_scope=True, model_class=UserReports)
        

        # Filter by folder when provided; otherwise, fetch root-level reports (no folder)
        if folder_id:
            report_q = report_q.where(UserReports.folder_id == (folder_id if hasattr(folder_id, 'hex') else UUID(str(folder_id))))
        else:
            report_q = report_q.where(UserReports.folder_id.is_(None))
            
        # Add HMAC filtering for reports if provided
        if hmac_filter:
            user_reports_ids = await self.filter_using_hmac(hmac_filter=hmac_filter, hmac_column=hmac_column)
            if not user_reports_ids:
                logger.info(f"[list_user_items_unified] No user_reports_ids found for HMAC filter {hmac_filter}, returning empty list")

            report_q = report_q.where(UserReports.user_reports_id.in_(user_reports_ids))
        
        # Build folders query with explicit FROM clause and manual filtering to avoid cartesian product
        folder_q = (
            select(
                literal(None, type_=UserReports.user_reports_id.type).label("user_reports_id"),
                Folder.folder_id.label("folder_id"),
                Folder.modified_at.label("modified_at"),
                literal("folder").label("item_type"),
                literal(None).label("report_name"),
                literal(None).label("report_type"),
                literal(None).label("report_status"),
                literal(None).label("report_search_args"),
                literal(None).label("subject_age"),
                literal(None).label("subject_mother_name"),
                literal(None).label("subject_name"),
                literal(None).label("subject_sex"),
                literal(None).label("subject_person_count"),
                literal(None).label("subject_company_count"),
                Folder.created_at.label("created_at"),
                Folder.parent_folder_id.label("parent_folder_id"),
                Folder.folder_name.label("folder_name"),
                Folder.folder_path.label("folder_path"),
                Folder.depth_level.label("depth_level"),
            )
            .select_from(Folder)
            .where(Folder.user_folder_id == (self.user_id if hasattr(self.user_id, 'hex') else UUID(str(self.user_id))) if self.user_id else None)  # Manual user filtering to avoid cartesian product
        )
        
        # Apply organization filtering manually if needed
        if self.organization_id:
            folder_q = folder_q.where(Folder.organization_id == (self.organization_id if hasattr(self.organization_id, 'hex') else UUID(str(self.organization_id))))
        else:
            # For standalone users, filter for folders with no organization_id
            folder_q = folder_q.where(Folder.organization_id.is_(None))
        
        # Filter by folder when provided; otherwise, fetch root-level folders (no parent)
        if folder_id:
            folder_q = folder_q.where(Folder.parent_folder_id == (folder_id if hasattr(folder_id, 'hex') else UUID(str(folder_id))))
        else:
            folder_q = folder_q.where(Folder.parent_folder_id.is_(None))
            
        # Add HMAC filtering for folders if provided
        if hmac_filter:
            folder_service = FolderService(db=self.db, user_id=self.user_id)
            folder_ids = await folder_service.filter_using_hmac_folder(hmac_filter)
            if not folder_ids:
                logger.info(f"[list_user_items_unified] No folder_ids found for HMAC filter {hmac_filter}, returning empty list")

            folder_q = folder_q.where(Folder.folder_id.in_(folder_ids))
        
        # Combine with UNION ALL
        unified = union_all(report_q, folder_q).subquery("unified")
        
        # Build final query with ordering and pagination
        order_column = unified.c.modified_at
        if column_order == "created_at":
            # Note: folders don't have created_at in the same way, so we'll use modified_at
            order_column = unified.c.modified_at
            
        order_direction = desc if order.lower() == "desc" else asc
        
        final = (
            select(
                unified.c.user_reports_id,
                unified.c.folder_id,
                unified.c.modified_at,
                unified.c.item_type,
                unified.c.report_name,
                unified.c.report_type,
                unified.c.report_status,
                unified.c.report_search_args,
                unified.c.subject_age,
                unified.c.subject_mother_name,
                unified.c.subject_name,
                unified.c.subject_sex,
                unified.c.subject_person_count,
                unified.c.subject_company_count,
                unified.c.created_at,
                unified.c.parent_folder_id,
                unified.c.folder_name,
                unified.c.folder_path,
                unified.c.depth_level,
                func.count().over().label("total_count"),
            )
            .order_by(order_direction(order_column))
            .limit(limit)
            .offset((page - 1) * limit)
        )

        try:
            # Log the SQL query for debugging
            logger.info("[list_user_items_unified] Executing final query with SQL: %s", str(final.compile(compile_kwargs={"literal_binds": True})))
            
            result = await self.db.execute(final)
            rows = result.mappings().all()
            
            logger.info("[list_user_items_unified] Raw database result - Number of rows: %s", len(rows))
            if rows:
                logger.info("[list_user_items_unified] First row sample: %s", dict(rows[0]))
                logger.info("[list_user_items_unified] Last row sample: %s", dict(rows[-1]))
            
            # Extract total count from first row (all rows have the same total_count due to window function)
            total_count = 0
            if rows:
                total_count = rows[0].get("total_count", 0)
                logger.info("[list_user_items_unified] Total count from database: %s", total_count)
            
            # Convert to list of dicts, removing total_count and filtering fields by item type
            items = []
            logger.info("[list_user_items_unified] Processing %s rows from database", len(rows))
            
            for i, row in enumerate(rows):
                item = dict(row)
                
                # Convert asyncpg UUID objects to regular Python UUID objects
                for key, value in item.items():
                    if hasattr(value, '__class__') and 'UUID' in str(value.__class__):
                        try:
                            item[key] = str(value)
                        except:
                            pass
                
                # Determine the correct ID field based on item type
                item_id = item.get("user_reports_id") if item.get("item_type") == "report" else item.get("folder_id")
                if item.get("item_type") == "folder":
                    folders_in_saved_reports.add(item.get("folder_id"))

                logger.info("[list_user_items_unified] Processing row %s: item_id=%s, item_type=%s", 
                          i, item_id, item.get("item_type"))
                
                # Remove total_count from individual items as it's not part of the item data
                item.pop("total_count", None)
                
                # Filter fields based on item type
                item_type = item.get("item_type")
                if item_type == "folder":
                    # Keep only folder-related fields
                    filtered_item = {
                        "folder_id": item.get("folder_id"),
                        "parent_folder_id": item.get("parent_folder_id"),
                        "modified_at": item.get("modified_at"),
                        "created_at": item.get("created_at"),
                        "item_type": item.get("item_type"),
                        "folder_name": item.get("folder_name"),
                        "folder_path": item.get("folder_path"),
                        "depth_level": item.get("depth_level")
                    }
                    logger.info("[list_user_items_unified] Created folder item: %s", filtered_item.get("folder_id"))
                elif item_type == "report":
                    # Keep only report-related fields
                    filtered_item = {
                        "user_reports_id": item.get("user_reports_id"),
                        "parent_folder_id": item.get("folder_id"),
                        "modified_at": item.get("modified_at"),
                        "created_at": item.get("created_at"),
                        "item_type": item.get("item_type"),
                        "report_name": item.get("report_name"),
                        "report_type": item.get("report_type"),
                        "report_status": item.get("report_status"),
                        "subject_name": item.get("subject_name")
                    }
                    
                    # Add conditional report fields based on get_data_child_folders
                    if get_data_child_folders:
                        filtered_item.update({
                            "report_search_args": item.get("report_search_args"),
                            "subject_age": item.get("subject_age"),
                            "subject_mother_name": item.get("subject_mother_name"),
                            "subject_sex": item.get("subject_sex"),
                            "subject_person_count": item.get("subject_person_count"),
                            "subject_company_count": item.get("subject_company_count")
                        })
                    logger.info("[list_user_items_unified] Created report item: %s", filtered_item.get("user_reports_id"))
                else:
                    # Fallback to all fields if item_type is unknown
                    filtered_item = item
                    logger.warning("[list_user_items_unified] Unknown item_type: %s for item_id: %s", item_type, item_id)
                
                items.append(filtered_item)
            
            logger.info("[list_user_items_unified] Retrieved %s items (total: %s)", len(items), total_count)
            
            # Get HMAC data for child folder counting if needed
            user_reports_ids = None
            folder_ids_to_add = None
            if hmac_filter:
                logger.info("[list_user_items_unified] Getting HMAC data for child folder processing")
                #look for folders more deep and reports inside these folders that are not in the saved_reports
                folder_data = None
                folder_path = None
                if folder_id:
                    folder_data = await folder_service.get_folder_by_id(folder_id=folder_id)
                    logger.info(f"[get_saved_reports_handler] folder_data: {folder_data}")
                    folder_path = folder_data.folder_path
                
                logger.info(f"[get_saved_reports_handler] folder_data: {folder_data}")
                user_reports_ids = await self.list_user_reports_deeper(hmac_filter=hmac_filter, hmac_column=hmac_column)
                logger.info(f"[get_saved_reports_handler] user_reports_ids: {user_reports_ids}")
                folder_ids_to_add = await self.list_folders_deeper(hmac_filter=hmac_filter)
                logger.info(f"[get_saved_reports_handler] folder_ids_to_add: {folder_ids_to_add}")

                # Use helper to get report_folder_map and folder_path_map
                report_folder_map, folder_path_map = await self.get_report_and_folder_paths(user_reports_ids, folder_ids_to_add, folder_id_base= folder_id)
                logger.info(f"[get_saved_reports_handler] folder_path_map: {folder_path_map}")

                # Get the set of next folders to add
                set_of_folders_to_add = self.get_next_folders_to_add(folder_path, folder_path_map)
                logger.info(f"[get_saved_reports_handler] set_of_folders_to_add (before filter): {set_of_folders_to_add}")

                # Remove folders already in saved_reports
                set_of_folders_to_add = self.filter_existing_folders(set_of_folders_to_add, folders_in_saved_reports)
                logger.info(f"[get_saved_reports_handler] set_of_folders_to_add (after filter): {set_of_folders_to_add}")

                # Insert folders into saved_reports in order
                await self.insert_folders_by_modified_at(set_of_folders_to_add, items, folder_service, folders_in_saved_reports, limit*page)



            # Process child folders if requested
            if get_data_child_folders and items:
                logger.info("[list_user_items_unified] Processing child folders for %s items", len(items))               

                
                # Process each folder item to add child data concurrently
                folder_items = [item for item in items if item.get("item_type") == "folder"]
                logger.info("[list_user_items_unified] Found %s folder items to process: %s", 
                          len(folder_items), [item.get("folder_id") for item in folder_items])
                
                if folder_items:
                    logger.info("[list_user_items_unified] Processing %s folders concurrently", len(folder_items))
                    
                    # Create async tasks for all folder processing
                    async def process_folder_item(item):
                        folder_id = item.get("folder_id")
                        logger.info("[list_user_items_unified] Fetching child data for folder %s", folder_id)
                        
                        # Create a deep copy of the item to avoid modifying the original
                        item_copy = copy.deepcopy(item)
                        
                        # Create separate async functions for each task with their own sessions
                        async def get_child_data():
                            async with async_session() as data_session:
                                data_service = UserReportsService(
                                    db=data_session,
                                    user_id=self.user_id,
                                    organization_id=self.organization_id
                                )
                                return await data_service.list_user_items_unified(
                                    limit=limit,
                                    page=page,
                                    order=order,
                                    column_order=column_order,
                                    hmac_filter=hmac_filter,
                                    hmac_column=hmac_column,
                                    folder_id=folder_id,
                                    get_data_child_folders=False  # Don't recurse deeper to avoid infinite loops
                                )
                        
                        async def get_count():
                            async with async_session() as count_session:
                                count_service = UserReportsService(
                                    db=count_session,
                                    user_id=self.user_id,
                                    organization_id=self.organization_id
                                )
                                return await count_service.number_of_items_subfolder(
                                    folder_id=folder_id,
                                    user_reports_id_hmac=user_reports_ids,
                                    folder_reports_id_hmac=folder_ids_to_add,
                                    number_default_of_mini_items=11
                                )
                        
                        # Execute both tasks concurrently with separate sessions
                        child_data, number_of_items = await asyncio.gather(
                            get_child_data(), get_count()
                        )
                        
                        number_default_of_mini_items = 11
                        item_copy["data"] = child_data[:number_default_of_mini_items]
                        item_copy["number_of_items"] = number_of_items
                        
                        return item_copy
                    
                    # Process all folders concurrently
                    processed_folders = await asyncio.gather(
                        *[process_folder_item(item) for item in folder_items]
                    )
                    
                    logger.info("[list_user_items_unified] Processed %s folders with child data", len(processed_folders))
                    for processed_item in processed_folders:
                        logger.info("[list_user_items_unified] Processed folder %s has %s child items", 
                                  processed_item.get("folder_id"), len(processed_item.get("data", [])))
                    
                    # Create a mapping of processed items by folder_id for efficient lookup
                    processed_items_map = {item.get("folder_id"): item for item in processed_folders}
                    logger.info("[list_user_items_unified] Created processed_items_map with keys: %s", list(processed_items_map.keys()))
                    
                    # Update the original items list with processed folder data
                    update_count = 0
                    for i, original_item in enumerate(items):
                        if original_item.get("item_type") == "folder":
                            folder_id = original_item.get("folder_id")
                            if folder_id in processed_items_map:
                                items[i] = processed_items_map[folder_id]
                                update_count += 1
                                logger.info("[list_user_items_unified] Updated folder item %s with child data", folder_id)
                    
                    logger.info("[list_user_items_unified] Updated %s folder items with child data", update_count)
                    
                    logger.info("[list_user_items_unified] Completed concurrent child folder processing")
                
                logger.info("[list_user_items_unified] Completed child folder processing")
            
            logger.info("[list_user_items_unified] Final result - Returning %s items", len(items))
            # Log the correct IDs based on item type
            final_ids = []
            for item in items:
                if item.get("item_type") == "report":
                    final_ids.append(f"report:{item.get('user_reports_id')}")
                else:
                    final_ids.append(f"folder:{item.get('folder_id')}")
            logger.info("[list_user_items_unified] Final item_ids: %s", final_ids)
            
            return items
            
        except Exception as e:
            logger.error("[list_user_items_unified] Failed to execute unified query: %s", e)
            raise FailToAccessUsersReportsError()