#!/usr/bin/env python3
"""
Database Setup Script

This script can be run independently or integrated into deployment scripts
to ensure the database is properly initialized before application startup.

Usage:
    python database/setup_db.py [options]

Options:
    --check-only        Only check database status, don't make changes
    --force-migrate     Force migration even if not needed
    --verbose          Enable verbose logging
    --no-interactive   Run without user prompts (for CI/CD)
"""

import sys
import os
import asyncio
import argparse
import logging
from pathlib import Path

# Add the backend directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from database.auto_init import DatabaseInitializer, should_auto_initialize
from database.db import check_db_connection
from core.config import settings

# Configure logging for this script
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

class DatabaseSetupCLI:
    """Command-line interface for database setup"""

    def __init__(self, args):
        self.args = args
        self.verbose = args.verbose
        self.check_only = args.check_only
        self.force_migrate = args.force_migrate
        self.no_interactive = args.no_interactive

        if self.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
            logging.getLogger('database').setLevel(logging.DEBUG)

    async def run(self) -> int:
        """
        Main entry point for database setup.

        Returns:
            0 for success, 1 for failure
        """
        try:
            logger.info("🗄️  Database Setup Script Starting...")
            logger.info(f"Database: {settings.DATABASE_URL.split('@')[1] if '@' in settings.DATABASE_URL else 'Unknown'}")
            logger.info(f"Check Only: {self.check_only}")
            logger.info(f"Force Migrate: {self.force_migrate}")

            # Step 1: Check database connectivity
            logger.info("🔌 Checking database connectivity...")

            if not await check_db_connection():
                logger.error("❌ Database connection failed!")
                logger.error("Please check:")
                logger.error("  - Database server is running")
                logger.error("  - Connection parameters are correct")
                logger.error("  - Network connectivity")
                return 1

            logger.info("✅ Database connectivity confirmed")

            # Step 2: Initialize database setup
            initializer = DatabaseInitializer()

            if self.check_only:
                return await self._check_database_status(initializer)
            else:
                return await self._setup_database(initializer)

        except KeyboardInterrupt:
            logger.info("\n👋 Database setup cancelled by user")
            return 1
        except Exception as e:
            logger.error(f"❌ Unexpected error: {e}")
            if self.verbose:
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
            return 1

    async def _check_database_status(self, initializer: DatabaseInitializer) -> int:
        """Check database status without making changes"""
        logger.info("🔍 Checking database status...")

        try:
            # Analyze current state
            is_first_run, migration_needed = await initializer._analyze_database_state()

            logger.info("📊 Database Status Report:")
            logger.info(f"  First Run: {'Yes' if is_first_run else 'No'}")
            logger.info(f"  Migration Needed: {'Yes' if migration_needed else 'No'}")

            # Count existing tables
            table_count = await initializer._count_application_tables()
            logger.info(f"  Application Tables: {table_count}")

            # Check alembic status
            alembic_available = initializer._is_alembic_available()
            logger.info(f"  Alembic Available: {'Yes' if alembic_available else 'No'}")

            if is_first_run:
                logger.info("🚀 Action Required: First run setup needed")
                logger.info("  Run without --check-only to initialize database")
            elif migration_needed:
                logger.info("🔄 Action Required: Migrations need to be applied")
                logger.info("  Run without --check-only to apply migrations")
            else:
                logger.info("✅ Database is up to date - no action needed")

            return 0

        except Exception as e:
            logger.error(f"❌ Error checking database status: {e}")
            return 1

    async def _setup_database(self, initializer: DatabaseInitializer) -> int:
        """Setup/initialize the database"""
        logger.info("🚀 Setting up database...")

        # Interactive confirmation if not in no-interactive mode
        if not self.no_interactive:
            response = input("\n⚠️  This will modify your database. Continue? (y/N): ")
            if response.lower() not in ('y', 'yes'):
                logger.info("❌ Database setup cancelled by user")
                return 1

        try:
            # Run auto-initialization
            result = await initializer.auto_initialize_database()

            # Process results
            if result['errors']:
                logger.error("❌ Database setup failed!")
                for error in result['errors']:
                    logger.error(f"  Error: {error}")
                return 1

            # Log success details
            logger.info("✅ Database setup completed successfully!")

            if result['is_first_run']:
                logger.info("🚀 First run initialization completed")
                logger.info(f"  Tables Created: {result.get('tables_created', 0)}")

            if result['migration_applied']:
                logger.info("🔄 Migrations applied successfully")

            if result['warnings']:
                logger.warning("⚠️  Warnings encountered:")
                for warning in result['warnings']:
                    logger.warning(f"  {warning}")

            # Final validation
            table_count = await initializer._count_application_tables()
            logger.info(f"📊 Final Status: {table_count} application tables in reports schema")

            return 0

        except Exception as e:
            logger.error(f"❌ Database setup failed: {e}")
            return 1

def create_deployment_health_check() -> str:
    """
    Create a health check script for deployment pipelines.

    Returns:
        Path to the created health check script
    """
    health_check_content = '''#!/bin/bash
# Database Health Check for Deployment
# This script checks if the database is ready for the application to start

set -euo pipefail

echo "🏥 Database Health Check Starting..."

# Check if Python and dependencies are available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 not found"
    exit 1
fi

# Run the database status check
cd "$(dirname "$0")/.."
python3 database/setup_db.py --check-only --no-interactive

if [ $? -eq 0 ]; then
    echo "✅ Database health check passed"
    exit 0
else
    echo "❌ Database health check failed"
    exit 1
fi
'''

    script_path = Path(__file__).parent / "health_check.sh"
    with open(script_path, 'w') as f:
        f.write(health_check_content)

    # Make it executable
    script_path.chmod(0o755)
    return str(script_path)

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="Database Setup and Management Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python database/setup_db.py                    # Setup database automatically
  python database/setup_db.py --check-only      # Check database status only
  python database/setup_db.py --force-migrate   # Force migration application
  python database/setup_db.py --verbose         # Enable verbose logging
  python database/setup_db.py --no-interactive  # Run without prompts (CI/CD)

Environment Variables:
  AUTO_INIT_DB=true/false   # Enable/disable auto-initialization
  DATABASE_URL             # Database connection string
  DB_HOST, DB_NAME, etc.   # Database connection parameters
        """
    )

    parser.add_argument(
        '--check-only',
        action='store_true',
        help='Only check database status, do not make changes'
    )

    parser.add_argument(
        '--force-migrate',
        action='store_true',
        help='Force migration application even if not needed'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    parser.add_argument(
        '--no-interactive',
        action='store_true',
        help='Run without user prompts (for CI/CD pipelines)'
    )

    parser.add_argument(
        '--create-health-check',
        action='store_true',
        help='Create deployment health check script'
    )

    args = parser.parse_args()

    # Special case: create health check script
    if args.create_health_check:
        script_path = create_deployment_health_check()
        print(f"✅ Health check script created: {script_path}")
        return 0

    # Run database setup
    cli = DatabaseSetupCLI(args)
    exit_code = asyncio.run(cli.run())
    sys.exit(exit_code)

if __name__ == "__main__":
    main()