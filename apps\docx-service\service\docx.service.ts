import { Cover } from "../components/docx/Cover";
import { LOGO_COMPLETA_HEADER } from "../config/assets";
import { REPORT_CONSTANTS } from "../config/constants";
import { IGenerateReportsPDFRequestInput, ReportType } from "../global";
import { getImageAsDataUrlAsync } from "../helpers";
import * as logger from "../utils/logger";
import {
  AlignmentType,
  Document,
  Footer,
  Header,
  ImageRun,
  Packer,
  PageNumber,
  Paragraph,
  TextRun,
} from "docx";
import { useDocxStrategyMap } from "../components/docx/strategies/docxStrategyFactory";

const createDocxService = () => {
  const generateDocx = async (
    params: IGenerateReportsPDFRequestInput
  ): Promise<Uint8Array<ArrayBufferLike>> => {
    if (
      !params.sections ||
      !Array.isArray(params.sections) ||
      params.sections.length === 0
    ) {
      throw new Error("Invalid sections provided in the request.");
    }
    if (!params.metadata) {
      throw new Error("Metadata is required in the request.");
    }

    const {
      sections,
      metadata,
      should_print_snap_logo,
      organization_logo,
    } = params;

    const organizationLogoBase64 = organization_logo
      ? await getImageAsDataUrlAsync(organization_logo)
      : undefined;

    const title =
      (metadata[REPORT_CONSTANTS.new_report.report_name] as string) ||
      "Relatório";

    const reportType = (metadata[REPORT_CONSTANTS.new_report.report_type] as ReportType) || '';
    const docxRenderers = useDocxStrategyMap(reportType);

    const printableSections = sections
        .filter(section => !section.subsection && docxRenderers[section.title as keyof typeof docxRenderers]);

    const docxChildren = printableSections.flatMap(section => {
        const renderer = docxRenderers[section.title as keyof typeof docxRenderers];
        if (typeof renderer === 'function') {
            return renderer(section as any).children;
        }
        return [];
    }).flat();

    const content = new Document({
      sections: [
        Cover({
          metadata,
          organization_logo: organizationLogoBase64,
          should_print_snap_logo,
        }),
        {
          headers: {
            default: new Header({
              children: [
                new Paragraph({
                  alignment: AlignmentType.RIGHT,
                  children: [
                    new ImageRun({
                      data: LOGO_COMPLETA_HEADER,
                      transformation: { width: 254, height: 40 },
                      type: "png",
                    }),
                    new TextRun(title),
                  ],
                }),
              ],
            }),
          },
          footers: {
            default: new Footer({
              children: [
                new Paragraph({
                  alignment: AlignmentType.RIGHT,
                  children: [
                    new TextRun({
                      children: [
                        "Página ",
                        PageNumber.CURRENT,
                        " de ",
                        PageNumber.TOTAL_PAGES,
                      ],
                    }),
                  ],
                }),
              ],
            }),
          },
          children: docxChildren,
        },
      ],
    });

    try {
      logger.info("Starting Docx generation process");
      const startTime = Date.now();

      const docxBuffer = await Packer.toBuffer(content);

      const generationTime = Date.now() - startTime;
      logger.info("Docx generation completed", {
        duration: generationTime,
        size: docxBuffer.length,
      });

      return docxBuffer;
    } catch (err) {
      logger.error("Docx generation error", {
        error: err as Error,
      });
      throw new Error((err as Error).message);
    }
  };

  return {
    generateDocx,
  };
};

export const DOCX_SERVICE = createDocxService();
