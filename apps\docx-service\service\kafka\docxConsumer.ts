import { Kafka, EachMessagePayload } from "kafkajs";
import * as logger from "../../utils/logger";
import {
  deleteTempDocxData,
  loadTempDocxData,
  storeGeneratedDocx,
} from "../../config/minio";

import { DOCX_SERVICE } from "../docx.service";

const DOCX_REQUEST_TOPIC = "docx-generation-requests";
const DOCX_REPLY_TOPIC = "docx-generation-results";
const DOCX_LOG_TOPIC = "docx-generation-logs";
const GROUP_ID = "docx-service";

const kafka = new Kafka({
  brokers: [
    `${process.env.KAFKA_CONTAINER_NAME}:${process.env.KAFKA_INTERNAL_PORT}`,
    `${process.env.KAFKA_EXTERNAL_URI}:${process.env.KAFKA_EXTERNAL_PORT}`,
  ],
});
const consumer = kafka.consumer({
  groupId: GROUP_ID,
});
const producer = kafka.producer();
const logProducer = kafka.producer();

async function logEvent(event: any) {
  await logProducer.send({
    topic: DOCX_LOG_TOPIC,
    messages: [{ value: JSON.stringify(event) }],
  });
}

export async function startKafkaConsumer() {
  logger.info("Starting Kafka DOCX Consumer...");
  await consumer.connect();
  await producer.connect();
  await logProducer.connect();
  logger.info("Connected to Kafka successfully");

  await consumer.subscribe({ topic: DOCX_REQUEST_TOPIC });
  logger.info("Subscribed to docx-generation-requests topic");

  logger.info("Kafka DOCX Consumer is ready and waiting for messages...");

  await consumer.run({
    eachMessage: async ({ message }: EachMessagePayload) => {
      const kafkaMessage = JSON.parse(message.value!.toString());
      logger.info("Processing DOCX generation request", {
        requestId: kafkaMessage.requestId,
      });
      logEvent({
        requestId: kafkaMessage.requestId,
        status: "started",
        timestamp: Date.now(),
        details: { dataReference: kafkaMessage.dataReference },
      });

      try {
        // Validate Kafka message structure
        if (!kafkaMessage.requestId) {
          throw new Error("Missing required field: requestId");
        }
        if (!kafkaMessage.dataReference) {
          throw new Error("Missing required field: dataReference");
        }

        // Load actual DOCX data from MinIO
        logger.info("Loading DOCX data from MinIO", {
          requestId: kafkaMessage.requestId,
          dataReference: kafkaMessage.dataReference,
        });

        const data = await loadTempDocxData(kafkaMessage.dataReference);
        if (!data) {
          throw new Error(
            `Failed to load DOCX data from MinIO. Reference: ${kafkaMessage.dataReference}`
          );
        }

        // Delete temp data immediately after successful load
        logger.info("Deleting temp data after successful load", {
          requestId: kafkaMessage.requestId,
          dataReference: kafkaMessage.dataReference,
        });
        await deleteTempDocxData(kafkaMessage.dataReference);

        // Validate loaded data structure
        if (!data.sections || !Array.isArray(data.sections)) {
          throw new Error("Missing required field: sections (must be array)");
        }
        if (!data.metadata) {
          throw new Error("Missing required field: metadata");
        }

        // Extract the same structure as HTTP controller
        const {
          sections,
          metadata,
          profile_image,
          should_print_snap_logo,
          organization_logo,
        } = data;

        logger.info("Generating DOCX file", {
          requestId: kafkaMessage.requestId,
        });
        const docxBuffer = await DOCX_SERVICE.generateDocx({
          sections,
          metadata,
          profile_image,
          should_print_snap_logo,
          organization_logo,
        });

        // Generate filename
        const filename = `${metadata.report_name || "report"}.docx`.replace(
          /[^a-zA-Z0-9.-]/g,
          "_"
        );

        // Store DOCX in MinIO instead of sending through Kafka
        const docxReference = `docx-${kafkaMessage.requestId}`;
        logger.info("Storing DOCX in MinIO", {
          requestId: kafkaMessage.requestId,
          docxReference: docxReference,
          docxSizeMB: (docxBuffer.length / 1024 / 1024).toFixed(2),
          filename: filename,
        });

        await storeGeneratedDocx(
          docxReference,
          Buffer.from(docxBuffer),
          filename
        );

        // Send only reference through Kafka (much smaller message)
        const resultMessage = {
          requestId: kafkaMessage.requestId,
          filename: filename,
          docxReference: docxReference,
          docxSizeBytes: docxBuffer.length,
          status: "success",
          metadata: {
            report_type: kafkaMessage.metadata?.report_type,
            user_reports_id: kafkaMessage.metadata?.user_reports_id,
            user_id: kafkaMessage.metadata?.user_id,
          },
        };

        logger.info("Result message", resultMessage);

        const messageSize = JSON.stringify(resultMessage).length;
        logger.info("Sending DOCX reference to Kafka", {
          requestId: kafkaMessage.requestId,
          messageSize: messageSize,
          docxReference: docxReference,
          filename: filename,
        });
        await producer.send({
          topic: DOCX_REPLY_TOPIC,
          messages: [
            {
              key: kafkaMessage.requestId,
              value: JSON.stringify(resultMessage),
            },
          ],
        });
        logger.info("DOCX reference sent successfully", {
          requestId: kafkaMessage.requestId,
        });

        logger.info("DOCX generation completed successfully", {
          requestId: kafkaMessage.requestId,
        });
        logEvent({
          requestId: kafkaMessage.requestId,
          status: "success",
          timestamp: Date.now(),
          filename: filename,
        });
      } catch (err: any) {
        logger.error("DOCX generation error", {
          requestId: kafkaMessage.requestId,
          error: err.message,
          stack: err.stack,
        });
        logEvent({
          requestId: kafkaMessage.requestId,
          status: "error",
          timestamp: Date.now(),
          error: err.message,
        });

        // Send error response back
        await producer.send({
          topic: DOCX_REPLY_TOPIC,
          messages: [
            {
              key: kafkaMessage.requestId,
              value: JSON.stringify({
                requestId: kafkaMessage.requestId,
                error: err.message,
                status: "error",
              }),
            },
          ],
        });
      }
    },
  });
}

process.on("SIGINT", async () => {
  logger.info("Received SIGINT, shutting down Kafka consumer gracefully...");

  await consumer.disconnect();
  await producer.disconnect();
  await logProducer.disconnect();
  logger.info("Kafka connections closed successfully");
  process.exit(0);
});

process.on("SIGTERM", async () => {
  logger.info("Received SIGTERM, shutting down Kafka consumer gracefully...");

  await consumer.disconnect();
  await producer.disconnect();
  await logProducer.disconnect();
  logger.info("Kafka connections closed successfully");
  process.exit(0);
});
