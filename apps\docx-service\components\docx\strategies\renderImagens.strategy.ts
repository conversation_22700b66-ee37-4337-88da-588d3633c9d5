import { ISectionOptions, Paragraph, TextRun, ImageRun, Table, TableRow, TableCell, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, getSingular, translateSource, getFieldValue, isValidUrl, isBase64Image, formatImageSrc } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintImagensProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      detalhes?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
    }>;
  };
}

export const renderImagens = ({ section }: RenderPrintImagensProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  const allImages = section.data?.flatMap(entry =>
    entry.detalhes?.filter((detalhe: ValueWithSource) => !detalhe.is_deleted) || []
  ) || [];

  if (!allImages.length) {
    return { children };
  }

  const rows: TableRow[] = [];
  let cells: TableCell[] = [];

  allImages.forEach((detalhe, index) => {
    const imageUrl = String(getFieldValue(Object.values(detalhe.value)[0]) || "");
    if (!imageUrl) return;

    const cellParagraphs = [
        new Paragraph({children: [new TextRun({text: `${translatePropToLabel(getSingular(detalhe.label) || 'IMAGEM').toUpperCase()} ${index + 1}`, bold: true})]})];

    if (isValidUrl(imageUrl) || isBase64Image(imageUrl)) {
        // This is a simplification. `ImageRun` needs a buffer or base64 string.
        // Assuming formatImageSrc returns something usable.
        // cellParagraphs.push(new Paragraph({children: [new ImageRun({data: formatImageSrc(imageUrl), transformation: {width: 150, height: 150}})]}));
    }
    cellParagraphs.push(new Paragraph(imageUrl));

    cells.push(new TableCell({children: cellParagraphs}));
    if(cells.length === 2) {
        rows.push(new TableRow({children: cells}));
        cells = [];
    }
  });

  if (cells.length > 0) {
    rows.push(new TableRow({ children: [...cells, new TableCell({ children: [new Paragraph("")] })] }));
  }

  if(rows.length > 0) {
      children.push(new Table({rows, width: {size: 100, type: WidthType.PERCENTAGE}, columnWidths: [4500, 4500]}));
  }

  return { children };
};
