import { ReportStatusProps } from '~/types/global';
import { BaseModel, BaseItemData, ItemType, DisplayableItem } from './base.model';
import { formatIsoDate } from '~/helpers';
import { REPORT_CONSTANTS } from '~/helpers/constants';

export interface FolderData {
  folder_id: string;
  folder_name: string;
  parent_folder_id: string | null;
  folder_path: string;
  depth_level: number;
  created_at: string;
  modified_at: string;
  number_of_items: number;
  data?: (FolderData | ReportReference)[];
}

export interface ReportReference {
  subject_name: string;
  report_name: string;
  user_reports_id: string;
  report_status: ReportStatusProps | string;
}

export interface NormalizedFolder extends BaseItemData {
  parentId: string | null;
  path: string;
  depthLevel: number;
  numberOfItems: number;
  children: (NormalizedFolder | NormalizedReportReference)[];
  type: ItemType.FOLDER;
}

export interface NormalizedReportReference extends BaseItemData {
  subjectName: string;
  type: ItemType.REPORT;
}

export class FolderModel implements BaseModel<NormalizedFolder>, DisplayableItem {
  readonly type = ItemType.FOLDER;
  
  constructor(public readonly raw: FolderData) {}

  get id(): string {
    return this.raw.folder_id;
  }

  get name(): string {
    return this.raw.folder_name;
  }

  get parentId(): string | null {
    return this.raw.parent_folder_id;
  }

  get path(): string {
    return this.raw.folder_path;
  }

  get depthLevel(): number {
    return this.raw.depth_level;
  }

  get createdAt(): string {
    const val = this.raw.created_at;
    return typeof val === "string" ? formatIsoDate(val) : formatIsoDate(new Date().toISOString());
  }

  get modifiedAt(): string {
    const val = this.raw.modified_at;
    return typeof val === "string" ? formatIsoDate(val) : formatIsoDate(new Date().toISOString());
  }

  get children(): (FolderData | ReportReference)[] {
    return this.raw.data || [];
  }

  get isRootFolder(): boolean {
    return this.parentId === null;
  }

  get childFolders(): FolderData[] {
    return this.children.filter(item => !this.isReportReference(item)) as FolderData[];
  }

  get childReports(): ReportReference[] {
    return this.children.filter(item => this.isReportReference(item)) as ReportReference[];
  }

  private isReportReference(item: any): item is ReportReference {
    return REPORT_CONSTANTS.new_report.report_name in item;
  }

  get numberOfItems(): number {
    return this.raw.number_of_items;
  }

  private normalizeChildren(): (NormalizedFolder | NormalizedReportReference)[] {
    if (!this.children.length) return [];

    return this.children.map(item => {
      if (this.isReportReference(item)) {
        return {
          id: item.user_reports_id,
          name: item.report_name,
          subjectName: item.subject_name,
          createdAt: "",
          modifiedAt: "",
          type: ItemType.REPORT
        };
      } else {
        const folderModel = new FolderModel(item);
        return folderModel.toJSON();
      }
    });
  }

  toJSON(): NormalizedFolder {
    return {
      id: this.id,
      name: this.name,
      parentId: this.parentId,
      path: this.path,
      depthLevel: this.depthLevel,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
      numberOfItems: this.numberOfItems,
      children: this.normalizeChildren(),
      type: ItemType.FOLDER
    };
  }
}