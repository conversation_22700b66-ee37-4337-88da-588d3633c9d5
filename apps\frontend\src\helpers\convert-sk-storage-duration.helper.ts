export const convertDurationToDate = (duration: string): Date => {
  const now = new Date();
  switch (duration) {
    case "1m":
      return new Date(now.getTime() + 1 * 60 * 1000);
    case "15m":
      return new Date(now.getTime() + 15 * 60 * 1000);
    case "30m":
      return new Date(now.getTime() + 30 * 60 * 1000);
    case "1h":
      return new Date(now.getTime() + 60 * 60 * 1000);
    case "session":
      return new Date(now.getTime() + 8 * 60 * 60 * 1000); // TODO - Por enquanto estou colocando 8 horas, alinhar com a nova lógica de sessão frontend
    default:
      return new Date(now.getTime() + 500);
  }
};