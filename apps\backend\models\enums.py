from enum import Enum

class invite_status(str, Enum):
    enviado = "enviado"
    negado = "negado"
    aceito = "aceito"
    cancelado = "cancelado"
    desvinculado = "desvinculado"

class user_status(str, Enum):
    ativo = "ativo"
    inativo = "inativo"

class organization_status(str, Enum):
    ativo = "ativo"
    inativo = "inativo"

class invite_type(str, Enum):
    investigador = "investigador"
    administrador = "administrador"
