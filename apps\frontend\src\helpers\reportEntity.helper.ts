import { REPORT_CONSTANTS } from "./constants";

/**
 * Determines if a report subject is a company or person based on report type and subject attributes
 * 
 * @param reportType - The type of report (cpf, cnpj, telefone, email)
 * @param subjectAge - The subject's age/date value
 * @param subjectSex - The subject's sex (optional)
 * @param subjectMotherName - The subject's mother name (optional)
 * @returns true if the subject is a company, false if it's a person
 */
export const isReportSubjectCompany = (
  reportType: string,
  subjectAge?: string | number | null,
  subjectSex?: string | null,
  subjectMotherName?: string | null
): boolean => {
  if (reportType === REPORT_CONSTANTS.types.cpf) {
    return false;
  }

  if (reportType === REPORT_CONSTANTS.types.cnpj) {
    return true;
  }

  if (reportType === REPORT_CONSTANTS.types.telefone || reportType === REPORT_CONSTANTS.types.email) {
    const hasSex = subjectSex && subjectSex.trim();
    const hasMotherName = subjectMotherName && subjectMotherName.trim();

    if (hasSex || hasMotherName) {
      return false;
    } else if (subjectAge != null && subjectAge !== "") {
      return true;
    }
  }

  return false;
};
