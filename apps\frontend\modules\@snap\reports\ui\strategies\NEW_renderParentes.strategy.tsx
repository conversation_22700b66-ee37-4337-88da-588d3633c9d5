import React, { useMemo, useCallback } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { Parente, TestFunctions, ArrayKey, UpdaterFunction, TestFunction, SectionTestFunction, CalculateFunction, ParentesSection } from "../../model/Parentes";
import { ValueWithSource } from "../../model/ValueWithSource";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { formatFieldValue, parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { ItemSeparator } from "../components/ItemSeparator";
import { useNestedRender } from "./NestedRenderContext";

export function useRenderParentes(
  sectionTitle: string
): ArrayRenderStrategy<Parente> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const includeKey = useCallback((isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted, [isTrash]);

  const testFunctions = useMemo((): TestFunctions => ({
    parentesco: (e: Parente) => e.parentesco?.is_deleted === true,
    detalhes: (e: Parente) => e.detalhes
      ? Object.values(e.detalhes).every((v: ValueWithSource<string>) => v.is_deleted === true)
      : false,
    pessoa: (e: Parente) => e.pessoa
      ? e.pessoa.every((p: ValueWithSource) =>
        (Object.values(p.value || {}) as ValueWithSource[]).every((v: ValueWithSource) => v.is_deleted === true))
      : true,
    telefones: (e: Parente) => e.telefones
      ? e.telefones.every((t: ValueWithSource) =>
        (Object.values(t.value || {}) as ValueWithSource[]).every((v: ValueWithSource) => v.is_deleted === true))
      : true,
    enderecos: (e: Parente) => e.enderecos
      ? e.enderecos.every((end: ValueWithSource) =>
        (Object.values(end.value || {}) as ValueWithSource[]).every((v: ValueWithSource) => v.is_deleted === true))
      : true,
  }), []);

  const testEntryDeleted: TestFunction = useCallback((entry: Parente): boolean => {
    return Object.values(testFunctions).every(testFn => testFn(entry));
  }, [testFunctions]);

  const testSectionDeleted: SectionTestFunction = useCallback((section: ParentesSection): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted),
    [testEntryDeleted]);

  const calculateDataCount: CalculateFunction = useCallback((section: ParentesSection): number => {
    if (!Array.isArray(section.data)) return 0;
    return section.data.reduce((count: number, entry: Parente) => {
      return testEntryDeleted(entry) ? count : count + 1;
    }, 0);
  }, [testEntryDeleted]);

  const applyCascadingDeletion: (entry: Parente, targetDeletedState: boolean) => void = useCallback((entry: Parente, targetDeletedState: boolean) => {
    if (entry.detalhes) {
      Object.values(entry.detalhes).forEach((detalhe: ValueWithSource<string>) => {
        if (detalhe) detalhe.is_deleted = targetDeletedState;
      });
    }

    (['pessoa', 'telefones', 'enderecos'] as const).forEach(key => {
      const field = entry[key];
      if (Array.isArray(field)) {
        field.forEach((item: ValueWithSource) => {
          item.is_deleted = targetDeletedState;
          if (item.value) {
            (Object.values(item.value) as ValueWithSource[]).forEach((campo: ValueWithSource) => {
              if (campo) campo.is_deleted = targetDeletedState;
            });
          }
        });
      }
    });
  }, []);

  const shouldShowEntry: TestFunction = useCallback((entry: Parente): boolean => {
    const hasDeletedContent = (field: ValueWithSource<string> | ValueWithSource[] | Record<string, ValueWithSource<string>> | undefined, isArray = false): boolean => {
      if (!field) return false;
      if (isArray && Array.isArray(field)) {
        return field.some((item: ValueWithSource) =>
          item.is_deleted === true || (Object.values(item.value || {}) as ValueWithSource[]).some((v: ValueWithSource) => v.is_deleted === true)
        );
      }
      if (!isArray && typeof field === 'object') {
        return 'is_deleted' in field ? field.is_deleted === true :
          (Object.values(field) as ValueWithSource<string>[]).some((v: ValueWithSource<string>) => v.is_deleted === true);
      }
      return false;
    };

    const hasNonDeletedContent = (field: ValueWithSource<string> | ValueWithSource[] | Record<string, ValueWithSource<string>> | undefined, isArray = false): boolean => {
      if (!field) return false;
      if (isArray && Array.isArray(field)) {
        return field.some((item: ValueWithSource) =>
          item.is_deleted !== true || (Object.values(item.value || {}) as ValueWithSource[]).some((v: ValueWithSource) => v.is_deleted !== true)
        );
      }
      if (!isArray && typeof field === 'object') {
        return 'is_deleted' in field ? field.is_deleted !== true :
          (Object.values(field) as ValueWithSource<string>[]).some((v: ValueWithSource<string>) => v.is_deleted !== true);
      }
      return false;
    };

    const checkFunction = isTrash ? hasDeletedContent : hasNonDeletedContent;

    return checkFunction(entry.parentesco) ||
      checkFunction(entry.detalhes) ||
      checkFunction(entry.pessoa, true) ||
      checkFunction(entry.telefones, true) ||
      checkFunction(entry.enderecos, true);
  }, [isTrash]);

  const shouldIncludeNestedBlock = useCallback((item: ValueWithSource) => {
    const vals = Object.values(item.value || {}) as ValueWithSource[];
    return isTrash
      ? vals.some((v: ValueWithSource) => v.is_deleted === true)
      : vals.some((v: ValueWithSource) => v.is_deleted === false);
  }, [isTrash]);

  const shouldIncludeList = useCallback((arrayItems: ValueWithSource[]) => {
    if (!Array.isArray(arrayItems)) return false;
    return arrayItems.some((item: ValueWithSource) => shouldIncludeNestedBlock(item));
  }, [shouldIncludeNestedBlock]);

  const updateEntries = useCallback((
    updater: UpdaterFunction,
    testDeleted: TestFunction = testEntryDeleted,
    testSectionDel: SectionTestFunction = testSectionDeleted,
    calcDataCount: CalculateFunction = calculateDataCount
  ) => {
    actions.updateSectionEntries?.(
      sectionTitle,
      updater,
      testDeleted as (entry: Record<string, any>) => boolean,
      testSectionDel as (section: Record<string, any>) => boolean,
      calcDataCount as (section: Record<string, any>) => number
    );
  }, [actions, sectionTitle, testEntryDeleted, testSectionDeleted, calculateDataCount]);

  const createNestedToggleHandlers = () => {
    const toggleValue = (target: ValueWithSource, fieldKey?: string) => {
      if (fieldKey && target?.value?.[fieldKey]) {
        target.value[fieldKey].is_deleted = !target.value[fieldKey].is_deleted;
        target.is_deleted = (Object.values(target.value) as ValueWithSource[]).every((campo: ValueWithSource) => campo.is_deleted === true);
      } else if (target?.value) {
        const targetDeletedState = isTrash ? false : true;
        target.is_deleted = targetDeletedState;
        (Object.values(target.value) as ValueWithSource[]).forEach((campo: ValueWithSource) => {
          if (campo) campo.is_deleted = targetDeletedState;
        });
      }
    };

    return {
      field: (entryIdx: number, arrayKey: ArrayKey, blockIdx: number, fieldKey: string) => {
        updateEntries((e: Parente, i?: number) => {
          if (i === entryIdx && e[arrayKey]?.[blockIdx]) {
            toggleValue(e[arrayKey][blockIdx], fieldKey);
          }
        });
      },
      block: (entryIdx: number, arrayKey: ArrayKey, blockIdx: number) => {
        updateEntries((e: Parente, i?: number) => {
          if (i === entryIdx && e[arrayKey]?.[blockIdx]) {
            toggleValue(e[arrayKey][blockIdx]);
          }
        });
      },
      list: (entryIdx: number, arrayKey: ArrayKey) => {
        updateEntries((e: Parente, i?: number) => {
          if (i === entryIdx && Array.isArray(e[arrayKey])) {
            const targetDeletedState = isTrash ? false : true;
            e[arrayKey].forEach((item: ValueWithSource) => {
              item.is_deleted = targetDeletedState;
              if (item.value) {
                (Object.values(item.value) as ValueWithSource[]).forEach((campo: ValueWithSource) => {
                  if (campo) campo.is_deleted = targetDeletedState;
                });
              }
            });
          }
        });
      }
    };
  };

  const nestedToggleHandlers = useMemo(() => createNestedToggleHandlers(), [updateEntries, isTrash]);

  const calculateNestedDataCount = useCallback((nestedSection: ParentesSection): number => {
    if (!Array.isArray(nestedSection.data)) return 0;
    return nestedSection.data.reduce((count: number, entry: Parente) => {
      const isEntryDeleted: boolean = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  }, [testEntryDeleted]);

  const ParentescoBlock = React.memo(({
    entry,
    index,
    sectionTitle
  }: {
    entry: Parente;
    index: number;
    sectionTitle: string;
  }) => {
    const nested = useNestedRender();

    if (!entry?.parentesco || !includeKey(entry.parentesco.is_deleted || false)) return null;

    const handleToggleParentesco = useCallback(() => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: ParentesSection) => {
          const targetEntry = nestedSection.data?.[index];
          if (targetEntry?.parentesco) {
            targetEntry.parentesco.is_deleted = !targetEntry.parentesco.is_deleted;
            applyCascadingDeletion(targetEntry, targetEntry.parentesco.is_deleted);
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: Parente, i?: number) => {
        if (i === index && e.parentesco) {
          e.parentesco.is_deleted = !e.parentesco.is_deleted;
          applyCascadingDeletion(e, e.parentesco.is_deleted);
        }
      });
    }, [nested, index, updateEntries, applyCascadingDeletion]);

    return (
      <CustomGridContainer cols={1} key={`parentesco-${index}`}>
        <CustomGridItem
          fullWidth
          className="mb-6"
          onToggleField={handleToggleParentesco}
        >
          <CustomReadOnlyInputField
            label={entry.parentesco.label.toUpperCase()}
            colorClass="bg-primary"
            labelTextClass="text-accent"
            value={parseValue(formatFieldValue(entry.parentesco.value))}
            tooltip={renderSourceTooltip(entry.parentesco.source)}
          />
        </CustomGridItem>
      </CustomGridContainer>
    );
  });

  const DetalhesBlock = React.memo(({
    entry,
    index,
    sectionTitle
  }: {
    entry: Parente;
    index: number;
    sectionTitle: string;
  }) => {
    const nested = useNestedRender();

    if (!entry?.detalhes) return null;
    const subs = Object.entries(entry.detalhes).filter(([, v]) =>
      includeKey((v as ValueWithSource<string>).is_deleted || false)
    );
    if (!subs.length) return null;

    const handleToggleDetalhe = useCallback((fieldKey: string) => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: ParentesSection) => {
          const targetEntry = nestedSection.data?.[index];
          const d = targetEntry?.detalhes?.[fieldKey];
          if (d) d.is_deleted = !d.is_deleted;
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: Parente, i?: number) => {
        if (i === index) {
          const d = e.detalhes?.[fieldKey];
          if (d) d.is_deleted = !d.is_deleted;
        }
      });
    }, [nested, index, updateEntries]);

    return (
      <CustomGridContainer
        cols={2}
        columnFirst
        key={`detalhes-${index}`}
        className="mb-6"
      >
        {subs.map(([fieldKey, val]) => (
          <CustomGridItem
            key={fieldKey}
            cols={1}
            onToggleField={() => handleToggleDetalhe(fieldKey)}
          >
            <CustomReadOnlyInputField
              label={translatePropToLabel((val as ValueWithSource<string>).label || fieldKey).toUpperCase()}
              value={parseValue(formatFieldValue((val as ValueWithSource<string>).value))}
              tooltip={renderSourceTooltip((val as ValueWithSource<string>).source)}
            />
          </CustomGridItem>
        ))}
      </CustomGridContainer>
    );
  });

  const createToggleHandler = useCallback((
    updateFn: (targetEntry: ParentesSection) => void,
    standaloneFn: () => void
  ) => {
    return () => {
      const nested = useNestedRender();
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: ParentesSection) => {
          updateFn(nestedSection);
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }
      standaloneFn();
    };
  }, [sectionTitle, calculateNestedDataCount]);

  const ArrayBlock = React.memo(({
    entry,
    index,
    sectionTitle,
    arrayKey,
    blockType,
  }: {
    entry: Parente;
    index: number;
    sectionTitle: string;
    arrayKey: 'pessoa' | 'telefones' | 'enderecos';
    blockType: string;
  }) => {
    const mode = useReportMode();
    const isTrash = mode === "trash";
    const nested = useNestedRender();

    const arrayData = entry[arrayKey];
    if (!arrayData?.length || !shouldIncludeList(arrayData)) return null;

    const blocks = arrayData
      .map((item: ValueWithSource, i: number) => ({ bloco: item, idx: i }))
      .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

    if (!blocks.length) return null;

    const handleToggleListTitle = useCallback(() => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: ParentesSection) => {
          const targetEntry = nestedSection.data?.[index];
          const arrayItems = targetEntry?.[arrayKey];
          if (arrayItems && Array.isArray(arrayItems)) {
            const targetDeletedState = isTrash ? false : true;
            arrayItems.forEach((item: ValueWithSource) => {
              item.is_deleted = targetDeletedState;
              if (item.value) {
                (Object.values(item.value) as ValueWithSource[]).forEach((campo: ValueWithSource) => {
                  if (campo) campo.is_deleted = targetDeletedState;
                });
              }
            });
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      nestedToggleHandlers.list(index, arrayKey);
    }, [nested, sectionTitle, index, arrayKey, isTrash]);

    const labelMap: Record<string, string> = {
      pessoa: "PESSOAS",
      telefones: "TELEFONES",
      enderecos: "ENDEREÇOS"
    };

    return (
      <CustomGridContainer cols={1} key={`${arrayKey}-${index}`} className="mb-4">
        <CustomGridItem
          cols={1}
          containerClassName="w-fit pr-12"
          onToggleField={handleToggleListTitle}
        >
          <ReportsCustomLabel
            label={labelMap[arrayKey]}
            colorClass="bg-primary"
          />
        </CustomGridItem>
        {arrayKey === 'telefones' ? (
          <CustomGridContainer cols={2}>
            {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
              <NestedArrayBlock
                key={`${arrayKey}-${index}-${origIdx}`}
                sectionTitle={sectionTitle}
                entryIdx={index}
                arrayKey={arrayKey}
                origIdx={origIdx}
                bloco={bloco}
                blockRenderIdx={blockRenderIdx}
                blockType={blockType}
              />
            ))}
          </CustomGridContainer>
        ) : (
          blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
            <NestedArrayBlock
              key={`${arrayKey}-${index}-${origIdx}`}
              sectionTitle={sectionTitle}
              entryIdx={index}
              arrayKey={arrayKey}
              origIdx={origIdx}
              bloco={bloco}
              blockRenderIdx={blockRenderIdx}
              blockType={blockType}
            />
          ))
        )}
      </CustomGridContainer>
    );
  });

  const NestedArrayBlock = React.memo(({
    sectionTitle,
    entryIdx,
    arrayKey,
    origIdx,
    bloco,
    blockRenderIdx,
    blockType
  }: {
    sectionTitle: string;
    entryIdx: number;
    arrayKey: string;
    origIdx: number;
    bloco: ValueWithSource;
    blockRenderIdx: number;
    blockType: string;
  }) => {
    const mode = useReportMode();
    const isTrash = mode === "trash";
    const nested = useNestedRender();

    const handleToggleBlock = useCallback(() => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: ParentesSection) => {
          const entry = nestedSection.data?.[entryIdx];
          const item = entry?.[arrayKey as ArrayKey]?.[origIdx];
          if (item?.value) {
            const targetDeletedState = isTrash ? false : true;
            item.is_deleted = targetDeletedState;
            (Object.values(item.value) as ValueWithSource[]).forEach((campo: ValueWithSource) => {
              if (campo) campo.is_deleted = targetDeletedState;
            });
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      nestedToggleHandlers.block(entryIdx, arrayKey as ArrayKey, origIdx);
    }, [nested, sectionTitle, entryIdx, arrayKey, origIdx, isTrash]);

    const handleToggleField = useCallback((fieldKey: string) => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: ParentesSection) => {
          const entry = nestedSection.data?.[entryIdx];
          const item = entry?.[arrayKey as ArrayKey]?.[origIdx];
          if (item?.value?.[fieldKey]) {
            item.value[fieldKey].is_deleted = !item.value[fieldKey].is_deleted;
            item.is_deleted = (Object.values(item.value) as ValueWithSource[]).every((c: ValueWithSource) => c.is_deleted === true);
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      nestedToggleHandlers.field(entryIdx, arrayKey as ArrayKey, origIdx, fieldKey);
    }, [nested, sectionTitle, entryIdx, arrayKey, origIdx]);

    return (
      <CustomGridContainer cols={1} key={`${arrayKey}-${origIdx}`} className="mb-4">
        <CustomGridItem
          cols={1}
          containerClassName="w-fit pr-12"
          onToggleField={handleToggleBlock}
        >
          <ReportsCustomLabel
            label={`${blockType.toUpperCase()} ${!isTrash ? blockRenderIdx + 1 : ""}`}
            colorClass="bg-primary"
          />
        </CustomGridItem>
        <div className="pl-5">
          <CustomGridContainer cols={blockType === "TELEFONE" ? 1 : 2}>
            {(Object.entries(bloco.value) as [string, ValueWithSource][])
              .filter(([_, v]) => (isTrash ? v.is_deleted : !v.is_deleted))
              .map(([fieldKey, fieldValue], index) => (
                <CustomGridItem key={`${arrayKey}-${origIdx}-${fieldKey}`} cols={1} className="py-1" onToggleField={() => handleToggleField(fieldKey)}>
                  <CustomReadOnlyInputField
                    label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                    colorClass="bg-border"
                    isFirstLabelList={index === 0}
                    icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                    value={parseValue(formatFieldValue(fieldValue.value))}
                    tooltip={renderSourceTooltip(fieldValue.source)}
                  />
                </CustomGridItem>
              ))}
          </CustomGridContainer>
        </div>
      </CustomGridContainer>
    );
  });

  const formatByKey: Record<
    string,
    (entry?: Parente, index?: number) => React.ReactElement | null
  > = {
    parentesco: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <ParentescoBlock entry={entry} index={index} sectionTitle={sectionTitle} />;
    },

    detalhes: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <DetalhesBlock entry={entry} index={index} sectionTitle={sectionTitle} />;
    },

    pessoa: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <ArrayBlock entry={entry} index={index} sectionTitle={sectionTitle} arrayKey="pessoa" blockType="PESSOA" />;
    },

    telefones: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <ArrayBlock entry={entry} index={index} sectionTitle={sectionTitle} arrayKey="telefones" blockType="TELEFONE" />;
    },

    enderecos: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <ArrayBlock entry={entry} index={index} sectionTitle={sectionTitle} arrayKey="enderecos" blockType="ENDEREÇO" />;
    },
  };

  const validateKeys = (keys: Array<keyof Parente>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: Parente, index?: number): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof Parente>;

    if (!validateKeys(keys)) {
      console.warn(`[Seção ${sectionTitle}] Chaves inválidas:`, keys);
    }

    const orderedKeys: Array<keyof Parente> = [
      'parentesco',
      'detalhes',
      'pessoa',
      'telefones',
      'enderecos'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    const elements: React.ReactElement[] = [];

    filteredKeys.forEach((chave) => {
      const element = formatByKey[chave]?.(entry, index);
      if (element) elements.push(element);
    });

    return elements;
  };

  const render = (dataArray: Parente[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn(`[Seção ${sectionTitle}] esperava array, mas recebeu:`, typeof dataArray);
      return [];
    }

    const filteredData = dataArray
      .map((entry, originalIndex) => ({ entry, originalIndex }))
      .filter(({ entry }) => shouldShowEntry(entry));

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach(({ entry, originalIndex }, filterIndex) => {
      const elements = renderSingleItem(entry, originalIndex);

      if (filteredData.length > 1) {
        const isLastItem = filterIndex === filteredData.length - 1;
        allElements.push(
          <div
            key={`pessoa-relacionada-${originalIndex}`}
            className={`relative mb-8 pb-1 ${isLastItem ? '!mb-0 !pb-0' : ''}`}
          >
            {elements}
            {!isLastItem && <ItemSeparator />}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      ((entry: Parente) => {
        if (entry.parentesco) {
          entry.parentesco.is_deleted = targetDeletedState;
        }
        applyCascadingDeletion(entry, targetDeletedState);
      }) as UpdaterFunction,
      testEntryDeleted as (entry: Record<string, any>) => boolean,
      testSectionDeleted as (section: Record<string, any>) => boolean,
      calculateDataCount as (section: Record<string, any>) => number
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted: testEntryDeleted as TestFunction,
    testSectionDeleted: testSectionDeleted as SectionTestFunction,
    deleteSectionEntries,
    calculateDataCount,
  } as ArrayRenderStrategy<Parente> & { calculateDataCount: typeof calculateDataCount };
}