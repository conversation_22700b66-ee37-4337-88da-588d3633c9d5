import { Navigate, Outlet, useLocation } from "react-router";
import { useEffect, useState } from "react";
import { useUserCRUD } from "~/hooks/useUserCRUD";
import { useUserActions, useUserHasAcceptedTerms } from "~/store/userStore";
import { DialogContainer } from "../DialogContainer";
import { PendingReportSockets } from "../PendingReportSockets";
import { Loading, ModalInstance } from "@snap/design-system";
import { useDialogActions } from "~/store/dialogStore";
import { EulaDialog } from "~/containers/report/EulaDialog";
import { Check } from "lucide-react";
import { AUTH_STORE_INSTANCE } from "~/store/auth";
import { useCredentialStoreEvents } from "~/store/credentials";
import { useCreateFolderActions } from "~/store/createFolderStore";

// Responsável por proteger rotas que requerem autenticação
// Redireciona para a página de login se o usuário não estiver autenticado
// Também verifica se o usuário aceitou os termos de uso e exibe o diálogo se necessário
export default function ProtectedRoute() {
  const location = useLocation();
  const { useQueryUser } = useUserCRUD();
  const { data: userData, isLoading, isError } = useQueryUser;
  const { setUser, setUserSalt, setHasAcceptedTerms } = useUserActions();
  const { openDialog } = useDialogActions();
  const hasAcceptedTerms = useUserHasAcceptedTerms();
  const [hasShownDialog, setHasShownDialog] = useState(false);
  const {
    events: { startNewAuthenticatedSession },
  } = AUTH_STORE_INSTANCE;
  const { setVerifiers } = useCredentialStoreEvents();

  useEffect(() => {
    if (userData) {
      startNewAuthenticatedSession({
        user: {
          id: userData.user_id,
          email: userData.email,
          name: userData.name,
        },
      });
      setVerifiers({
        iv: userData.verifier?.iv || null,
        salt: userData.salt || null,
        encrypted: userData.verifier?.encrypted || null,
      });
      setUser({ ...userData });
      setUserSalt(userData?.salt || null);
      setHasAcceptedTerms(!!userData?.accept_terms);
    }
  }, [userData, setUser, setUserSalt, setHasAcceptedTerms]);

  useEffect(() => {
    if (userData && !userData.accept_terms && !hasShownDialog) {
      console.log(userData);
      setHasShownDialog(true);
      openDialog({
        title: "Termos de Uso",
        icon: <Check />,
        content: <EulaDialog.Content />,
        footer: <EulaDialog.Footer />,
        className: "max-w-4xl",
        preventOutsideClose: true,
      });
    }
  }, [userData, hasShownDialog, openDialog]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen w-full">
        <Loading size="lg" />
      </div>
    );
  }

  if (isError || !userData) {
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  if (!userData.accept_terms) {
    return (
      <div className="flex items-center justify-center h-screen w-full">
        <ModalInstance />
        <DialogContainer />
        <Loading size="lg" />
      </div>
    );
  }

  return (
    <>
      <ModalInstance />
      <PendingReportSockets />
      <DialogContainer />
      <Outlet />
    </>
  );
}
