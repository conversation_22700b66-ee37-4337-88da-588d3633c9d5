import { create } from "zustand";
import { UserLogEntry, UserLogsFilters } from "~/types/global";

interface UserLogsStoreActions {
  setLogsList: (list: UserLogEntry[]) => void;
  appendLogsList: (list: UserLogEntry[]) => void;
  setPage: (page: number) => void;
  incrementPage: () => void;
  setLimit: (limit: number) => void;
  setReportTypeFilter: (reportType: string) => void;
  setCreatedStartAtFilter: (date: string) => void;
  setCreatedEndAtFilter: (date: string) => void;
  setIsUserFilter: (isUser: boolean) => void;
  setOrder: (order: string) => void;
  setColumnOrder: (column: string) => void;
  clearFilters: () => void;
  resetPagination: () => void;
  setTotalItems: (total: number) => void;
}

interface UserLogsStoreState {
  logsList: UserLogEntry[];
  filters: UserLogsFilters;
  totalItems: number;
  actions: UserLogsStoreActions;
}

const defaultFilters: UserLogsFilters = {
  is_user: true,
  column_order: "created_at",
  order: "desc",
  report_type: "",
  limit: 10,
  page: 1,
  created_start_at: "",
  created_end_at: ""
};

const useUserLogsStore = create<UserLogsStoreState>((set) => ({
  logsList: [],
  filters: defaultFilters,
  totalItems: 0,
  actions: {
    setLogsList: (list: UserLogEntry[]) => set({ logsList: list }),
    appendLogsList: (list: UserLogEntry[]) =>
      set(state => ({ logsList: [...state.logsList, ...list] })),
    setPage: (page: number) =>
      set(state => ({ filters: { ...state.filters, page } })),
    incrementPage: () =>
      set(state => ({ filters: { ...state.filters, page: state.filters.page! + 1 } })),
    setLimit: (limit: number) =>
      set(state => ({ filters: { ...state.filters, limit } })),
    setReportTypeFilter: (report_type: string) =>
      set(state => ({ filters: { ...state.filters, report_type, page: 1 } })),
    setCreatedStartAtFilter: (created_start_at: string) =>
      set(state => ({ filters: { ...state.filters, created_start_at, page: 1 } })),
    setCreatedEndAtFilter: (created_end_at: string) =>
      set(state => ({ filters: { ...state.filters, created_end_at, page: 1 } })),
    setIsUserFilter: (is_user: boolean) =>
      set(state => ({ filters: { ...state.filters, is_user, page: 1 } })),
    setOrder: (order: string) =>
      set(state => ({ filters: { ...state.filters, order, page: 1 } })),
    setColumnOrder: (column_order: string) =>
      set(state => ({ filters: { ...state.filters, column_order, page: 1 } })),
    clearFilters: () =>
      set({ filters: defaultFilters }),
    resetPagination: () =>
      set(state => ({ filters: { ...state.filters, page: 1 } })),
    setTotalItems: (totalItems: number) => set({ totalItems }),
  },
}));

export const useUserLogsList = () => useUserLogsStore(state => state.logsList);
export const useUserLogsFilters = () => useUserLogsStore(state => state.filters);
export const useUserLogsTotalItems = () => useUserLogsStore(state => state.totalItems);
export const useUserLogsActions = () => useUserLogsStore(state => state.actions);

export default useUserLogsStore;
