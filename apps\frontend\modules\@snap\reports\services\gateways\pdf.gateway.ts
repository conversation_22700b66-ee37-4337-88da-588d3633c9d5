import { pdfClient } from "../clients/pdf.client";
import type { ReportDocumentProps } from "../../hooks/useReportPDF";

class PDFGateway {
  async generatePDF(props: ReportDocumentProps): Promise<string> {
    try {
      const response = await pdfClient.post('/create/pdf', props, {
        responseType: 'blob'
      });

      const pdfBlob = response.data;
      const url = URL.createObjectURL(pdfBlob);

      console.log('PDF generated successfully by service');
      return url;
    } catch (error) {
      console.error('Error calling PDF service:', error);
      throw error;
    }
  }

  onProgress(enabled: boolean): void {
    // No-op for service-based implementation
    console.log('PDF service progress logging:', enabled);
  }
}

export const pdfGateway = new PDFGateway();
pdfGateway.onProgress(true);
