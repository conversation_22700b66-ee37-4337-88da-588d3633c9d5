import { useCallback } from "react";
import { KeyRound } from "lucide-react";
import { useDialogActions } from "~/store/dialogStore";
import { useCredentialStoreEvents } from "~/store/credentials";
import { SecretKeyDialog } from "~/containers/report/SecretKeyDialog";

export const useSecretKeyDialog = () => {
  const { useHasValidVerifiers } = useCredentialStoreEvents();
  const { openDialog } = useDialogActions();
  const hasValidVerifiers = useHasValidVerifiers();

  const openSecretKeyDialog = useCallback((onSuccess?: () => void) => {
    openDialog({
      title: hasValidVerifiers ? "INSERIR SENHA" : "CRIAR SENHA",
      icon: <KeyRound />,
      content: <SecretKeyDialog.Content />,
      footer: <SecretKeyDialog.Footer onOpen={() => openSecretKeyDialog(onSuccess)} onSuccess={onSuccess} />,
      className: "max-w-xl",
    });
  }, [openDialog, hasValidVerifiers]);

  return {
    openSecretKeyDialog,
  };
};
