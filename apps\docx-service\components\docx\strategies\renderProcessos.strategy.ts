import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, translateSource, getFieldLabel, getFieldValue, getSingular, getPlural } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintProcessosProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      numero?: ValueWithSource;
      detalhes?: Record<string, ValueWithSource>;
      movimentações?: ValueWithSource;
      [key: string]: any;
    }>
  };
}

export const renderProcessos = ({ section }: RenderPrintProcessosProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((processo) => {
    if (processo.numero && !processo.numero.is_deleted) {
      children.push(new Paragraph({
        children: [
          new TextRun({ text: `${(processo.numero.label || "Número do Processo").toUpperCase()}: `, bold: true, color: "FE473C" }),
          new TextRun(String(processo.numero.value)),
          new TextRun({ text: ` | ${processo.numero.source?.map(translateSource).join(', ')}`, size: 16, color: "FE473C" }),
        ],
        spacing: { after: 200 }
      }));
    }

    if (processo.detalhes) {
      const tableRows = Object.entries(processo.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => {
          return new TableRow({
            children: [
              new TableCell({
                children: [new Paragraph({
                  children: [
                    new TextRun({ text: "■ ", color: "CCCCCC", bold: true }),
                    new TextRun({ text: `${translatePropToLabel(field.label || key).toUpperCase()}`, bold: true, color: "889EA3" }),
                    new TextRun({ text: ` | ${field.source?.map(translateSource).join(', ')}`, size: 16, color: "FE473C" }),
                  ]
                })],
                shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
              }),
              new TableCell({
                children: [new Paragraph(String(field.value))],
                shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
              }),
            ],
          });
        });

      if (tableRows.length > 0) {
        children.push(new Table({
          rows: tableRows,
          width: { size: 100, type: WidthType.PERCENTAGE },
          columnWidths: [3500, 5500],
        }));
      }
    }

    if (processo["movimentações"] && !processo["movimentações"].is_deleted) {
        children.push(new Paragraph({ text: "Movimentações", style: "subtitle"}));
        children.push(new Paragraph(String(processo["movimentações"].value)));
    }

    Object.entries(processo)
        .filter(([key, value]) => Array.isArray(value) && key !== 'detalhes')
        .forEach(([key, participants]) => {
            const validParticipants = (participants as any[]).filter(p => !p.is_deleted);
            if (validParticipants.length === 0) return;

            children.push(new Paragraph({ text: getPlural(translatePropToLabel(key)).toUpperCase(), style: "subtitle"}));

            validParticipants.forEach((participant, index) => {
                children.push(new Paragraph({ text: `${translatePropToLabel(getSingular(participant.label) || key).toUpperCase()} ${index + 1}`}));
                const pRows = Object.entries(participant.value as Record<string, ValueWithSource>)
                    .filter(([_, field]) => !field.is_deleted)
                    .map(([fieldKey, fieldValue]) => new TableRow({
                        children: [
                            new TableCell({ children: [new Paragraph(translatePropToLabel(getFieldLabel(fieldKey, fieldValue)).toUpperCase())]}),
                            new TableCell({ children: [new Paragraph(String(getFieldValue(fieldValue) || ""))]}),
                        ]
                    }));
                if (pRows.length > 0) {
                    children.push(new Table({ rows: pRows, width: { size: 100, type: WidthType.PERCENTAGE }}));
                }
            });
        });

  });

  return { children };
};
