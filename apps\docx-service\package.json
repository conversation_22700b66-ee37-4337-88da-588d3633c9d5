{"name": "docx-service", "version": "1.0.0", "description": "Docx service for docx generation", "main": "dist/main.js", "scripts": {"start": "node dist/main.js", "build": "tsc && copyfiles -u 1 \"assets/**/*\" dist/assets", "dev": "ts-node-dev --respawn --transpile-only --ignore-watch node_modules --no-notify main.ts"}, "dependencies": {"cors": "^2.8.5", "docx": "^9.5.1", "dotenv": "^16.4.7", "express": "^5.1.0", "kafkajs": "^2.2.4", "minio": "^8.0.1"}, "devDependencies": {"@types/compression": "^1.8.1", "@types/cors": "^2.8.17", "@types/express": "^5.0.3", "@types/minio": "^7.1.1", "@types/node": "^24.0.10", "copyfiles": "^2.4.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}