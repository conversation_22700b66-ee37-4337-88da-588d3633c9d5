trigger:
  - master

pool:
  name: <PERSON><PERSON>gentSnapReport
  demands:
    - agent.name -equals agent_master

jobs:
- job: Deploy
  timeoutInMinutes: 0   # disable timeout for this whole job
  steps:
    - script: |
        echo "Pulling latest code (force overwrite)"
        cd /home/<USER>/snap_reports/Snap_Reports_10
        git fetch --all
        git reset --hard origin/master
        git clean -fd
      displayName: 'Pull latest from master branch'

    - script: |
        echo "Running deploy script"
        cd /home/<USER>/snap_reports/Snap_Reports_10
        chmod +x generate_deploy.sh
        ./generate_deploy.sh  --profile prod \
          --client-id-google $(CLIENT_ID_GOOGLE) \
          --client-secret-google $(CLIENT_SECRET_GOOGLE) \
          --client-id-microsoft $(CLIENT_ID_MICROSOFT) \
          --client-secret-microsoft $(CLIENT_SECRET_MICROSOFT) \
          --keycloak-admin-password $(KEYCLOAK_ADMIN_PASSWORD) \
          --snap-api-client-secret $(SNAP_API_CLIENT_SECRET) \
          --captcha-key $(CAPTCHA_KEY)
      displayName: 'Run Docker Deploy Script'
