import {
  ISectionOptions,
  Paragraph,
  TextRun,
  Table,
  TableRow,
  TableCell,
  ShadingType,
  WidthType,
} from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintDiariosOficiaisProps {
  section: Omit<ReportSection, "data"> & {
    data: Array<{
      local?: ValueWithSource<string>;
      ["descrição"]?: ValueWithSource<string>;
      ["texto correspondente"]?: ValueWithSource<string>;
      detalhes?: Record<string, ValueWithSource>;
      [key: string]: any;
    }>;
  };
}

export const renderDiariosOficiais = ({
  section,
}: RenderPrintDiariosOficiaisProps): ISectionOptions => {
  const children: Array<Paragraph | Table> = [
    createSectionTitle(section.title),
  ];

  if (!section.data?.length) {
    children.push(new Paragraph("Nenhuma informação encontrada."));
    return { children };
  }

  section.data.forEach((diario, idx) => {
    // Render local, descrição, texto correspondente as a table if any exists
    const infoRows: TableRow[] = [];

    if (diario.local && !diario.local.is_deleted) {
      infoRows.push(
        new TableRow({
          children: [
            new TableCell({
              children: [
                new Paragraph({
                  children: [
                    new TextRun({ text: "LOCAL", bold: true, color: "FE473C" }),
                    new TextRun({
                      text: ` | ${
                        diario.local.source?.map(translateSource).join(", ") ||
                        ""
                      }`,
                      size: 16,
                      color: "FE473C",
                    }),
                  ],
                }),
              ],
              shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
            }),
            new TableCell({
              children: [new Paragraph(diario.local.value)],
              shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
            }),
          ],
        })
      );
    }

    if (diario["descrição"] && !diario["descrição"].is_deleted) {
      infoRows.push(
        new TableRow({
          children: [
            new TableCell({
              children: [
                new Paragraph({
                  children: [
                    new TextRun({
                      text: translatePropToLabel(
                        diario["descrição"].label || "Descrição"
                      ).toUpperCase(),
                      bold: true,
                      color: "889EA3",
                    }),
                    new TextRun({
                      text: ` | ${
                        diario["descrição"].source
                          ?.map(translateSource)
                          .join(", ") || ""
                      }`,
                      size: 16,
                      color: "FE473C",
                    }),
                  ],
                }),
              ],
              shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
            }),
            new TableCell({
              children: [new Paragraph(diario["descrição"].value)],
              shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
            }),
          ],
        })
      );
    }

    if (
      diario["texto correspondente"] &&
      !diario["texto correspondente"].is_deleted
    ) {
      infoRows.push(
        new TableRow({
          children: [
            new TableCell({
              children: [
                new Paragraph({
                  children: [
                    new TextRun({
                      text: translatePropToLabel(
                        diario["texto correspondente"].label ||
                          "Texto Correspondente"
                      ).toUpperCase(),
                      bold: true,
                      color: "889EA3",
                    }),
                    new TextRun({
                      text: ` | ${
                        diario["texto correspondente"].source
                          ?.map(translateSource)
                          .join(", ") || ""
                      }`,
                      size: 16,
                      color: "FE473C",
                    }),
                  ],
                }),
              ],
              shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
            }),
            new TableCell({
              children: [new Paragraph(diario["texto correspondente"].value)],
              shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
            }),
          ],
        })
      );
    }

    if (infoRows.length > 0) {
      children.push(
        new Table({
          rows: infoRows,
          width: { size: 100, type: WidthType.PERCENTAGE },
          columnWidths: [3500, 5500],
          borders: {
            top: { style: "none", size: 0, color: "FFFFFF" },
            bottom: { style: "none", size: 0, color: "FFFFFF" },
            left: { style: "none", size: 0, color: "FFFFFF" },
            right: { style: "none", size: 0, color: "FFFFFF" },
            insideHorizontal: { style: "none", size: 0, color: "FFFFFF" },
            insideVertical: { style: "none", size: 0, color: "FFFFFF" },
          },
        })
      );
    }

    // Render detalhes as a separate table if exists
    if (diario.detalhes) {
      const detalhesRows: TableRow[] = Object.entries(diario.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(
          ([key, field]) =>
            new TableRow({
              children: [
                new TableCell({
                  children: [
                    new Paragraph({
                      children: [
                        new TextRun({
                          text: translatePropToLabel(
                            field.label || key
                          ).toUpperCase(),
                          bold: true,
                          color: "889EA3",
                        }),
                        new TextRun({
                          text: ` | ${
                            field.source?.map(translateSource).join(", ") || ""
                          }`,
                          size: 16,
                          color: "FE473C",
                        }),
                      ],
                    }),
                  ],
                  shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
                }),
                new TableCell({
                  children: [new Paragraph(String(field.value))],
                  shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
                }),
              ],
            })
        );

      if (detalhesRows.length > 0) {
        children.push(
          new Table({
            rows: detalhesRows,
            width: { size: 100, type: WidthType.PERCENTAGE },
            columnWidths: [3500, 5500],
            borders: {
              top: { style: "none", size: 0, color: "FFFFFF" },
              bottom: { style: "none", size: 0, color: "FFFFFF" },
              left: { style: "none", size: 0, color: "FFFFFF" },
              right: { style: "none", size: 0, color: "FFFFFF" },
              insideHorizontal: { style: "none", size: 0, color: "FFFFFF" },
              insideVertical: { style: "none", size: 0, color: "FFFFFF" },
            },
          })
        );
      }
    }
  });

  return { children };
};
