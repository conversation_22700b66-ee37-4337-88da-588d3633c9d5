import React from 'react';

export interface SVGPresentationAttributes {
  fill?: string;
  fillOpacity?: string | number;
  fillRule?: 'nonzero' | 'evenodd';
  stroke?: string;
  strokeWidth?: string | number;
  strokeOpacity?: string | number;
  strokeLinecap?: 'butt' | 'round' | 'square';
  strokeLinejoin?: 'miter' | 'round' | 'bevel';
  strokeDasharray?: string;
  strokeDashoffset?: string | number;
  opacity?: string | number;
  transform?: string;
  clipPath?: string;
  clipRule?: 'nonzero' | 'evenodd';
  mask?: string;
  filter?: string;
}

export interface SVGProps extends SVGPresentationAttributes {
  className?: string;
  style?: React.CSSProperties | SVGPresentationAttributes;
  width?: string | number;
  height?: string | number;
  viewBox?: string;
  preserveAspectRatio?: string;
  children?: React.ReactNode;
}

/**
 * The <SVG /> element is a container that defines a new coordinate system and viewport. 
 * It is used as the outermost element of SVG documents.
 */
export const Svg: React.FC<SVGProps> = ({
  style,
  width,
  height,
  viewBox,
  preserveAspectRatio,
  children,
  ...svgAttributes
}) => {
  // Convert style to appropriate format
  const svgStyle = typeof style === 'object' ? style : {};

  return (
    <svg
      className="pdf-svg"
      width={width}
      height={height}
      viewBox={viewBox}
      preserveAspectRatio={preserveAspectRatio}
      style={svgStyle}
      {...svgAttributes}
    >
      {children}
    </svg>
  );
};

export interface RectProps extends SVGPresentationAttributes {
  style?: SVGPresentationAttributes;
  x?: string | number;
  y?: string | number;
  width: string | number;
  height: string | number;
  rx?: string | number;
  ry?: string | number;
  children?: React.ReactNode;
}

/**
 * The <Rect /> element is used to create a rectangle and variations of a rectangle shape.
 */
export const Rect: React.FC<RectProps> = ({
  style,
  x,
  y,
  width,
  height,
  rx,
  ry,
  children,
  ...rectAttributes
}) => {
  return (
    <rect
      className="pdf-rect"
      x={x}
      y={y}
      width={width}
      height={height}
      rx={rx}
      ry={ry}
      style={style}
      {...rectAttributes}
    >
      {children}
    </rect>
  );
};

export interface PathProps extends SVGPresentationAttributes {
  style?: SVGPresentationAttributes;
  d: string;
  children?: React.ReactNode;
}

/**
 * The <Path /> element is the most powerful element in the SVG library of basic shapes.
 * It can be used to create lines, curves, arcs, and more.
 */
export const Path: React.FC<PathProps> = ({
  style,
  d,
  children,
  ...pathAttributes
}) => {
  return (
    <path
      className="pdf-path"
      d={d}
      style={style}
      {...pathAttributes}
    >
      {children}
    </path>
  );
};

export default { Svg, Rect, Path };
