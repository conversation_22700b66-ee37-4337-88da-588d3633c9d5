import secrets
import logging
from exceptions.business_exceptions import SaltGenerationError

logger = logging.getLogger(__name__)

def create_salt():
    logger.info("[create_salt] Generating new salt...")
    try:
        salt = secrets.token_hex(16)
        logger.info("[create_salt] Salt generated: %s", salt)
        return salt
    except Exception as e:
        logger.error("[create_salt] Error generating salt: %s", e)
        raise SaltGenerationError(str(e))

