import json
import logging
import asyncio
import uuid
import random
import os
import socket
import signal

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from fastapi import FastAPI
from fastapi.encoders import jsonable_encoder

from core.config import settings
from core.constants import SummaryReportStatus, Timeouts
from services.minio_service import load_from_minio, store_temp_pdf_data, delete_temp_pdf_data

logger = logging.getLogger(__name__)

# PDF Consumer Configuration
PDF_REQUEST_TOPIC = "pdf-generation-requests"
PDF_REPLY_TOPIC = "pdf-generation-results"

hostname = socket.gethostname()
PID = os.getpid()
PDF_GROUP_ID = f"pdf-backend-responses-{hostname}-{PID}"

# Global variables for PDF response consumer
_pdf_response_consumer = None
_pending_pdf_requests = {}
_pdf_cleanup_task = None
_pdf_producer = None
_pdf_messages_received = 0  # Counter for received messages

# PDF request timeout (30 minutes)
PDF_REQUEST_TIMEOUT = 1800  # 30 minutes in seconds
# Cleanup interval for abandoned requests (5 minutes)
PDF_CLEANUP_INTERVAL = 300  # 5 minutes in seconds

# Graceful shutdown flag
_shutdown_requested = False

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    global _shutdown_requested
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")
    _shutdown_requested = True

# Register signal handlers
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)


async def _get_pdf_producer():
    """Get or create a persistent Kafka producer for PDF requests"""
    global _pdf_producer
    
    if _pdf_producer is None or _pdf_producer._closed:
        logger.info("[_get_pdf_producer] Creating new Kafka producer for PDF requests")
        kfk_servers = [f"{settings.KAFKA_CONTAINER_NAME}:{settings.KAFKA_INTERNAL_PORT}",
                       f"{settings.KAFKA_EXTERNAL_URI}:{settings.KAFKA_EXTERNAL_PORT}"]
        
        try:
            _pdf_producer = AIOKafkaProducer(
                bootstrap_servers=kfk_servers,
                compression_type='gzip',  # Compress messages to reduce network overhead
                acks=1,  # Wait for leader acknowledgment only (faster than 'all')
                linger_ms=0,  # Send immediately, no batching delay
                max_batch_size=32768,  # Larger batch size for better efficiency (32KB)
                request_timeout_ms=10000,  # 10 second timeout for produce requests
                retry_backoff_ms=100  # 100ms backoff for retries
            )
            await _pdf_producer.start()
            logger.info("[_get_pdf_producer] Kafka producer started successfully with gzip compression and optimized settings")
        except Exception as e:
            logger.error(f"[_get_pdf_producer] Failed to create persistent Kafka producer: {e}")
            # Fallback to creating a new producer for this request
            logger.info("[_get_pdf_producer] Falling back to temporary producer")
            temp_producer = AIOKafkaProducer(
                bootstrap_servers=kfk_servers,
                compression_type='gzip',  # Compress messages to reduce network overhead
                acks=1,  # Wait for leader acknowledgment only (faster than 'all')
                linger_ms=0,  # Send immediately, no batching delay
                max_batch_size=32768,  # Larger batch size for better efficiency (32KB)
                request_timeout_ms=10000,  # 10 second timeout for produce requests
                retry_backoff_ms=100  # 100ms backoff for retries
            )
            await temp_producer.start()
            return temp_producer
    
    return _pdf_producer

async def start_minio_event_consumer(app: FastAPI):
    logger.info("[start_minio_event_consumer] Starting MinIO Kafka Consumer...")
    logger.info(
        f"[start_minio_event_consumer] Setting Kafka consumer at"
        f" {settings.KAFKA_CONTAINER_NAME}:{settings.KAFKA_INTERNAL_PORT} or \n"
        f"{settings.KAFKA_EXTERNAL_URI}:{settings.KAFKA_EXTERNAL_PORT}")

    kfk_servers = [f"{settings.KAFKA_CONTAINER_NAME}:{settings.KAFKA_INTERNAL_PORT}",
                   f"{settings.KAFKA_EXTERNAL_URI}:{settings.KAFKA_EXTERNAL_PORT}"]

    consumer = AIOKafkaConsumer(
        "processed-reports",
        bootstrap_servers=kfk_servers,
        group_id="minio-ws-consumer"
    )
    await consumer.start()
    logger.info("[start_minio_event_consumer] MinIO Kafka Consumer started successfully.")
    
    # Add graceful shutdown handler
    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info("[shutdown_event] Application shutdown initiated")
        logger.info("[shutdown_event] Closing Kafka consumer gracefully...")
        try:
            await consumer.stop()
            logger.info("[shutdown_event] Kafka consumer closed successfully")
        except Exception as e:
            logger.error(f"[shutdown_event] Error closing Kafka consumer: {e}")
        
        # Close PDF response consumer if running
        global _pdf_response_consumer
        if _pdf_response_consumer is not None:
            try:
                logger.info("[shutdown_event] Closing PDF response consumer...")
                await _pdf_response_consumer.stop()
                logger.info("[shutdown_event] PDF response consumer closed successfully")
            except Exception as e:
                logger.error(f"[shutdown_event] Error closing PDF response consumer: {e}")
        
        # Close PDF producer if running
        global _pdf_producer
        if _pdf_producer is not None:
            try:
                logger.info("[shutdown_event] Closing PDF producer...")
                await _pdf_producer.stop()
                logger.info("[shutdown_event] PDF producer closed successfully")
            except Exception as e:
                logger.error(f"[shutdown_event] Error closing PDF producer: {e}")
        
        logger.info("[shutdown_event] Application shutdown completed")

    try:
        async for msg in consumer:
            try:
                logger.info("[start_minio_event_consumer] Received message")
                
                payload = json.loads(msg.value.decode())

                logger.info(f"[start_minio_event_consumer] Received payload: {payload}" )

                record = payload.get("Records", [{}])[0]

                logger.info(f"[start_minio_event_consumer] Received record: {record}")

                key = record.get("s3", {}).get("object", {}).get("key")

                logger.info(f"[start_minio_event_consumer] Received event for key: {key}")
                # key = payload['Records'][0]['s3']['object']['key']

                if not key:
                    logger.warning(f"[start_minio_event_consumer] No object key found in message: {payload}",)
                    continue

                logger.info("[start_minio_event_consumer] Received event for key: %s", key)

                # Assume file name is user_id_reports_id.json
                if "_" not in key:
                    logger.warning(f"[start_minio_event_consumer] Invalid key format: {key}")
                    continue

                user_id, reports_id = key.replace(".json", "").split("_", 1)

                manager = app.state.connection_manager
                websocket = manager.get_connection(user_id)

                if not websocket:
                    logger.warning(f"[start_minio_event_consumer] No active WebSocket found for user_id={user_id}."
                                   f" Skipping processing.")
                    continue  # Skip deleting the report

                logger.info(f"[start_minio_event_consumer] Found WebSocket for user_id={user_id}."
                            f"Report_id={reports_id}>Loading report...")

                try:
                    result = await load_from_minio(
                        bucket_name="processed-reports",
                        object_name=f"{user_id}_{reports_id}.json",
                        user_id=user_id
                    )
                except Exception as e:
                    logger.exception(f"[start_minio_event_consumer] Error loading report {user_id}_{reports_id}.json"
                                     f" from minio - {e}")
                    continue

                try:
                    safe_result = jsonable_encoder(result)
                    logger.info("[start_minio_event_consumer] Safe Result=%s", safe_result.keys())

                    message = {
                        "id": reports_id,
                        "status_code": SummaryReportStatus.success,
                        "result": safe_result,
                    }

                    try:
                        await websocket.send_json(message)
                        logger.info(f"[start_minio_event_consumer] Sent report to user_id={user_id}, "
                                    f"reports_id={reports_id}, status = {message['status_code']}")

                    except Exception as send_err:
                        logger.warning(f"[start_minio_event_consumer] Failed to send WebSocket message: {send_err}")
                        # manager.disconnect(user_id)
                        continue  # DO NOT delete the report


                    # try:
                    #     await delete_from_minio(
                    #         bucket_name="processed-reports",
                    #         object_name=f"{user_id}_{reports_id}.json",
                    #         user_id=user_id
                    #     )
                    # except Exception as delete_err:
                    #     logger.warning(f"[start_minio_event_consumer] Failed to delete from MinIO: {delete_err}")

                except RuntimeError as send_err:
                    logger.warning(
                        "[start_minio_event_consumer] Failed to send WebSocket message: %s. Cleaning up.",
                        send_err
                    )
                    # manager.disconnect(user_id)

            except Exception as e:
                logger.exception(f"[start_minio_event_consumer] Error processing Kafka message - {e}")

    finally:
        logger.info("[start_minio_event_consumer] Stopping MinIO Kafka Consumer...")
        await consumer.stop()


async def start_pdf_event_consumer(app: FastAPI):
    """Optional: Start PDF response consumer proactively during app startup"""
    logger.info("[start_pdf_event_consumer] Starting PDF Kafka Consumer...")
    logger.info("[start_pdf_event_consumer] This will start the PDF response consumer that listens for completed PDFs")
    await _start_pdf_response_consumer()
    logger.info("[start_pdf_event_consumer] PDF Kafka Consumer started successfully.")


async def _start_pdf_response_consumer():
    """Start a single consumer for all PDF responses"""
    global _pdf_response_consumer, _pending_pdf_requests

    if _pdf_response_consumer is not None:
        logger.info("[_start_pdf_response_consumer] PDF response consumer already running, skipping initialization.")
        return

    logger.info("[_start_pdf_response_consumer] Starting global PDF response consumer...")
    logger.info(f"[_start_pdf_response_consumer] Consumer group: {PDF_GROUP_ID}")
    logger.info(f"[_start_pdf_response_consumer] Hostname: {hostname}, PID: {PID}")

    kfk_servers = [f"{settings.KAFKA_CONTAINER_NAME}:{settings.KAFKA_INTERNAL_PORT}",
                   f"{settings.KAFKA_EXTERNAL_URI}:{settings.KAFKA_EXTERNAL_PORT}"]

    logger.info(f"[_start_pdf_response_consumer] Topic: {PDF_REPLY_TOPIC}")
    logger.info(f"[_start_pdf_response_consumer] Kafka servers: {kfk_servers}")

    _pdf_response_consumer = AIOKafkaConsumer(
        PDF_REPLY_TOPIC,
        bootstrap_servers=kfk_servers,
        group_id=PDF_GROUP_ID,
        auto_offset_reset='earliest',  # Changed from 'latest' to ensure we don't miss messages
        enable_auto_commit=True
    )
    
    try:
        logger.info(f"[_start_pdf_response_consumer] Attempting to start consumer...")
        await _pdf_response_consumer.start()
        logger.info(f"[_start_pdf_response_consumer] Consumer started successfully")
        
        # Add additional verification that consumer is actually running
        logger.info(f"[_start_pdf_response_consumer] Consumer state verification:")
        logger.info(f"[_start_pdf_response_consumer] - Consumer object: {_pdf_response_consumer}")
        logger.info(f"[_start_pdf_response_consumer] - Consumer running: {not _pdf_response_consumer._closed}")
        logger.info(f"[_start_pdf_response_consumer] - Consumer group: {PDF_GROUP_ID}")
        logger.info(f"[_start_pdf_response_consumer] - Consumer topic: {PDF_REPLY_TOPIC}")
        
    except Exception as e:
        logger.error(f"[_start_pdf_response_consumer] Failed to start consumer: {e}")
        logger.error(f"[_start_pdf_response_consumer] Exception details: {type(e).__name__}: {str(e)}")
        raise

    # Start background task to process responses
    asyncio.create_task(_process_pdf_responses())
    
    # Start background cleanup task
    global _pdf_cleanup_task
    if _pdf_cleanup_task is None or _pdf_cleanup_task.done():
        _pdf_cleanup_task = asyncio.create_task(_cleanup_abandoned_pdf_requests())
        logger.info("[_start_pdf_response_consumer] PDF cleanup task started")
    
    logger.info(f"[_start_pdf_response_consumer] Global PDF response consumer started successfully with timeout: {PDF_REQUEST_TIMEOUT}s, cleanup interval: {PDF_CLEANUP_INTERVAL}s")


async def _process_pdf_responses():
    """Background task to process all PDF responses"""
    global _pdf_response_consumer, _pending_pdf_requests

    logger.info(f"[_process_pdf_responses] Starting PDF response processor for topic: {PDF_REPLY_TOPIC}")
    logger.info(f"[_process_pdf_responses] Consumer group: {PDF_GROUP_ID}")
    logger.info(f"[_process_pdf_responses] Consumer object: {_pdf_response_consumer}")
    logger.info(f"[_process_pdf_responses] Consumer running: {not _pdf_response_consumer._closed}")
    logger.info(f"[_process_pdf_responses] Waiting for messages...")
    
    try:
        logger.info(f"[_process_pdf_responses] About to start consuming messages from topic: {PDF_REPLY_TOPIC}")
        logger.info(f"[_process_pdf_responses] Consumer is ready to receive messages...")
        logger.info(f"[_process_pdf_responses] Current pending requests: {list(_pending_pdf_requests.keys())}")
        
        async for msg in _pdf_response_consumer:
            # Check for shutdown signal
            if _shutdown_requested:
                logger.info("[_process_pdf_responses] Shutdown requested, stopping message processing")
                break
                
            global _pdf_messages_received
            _pdf_messages_received += 1
            logger.info(f"[_process_pdf_responses] Received message #{_pdf_messages_received} from Kafka topic: {msg.topic}, partition: {msg.partition}, offset: {msg.offset}")
            logger.info(f"[_process_pdf_responses] Message timestamp: {msg.timestamp}, key: {msg.key.decode() if msg.key else 'None'}")
            logger.info(f"[_process_pdf_responses] Message size: {len(msg.value)} bytes")
            
            try:
                raw_message = msg.value.decode()
                logger.info(f"[_process_pdf_responses] Received raw message from Kafka: {raw_message}")
                result = json.loads(raw_message)
                request_id = result.get("requestId")
                status = result.get('status', 'unknown')
                timestamp = result.get('timestamp', 'unknown')

                logger.info(f"[_process_pdf_responses] Parsed PDF response - Request ID: {request_id}, Status: {status}, Timestamp: {timestamp}")
                logger.info(f"[_process_pdf_responses] Response metadata: {result.get('metadata', {})}")
                logger.info(f"[_process_pdf_responses] Response filename: {result.get('filename', 'unknown')}")
                logger.info(f"[_process_pdf_responses] Response PDF reference: {result.get('pdfReference', 'unknown')}")

                if request_id in _pending_pdf_requests:
                    future = _pending_pdf_requests[request_id]  # Don't remove it yet
                    logger.info(f"[_process_pdf_responses] Found pending request {request_id} in local cache")
                    logger.info(f"[_process_pdf_responses] Future state - Done: {future.done()}, Cancelled: {future.cancelled()}")
                    
                    if not future.done():
                        if result.get("status") == "error":
                            error_msg = f"PDF generation failed: {result.get('error', 'Unknown error')}"
                            error_details = result.get('error_details', {})
                            logger.error(f"[_process_pdf_responses] Setting exception for request {request_id}: {error_msg}")
                            logger.error(f"[_process_pdf_responses] Error details: {error_details}")
                            future.set_exception(Exception(error_msg))
                            # Remove failed requests immediately
                            _pending_pdf_requests.pop(request_id, None)
                            logger.info(f"[_process_pdf_responses] Removed failed request {request_id} from pending requests")
                        else:
                            logger.info(f"[_process_pdf_responses] Setting successful result for request {request_id}")
                            logger.info(f"[_process_pdf_responses] Result details: {result}")
                            future.set_result(result)
                            logger.info(f"[_process_pdf_responses] Future set successfully for request {request_id}")
                            logger.info(f"[_process_pdf_responses] Remaining pending requests: {list(_pending_pdf_requests.keys())}")
                            # Don't remove successful requests - let polling function handle cleanup
                    else:
                        logger.warning(f"[_process_pdf_responses] Future for request {request_id} is already done, ignoring duplicate response")
                else:
                    logger.warning(f"[_process_pdf_responses] Received response for unknown request: {request_id}")
                    logger.info(f"[_process_pdf_responses] Current pending requests: {list(_pending_pdf_requests.keys())}")
                    logger.info(f"[_process_pdf_responses] This might indicate a worker coordination issue - request was sent by a different worker")
                    logger.info(f"[_process_pdf_responses] Response details for unknown request: {result}")
            except Exception as e:
                logger.exception(f"[_process_pdf_responses] Error processing PDF response: {e}")
    except Exception as e:
        if _shutdown_requested:
            logger.info("[_process_pdf_responses] Shutdown requested, stopping consumer loop")
            return
        logger.exception(f"[_process_pdf_responses] Error in PDF response processor: {e}")
        logger.error(f"[_process_pdf_responses] Consumer failed with exception type: {type(e).__name__}")
        logger.error(f"[_process_pdf_responses] Consumer failed with exception details: {str(e)}")
        # Try to restart the consumer after a delay
        logger.info(f"[_process_pdf_responses] Will attempt to restart consumer after 30 seconds...")
        await asyncio.sleep(30)
        # Reset the consumer so it can be restarted
        _pdf_response_consumer = None
        logger.info("[_process_pdf_responses] Consumer reset completed, ready for restart")


async def send_pdf_request_and_wait(data):
    """Send PDF request to Kafka and wait for response"""
    global _pending_pdf_requests, _pdf_response_consumer
    
    start_time = asyncio.get_event_loop().time()

    # Ensure response consumer is running
    logger.info(f"[send_pdf_request_and_wait] Ensuring PDF response consumer is running...")
    await _start_pdf_response_consumer()
    logger.info(f"[send_pdf_request_and_wait] PDF response consumer should be running now")

    request_id = str(uuid.uuid4())

    logger.info(f"[send_pdf_request_and_wait] Sending PDF request with ID: {request_id}")

    # Create future for this request with timestamp for tracking
    future = asyncio.Future()
    future._created_time = asyncio.get_event_loop().time()
    future._worker_id = f"{hostname}-{PID}"
    _pending_pdf_requests[request_id] = future

    # Store sensitive PDF data in MinIO temporarily
    data_reference = f"pdf-request-{request_id}"
    
    # Prepare Kafka message while storing data in MinIO (concurrent operations)
    user_context = data.get('user_context', {})
    kafka_message = {
        'requestId': request_id,
        'dataReference': data_reference,
        'metadata': {
            'report_type': user_context.get('report_type'),
            'user_reports_id': user_context.get('user_reports_id'),
            'user_id': user_context.get('user_id')
        },
        'timestamp': asyncio.get_event_loop().time()
    }
    
    # Store data in MinIO
    minio_start = asyncio.get_event_loop().time()
    logger.info(f"[send_pdf_request_and_wait] Starting MinIO storage for data reference: {data_reference}")
    logger.info(f"[send_pdf_request_and_wait] Data size to store: {len(json.dumps(data))} bytes")
    logger.info(f"[send_pdf_request_and_wait] Data keys: {list(data.keys())}")
    
    try:
        await store_temp_pdf_data(data_reference, data)
        minio_time = asyncio.get_event_loop().time() - minio_start
        logger.info(f"[send_pdf_request_and_wait] Successfully stored PDF data in MinIO with reference: {data_reference}")
        logger.info(f"[send_pdf_request_and_wait] MinIO storage operation completed in {minio_time:.2f}s")
        logger.info(f"[send_pdf_request_and_wait] Data reference ready for PDF service: {data_reference}")
    except Exception as e:
        minio_time = asyncio.get_event_loop().time() - minio_start
        logger.error(f"[send_pdf_request_and_wait] Failed to store PDF data in MinIO after {minio_time:.2f}s: {e}")
        logger.error(f"[send_pdf_request_and_wait] Exception type: {type(e).__name__}")
        logger.error(f"[send_pdf_request_and_wait] Exception details: {str(e)}")
        _pending_pdf_requests.pop(request_id, None)
        raise

    # Send request using persistent producer
    kafka_start = asyncio.get_event_loop().time()
    logger.info(f"[send_pdf_request_and_wait] Starting Kafka message send for request: {request_id}")
    logger.info(f"[send_pdf_request_and_wait] Kafka topic: {PDF_REQUEST_TOPIC}")
    logger.info(f"[send_pdf_request_and_wait] Kafka message key: {request_id}")
    
    try:
        producer = await _get_pdf_producer()
        logger.info(f"[send_pdf_request_and_wait] Kafka producer obtained successfully")
        
        message_size = len(json.dumps(kafka_message))
        original_data_size = len(json.dumps(data))
        compression_ratio = (1 - message_size / original_data_size) * 100 if original_data_size > 0 else 0
        
        logger.info(f"[send_pdf_request_and_wait] Kafka message size: {message_size} bytes")
        logger.info(f"[send_pdf_request_and_wait] Original data size: {original_data_size} bytes")
        logger.info(f"[send_pdf_request_and_wait] Data compression ratio: {compression_ratio:.1f}%")
        logger.info(f"[send_pdf_request_and_wait] Kafka message content: {kafka_message}")
        
        # Use send() instead of send_and_wait() for better performance
        # The message will be sent asynchronously without waiting for acknowledgment
        await producer.send(PDF_REQUEST_TOPIC, json.dumps(kafka_message).encode(), key=request_id.encode())
        kafka_time = asyncio.get_event_loop().time() - kafka_start
        logger.info(f"[send_pdf_request_and_wait] PDF request sent successfully to Kafka")
        logger.info(f"[send_pdf_request_and_wait] Kafka send operation completed in {kafka_time:.2f}s")
        logger.info(f"[send_pdf_request_and_wait] Request {request_id} is now waiting for PDF service response")
    except Exception as e:
        kafka_time = asyncio.get_event_loop().time() - kafka_start
        logger.error(f"[send_pdf_request_and_wait] Failed to send Kafka message after {kafka_time:.2f}s")
        logger.error(f"[send_pdf_request_and_wait] Exception type: {type(e).__name__}")
        logger.error(f"[send_pdf_request_and_wait] Exception details: {str(e)}")
        logger.error(f"[send_pdf_request_and_wait] Kafka topic: {PDF_REQUEST_TOPIC}")
        logger.error(f"[send_pdf_request_and_wait] Kafka message key: {request_id}")
        _pending_pdf_requests.pop(request_id, None)
        raise

    try:
        # Wait for response with timeout
        setup_time = asyncio.get_event_loop().time() - start_time
        logger.info(f"[send_pdf_request_and_wait] Setup completed in {setup_time:.2f}s")
        logger.info(f"[send_pdf_request_and_wait] Waiting for PDF response with timeout of {PDF_REQUEST_TIMEOUT} seconds")
        logger.info(f"[send_pdf_request_and_wait] Request ID: {request_id}")
        logger.info(f"[send_pdf_request_and_wait] Future object: {future}")
        logger.info(f"[send_pdf_request_and_wait] Future done: {future.done()}, Future cancelled: {future.cancelled()}")
        
        result = await asyncio.wait_for(future, timeout=PDF_REQUEST_TIMEOUT)
        total_time = asyncio.get_event_loop().time() - start_time
        logger.info(f"[send_pdf_request_and_wait] PDF request completed successfully")
        logger.info(f"[send_pdf_request_and_wait] Request ID: {request_id}")
        logger.info(f"[send_pdf_request_and_wait] Total processing time: {total_time:.2f}s")
        logger.info(f"[send_pdf_request_and_wait] Result received: {result}")
        return result
    except asyncio.TimeoutError:
        total_time = asyncio.get_event_loop().time() - start_time
        logger.error(f"[send_pdf_request_and_wait] PDF request timed out")
        logger.error(f"[send_pdf_request_and_wait] Request ID: {request_id}")
        logger.error(f"[send_pdf_request_and_wait] Timeout duration: {PDF_REQUEST_TIMEOUT} seconds")
        logger.error(f"[send_pdf_request_and_wait] Total time elapsed: {total_time:.2f}s")
        logger.error(f"[send_pdf_request_and_wait] Future state - Done: {future.done()}, Cancelled: {future.cancelled()}")
        
        # Clean up pending request and temporary data
        _pending_pdf_requests.pop(request_id, None)
        logger.info(f"[send_pdf_request_and_wait] Removed timed out request {request_id} from pending requests")
        
        try:
            logger.info(f"[send_pdf_request_and_wait] Starting cleanup of temporary PDF data: {data_reference}")
            await delete_temp_pdf_data(data_reference)
            logger.info(f"[send_pdf_request_and_wait] Successfully cleaned up temporary PDF data: {data_reference}")
        except Exception as cleanup_error:
            logger.warning(f"[send_pdf_request_and_wait] Failed to cleanup temporary PDF data {data_reference}")
            logger.warning(f"[send_pdf_request_and_wait] Cleanup error: {cleanup_error}")
            logger.warning(f"[send_pdf_request_and_wait] Cleanup error type: {type(cleanup_error).__name__}")
        
        raise TimeoutError(f"PDF generation timed out after {PDF_REQUEST_TIMEOUT} seconds for request {request_id}")
    except Exception as e:
        # Clean up pending request and temporary data
        _pending_pdf_requests.pop(request_id, None)
        try:
            await delete_temp_pdf_data(data_reference)
            logger.info(f"[send_pdf_request_and_wait] Cleaned up temporary PDF data after error: {data_reference}")
        except Exception as cleanup_error:
            logger.warning(f"[send_pdf_request_and_wait] Failed to cleanup temporary PDF data {data_reference} after error: {cleanup_error}")
        logger.error(f"[send_pdf_request_and_wait] PDF generation error for request {request_id}: {e}")
        raise e


async def _cleanup_abandoned_pdf_requests():
    """Background task to clean up abandoned PDF requests"""
    global _pending_pdf_requests
    
    logger.info("[_cleanup_abandoned_pdf_requests] Starting PDF request cleanup task")
    
    while True:
        try:
            # Check for shutdown signal
            if _shutdown_requested:
                logger.info("[_cleanup_abandoned_pdf_requests] Shutdown requested, stopping cleanup task")
                break
                
            await asyncio.sleep(PDF_CLEANUP_INTERVAL)
            
            current_time = asyncio.get_event_loop().time()
            abandoned_requests = []
            
            # Find abandoned requests (older than timeout)
            logger.info(f"[_cleanup_abandoned_pdf_requests] Checking {len(_pending_pdf_requests)} pending requests for cleanup")
            for request_id, future in list(_pending_pdf_requests.items()):
                if future.done():
                    # Future is already done, remove it
                    _pending_pdf_requests.pop(request_id, None)
                    logger.info(f"[_cleanup_abandoned_pdf_requests] Removed completed future for request: {request_id}")
                elif hasattr(future, '_created_time') and (current_time - future._created_time) > PDF_REQUEST_TIMEOUT:
                    # Request has exceeded timeout, mark as abandoned
                    abandoned_requests.append(request_id)
                    logger.info(f"[_cleanup_abandoned_pdf_requests] Marking request {request_id} as abandoned (age: {current_time - future._created_time:.2f}s)")
            
            # Clean up abandoned requests
            for request_id in abandoned_requests:
                logger.info(f"[_cleanup_abandoned_pdf_requests] Cleaning up abandoned request: {request_id}")
                future = _pending_pdf_requests.pop(request_id, None)
                if future and not future.done():
                    future.set_exception(TimeoutError(f"PDF request abandoned after {PDF_REQUEST_TIMEOUT} seconds"))
                    logger.warning(f"[_cleanup_abandoned_pdf_requests] Cleaned up abandoned PDF request: {request_id}")
                else:
                    logger.info(f"[_cleanup_abandoned_pdf_requests] Request {request_id} was already removed or completed")
            
            if abandoned_requests:
                logger.info(f"[_cleanup_abandoned_pdf_requests] Cleaned up {len(abandoned_requests)} abandoned PDF requests")
            else:
                logger.debug(f"[_cleanup_abandoned_pdf_requests] No abandoned PDF requests found. Active requests: {len(_pending_pdf_requests)}")
                
        except Exception as e:
            logger.exception(f"[_cleanup_abandoned_pdf_requests] Error in cleanup task: {e}")
            # Continue running the cleanup task even if there's an error
            await asyncio.sleep(60)  # Wait 1 minute before retrying


def get_pdf_requests_status():
    """Get current status of pending PDF requests for monitoring"""
    global _pending_pdf_requests
    
    current_time = asyncio.get_event_loop().time()
    status = {
        'total_pending': len(_pending_pdf_requests),
        'requests': []
    }
    
    for request_id, future in _pending_pdf_requests.items():
        request_info = {
            'request_id': request_id,
            'done': future.done(),
            'cancelled': future.cancelled()
        }
        
        if hasattr(future, '_created_time'):
            age_seconds = current_time - future._created_time
            request_info['age_seconds'] = age_seconds
            request_info['timeout_remaining'] = max(0, PDF_REQUEST_TIMEOUT - age_seconds)
        
        status['requests'].append(request_info)
    
    return status


async def check_pdf_service_status():
    """Check if PDF service is running and processing messages"""
    global _pending_pdf_requests, _pdf_response_consumer
    
    status = {
        'response_consumer_running': _pdf_response_consumer is not None and not _pdf_response_consumer._closed,
        'pending_requests_count': len(_pending_pdf_requests),
        'pending_requests': []
    }
    
    current_time = asyncio.get_event_loop().time()
    for request_id, future in _pending_pdf_requests.items():
        request_info = {
            'request_id': request_id,
            'age_seconds': current_time - future._created_time if hasattr(future, '_created_time') else 0,
            'done': future.done(),
            'cancelled': future.cancelled()
        }
        status['pending_requests'].append(request_info)
    
    return status


async def debug_pdf_request(request_id: str):
    """Debug a specific PDF request"""
    global _pending_pdf_requests
    
    if request_id in _pending_pdf_requests:
        future = _pending_pdf_requests[request_id]
        current_time = asyncio.get_event_loop().time()
        
        debug_info = {
            'request_id': request_id,
            'exists': True,
            'done': future.done(),
            'cancelled': future.cancelled(),
            'worker_id': getattr(future, '_worker_id', 'unknown'),
            'age_seconds': current_time - future._created_time if hasattr(future, '_created_time') else 0,
            'timeout_remaining': max(0, PDF_REQUEST_TIMEOUT - (current_time - future._created_time)) if hasattr(future, '_created_time') else 0
        }
        
        if future.done():
            try:
                debug_info['result'] = future.result()
            except Exception as e:
                debug_info['exception'] = str(e)
    else:
        debug_info = {
            'request_id': request_id,
            'exists': False,
            'available_requests': list(_pending_pdf_requests.keys())
        }
    
    return debug_info


async def start_pdf_generation_async(data):
    """Start PDF generation asynchronously - returns immediately with request ID"""
    global _pending_pdf_requests, _pdf_response_consumer
    
    start_time = asyncio.get_event_loop().time()

    # Ensure response consumer is running
    await _start_pdf_response_consumer()

    request_id = str(uuid.uuid4())

    logger.info(f"[start_pdf_generation_async] Starting PDF generation with ID: {request_id}")

    # Create future for this request with timestamp for tracking
    future = asyncio.Future()
    future._created_time = asyncio.get_event_loop().time()
    future._worker_id = f"{hostname}-{PID}"
    _pending_pdf_requests[request_id] = future
    
    logger.info(f"[start_pdf_generation_async] Added request {request_id} to pending requests. Total pending: {len(_pending_pdf_requests)}")
    logger.info(f"[start_pdf_generation_async] Pending requests after adding: {list(_pending_pdf_requests.keys())}")

    # Store sensitive PDF data in MinIO temporarily
    data_reference = f"pdf-request-{request_id}"
    
    # Prepare Kafka message
    user_context = data.get('user_context', {})
    kafka_message = {
        'requestId': request_id,
        'dataReference': data_reference,
        'metadata': {
            'report_type': user_context.get('report_type'),
            'user_reports_id': user_context.get('user_reports_id'),
            'user_id': user_context.get('user_id')
        },
        'timestamp': asyncio.get_event_loop().time()
    }
    
    # Store data in MinIO
    minio_start = asyncio.get_event_loop().time()
    try:
        await store_temp_pdf_data(data_reference, data)
        minio_time = asyncio.get_event_loop().time() - minio_start
        logger.info(f"[start_pdf_generation_async] Stored PDF data in MinIO with reference: {data_reference} (took {minio_time:.2f}s)")
    except Exception as e:
        logger.error(f"[start_pdf_generation_async] Failed to store PDF data in MinIO: {e}")
        logger.info(f"[start_pdf_generation_async] Removing request {request_id} from pending requests due to MinIO error")
        _pending_pdf_requests.pop(request_id, None)
        raise

    # Send request using persistent producer
    kafka_start = asyncio.get_event_loop().time()
    try:
        producer = await _get_pdf_producer()
        
        message_size = len(json.dumps(kafka_message))
        logger.info(f"[start_pdf_generation_async] Kafka message size: {message_size} bytes (vs original data size: {len(json.dumps(data))} bytes)")
        
        await producer.send(PDF_REQUEST_TOPIC, json.dumps(kafka_message).encode(), key=request_id.encode())
        kafka_time = asyncio.get_event_loop().time() - kafka_start
        logger.info(f"[start_pdf_generation_async] PDF request sent successfully: {request_id} (Kafka send took {kafka_time:.2f}s)")
    except Exception as e:
        kafka_time = asyncio.get_event_loop().time() - kafka_start
        logger.error(f"[start_pdf_generation_async] Failed to send Kafka message after {kafka_time:.2f}s: {e}")
        logger.info(f"[start_pdf_generation_async] Removing request {request_id} from pending requests due to Kafka error")
        _pending_pdf_requests.pop(request_id, None)
        raise

    setup_time = asyncio.get_event_loop().time() - start_time
    logger.info(f"[start_pdf_generation_async] PDF generation started successfully in {setup_time:.2f}s for request: {request_id}")
    
    return {
        "request_id": request_id,
        "status": "processing",
        "setup_time": setup_time
    }


async def get_pdf_status(request_id: str):
    """Get the status of a PDF generation request"""
    global _pending_pdf_requests
    
    if request_id not in _pending_pdf_requests:
        return {"status": "not_found", "error": "Request ID not found"}
    
    future = _pending_pdf_requests[request_id]
    current_time = asyncio.get_event_loop().time()
    
    if future.done():
        try:
            result = future.result()
            # Clean up the completed future
            _pending_pdf_requests.pop(request_id, None)
            return {
                "status": "completed",
                "result": result
            }
        except Exception as e:
            # Clean up the failed future
            _pending_pdf_requests.pop(request_id, None)
            return {
                "status": "failed",
                "error": str(e)
            }
    else:
        # Still processing
        age_seconds = current_time - future._created_time if hasattr(future, '_created_time') else 0
        timeout_remaining = max(0, PDF_REQUEST_TIMEOUT - age_seconds)
        
        return {
            "status": "processing",
            "age_seconds": age_seconds,
            "timeout_remaining": timeout_remaining
        }


async def is_pdf_ready(request_id: str):
    """Check if PDF is ready - returns True if ready, False if still processing"""
    global _pending_pdf_requests
    
    if request_id not in _pending_pdf_requests:
        return False  # Not found, so not ready
    
    future = _pending_pdf_requests[request_id]
    
    if future.done():
        try:
            # PDF is ready, clean up and return True
            future.result()  # This will raise exception if there was an error
            _pending_pdf_requests.pop(request_id, None)
            return True
        except Exception:
            # PDF failed, clean up and return False
            _pending_pdf_requests.pop(request_id, None)
            return False
    else:
        # Still processing
        return False


async def get_pdf_result(request_id: str):
    """Get the PDF result if ready, None if not ready"""
    global _pending_pdf_requests
    
    if request_id not in _pending_pdf_requests:
        logger.debug(f"[get_pdf_result] Request {request_id} not found in pending requests")
        return None  # Not found
    
    future = _pending_pdf_requests[request_id]
    
    if future.done():
        try:
            result = future.result()
            logger.info(f"[get_pdf_result] Request {request_id} is done, returning result")
            # Don't remove it here - let the polling function handle cleanup
            return result
        except Exception as e:
            logger.error(f"[get_pdf_result] Request {request_id} failed with exception: {e}")
            _pending_pdf_requests.pop(request_id, None)
            raise e  # Re-raise the exception
    else:
        logger.debug(f"[get_pdf_result] Request {request_id} is still processing")
        return None  # Not ready yet


async def test_response_consumer():
    """Test if the response consumer is working properly"""
    global _pdf_response_consumer
    
    if _pdf_response_consumer is None:
        return {"status": "error", "message": "Response consumer not initialized"}
    
    try:
        # Check if consumer is running
        if _pdf_response_consumer._closed:
            return {"status": "error", "message": "Response consumer is closed"}
        
        # Get consumer metadata
        metadata = await _pdf_response_consumer.fetch_all_metadata()
        topics = list(metadata.topics().keys())
        
        # Test if we can get topic information
        topic_metadata = metadata.topics().get(PDF_REPLY_TOPIC)
        if topic_metadata:
            partitions = len(topic_metadata.partitions)
        else:
            partitions = 0
        
        return {
            "status": "running",
            "topic": PDF_REPLY_TOPIC,
            "group_id": PDF_GROUP_ID,
            "available_topics": topics,
            "topic_exists": PDF_REPLY_TOPIC in topics,
            "partitions": partitions,
            "consumer_closed": _pdf_response_consumer._closed,
            "hostname": hostname,
            "pid": PID,
            "messages_received": _pdf_messages_received
        }
    except Exception as e:
        return {"status": "error", "message": f"Error checking consumer: {str(e)}"}


async def generate_pdf_with_polling(data, max_wait_time: int = 300):
    """Generate PDF with polling - returns result when ready or raises timeout"""
    global _pending_pdf_requests
    
    start_time = asyncio.get_event_loop().time()
    
    # Start PDF generation
    request_info = await start_pdf_generation_async(data)
    request_id = request_info["request_id"]
    
    logger.info(f"[generate_pdf_with_polling] Started PDF generation for request: {request_id}")
    logger.info(f"[generate_pdf_with_polling] Current pending requests after start: {list(_pending_pdf_requests.keys())}")
    
    # Poll until ready or timeout
    while True:
        current_time = asyncio.get_event_loop().time()
        elapsed_time = current_time - start_time
        
        if elapsed_time > max_wait_time:
            logger.error(f"[generate_pdf_with_polling] Timeout after {elapsed_time:.2f}s for request: {request_id}")
            raise TimeoutError(f"PDF generation timed out after {max_wait_time} seconds")
        
        # Check if ready
        result = await get_pdf_result(request_id)
        if result is not None:
            # Clean up the completed request
            _pending_pdf_requests.pop(request_id, None)
            total_time = asyncio.get_event_loop().time() - start_time
            logger.info(f"[generate_pdf_with_polling] PDF ready after {total_time:.2f}s for request: {request_id}")
            return result
        
        # Debug: Check what's in pending requests
        logger.info(f"[generate_pdf_with_polling] Still waiting for request {request_id}. Pending requests: {list(_pending_pdf_requests.keys())}")
        
        # Additional debug: Check if the specific request exists and its status
        if request_id in _pending_pdf_requests:
            future = _pending_pdf_requests[request_id]
            logger.info(f"[generate_pdf_with_polling] Request {request_id} exists, future.done(): {future.done()}")
        else:
            logger.warning(f"[generate_pdf_with_polling] Request {request_id} NOT found in pending requests!")
        
        check_interval = random.randint(15, 30)
        logger.info(f"[generate_pdf_with_polling] Waiting {check_interval}s before next check for request: {request_id}")
        # Wait before next check
        await asyncio.sleep(check_interval)


async def cleanup_pdf_producer():
    """Clean up the persistent PDF producer on application shutdown"""
    global _pdf_producer
    
    if _pdf_producer and not _pdf_producer._closed:
        logger.info("[cleanup_pdf_producer] Stopping persistent PDF producer")
        await _pdf_producer.stop()
        _pdf_producer = None
        logger.info("[cleanup_pdf_producer] PDF producer stopped successfully")