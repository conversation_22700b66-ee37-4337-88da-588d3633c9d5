
services:
  postgres:
    image: postgres:16
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASS}
      # Locale configuration to prevent collation issues
      LC_ALL: C.UTF-8
      LANG: C.UTF-8
      POSTGRES_INITDB_ARGS: "--locale=C.UTF-8 --encoding=UTF8"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER}"]
      interval: 5s
      timeout: 5s
      retries: 5
    ports:
      - "5432:5432"
    volumes:
      - ./apps/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
      - postgres_data:/var/lib/postgresql/data
    command:
      - "postgres"
      - "-c"
      - "max_connections=300"
      - "-c"
      - "shared_buffers=256MB"
      - "-c"
      - "work_mem=16MB"
      - "-c"
      - "maintenance_work_mem=128MB"
      - "-c"
      - "effective_cache_size=1GB"
      - "-c"
      - "random_page_cost=1.1"
      - "-c"
      - "effective_io_concurrency=200"
      - "-c"
      - "max_worker_processes=8"
      - "-c"
      - "max_parallel_workers_per_gather=4"
      - "-c"
      - "max_parallel_workers=8"
      - "-c"
      - "shared_preload_libraries=pg_stat_statements"
      - "-c"
      - "pg_stat_statements.track=all"
      - "-c"
      - "ssl=${POSTGRES_SSL:-off}"
      - "-c"
      - "ssl_cert_file=${POSTGRES_SSL_CERT_FILE:-}"
      - "-c"
      - "ssl_key_file=${POSTGRES_SSL_KEY_FILE:-}"
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: "2G"
        reservations:
          cpus: "0.5"
          memory: "1G"
    restart: unless-stopped

    networks:
      - mystack-net

volumes:
  postgres_data:

