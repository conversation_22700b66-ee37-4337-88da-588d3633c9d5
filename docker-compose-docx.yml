services:
  docx:
    image: my-docx-image
    ports:
      - "3008:3008"
    environment:
      NODE_OPTIONS: "--max-old-space-size=128"
      FRONTEND_REDIRECT_URL: ${FRONTEND_REDIRECT_URL}
      ENABLE_KAFKA_CONSUMER: ${ENABLE_KAFKA_CONSUMER:-true}
      KAFKA_CONTAINER_NAME: ${KAFKA_CONTAINER_NAME}
      KAFKA_INTERNAL_PORT: ${KAFKA_INTERNAL_PORT}
      KAFKA_EXTERNAL_URI: ${KAFKA_EXTERNAL_URI}
      KAFKA_EXTERNAL_PORT: ${KAFKA_EXTERNAL_PORT}
      MINIO_CONTAINER_NAME: ${MINIO_CONTAINER_NAME}
      MINIO_S3_INTERNAL_PORT: ${MINIO_S3_INTERNAL_PORT}
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: "256M"
        reservations:
          cpus: "0.25"
          memory: 128M
      restart_policy:
        condition: on-failure
    networks:
      - mystack-net
