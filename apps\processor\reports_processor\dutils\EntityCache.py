from reports_processor.constants import MASKED_VALUE, CacheableEntityTypes, PreProcessEntity
from reports_processor.formatters.MainValuesFormatter import format_normalized_document
from reports_processor.utils import normalize_document, normalize_name, is_masked_document_match, do_names_match



class EntityCache:
    """Track and merge entity values, preferring unmasked versions"""

    _store = {}

    @classmethod
    def _get_entity_store(cls, entity_type: CacheableEntityTypes):
        return cls._store.setdefault(entity_type, {})

    @classmethod
    def _make_key(cls, entity_type: CacheableEntityTypes, doc: str, name: str = None):
        is_masked = MASKED_VALUE in doc
        normalized_doc = normalize_document(doc)
        if entity_type == CacheableEntityTypes.PESSOA and is_masked:
            return (normalized_doc, normalize_name(name))

        return normalized_doc

    @classmethod
    def add_entity(cls, entity_type: CacheableEntityTypes, entity_data: dict, source_name):
        doc = entity_data.get(entity_type.main_document, None)
        if not doc:
            return

        name = entity_data.get(entity_type.name, None)
        cls.add(entity_type, doc, name, source_name)


    @classmethod
    def add(cls, entity_type: CacheableEntityTypes, doc: str, name, source_name: str):
        store = cls._get_entity_store(entity_type)
        is_masked = MASKED_VALUE in doc
        key = cls._make_key(entity_type, doc, name)
        normalized_doc = key[0] if type(key) is tuple else key

        formatted = format_normalized_document(normalized_doc, entity_type.main_document)

        new_entry = {
            "raw": doc,
            "formatted": formatted,
            "is_masked": is_masked,
            "source": {source_name}
        }
        if name:
            new_entry["name"] = {name}

        if key in store:
            store[key]['source'].add(source_name)
            if name:
                name_store = store[key].get('name', None)
                if name_store:
                    name_store.add(name)
                else:
                    store[key]['name'] = {name}

        else:
            if is_masked:
                for other_key, other_value in store.items():
                    if isinstance(other_key, str):  # unmasked key
                        if is_masked_document_match(normalized_doc, other_key) and do_names_match(name, other_value.get("name", None)):
                            store[key] = store[other_key]
                            return

            store[key] = new_entry

        if entity_type is CacheableEntityTypes.PESSOA and not is_masked:
            cls._upgrade_masked_person(entity_type, doc, name, key)

    @classmethod
    def _upgrade_masked_person(cls, entity_type: CacheableEntityTypes, unmasked_doc: str, name: str, main_key: str):
        store = cls._get_entity_store(entity_type)
        unmasked_digits = normalize_document(unmasked_doc)
        target_name = normalize_name(name)

        to_upgrade = []
        for key, value in store.items():
            if isinstance(key, tuple):  # it's a masked entry
                masked_doc, entry_name = key
                if do_names_match(entry_name, target_name) and is_masked_document_match(masked_doc, unmasked_digits):
                    to_upgrade.append(key)

        for k in to_upgrade:
            store[k] = store[main_key]

    @classmethod
    def lookup(cls, entity_type: CacheableEntityTypes, doc: str, name: str = None):
        store = cls._get_entity_store(entity_type)
        key = cls._make_key(entity_type, doc, name)
        result = store.get(key)

        # if result and is_masked(doc) and entity_type == "person":
        #     for other_key, other_value in store.items():
        #         if not isinstance(other_key, tuple):  # unmasked entry
        #             if is_masked_document_match(doc, other_key):
        #                 return other_value
        return result

    @classmethod
    def get_formatted_value(cls, entity_type: CacheableEntityTypes, doc: str, name: str = None):
        result = cls.lookup(entity_type, doc, name)
        return result["formatted"] if result else None

    @classmethod
    def lookup_entity(cls, entity_type: CacheableEntityTypes, entity_data):
        doc = entity_data.get(entity_type.main_document, None)
        if type(doc) is dict:
            doc = doc.get("value", None)

        name = entity_data.get(entity_type.name, None)
        if type(name) is dict:
            name = name.get("value", None)

        if not doc:
            return None

        result = cls.lookup(entity_type, doc, name)
        return result if result else None

    @classmethod
    def get_formatted_value_entity(cls, entity_type: CacheableEntityTypes, entity_data):
        result = cls.lookup_entity(entity_type, entity_data)
        return (result["formatted"], result["source"]) if result else None