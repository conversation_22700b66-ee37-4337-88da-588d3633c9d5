interface AuthenticationState {
  isAuthenticated: boolean;
  hasAcceptedTerms: boolean;
  session: {
    id: string;
    userAgent: string;
    ip: string | null;
    createdAt: Date;
    expiresAt: Date;
  } | null;
  user: {
    id: string;
    email: string;
    name: string;
  } | null;
}

interface AuthorizationState {
  isAuthorized: boolean;
  permissions: string[];
}

export interface AuthState {
  authentication: AuthenticationState;
  authorization: AuthorizationState;
}

interface AuthenticationActions {
  setIsAuthenticated: (isAuthenticated: boolean) => void;
  setHasAcceptedTerms: (hasAcceptedTerms: boolean) => void;
  setSession: (session: AuthenticationState["session"]) => void;
  setUser: (user: AuthenticationState["user"]) => void;
  clearAuthentication: () => void;
}

interface AuthorizationActions {
  setIsAuthorized: (isAuthorized: boolean) => void;
  setPermissions: (permissions: string[]) => void;
  clearAuthorization: () => void;
}

export interface AuthMutations
  extends AuthenticationActions,
    AuthorizationActions {}
