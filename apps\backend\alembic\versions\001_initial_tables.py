"""Initial migration - create all existing tables

Revision ID: 001
Revises:
Create Date: 2025-09-25 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create reports schema if it doesn't exist
    op.execute("CREATE SCHEMA IF NOT EXISTS reports")

    # Create EULA table first (no dependencies)
    op.create_table('eula',
        sa.Column('version', sa.String(), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=False),
        sa.PrimaryKeyConstraint('version'),
        schema='reports'
    )

    # Create Users table
    op.create_table('users',
        sa.Column('user_id', postgresql.UUID(), nullable=False),
        sa.Column('image', sa.Text(), nullable=True),
        sa.Column('name', sa.Text(), nullable=True),
        sa.Column('credits', sa.BigInteger(), nullable=True),
        sa.Column('email', sa.Text(), nullable=True),
        sa.Column('last_login', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=True),
        sa.Column('report_types', postgresql.JSONB(astext_type=sa.Text()), nullable=False, server_default='[]'),
        sa.Column('salt', sa.Text(), nullable=True),
        sa.Column('verifier', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('role', sa.Text(), nullable=False, server_default='standalone'),
        sa.Column('api_key', sa.String(), nullable=True),
        sa.Column('credits_monthly', sa.BigInteger(), nullable=True),
        sa.Column('next_reset_credits', sa.DateTime(timezone=True), nullable=True),
        sa.Column('date_accept_terms', sa.DateTime(timezone=True), nullable=True),
        sa.Column('eula_version', sa.String(), nullable=True),
        sa.ForeignKeyConstraint(['eula_version'], ['reports.eula.version']),
        sa.PrimaryKeyConstraint('user_id'),
        schema='reports'
    )

    # Create indexes for users table
    op.create_index('idx_users_api_key', 'users', ['api_key'], unique=False, schema='reports')
    op.create_index('idx_users_report_types', 'users', ['report_types'], unique=False, schema='reports', postgresql_using='gin')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False, schema='reports')

    # Create Organizations table
    op.create_table('organizations',
        sa.Column('organization_id', postgresql.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=False),
        sa.Column('name', sa.Text(), nullable=False),
        sa.Column('image_logo', sa.Text(), nullable=True),
        sa.Column('api_key', sa.Text(), nullable=True),
        sa.Column('status_organization', postgresql.ENUM('ativo', 'inativo', name='organization_status', create_type=True), nullable=False, server_default='ativo'),
        sa.Column('print_snap_logo', sa.Boolean(), nullable=True, server_default='true'),
        sa.PrimaryKeyConstraint('organization_id'),
        schema='reports'
    )

    # Create indexes for organizations table
    op.create_index('idx_organizations_api_key', 'organizations', ['api_key'], unique=False, schema='reports')
    op.create_index('idx_organizations_status', 'organizations', ['status_organization'], unique=False, schema='reports')

    # Create Folders table
    op.create_table('folders',
        sa.Column('folder_id', postgresql.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('organization_id', postgresql.UUID(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=False),
        sa.Column('folder_name', sa.Text(), nullable=False),
        sa.Column('folder_description', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['organization_id'], ['reports.organizations.organization_id']),
        sa.PrimaryKeyConstraint('folder_id'),
        schema='reports'
    )

    # Create OrganizationUsers table (junction table)
    op.create_table('organization_users',
        sa.Column('user_id', postgresql.UUID(), nullable=False),
        sa.Column('organization_id', postgresql.UUID(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=False),
        sa.Column('role', sa.Text(), nullable=False, server_default='member'),
        sa.ForeignKeyConstraint(['organization_id'], ['reports.organizations.organization_id']),
        sa.ForeignKeyConstraint(['user_id'], ['reports.users.user_id']),
        sa.PrimaryKeyConstraint('user_id', 'organization_id'),
        schema='reports'
    )

    # Create UserReportLedger table
    op.create_table('user_report_ledger',
        sa.Column('user_reports_id', postgresql.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('user_id', postgresql.UUID(), nullable=False),
        sa.Column('organization_id', postgresql.UUID(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=False),
        sa.Column('report_type', sa.Text(), nullable=True),
        sa.Column('report_status', sa.Text(), nullable=True),
        sa.Column('credits_used', sa.BigInteger(), nullable=True),
        sa.ForeignKeyConstraint(['organization_id'], ['reports.organizations.organization_id']),
        sa.ForeignKeyConstraint(['user_id'], ['reports.users.user_id']),
        sa.PrimaryKeyConstraint('user_reports_id'),
        sa.UniqueConstraint('user_reports_id', name='user_reports_id_unique'),
        schema='reports'
    )

    # Create Invites table
    op.create_table('invite',
        sa.Column('invite_id', postgresql.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('user_sender_id', postgresql.UUID(), nullable=False),
        sa.Column('organization_id', postgresql.UUID(), nullable=False),
        sa.Column('status_invite', postgresql.ENUM('enviado', 'negado', 'aceito', 'cancelado', 'desvinculado', name='invite_status', create_type=True), nullable=False, server_default='enviado'),
        sa.Column('type_invite', postgresql.ENUM('investigador', 'administrador', name='invite_type', create_type=True), nullable=False, server_default='investigador'),
        sa.Column('report_types', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('credits_sent', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('sent_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('email_invited', sa.Text(), nullable=False),
        sa.ForeignKeyConstraint(['organization_id'], ['reports.organizations.organization_id']),
        sa.ForeignKeyConstraint(['user_sender_id'], ['reports.users.user_id']),
        sa.PrimaryKeyConstraint('invite_id'),
        schema='reports'
    )

    # Create indexes for invite table
    op.create_index('idx_invite_user_sender_id', 'invite', ['user_sender_id'], unique=False, schema='reports')
    op.create_index('idx_invite_organization_id', 'invite', ['organization_id'], unique=False, schema='reports')
    op.create_index('idx_invite_status_invite', 'invite', ['status_invite'], unique=False, schema='reports')
    op.create_index('idx_invite_type_invite', 'invite', ['type_invite'], unique=False, schema='reports')
    op.create_index('idx_invite_sent_at', 'invite', ['sent_at'], unique=False, schema='reports')
    op.create_index('idx_invite_email_invited', 'invite', ['email_invited'], unique=False, schema='reports')
    op.create_index('idx_invite_report_types', 'invite', ['report_types'], unique=False, schema='reports', postgresql_using='gin')

    # Create UserReports table
    op.create_table('user_reports',
        sa.Column('user_id', postgresql.UUID(), nullable=False),
        sa.Column('user_reports_id', postgresql.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('organization_id', postgresql.UUID(), nullable=True),
        sa.Column('folder_id', postgresql.UUID(), nullable=True),
        sa.Column('data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=False),
        sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=False),
        sa.Column('report_name', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('report_status', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('report_type', sa.Text(), nullable=True),
        sa.Column('subject_name', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('subject_mother_name', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('subject_age', sa.Date(), nullable=True),
        sa.Column('subject_sex', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('subject_person_count', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('subject_company_count', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('report_search_args', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('subject_name_related', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('number_of_relations', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['folder_id'], ['reports.folders.folder_id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['organization_id'], ['reports.organizations.organization_id']),
        sa.ForeignKeyConstraint(['user_id'], ['reports.users.user_id']),
        sa.ForeignKeyConstraint(['user_reports_id'], ['reports.user_report_ledger.user_reports_id']),
        sa.PrimaryKeyConstraint('user_id', 'user_reports_id'),
        sa.UniqueConstraint('user_reports_id', name='user_reports_id_unique'),
        schema='reports'
    )

    # Create comprehensive indexes for user_reports
    op.create_index('idx_user_reports_report_status_status_report', 'user_reports', ['report_status'], unique=False, schema='reports', postgresql_using='gin')
    op.create_index('idx_user_reports_report_type', 'user_reports', ['report_type'], unique=False, schema='reports')
    op.create_index('ix_user_reports_user_type', 'user_reports', ['user_id', 'report_type'], unique=False, schema='reports')
    op.create_index('ix_user_reports_user_org_type', 'user_reports', ['user_id', 'organization_id', 'report_type'], unique=False, schema='reports')
    op.create_index('ix_user_reports_user_folder', 'user_reports', ['user_id', 'folder_id'], unique=False, schema='reports')
    op.create_index('ix_user_reports_user_org', 'user_reports', ['user_id', 'organization_id'], unique=False, schema='reports')
    op.create_index('ix_user_reports_user_created_at_desc', 'user_reports', ['user_id', 'created_at'], unique=False, schema='reports')
    op.create_index('ix_user_reports_user_modified_at_desc', 'user_reports', ['user_id', 'modified_at'], unique=False, schema='reports')
    op.create_index('ix_user_reports_user_folder_created_at_desc', 'user_reports', ['user_id', 'folder_id', 'created_at'], unique=False, schema='reports')
    op.create_index('ix_user_reports_user_folder_modified_at_desc', 'user_reports', ['user_id', 'folder_id', 'modified_at'], unique=False, schema='reports')
    op.create_index('ix_user_reports_user_org_created_at_desc', 'user_reports', ['user_id', 'organization_id', 'created_at'], unique=False, schema='reports')
    op.create_index('ix_user_reports_user_org_modified_at_desc', 'user_reports', ['user_id', 'organization_id', 'modified_at'], unique=False, schema='reports')
    op.create_index('ix_user_reports_user_org_folder_created_desc', 'user_reports', ['user_id', 'organization_id', 'folder_id', 'created_at'], unique=False, schema='reports')
    op.create_index('ix_user_reports_user_org_folder_modified_desc', 'user_reports', ['user_id', 'organization_id', 'folder_id', 'modified_at'], unique=False, schema='reports')

    # Create ReportExecutions table
    op.create_table('report_executions',
        sa.Column('execution_id', postgresql.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('user_reports_id', postgresql.UUID(), nullable=False),
        sa.Column('user_id', postgresql.UUID(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=False),
        sa.Column('status', sa.Text(), nullable=False),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('execution_time_ms', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['reports.users.user_id']),
        sa.ForeignKeyConstraint(['user_reports_id'], ['reports.user_report_ledger.user_reports_id']),
        sa.PrimaryKeyConstraint('execution_id'),
        schema='reports'
    )

    # Create UserColumnsHmac table
    op.create_table('user_columns_hmac',
        sa.Column('user_columns_hmac_id', postgresql.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('user_reports_id', postgresql.UUID(), nullable=False),
        sa.Column('column_name', sa.Text(), nullable=False),
        sa.Column('hmac_value', sa.Text(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=False),
        sa.ForeignKeyConstraint(['user_reports_id'], ['reports.user_reports.user_reports_id']),
        sa.PrimaryKeyConstraint('user_columns_hmac_id'),
        schema='reports'
    )

    # Create FolderHmac table
    op.create_table('folder_hmac',
        sa.Column('folder_hmac_id', postgresql.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('folder_id', postgresql.UUID(), nullable=False),
        sa.Column('hmac_value', sa.Text(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\'UTC\', now())'), nullable=False),
        sa.ForeignKeyConstraint(['folder_id'], ['reports.folders.folder_id']),
        sa.PrimaryKeyConstraint('folder_hmac_id'),
        schema='reports'
    )


def downgrade() -> None:
    # Drop tables in reverse dependency order
    op.drop_table('folder_hmac', schema='reports')
    op.drop_table('user_columns_hmac', schema='reports')
    op.drop_table('report_executions', schema='reports')
    op.drop_table('user_reports', schema='reports')
    op.drop_table('invite', schema='reports')
    op.drop_table('user_report_ledger', schema='reports')
    op.drop_table('organization_users', schema='reports')
    op.drop_table('folders', schema='reports')
    op.drop_table('organizations', schema='reports')
    op.drop_table('users', schema='reports')
    op.drop_table('eula', schema='reports')

    # Drop custom enum
    op.execute('DROP TYPE IF EXISTS reports.organization_status CASCADE')

    # Drop reports schema if empty (will fail if other objects exist, which is safe)
    op.execute('DROP SCHEMA IF EXISTS reports')