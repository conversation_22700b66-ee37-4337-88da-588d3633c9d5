import React, { useCallback } from "react";
import {
  useReportActions,
  useReportMode,
  useReportSections,
  useIsTrashEnabled,
  useIsPrintEnabled,
} from "../../context/ReportContext";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { ReactiveModalProvider } from "./ProcessoModalComponents";
import { translatePropToLabel } from "../../helpers";
import { useReportActionsWithAutoSave } from "~/hooks/useReportActionsWithAutoSave";

import { Accordion } from '../components/CustomAccordion';
import { Badge } from '../components/base/badge';
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { ActionButton } from "../components/ActionButton";
import {
  useModalControl,
  StandardList,
  ListItem,
  Button,
  ModalClose,
  Text
} from "@snap/design-system";
import { X } from 'lucide-react';
import { Processo } from "../../model/Processo";
import { MAX_PAGE_HEIGHT } from "../../config/constants";

export function useRenderProcessos(
  sectionTitle: string
): ArrayRenderStrategy<Processo> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";
  const { open } = useModalControl();
  const sections = useReportSections();
  const isTrashEnabled = useIsTrashEnabled();
  const isPrintEnabled = useIsPrintEnabled();
  const autoSaveActions = useReportActionsWithAutoSave();
  const mainSection = sections.find((s) => s.title === sectionTitle && !s.subsection);
  const subSections = sections.filter((s) => s.title === sectionTitle && !!s.subsection);

  const testFieldDeleted = (field: any): boolean => {
    return field?.is_deleted === true;
  };

  const testDetalhesDeleted = (detalhes: Record<string, any>): boolean => {
    if (!detalhes || Object.keys(detalhes).length === 0) return true;
    return Object.values(detalhes).every((field: any) => testFieldDeleted(field));
  };

  const testParticipantArrayDeleted = (participants: any[]): boolean => {
    if (!Array.isArray(participants) || participants.length === 0) return true;
    return participants.every((participant: any) => {
      if (participant.is_deleted) return true;
      if (participant.value && typeof participant.value === 'object') {
        return Object.values(participant.value).every((field: any) => testFieldDeleted(field));
      }
      return false;
    });
  };

  const testEntryDeleted = (entry: any): boolean => {
    if (entry.numero && !testFieldDeleted(entry.numero)) return false;

    if (entry.detalhes && !testDetalhesDeleted(entry.detalhes)) return false;

    if (entry.movimentações && !testFieldDeleted(entry.movimentações)) return false;
    const participantKeys = Object.keys(entry).filter(key =>
      key !== 'numero' && key !== 'detalhes' && key !== 'movimentações' && Array.isArray(entry[key])
    );

    for (const key of participantKeys) {
      if (!testParticipantArrayDeleted(entry[key])) return false;
    }

    return true;
  };

  const testEntryHasDeletedFields = (entry: any): boolean => {
    if (entry.numero && testFieldDeleted(entry.numero)) return true;

    if (entry.detalhes && Object.values(entry.detalhes).some((d: any) => testFieldDeleted(d))) return true;

    if (entry.movimentações && testFieldDeleted(entry.movimentações)) return true;
    const participantKeys = Object.keys(entry).filter(key =>
      key !== 'numero' && key !== 'detalhes' && key !== 'movimentações' && Array.isArray(entry[key])
    );

    for (const key of participantKeys) {
      const participants = entry[key] as any[];
      if (participants.some(p => p.is_deleted || (p.value && Object.values(p.value).some((f: any) => testFieldDeleted(f))))) {
        return true;
      }
    }

    return false;
  };

  const testSectionDeleted = (section: any): boolean => {
    if (!Array.isArray(section.data)) return true;

    if (!section.subsection) {
      return section.data.every((entry: any) => testEntryDeleted(entry));
    }

    return section.is_deleted === true;
  };

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;
    return section.data.reduce((count: number, entry: any) => {
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  };


  const openProcessoDetails = useCallback((proc: Processo) => {
    const fullProcessoData = mainSection?.data.find((r: any) => r.numero.value === proc.numero.value);
    if (!fullProcessoData) return;

    const numeroValue = proc.numero.value;
    console.log('openProcessoDetails called for numeroValue:', numeroValue);
    console.log('openProcessoDetails - fullProcessoData:', fullProcessoData);

    open({
      modal: () => ({
        title: (
          <div className="flex items-center gap-2">
            <Text variant="label-lg" className="uppercase">Processo</Text>
            {isTrash && (
              <Badge variant="outline">
                <Text className="uppercase">lixeira</Text>
              </Badge>
            )}
          </div>
        ),
        icon: (
          <ModalClose>
            <X className="cursor-pointer" />
          </ModalClose>
        ),
        content: (
          <ReactiveModalProvider
            numeroValue={numeroValue}
            sectionTitle={sectionTitle}
            isTrashEnabled={isTrashEnabled}
            isPrintEnabled={isPrintEnabled}
            autoSaveActions={autoSaveActions}
            mode={mode}
            testEntryDeleted={testEntryDeleted}
            testSectionDeleted={testSectionDeleted}
            calculateDataCount={calculateDataCount}
          />
        ),
        footer: null,
      }),
      config: {
        content: { className: "max-w-5xl" },
        contentWrapper: {
          className: `overflow-y-auto overflow-x-hidden [scrollbar-gutter:stable] ${MAX_PAGE_HEIGHT}`
        }
      },
    });
  }, [open, mainSection, autoSaveActions]);

  const deleteSubsectionEntries = useCallback((subsectionName: string) => {
    const updater = actions.updateSubsectionWithMainSection;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      subsectionName,
      'numero',
      entry => {
        if (entry.numero) {
          entry.numero.is_deleted = targetDeletedState;
        }

        if (entry.detalhes) {
          Object.keys(entry.detalhes).forEach(key => {
            if (entry.detalhes[key]) {
              entry.detalhes[key].is_deleted = targetDeletedState;
            }
          });
        }

        if (entry.movimentações) {
          entry.movimentações.is_deleted = targetDeletedState;
        }

        const participantKeys = Object.keys(entry).filter(key =>
          key !== 'numero' && key !== 'detalhes' && key !== 'movimentações' && Array.isArray(entry[key])
        );

        participantKeys.forEach(key => {
          if (Array.isArray(entry[key])) {
            entry[key].forEach((participant: any) => {
              participant.is_deleted = targetDeletedState;
              if (participant.value && typeof participant.value === 'object') {
                Object.keys(participant.value).forEach(fieldKey => {
                  if (participant.value[fieldKey]) {
                    participant.value[fieldKey].is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        });
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  }, [actions, mode, sectionTitle]);

  const deleteIndividualProcesso = useCallback((numeroValue: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        if (entry.numero?.value === numeroValue) {
          if (entry.numero) {
            entry.numero.is_deleted = targetDeletedState;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount,
      true,
      {
        matchingProp: 'numero',
        updaterFn: entry => {
          if (entry.numero?.value === numeroValue) {
            if (entry.numero) {
              entry.numero.is_deleted = targetDeletedState;
            }
            if (entry.detalhes) {
              Object.keys(entry.detalhes).forEach(key => {
                if (entry.detalhes[key]) {
                  entry.detalhes[key].is_deleted = targetDeletedState;
                }
              });
            }
            if (entry.movimentações) {
              entry.movimentações.is_deleted = targetDeletedState;
            }
            const participantKeys = Object.keys(entry).filter(key =>
              key !== 'numero' && key !== 'detalhes' && key !== 'movimentações' && Array.isArray(entry[key])
            );
            participantKeys.forEach(key => {
              if (Array.isArray(entry[key])) {
                entry[key].forEach((participant: any) => {
                  participant.is_deleted = targetDeletedState;
                  if (participant.value && typeof participant.value === 'object') {
                    Object.keys(participant.value).forEach(fieldKey => {
                      if (participant.value[fieldKey]) {
                        participant.value[fieldKey].is_deleted = targetDeletedState;
                      }
                    });
                  }
                });
              }
            });
          }
        }
      }
    );
  }, [actions, mode, sectionTitle]);

  const validateKeys = (keys: Array<keyof Processo>): boolean => {
    return keys.some((campo) => campo === 'numero' || campo === 'detalhes' || campo === 'movimentações');
  };

  const formatByKey: Record<string, any> = {};

  const render = (): React.ReactElement[] => {
    if (!mainSection) {
      return [<div key="no-data">Nenhum dado encontrado</div>];
    }

    if (!subSections.length) {
      return [<div key="no-data">Nenhum dado encontrado</div>];
    }

    const filteredSubSections = subSections.filter(sec => {

      if (isTrash) {
        const shouldShow = sec.is_deleted || sec.data.some(proc => {
          const fullProcesso = mainSection.data.find(p => p.numero.value === proc.numero.value);
          const hasDeletedFields = fullProcesso && testEntryHasDeletedFields(fullProcesso);
          return hasDeletedFields;
        });
        return shouldShow;
      } else {
        const shouldShow = !sec.is_deleted && sec.data.some(proc => {
          const fullProcesso = mainSection.data.find(p => p.numero.value === proc.numero.value);
          const isNotCompletelyDeleted = fullProcesso && !testEntryDeleted(fullProcesso);
          return isNotCompletelyDeleted;
        });
        return shouldShow;
      }
    });

    if (filteredSubSections.length === 0) {
      if (isTrash && mainSection.data.some(proc => testEntryHasDeletedFields(proc))) {
        return [<div key="no-data">Processos deletados encontrados, mas nenhuma subseção visível</div>];
      }
      return [<div key="no-data">Nenhum dado encontrado</div>];
    }

    return [
      <Accordion type="multiple" key="processos-accordion">
        {filteredSubSections.map((sec, i) => {
          const filteredProcessos = sec.data.filter(proc => {
            const fullProcesso = mainSection.data.find(p => p.numero.value === proc.numero.value);
            if (!fullProcesso) return false;

            if (isTrash) {
              return testEntryHasDeletedFields(fullProcesso);
            } else {
              return !testEntryDeleted(fullProcesso);
            }
          });

          const dataCount = filteredProcessos.length;

          return (
            <Accordion.Item key={`${sec.subsection}-${i}`} value={sec.subsection} className='border-b-neutral-100 border-b-1'>
              <div className="group">
                <Accordion.Trigger className="px-0 py-0 pr-4 mb-0 no-underline [&_div:first-child]:border-neutral-100 cursor-pointer bg-transparent">
                  <div className="flex items-center gap-4 w-full">
                    <h3 className="font-mono text-lg uppercase">{translatePropToLabel(sec.subsection)}</h3>
                    <div className="flex items-center gap-2">
                      {!isTrash && (
                        <Badge variant="secondary" className="rounded-2xl px-4 py-0.5 bg-gray-400">
                          {dataCount}
                        </Badge>
                      )}
                    </div>
                  </div>
                  {isTrashEnabled && (
                    <ActionButton
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteSubsectionEntries(sec.subsection);
                      }}
                      title={isTrash ? "Restaurar subseção" : "Deletar subseção"}
                      isTrashMode={isTrash}
                      size="lg"
                    />
                  )}
                </Accordion.Trigger>
              </div>
              <Accordion.Content className="px-8 py-4 border-t">
                <StandardList withSeparator>
                  {filteredProcessos.map(proc => (
                    <ListItem key={proc.numero.value} className={`w-full flex justify-between gap-2 ${isTrash ? 'flex-col items-start' : ''} group`}>
                      <div className="flex items-center gap-2">
                        <ReportsCustomLabel
                          label={`Número: ${proc.numero.value}`}
                          colorClass="bg-primary"
                        />
                      </div>
                      <div className="flex items-center gap-2">
                        <Button onClick={() => openProcessoDetails(proc as Processo)}>
                          Ver detalhes
                        </Button>
                        {
                          isTrashEnabled && (
                            <ActionButton
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteIndividualProcesso(proc.numero.value);
                              }}
                              title={isTrash ? "Restaurar processo" : "Deletar processo"}
                              isTrashMode={isTrash}
                              size="lg"
                              className="w-6 h-6"
                            />
                          )
                        }

                      </div>
                    </ListItem>
                  ))}
                </StandardList>
              </Accordion.Content>
            </Accordion.Item>
          );
        })}
      </Accordion>
    ];
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        if (entry.numero) {
          entry.numero.is_deleted = targetDeletedState;
        }

        if (entry.detalhes) {
          Object.keys(entry.detalhes).forEach(key => {
            if (entry.detalhes[key]) {
              entry.detalhes[key].is_deleted = targetDeletedState;
            }
          });
        }

        if (entry.movimentações) {
          entry.movimentações.is_deleted = targetDeletedState;
        }

        const participantKeys = Object.keys(entry).filter(key =>
          key !== 'numero' && key !== 'detalhes' && key !== 'movimentações' && Array.isArray(entry[key])
        );

        participantKeys.forEach(key => {
          if (Array.isArray(entry[key])) {
            entry[key].forEach((participant: any) => {
              participant.is_deleted = targetDeletedState;
              if (participant.value && typeof participant.value === 'object') {
                Object.keys(participant.value).forEach(fieldKey => {
                  if (participant.value[fieldKey]) {
                    participant.value[fieldKey].is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        });
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount,
      true
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
