import { Browser } from "puppeteer";

export type ReportType = "cpf" | "cnpj" | "email" | "telefone";

export interface ReportSection {
  title: string;
  subtitle: string;
  subsection: string;
  source: string[];
  data_count: number;
  is_deleted?: boolean;
  data: Array<Record<string, any>>;
}

export interface ReportMetadata {
  [key: string]: any;
  report_id: string;
  report_status: string;
  report_type: string;
  report_search_args: object;
  report_name: string;
  creation_at: string;
  modified_at: string;
  subject_name: string;
  subject_mother_name: string;
  subject_age: number | null;
  subject_sex: string;
}

export interface IGenerateReportsPDFRequestInput {
  sections: ReportSection[];
  metadata: ReportMetadata;
  profile_image?: string;
  should_print_snap_logo?: boolean;
  organization_logo?: string;
}

export interface IReportsPdfHeaderProps {
  reportType: string;
  searchValue: string;
  title: string;
  organization_logo?: string;
  should_print_snap_logo?: boolean;
}

export interface ValueWithSource<T = any> {
  value: T;
  label: string;
  source: string[];
  is_deleted?: boolean;
}

export interface Dictionary {
  [key: string]: string;
}

export interface IGenerateReportsPDFInput {
  browserRef: Browser;
  header: string;
  content: string;
  footer: string;
  hasOrganizationLogo?: boolean;
  should_print_snap_logo?: boolean;
}

export type IGenerateReportsHTMLOutput = Omit<IGenerateReportsPDFInput, 'browserRef'>;
export type IGenerateReportsHTMLInput = IGenerateReportsPDFRequestInput;
export type IReportsPDFControllerBody = {
  browserRef: Browser
} & IGenerateReportsPDFRequestInput;

export interface ServerConfig {
  port: number;
  host: string;
}

export interface IGenerateCoverPDFInput {
  metadata: ReportMetadata;
  should_print_snap_logo?: boolean;
  organization_logo?: string;
}

export type PDFGeneratorFunction = (params: IGenerateReportsHTMLOutput) => Promise<Uint8Array<ArrayBufferLike>>;
export type CoverGeneratorFunction = (params: IGenerateCoverPDFInput) => Promise<Uint8Array<ArrayBufferLike>>;