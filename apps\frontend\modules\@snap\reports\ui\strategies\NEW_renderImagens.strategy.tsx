import React, { useCallback, useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import {
  Imagem,
  ImagensSection,
  TestFunction,
  SectionTestFunction,
  CalculateFunction,
  UpdaterFunction
} from "../../model/Imagens";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { ValidatedImage } from "../components/ValidateImage";
import { useScreenSize } from "../../hooks/useScreenSize";
import { useNestedRender } from "./NestedRenderContext";
import { formatFieldValue, parseValue, translatePropToLabel } from "../../helpers";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { GridItem } from "@snap/design-system";
import { ValueWithSource } from "../../model/ValueWithSource";

export function useRenderImagens(sectionTitle: string): ArrayRenderStrategy<Imagem> {
  const isXLScreen = useScreenSize(1920);
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const shouldIncludeBlock = (detalhe: ValueWithSource<any>): boolean => {
    const vals = Object.values(detalhe.value || {}) as ValueWithSource[];
    return isTrash
      ? vals.some((v) => v.is_deleted === true)
      : vals.some((v) => v.is_deleted === false);
  };

  const testEntryDeleted: TestFunction = useCallback((entry: Imagem): boolean => {
    return entry.detalhes?.every((d: ValueWithSource<any>) =>
      Object.values(d.value).every((v: any) => (v as ValueWithSource).is_deleted === true)
    ) ?? false;
  }, []);

  const testSectionDeleted: SectionTestFunction = useCallback((section: ImagensSection): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted),
    [testEntryDeleted]);

  const calculateDataCount: CalculateFunction = useCallback((section: ImagensSection): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: Imagem) => {
      if (!entry.detalhes) return count;

      const nonDeletedBlocks = entry.detalhes.filter(
        (bloco: ValueWithSource<any>) => bloco.is_deleted !== true
      ).length;

      return count + nonDeletedBlocks;
    }, 0);
  }, []);

  const shouldShowEntry: TestFunction = useCallback((entry: Imagem): boolean => {
    if (!entry?.detalhes?.length) return false;

    // Filter detalhes based on whether they contain properties that should be shown
    const filteredDetalhes = entry.detalhes.filter((detalhe: ValueWithSource<any>) => {
      return shouldIncludeBlock(detalhe);
    });

    return filteredDetalhes.length > 0;
  }, [isTrash]);

  const updateEntries = useCallback((
    updater: UpdaterFunction,
    testDeleted: TestFunction = testEntryDeleted,
    testSectionDel: SectionTestFunction = testSectionDeleted,
    calcDataCount: CalculateFunction = calculateDataCount
  ) => {
    actions.updateSectionEntries?.(
      sectionTitle,
      updater,
      testDeleted as (entry: Record<string, any>) => boolean,
      testSectionDel as (section: Record<string, any>) => boolean,
      calcDataCount as (section: Record<string, any>) => number
    );
  }, [actions, sectionTitle, testEntryDeleted, testSectionDeleted, calculateDataCount]);

  const calculateNestedDataCount = useCallback((nestedSection: ImagensSection): number => {
    if (!Array.isArray(nestedSection.data)) return 0;
    return nestedSection.data.reduce((count: number, entry: Imagem) => {
      if (!entry.detalhes) return count;
      const nonDeletedBlocks = entry.detalhes.filter(
        (bloco: ValueWithSource<any>) => bloco.is_deleted !== true
      ).length;
      return count + nonDeletedBlocks;
    }, 0);
  }, []);

  function ImagemBlock({
    sectionTitle,
    entryIndex,
    origIdx,
    bloco,
    blockRenderIdx,
  }: {
    sectionTitle: string;
    entryIndex: number;
    origIdx: number;
    bloco: ValueWithSource<any>;
    blockRenderIdx: number;
  }) {
    const actions = useReportActions();
    const mode = useReportMode();
    const isTrash = mode === "trash";
    const nested = useNestedRender();

    const handleToggleBlock = () => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection) => {
          const entry = nestedSection.data?.[entryIndex];
          const detalhe = entry?.detalhes?.[origIdx];
          if (detalhe?.value) {
            const target = isTrash ? false : true;
            detalhe.is_deleted = target;
            (Object.values(detalhe.value) as ValueWithSource[]).forEach((campo) => {
              if (campo) campo.is_deleted = target;
            });
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((entry: Imagem, i?: number) => {
        if (i === entryIndex) {
          const detalhe = entry.detalhes?.[origIdx] as ValueWithSource;
          if (detalhe?.value) {
            const target = isTrash ? false : true;
            detalhe.is_deleted = target;
            (Object.values(detalhe.value) as ValueWithSource[]).forEach((campo) => {
              if (campo) campo.is_deleted = target;
            });
          }
        }
      });
    };

    const handleToggleField = (fieldKey: string) => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection) => {
          const entry = nestedSection.data?.[entryIndex];
          const detalhe = entry?.detalhes?.[origIdx];
          if (detalhe?.value && (detalhe.value as any)?.[fieldKey]) {
            (detalhe.value as any)[fieldKey].is_deleted = !(detalhe.value as any)[fieldKey].is_deleted;
            detalhe.is_deleted = Object.values(detalhe.value).every((c: any) => c.is_deleted === true);
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((entry: Imagem, i?: number) => {
        if (i === entryIndex) {
          const detalhe = entry.detalhes?.[origIdx];
          if (detalhe?.value && (detalhe.value as any)?.[fieldKey]) {
            (detalhe.value as any)[fieldKey].is_deleted = !(detalhe.value as any)[fieldKey].is_deleted;
            detalhe.is_deleted = Object.values(detalhe.value).every((c: any) => c.is_deleted === true);
          }
        }
      });
    };

    const entries = Object.entries(bloco.value)
      .filter(([_, v]: any) => (isTrash ? v.is_deleted : !v.is_deleted));
    const urlEntry = entries.find(([key]: any) => key === 'url') as any;
    const otherEntries = entries.filter(([key]: any) => key !== 'url');
    const isUrlEntryVisible = urlEntry && (isTrash ? urlEntry[1].is_deleted : !urlEntry[1].is_deleted);

    return (
      <GridItem cols={1}>
        <CustomGridItem cols={1} className="py-1" onToggleField={handleToggleBlock}>
          <ReportsCustomLabel label={`IMAGEM ${isTrash ? "" : blockRenderIdx + 1}`} colorClass="bg-primary" />
        </CustomGridItem>
        <div className="pl-5">



          {urlEntry && (
            <CustomGridItem key={`img-${origIdx}-url`} cols={1} className="py-1" onToggleField={() => handleToggleField('url')}>
              <CustomReadOnlyInputField
                label={translatePropToLabel((urlEntry[1] as any).label || 'url').toUpperCase()}
                colorClass="bg-border"
                icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                isFirstLabelList={true}
                value={parseValue(formatFieldValue((urlEntry[1] as any).value) || "")}
                tooltip={renderSourceTooltip((urlEntry[1] as any).source)}
              />
              {/* imagem sempre no final */}
              {(urlEntry[1] as any).value && (
                <ValidatedImage
                  src={String((urlEntry[1] as any).value)}
                  alt={`Imagem ${blockRenderIdx + 1}`}
                  className="w-full max-w-full h-60 mx-auto mt-2 bg-background/40 rounded-sm"
                />
              )}
            </CustomGridItem>
          )}

          {otherEntries.map(([fieldKey, fieldValue]: any, index) => (
            <CustomGridItem key={`img-${origIdx}-${fieldKey}`} cols={1} className="py-1" onToggleField={() => handleToggleField(fieldKey)}>
              <CustomReadOnlyInputField
                label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                colorClass="bg-border"
                icon={(!isUrlEntryVisible && index === 0) ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                isFirstLabelList={!isUrlEntryVisible && index === 0}
                value={parseValue(formatFieldValue(fieldValue.value || ""))}
                tooltip={renderSourceTooltip(fieldValue.source)}
              />
            </CustomGridItem>
          ))}
        </div>
      </GridItem>
    );
  }

  const DetalhesBlock = React.memo(({
    entry,
    index,
    sectionTitle
  }: {
    entry: Imagem;
    index: number;
    sectionTitle: string;
  }) => {
    const nested = useNestedRender();

    if (!entry?.detalhes?.length) return null;

    // Filter detalhes based on whether they contain properties that should be shown
    const filteredDetalhes = entry.detalhes
      .map((d, idx) => ({ bloco: d, idx }))
      .filter(({ bloco }) => shouldIncludeBlock(bloco));

    if (filteredDetalhes.length === 0) return null;

    return (
      <CustomGridContainer cols={isXLScreen ? 4 : 2}>
        {filteredDetalhes.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
          <ImagemBlock
            key={`img-${origIdx}`}
            sectionTitle={sectionTitle}
            entryIndex={index}
            origIdx={origIdx}
            bloco={bloco}
            blockRenderIdx={blockRenderIdx}
          />
        ))}
      </CustomGridContainer>
    );
  });

  const formatByKey: Record<
    string,
    (entry?: Imagem, index?: number) => React.ReactElement | null
  > = {
    detalhes: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <DetalhesBlock entry={entry} index={index} sectionTitle={sectionTitle} />;
    },
  };

  const validateKeys = (keys: Array<keyof Imagem>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: Imagem, index?: number): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof Imagem>;

    if (!validateKeys(keys)) {
      console.warn(`[Seção ${sectionTitle}] Chaves inválidas:`, keys);
    }

    const orderedKeys: Array<keyof Imagem> = [
      'detalhes'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    const elements: React.ReactElement[] = [];

    filteredKeys.forEach((chave) => {
      const element = formatByKey[chave]?.(entry, index);
      if (element) elements.push(element);
    });

    return elements;
  };

  const render = (dataArray: Imagem[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn(`[Seção ${sectionTitle}] esperava array, mas recebeu:`, typeof dataArray);
      return [];
    }

    const filteredData = dataArray
      .map((entry, originalIndex) => ({ entry, originalIndex }))
      .filter(({ entry }) => shouldShowEntry(entry));

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach(({ entry, originalIndex }, filterIndex) => {
      const elements = renderSingleItem(entry, originalIndex);

      if (filteredData.length > 1) {
        const isLastItem = filterIndex === filteredData.length - 1;
        allElements.push(
          <div
            key={`imagem-${originalIndex}`}
            className={`relative mb-8 pb-1 ${isLastItem ? '!mb-0 !pb-0' : ''}`}
          >
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      ((entry: Imagem) => {
        // Percorre todos os detalhes e marca todas as propriedades como deletadas/restauradas
        if (entry.detalhes) {
          entry.detalhes.forEach((detalhe: any) => {
            if (detalhe?.value) {
              detalhe.is_deleted = targetDeletedState;
              Object.values(detalhe.value).forEach((campo: any) => {
                if (campo) {
                  campo.is_deleted = targetDeletedState;
                }
              });
            }
          });
        }
      }) as UpdaterFunction,
      testEntryDeleted as (entry: Record<string, any>) => boolean,
      testSectionDeleted as (section: Record<string, any>) => boolean,
      calculateDataCount as (section: Record<string, any>) => number
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
    calculateDataCount,
  } as ArrayRenderStrategy<Imagem> & { calculateDataCount: typeof calculateDataCount };
}
