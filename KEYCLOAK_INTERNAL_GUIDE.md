# Keycloak Internal-Only Configuration Guide

## 🔒 **MAXIMUM SECURITY: Internal-Only Keycloak**

Your Keycloak is now configured to be **completely internal** with **zero external access**. This provides the highest level of security by eliminating any external attack surface.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────┐
│                    VM (Your Server)                 │
│                                                     │
│  ┌──────────────┐    ┌─────────────────────────────┐│
│  │   Frontend   │    │       Docker Network        ││
│  │   (NGINX)    │    │       (mystack-net)         ││
│  │              │    │                             ││
│  └──────────────┘    │  ┌─────────────────────────┐││
│         │             │  │      Keycloak           │││
│  ┌──────────────┐    │  │   (INTERNAL ONLY)      │││
│  │   Backend    │◄───┼──┤  http://keycloak:8080   │││
│  │     API      │    │  └─────────────────────────┘││
│  └──────────────┘    │                             ││
│         │             │  ┌─────────────────────────┐││
│  ┌──────────────┐    │  │     PostgreSQL          │││
│  │  Other Apps  │◄───┼──┤   (Database)            │││
│  └──────────────┘    │  └─────────────────────────┘││
│                       └─────────────────────────────┘│
└─────────────────────────────────────────────────────┘
         ▲
         │ SSH Access Only
         │ (for admin operations)
    ┌──────────┐
    │   You    │
    │ (Admin)  │
    └──────────┘
```

## 🚀 Deployment

### **Start Keycloak (Internal Mode)**

**Production:**
```bash
./scripts/deploy-keycloak-production.sh
```

**Development:**
```bash
./scripts/deploy-keycloak-development.sh
```

### **Test Internal Connectivity**
```bash
./scripts/keycloak-internal-test.sh
```

## 🔧 Administration Access

### **VM Admin Access**
```bash
# Get admin access info and credentials
./scripts/keycloak-admin-access.sh
```

### **SSH Tunnel for GUI Access (Development)**
From your local machine:
```bash
# Replace VM_IP with your server IP
ssh -L 8080:localhost:8080 -L 9000:localhost:9000 user@VM_IP

# Then access from browser:
# http://localhost:8080/admin/
```

### **Direct Container Access**
```bash
# Get container shell
docker exec -it $(docker-compose -f docker-compose-keycloak.yml ps -q keycloak) /bin/bash

# Inside container, access local endpoints:
curl http://localhost:8080/admin/
curl http://localhost:9000/health
```

## 🔗 Application Integration

### **Backend Applications (Same Docker Network)**

```bash
# Environment variables for your backend
KEYCLOAK_URL=http://keycloak:8080
KEYCLOAK_REALM_URL=http://keycloak:8080/realms/YOUR_REALM
KEYCLOAK_AUTH_URL=http://keycloak:8080/realms/YOUR_REALM/protocol/openid-connect
```

**Example API Integration:**
```javascript
// Backend service configuration
const keycloakConfig = {
  baseUrl: 'http://keycloak:8080',
  realm: 'your-realm',
  clientId: 'your-client',
  // No external URLs needed!
};
```

### **Frontend Applications**

Your frontend **never directly contacts Keycloak**. All authentication flows through your backend:

```javascript
// Frontend flow:
// 1. Frontend → Backend API → Keycloak (internal)
// 2. Backend validates tokens with Keycloak (internal)  
// 3. Backend returns data to Frontend

// Example frontend auth flow:
const authFlow = {
  login: () => {
    // Frontend redirects to your backend auth endpoint
    window.location.href = '/api/auth/login';
  },
  
  validateToken: async (token) => {
    // Backend validates with Keycloak internally
    const response = await fetch('/api/auth/validate', {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.json();
  }
};
```

## 🔒 Security Benefits

### **Zero External Attack Surface**
- ✅ No external ports exposed
- ✅ No external network access
- ✅ No reverse proxy configuration needed
- ✅ No SSL certificate management for auth
- ✅ No CORS configuration needed

### **Defense in Depth**
- ✅ Docker network isolation
- ✅ VM-level access control
- ✅ Container-to-container encryption possible
- ✅ Admin access requires VM SSH
- ✅ All authentication internal

### **Compliance & Audit**
- ✅ Air-gapped authentication service
- ✅ All access logged and traceable
- ✅ No external data transmission
- ✅ Internal-only data flows

## 📊 Monitoring & Health Checks

### **Internal Health Monitoring**
```bash
# From VM or container in same network
curl http://keycloak:8080/realms/master
curl http://keycloak:9000/health/ready
curl http://keycloak:9000/metrics
```

### **Application Health Checks**
Your applications can health-check Keycloak internally:
```bash
# Add to your backend health check
curl -f http://keycloak:8080/realms/YOUR_REALM/.well-known/openid_configuration
```

## 🚨 Troubleshooting

### **Keycloak Not Accessible**
```bash
# Check container status
docker-compose -f docker-compose-keycloak.yml ps

# Check logs
docker-compose -f docker-compose-keycloak.yml logs keycloak

# Test internal connectivity
./scripts/keycloak-internal-test.sh
```

### **Application Can't Connect**
```bash
# Verify Docker network
docker network ls | grep mystack-net

# Test from your app container
docker exec -it YOUR_APP_CONTAINER curl http://keycloak:8080/realms/master
```

### **Admin Access Issues**
```bash
# Get admin access info
./scripts/keycloak-admin-access.sh

# Check credentials
docker exec $(docker-compose -f docker-compose-keycloak.yml ps -q keycloak) printenv | grep KEYCLOAK_ADMIN
```

## 💡 Development Tips

### **Local Development with Internal Keycloak**
1. Deploy Keycloak internally: `./scripts/deploy-keycloak-development.sh`
2. Access via SSH tunnel for admin work
3. Configure your local backend to connect to `http://keycloak:8080`
4. Test connectivity: `./scripts/keycloak-internal-test.sh`

### **Realm Configuration**
```bash
# Export realm configuration
docker exec $(docker-compose -f docker-compose-keycloak.yml ps -q keycloak) \
  /opt/keycloak/bin/kc.sh export --file /tmp/realm-export.json --realm YOUR_REALM

# Copy to VM
docker cp CONTAINER_ID:/tmp/realm-export.json ./realm-backup.json
```

## 🔄 Migration from External Setup

If migrating from external Keycloak:

1. **Update Application URLs:**
   - Change from `https://auth.domain.com` to `http://keycloak:8080`
   - Update all backend service configurations

2. **Remove External Configurations:**
   - Remove reverse proxy rules
   - Remove SSL certificates for auth domain
   - Remove external DNS entries

3. **Test Internal Connectivity:**
   - Run `./scripts/keycloak-internal-test.sh`
   - Verify all applications can reach Keycloak internally

## 📚 Additional Resources

- **Security:** Zero external exposure
- **Performance:** Direct container-to-container communication
- **Maintenance:** VM SSH access for admin operations
- **Scalability:** Internal load balancing possible
- **Backup:** Standard Docker volume backup procedures

---

## 🎯 **Summary**

Your Keycloak is now **completely internal and secure**:
- **No external access** - Maximum security
- **VM-only admin** - Controlled access
- **Internal communication** - Fast and secure
- **Zero attack surface** - External threats eliminated

Perfect for production environments requiring maximum security! 🔒
