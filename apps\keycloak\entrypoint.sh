#!/bin/bash
set -e

echo "🚀 Keycloak Entrypoint Starting..."
echo "📊 Environment: ${KC_ENV:-development}"

# --- Security checks ---
if [ -z "$KC_DB_URL" ]; then
    echo "❌ ERROR: KC_DB_URL not set"
    exit 1
fi
if [ -z "$KC_DB_USERNAME" ] || [ -z "$KC_DB_PASSWORD" ]; then
    echo "❌ ERROR: Database credentials not set"
    exit 1
fi

# Load admin credentials
if [ -f "/run/secrets/keycloak_admin_user" ]; then
    export KEYCLOAK_ADMIN=$(cat /run/secrets/keycloak_admin_user)
    echo "📋 Admin user loaded from secret"
else
    export KEYCLOAK_ADMIN=${KEYCLOAK_ADMIN:-admin}
fi
if [ -f "/run/secrets/keycloak_admin_password" ]; then
    export KEYCLOAK_ADMIN_PASSWORD=$(cat /run/secrets/keycloak_admin_password)
else
    export KEYCLOAK_ADMIN_PASSWORD=${KEYCLOAK_ADMIN_PASSWORD:-admin123}
fi

echo "ℹ️ Starting Keycloak with admin user: ${KEYCLOAK_ADMIN}"

# --- JSON Validation ---
echo "🔍 Validating realm.json configuration..."
REALM_JSON_PATH="/opt/keycloak/data/import/realm.json"

if [ ! -f "$REALM_JSON_PATH" ]; then
    echo "❌ ERROR: realm.json not found at $REALM_JSON_PATH"
    exit 1
fi

if [ ! -s "$REALM_JSON_PATH" ]; then
    echo "❌ ERROR: realm.json is empty"
    exit 1
fi

# Validate JSON syntax using jq (with fallback)
if command -v jq >/dev/null 2>&1; then
    echo "🔍 Validating realm.json with jq..."

    if ! jq empty "$REALM_JSON_PATH" 2>/dev/null; then
        echo "❌ ERROR: realm.json has invalid JSON syntax"
        echo "📄 Getting detailed error information..."

        # Capture detailed jq error
        JQ_ERROR=$(jq empty "$REALM_JSON_PATH" 2>&1 || true)
        echo "🔍 JSON Parser Error: $JQ_ERROR"

        # Extract line number from jq error message
        LINE_NUM=$(echo "$JQ_ERROR" | grep -o 'line [0-9]*' | grep -o '[0-9]*' | head -1)

        if [ -n "$LINE_NUM" ]; then
            echo "📍 Error detected at line $LINE_NUM"
            echo "📄 Context around the error:"
            start_line=$((LINE_NUM - 2))
            end_line=$((LINE_NUM + 2))
            [ "$start_line" -lt 1 ] && start_line=1

            sed -n "${start_line},${end_line}p" "$REALM_JSON_PATH" | nl -v$start_line -ba | while read num line; do
                if [ "$num" -eq "$LINE_NUM" ]; then
                    echo ">>> $num: $line  ⚠️  ERROR HERE"
                else
                    echo "    $num: $line"
                fi
            done
        else
            echo "📄 Showing first 15 lines for manual inspection:"
            head -15 "$REALM_JSON_PATH" | nl -ba
        fi
        exit 1
    fi

    # Extract realm name using jq
    REALM_NAME=$(jq -r '.realm // empty' "$REALM_JSON_PATH" 2>/dev/null)
else
    echo "⚠️  jq not available, using basic validation..."

    # Basic JSON validation fallback
    OPEN_BRACES=$(grep -o '{' "$REALM_JSON_PATH" | wc -l)
    CLOSE_BRACES=$(grep -o '}' "$REALM_JSON_PATH" | wc -l)
    OPEN_BRACKETS=$(grep -o '\[' "$REALM_JSON_PATH" | wc -l)
    CLOSE_BRACKETS=$(grep -o '\]' "$REALM_JSON_PATH" | wc -l)

    echo "📊 Basic structure check:"
    echo "  - Braces: $OPEN_BRACES opening, $CLOSE_BRACES closing"
    echo "  - Brackets: $OPEN_BRACKETS opening, $CLOSE_BRACKETS closing"

    if [ "$OPEN_BRACES" -ne "$CLOSE_BRACES" ] || [ "$OPEN_BRACKETS" -ne "$CLOSE_BRACKETS" ]; then
        echo "❌ ERROR: Mismatched braces or brackets in JSON"
        exit 1
    fi

    # Extract realm name using grep
    REALM_NAME=$(grep '"realm"[[:space:]]*:' "$REALM_JSON_PATH" | head -1 | sed 's/.*"realm"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/')
fi
if [ -z "$REALM_NAME" ]; then
    echo "❌ ERROR: realm.json missing 'realm' field"
    exit 1
fi

echo "✅ JSON validation passed - realm: $REALM_NAME"

# --- Show secret value that will be imported ---
echo "🔍 Showing secret value from realm.json that will be imported..."
IMPORT_SECRET=$(grep -A10 '"clientId" : "myclient"' "$REALM_JSON_PATH" | grep '"secret"' | cut -d'"' -f4)
if [ -n "$IMPORT_SECRET" ]; then
  echo "📝 Secret in realm.json for myclient: ${IMPORT_SECRET:0:8}...${IMPORT_SECRET: -8}"
else
  echo "⚠️ No secret found for myclient in realm.json"
fi

# --- Startup logic ---
# Check deployment type to decide on import strategy
DEPLOYMENT_TYPE_FILE="/opt/keycloak/deployment_type.txt"

if [ -f "$DEPLOYMENT_TYPE_FILE" ]; then
  DEPLOYMENT_TYPE=$(cat "$DEPLOYMENT_TYPE_FILE")
  if [ "$DEPLOYMENT_TYPE" = "new" ]; then
    IMPORT_FLAG="--import-realm"
    echo "🆕 New deployment - will import realm.json"
  else
    IMPORT_FLAG=""
    echo "♻️ Existing deployment - will skip realm import"
  fi
else
  # Fallback: assume new deployment
  IMPORT_FLAG="--import-realm"
  echo "🆕 No deployment marker found, assuming new deployment"
fi

if [ "$KC_ENV" = "production" ]; then
  KC_CMD="start --optimized --cache-config-file=cache-ispn-jdbc-ping.xml $IMPORT_FLAG"
else
  KC_CMD="start $IMPORT_FLAG"
fi

/opt/keycloak/bin/kc.sh $KC_CMD &
KC_PID=$!

# --- Wait for Keycloak readiness ---
echo "⏳ Waiting for Keycloak to become ready..."

until /opt/keycloak/bin/kcadm.sh config credentials \
  --server http://localhost:8080 \
  --realm master \
  --user "$KEYCLOAK_ADMIN" \
  --password "$KEYCLOAK_ADMIN_PASSWORD" >/dev/null 2>&1; do
    echo "⌛ Still waiting... retrying in 5s"
    sleep 5
done
echo "✅ Keycloak is ready."

# --- Post-startup realm verification ---
TARGET_REALM="SnapReportsRealm"

# For existing deployments, add new roles/groups only
DEPLOYMENT_TYPE_FILE="/opt/keycloak/deployment_type.txt"
if [ -f "$DEPLOYMENT_TYPE_FILE" ]; then
  DEPLOYMENT_TYPE=$(cat "$DEPLOYMENT_TYPE_FILE")
  if [ "$DEPLOYMENT_TYPE" = "existing" ]; then
    echo "♻️ Existing deployment - adding new roles/groups only..."

    # Use kcadm to add new roles/groups safely
    REALM_JSON_PATH="/opt/keycloak/data/import/realm.json"

    echo "🔍 Checking for new roles..."
    # Extract role names using grep and sed (no jq needed)
    grep -o '"name"[[:space:]]*:[[:space:]]*"[^"]*"' "$REALM_JSON_PATH" | \
    sed 's/"name"[[:space:]]*:[[:space:]]*"\([^"]*\)"/\1/' | \
    grep -E '^role-' | while read -r role_name; do
      if [ -n "$role_name" ]; then
        # Check if role exists
        if ! /opt/keycloak/bin/kcadm.sh get roles/$role_name -r ${TARGET_REALM} >/dev/null 2>&1; then
          echo "➕ Adding new role: $role_name"

          # Extract description using grep/sed
          role_desc=$(grep -A1 -B1 "\"name\"[[:space:]]*:[[:space:]]*\"$role_name\"" "$REALM_JSON_PATH" | \
                     grep '"description"' | \
                     sed 's/.*"description"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/' || echo "")

          /opt/keycloak/bin/kcadm.sh create roles -r ${TARGET_REALM} -s name="$role_name" -s description="$role_desc" 2>/dev/null || echo "⚠️ Role $role_name might already exist"
        else
          echo "✓ Role $role_name already exists"
        fi
      fi
    done

    echo "🔍 Checking for new groups..."
    # Extract all group names from the groups section using shell commands
    # Find the groups section and extract group names
    sed -n '/"groups"[[:space:]]*:[[:space:]]*\[/,/^\][[:space:]]*[,}]*$/p' "$REALM_JSON_PATH" | \
    grep '"name"[[:space:]]*:[[:space:]]*"[^"]*"' | \
    sed 's/.*"name"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/' | while read -r group_name; do
      if [ -n "$group_name" ]; then
        # Get group ID by finding exact name match from all groups
        GROUP_UUID=$(/opt/keycloak/bin/kcadm.sh get groups -r ${TARGET_REALM} | grep -B1 -A1 "\"name\"[[:space:]]*:[[:space:]]*\"$group_name\"" | grep '"id"' | cut -d'"' -f4)

        if [ -z "$GROUP_UUID" ]; then
          echo "➕ Adding new group: $group_name"
          /opt/keycloak/bin/kcadm.sh create groups -r ${TARGET_REALM} -s name="$group_name" 2>/dev/null || echo "⚠️ Group $group_name might already exist"

          # Get the group ID after creation
          GROUP_UUID=$(/opt/keycloak/bin/kcadm.sh get groups -r ${TARGET_REALM} | grep -B1 -A1 "\"name\"[[:space:]]*:[[:space:]]*\"$group_name\"" | grep '"id"' | cut -d'"' -f4)

          if [ -n "$GROUP_UUID" ]; then
            echo "🔗 Assigning roles to group: $group_name"
            # Extract roles for this group - get complete realmRoles array
            # Find the group section and extract ALL roles from its realmRoles array
            sed -n "/\"name\"[[:space:]]*:[[:space:]]*\"$group_name\"/,/^[[:space:]]*}/p" "$REALM_JSON_PATH" | \
            sed -n '/"realmRoles"[[:space:]]*:[[:space:]]*\[/,/\]/p' | \
            grep -o '"[^"]*"' | sed 's/"//g' | grep -v "realmRoles" | while read -r role_name; do
              if [ -n "$role_name" ] && [ "$role_name" != "realmRoles" ]; then
                echo "  ↳ Assigning role: $role_name"
                /opt/keycloak/bin/kcadm.sh add-roles -r ${TARGET_REALM} --gid $GROUP_UUID --rolename "$role_name" 2>/dev/null || echo "    ⚠️ Role $role_name assignment failed"
              fi
            done
          fi
        else
          echo "✓ Group $group_name already exists"
          # Even if group exists, check for new roles to assign
          # GROUP_UUID already set above
          if [ -n "$GROUP_UUID" ]; then
            echo "🔗 Checking for new roles to assign to existing group: $group_name"

            # Get current roles assigned to the group
            CURRENT_GROUP_ROLES=$(mktemp)
            /opt/keycloak/bin/kcadm.sh get groups/$GROUP_UUID/role-mappings/realm -r ${TARGET_REALM} | grep '"name"' | cut -d'"' -f4 > "$CURRENT_GROUP_ROLES"

            # Extract roles that should be assigned to this group from realm.json
            EXPECTED_GROUP_ROLES=$(mktemp)

            # Find realmRoles for this group - extract the complete array
            # Find the group section and extract ALL roles from its realmRoles array
            sed -n "/\"name\"[[:space:]]*:[[:space:]]*\"$group_name\"/,/^[[:space:]]*}/p" "$REALM_JSON_PATH" | \
            sed -n '/"realmRoles"[[:space:]]*:[[:space:]]*\[/,/\]/p' | \
            grep -o '"[^"]*"' | sed 's/"//g' | grep -v "realmRoles" > "$EXPECTED_GROUP_ROLES"

            # Compare expected vs current roles and add missing ones
            while read -r expected_role; do
              if [ -n "$expected_role" ] && ! grep -q "^$expected_role$" "$CURRENT_GROUP_ROLES"; then
                echo "  ➕ Assigning new role: $expected_role to group $group_name"
                /opt/keycloak/bin/kcadm.sh add-roles -r ${TARGET_REALM} --gid $GROUP_UUID --rolename "$expected_role" 2>/dev/null || echo "    ⚠️ Role $expected_role assignment failed"
              else
                echo "  ✓ Role $expected_role already assigned to group $group_name"
              fi
            done < "$EXPECTED_GROUP_ROLES"

            # Clean up temp files
            rm -f "$CURRENT_GROUP_ROLES" "$EXPECTED_GROUP_ROLES"
          fi
        fi
      fi
    done

    echo "✅ Existing deployment update completed."
  else
    echo "🆕 New deployment - realm should have been imported at startup"

    # Verify import worked and ensure client secret is correctly set
    if /opt/keycloak/bin/kcadm.sh get realms | grep -q "\"realm\" : \"${TARGET_REALM}\""; then
      CLIENT_UUID=$(/opt/keycloak/bin/kcadm.sh get clients -r ${TARGET_REALM} -q clientId=myclient --fields id | grep '"id"' | cut -d'"' -f4)
      if [ -n "$CLIENT_UUID" ]; then
        # Get the secret that should be used (from realm.json) - use same logic as above
        REALM_JSON_SECRET=$(grep -A15 '"clientId"[[:space:]]*:[[:space:]]*"myclient"' "$REALM_JSON_PATH" | grep '"secret"' | cut -d'"' -f4)
        if [ -z "$REALM_JSON_SECRET" ]; then
          REALM_JSON_SECRET=$(grep -A15 '"clientId": "myclient"' "$REALM_JSON_PATH" | grep '"secret"' | cut -d'"' -f4)
        fi
        if [ -z "$REALM_JSON_SECRET" ]; then
          REALM_JSON_SECRET=$(grep -A15 '"clientId":"myclient"' "$REALM_JSON_PATH" | grep '"secret"' | cut -d'"' -f4)
        fi

        # Get the current secret in Keycloak
        CURRENT_SECRET=$(/opt/keycloak/bin/kcadm.sh get clients/$CLIENT_UUID -r ${TARGET_REALM} --fields secret 2>/dev/null | grep '"secret"' | cut -d'"' -f4)

        echo "🔍 Secret from realm.json: ${REALM_JSON_SECRET:0:8}...${REALM_JSON_SECRET: -8}"
        echo "🔍 Current secret in Keycloak: ${CURRENT_SECRET:0:8}...${CURRENT_SECRET: -8}"

        # If secrets don't match, update Keycloak with the realm.json value
        if [ "$REALM_JSON_SECRET" != "$CURRENT_SECRET" ]; then
          echo "🔄 Secrets don't match, updating Keycloak with realm.json value..."
          /opt/keycloak/bin/kcadm.sh update clients/$CLIENT_UUID -r ${TARGET_REALM} -s secret="$REALM_JSON_SECRET"

          # Verify the update
          UPDATED_SECRET=$(/opt/keycloak/bin/kcadm.sh get clients/$CLIENT_UUID -r ${TARGET_REALM} --fields secret 2>/dev/null | grep '"secret"' | cut -d'"' -f4)
          echo "✅ Secret updated to: ${UPDATED_SECRET:0:8}...${UPDATED_SECRET: -8}"
        else
          echo "✅ Secrets match: ${CURRENT_SECRET:0:8}...${CURRENT_SECRET: -8}"
        fi

        # Ensure service accounts are enabled
        echo "🔧 Ensuring service accounts are enabled..."
        /opt/keycloak/bin/kcadm.sh update clients/$CLIENT_UUID -r ${TARGET_REALM} -s serviceAccountsEnabled=true

        # Create service account user if missing
        SERVICE_ACCOUNT_USER="service-account-myclient"
        if ! /opt/keycloak/bin/kcadm.sh get users -r ${TARGET_REALM} -q username=$SERVICE_ACCOUNT_USER | grep -q '"username"'; then
          echo "🔨 Creating service account user: $SERVICE_ACCOUNT_USER"
          /opt/keycloak/bin/kcadm.sh create users -r ${TARGET_REALM} \
            -s username=$SERVICE_ACCOUNT_USER \
            -s enabled=true \
            -s serviceAccountClientId=myclient
          echo "✅ Service account user created: $SERVICE_ACCOUNT_USER"
        else
          echo "✅ Service account user already exists: $SERVICE_ACCOUNT_USER"
        fi

        # Update Docker secret with the actual Keycloak secret
        echo "🔄 Updating Docker secret with Keycloak client secret..."
        /opt/keycloak/update-docker-secret.sh
      fi
    fi
  fi
fi

# --- Run configuration scripts ---
/opt/keycloak/assign-roles.sh

wait $KC_PID
