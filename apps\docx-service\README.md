# DOCX Service

Este é um serviço de back-end para a geração de relatórios em formato `.docx` a partir de dados enviados em formato JSON.

## ✨ Funcionalidades

- Geração de documentos DOCX dinamicamente.
- Estrutura modular para renderizar diferentes seções do documento.
- Suporte para imagens, fontes customizadas e metadados.
- Execução em um contêiner Docker leve e seguro.
- Monitoramento com logs detalhados e endpoint de Health Check.

## Prerequisites

- [Node.js](https://nodejs.org/) (versão 20 ou superior)
- [pnpm](https://pnpm.io/)
- [Docker](https://www.docker.com/) (para execução em contêiner)

## 🚀 Começando

### 1. Clone o Repositório

```bash
git clone <url-do-repositorio>
cd docx-service
```

### 2. Instale as Dependências

```bash
pnpm install
```

### 3. Configure as Variáveis de Ambiente

Crie um arquivo `.env` na raiz do projeto, baseado no exemplo abaixo. Este serviço depende do MinIO para buscar assets como imagens e fontes.

```env
# Exemplo de .env
MINIO_ENDPOINT=your_minio_endpoint
MINIO_PORT=9000
MINIO_ACCESS_KEY=your_access_key
MINIO_SECRET_KEY=your_secret_key
MINIO_BUCKET=your_bucket_name
```

### 4. Executando Localmente (Desenvolvimento)

Para iniciar o servidor em modo de desenvolvimento com hot-reload:

```bash
pnpm run dev
```

O serviço estará disponível em `http://localhost:3008`.

### 5. Gerando a Build de Produção

Para compilar o código TypeScript para JavaScript:

```bash
pnpm run build
```

Os arquivos compilados serão salvos no diretório `dist/`.

### 6. Iniciando em Produção

Após gerar a build, inicie o serviço com:

```bash
pnpm run start
```

## 🐳 Executando com Docker

A maneira mais simples de executar o serviço é usando o Docker Compose.

```bash
docker-compose up
```

O serviço estará disponível na porta `3008` do seu host. Para reconstruir a imagem, use:

```bash
docker-compose up --build
```

## 📝 API

### `POST /docx`

Endpoint principal para a geração de um novo documento `.docx`.

- **Headers**
  - `Content-Type: application/json`
- **Corpo da Requisição (Body)**
  - O corpo deve ser um JSON contendo os metadados e as seções do relatório. Veja um exemplo em `__tests__/input-paylod.json`.
- **Resposta de Sucesso**
  - **Código**: `200 OK`
  - **Headers**:
    - `Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document`
    - `Content-Disposition: attachment; filename="nome_do_relatorio.docx"`
  - **Corpo**: O arquivo `.docx` gerado.

## 📊 Monitoramento e Logs

### Sistema de Logging

- **Biblioteca**: Logger customizado (`utils/logger.ts`).
- **Níveis**: `info`, `debug`, `error`.
- **Métricas**: Tempo de processamento da geração do DOCX e tamanho do request.
- **Contexto**: Logs detalhados para cada etapa do processo de geração.

### Health Check

- **Endpoint**: `GET /health`
- **Resposta**: Status `200` com a mensagem `"OK"`.
- **Uso**: Ideal para ser usado por orquestradores de contêineres (como Kubernetes ou Docker Swarm) para verificar o estado da aplicação.

## 🔒 Segurança

- **Usuário Não-Root**: O contêiner é executado com um usuário de privilégios limitados (`nodejs`) para mitigar riscos de segurança.
- **CORS**: Configurado para permitir requisições de origens externas, podendo ser ajustado conforme a necessidade.
