#!/bin/bash

# Keycloak Internal Admin Access Script
# ====================================
# This script provides secure access to internal Keycloak admin interface
# Only accessible from within the VM

set -e

echo "🔐 Keycloak Internal Admin Access"
echo "================================"

# Check if Keycloak container is running
if ! docker-compose -f docker-compose-keycloak.yml ps keycloak | grep -q "Up"; then
    echo "❌ ERROR: Keycloak container is not running"
    echo "Start Keycloak first with one of:"
    echo "  ./scripts/deploy-keycloak-development.sh"
    echo "  ./scripts/deploy-keycloak-production.sh"
    exit 1
fi

# Get container info
CONTAINER_ID=$(docker-compose -f docker-compose-keycloak.yml ps -q keycloak)
KEYCLOAK_IP=$(docker inspect $CONTAINER_ID | grep '"IPAddress"' | head -1 | cut -d '"' -f 4)

echo "📊 Keycloak Container Status:"
echo "  Container ID: $CONTAINER_ID"
echo "  Internal IP: $KEYCLOAK_IP"
echo "  Network: mystack-net"
echo ""

# Display access methods
echo "🌐 Admin Access Methods (VM only):"
echo ""
echo "1. Direct container access:"
echo "   http://$KEYCLOAK_IP:8080/admin/"
echo ""
echo "2. Using Docker network:"
echo "   curl -s http://keycloak:8080/admin/ (from other containers)"
echo ""
echo "3. Container shell access:"
echo "   docker exec -it $CONTAINER_ID /bin/bash"
echo ""

# Show admin credentials
ENV_FILE=""
if [ -f "config/keycloak-production.env" ] && grep -q "KC_ENV=production" config/keycloak-production.env 2>/dev/null; then
    ENV_FILE="config/keycloak-production.env"
elif [ -f "config/keycloak-development.env" ]; then
    ENV_FILE="config/keycloak-development.env"
fi

if [ -n "$ENV_FILE" ]; then
    echo "🔑 Admin Credentials:"
    if docker secret ls | grep -q keycloak_admin_user; then
        echo "  Username: (stored in Docker secret keycloak_admin_user)"
        echo "  Password: (stored in Docker secret keycloak_admin_password)"
    else
        ADMIN_USER=$(docker exec $CONTAINER_ID printenv KEYCLOAK_ADMIN 2>/dev/null || echo "admin")
        ADMIN_PASS=$(docker exec $CONTAINER_ID printenv KEYCLOAK_ADMIN_PASSWORD 2>/dev/null || echo "admin123")
        echo "  Username: $ADMIN_USER"
        echo "  Password: $ADMIN_PASS"
    fi
else
    echo "⚠️ Could not determine admin credentials"
fi

echo ""
echo "📊 Health & Monitoring (VM only):"
echo "  Health: http://$KEYCLOAK_IP:9000/health"
echo "  Metrics: http://$KEYCLOAK_IP:9000/metrics"
echo "  Ready: http://$KEYCLOAK_IP:9000/health/ready"
echo ""

# Option to create temporary port forward for GUI access
echo "🚀 Temporary GUI Access Options:"
echo ""
echo "A) Create temporary port forward (CAREFUL - only for development):"
echo "   docker-compose -f docker-compose-keycloak.yml -f docker-compose.keycloak-development.yml exec keycloak echo 'Adding temporary port forward...'"
echo "   # Then manually add 'ports: [\"8080:8080\"]' to development override temporarily"
echo ""
echo "B) Use SSH tunnel from your local machine:"
echo "   ssh -L 8080:$KEYCLOAK_IP:8080 -L 9000:$KEYCLOAK_IP:9000 user@$(hostname -I | awk '{print $1}')"
echo "   # Then access http://localhost:8080/admin/ from your local browser"
echo ""

# Show container logs
echo "📋 Recent Keycloak Logs:"
echo "========================"
docker logs $CONTAINER_ID --tail=10

echo ""
echo "🔒 Security Notes:"
echo "  ✅ Keycloak is completely internal - no external access"
echo "  ✅ Only accessible from within VM or Docker network"
echo "  ✅ All authentication flows work internally between containers"
echo "  ✅ Admin access requires VM access (SSH)"
echo ""
