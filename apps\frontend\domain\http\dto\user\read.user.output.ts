import type { UserData } from "~/types/global";

/**
 * ReadUserOutputDTO é uma classe estática responsável por validar e estruturar
 * os dados recebidos de usuário (UserData) vindos de chamadas HTTP.
 *
 * Ela garante que os dados recebidos sigam o contrato esperado, validando os campos obrigatórios
 * e fornecendo um método seguro para processar e congelar o objeto.
 */
export class ReadUserOutputDTO {
  // Parâmetros obrigatórios para UserData
  static #requiredParams = ["user_id", "name", "email", "last_login"];

  /**
   * Valida os dados do usuário.
   * @param {Partial<UserData>} data - Objeto contendo informações do usuário.
   * @throws {Error} Caso algum dos parâmetros obrigatórios esteja ausente ou inválido.
   * @private
   */
  static #validateUserData(data: Partial<UserData>) {
    if (!data || typeof data !== "object") {
      throw new Error(
        "Formato de dados do usuário inválido. Esperado um objeto."
      );
    }
    for (const param of this.#requiredParams) {
      if (!(param in data)) {
        throw new Error(`Parâmetro obrigatório ausente: ${param}`);
      }
    }
    if (typeof data.user_id !== "string" || !data.user_id.trim()) {
      throw new Error("user_id inválido. Esperado uma string não vazia.");
    }
    if (typeof data.name !== "string" || !data.name.trim()) {
      throw new Error("name inválido. Esperado uma string não vazia.");
    }
    if (typeof data.email !== "string" || !data.email.trim()) {
      throw new Error("email inválido. Esperado uma string não vazia.");
    }
    // if (typeof data.credits !== "number" || isNaN(data.credits)) {
    //   throw new Error("credits inválido. Esperado um número.");
    // }
    // if (typeof data.saved_reports !== "number" || isNaN(data.saved_reports)) {
    //   throw new Error("saved_reports inválido. Esperado um número.");
    // }
    // if (!Array.isArray(data.report_types)) {
    //   throw new Error("report_types inválido. Esperado um array de strings.");
    // }
    if (typeof data.last_login !== "string" || !data.last_login.trim()) {
      throw new Error("last_login inválido. Esperado uma string não vazia.");
    }
  }

  /**
   * Processa e retorna os dados do usuário em formato estruturado e imutável.
   * @param {Partial<UserData>} data - Objeto contendo informações do usuário.
   * @returns {Readonly<UserData>} Objeto UserData validado e congelado.
   * @throws {Error} Caso algum parâmetro obrigatório esteja ausente ou inválido.
   */
  static process(data: Partial<UserData>): Readonly<UserData> {
    this.#validateUserData(data);
    return Object.freeze({ ...data }) as Readonly<UserData>;
  }
}
