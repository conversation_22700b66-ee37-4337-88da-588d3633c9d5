import { UserInviteResponse } from "~/types/global";

export const ENABLE_MOCKS = false

export const mockInvites: UserInviteResponse[] = [
  {
    invite_id: "mock-invite-1",
    status_invite: "enviado",
    user_sender_id: "sender-1",
    name_sender: "<PERSON>",
    email_sender: "<EMAIL>",
    organization_name: "Mesma Organização",
  },
  {
    invite_id: "mock-invite-2", 
    status_invite: "enviado",
    user_sender_id: "sender-2",
    name_sender: "<PERSON>",
    email_sender: "<EMAIL>",
    organization_name: "Nova Organização",
  }
];

export const mockAnswerInviteMutation = {
  mutate: (params: any, options?: any) => {
    console.log("Mock mutation called with:", params);
    setTimeout(() => {
      if (options?.onSuccess) {
        options.onSuccess();
      }
    }, 2000);
  },
  isPending: false
};

export const mockUserData = {
  organization_name: "Mesma Organização"
};