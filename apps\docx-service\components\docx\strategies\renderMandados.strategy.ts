import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, getSingular, parseValue, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintMandadosProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      numero?: ValueWithSource;
      pessoa?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      processos?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      telefones?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      detalhes?: Record<string, ValueWithSource>;
      [key: string]: any;
    }>;
  };
}

export const renderMandados = ({ section }: RenderPrintMandadosProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((mandado) => {
    if (mandado.numero && !mandado.numero.is_deleted) {
      children.push(new Paragraph({
        children: [
          new TextRun({ text: `${(mandado.numero.label || "Número do Mandado").toUpperCase()}: `, bold: true, color: "FE473C" }),
          new TextRun(String(mandado.numero.value)),
          new TextRun({ text: ` | ${mandado.numero.source?.map(translateSource).join(', ')}`, size: 16, color: "FE473C" }),
        ],
        spacing: { after: 200 }
      }));
    }

    if (mandado.detalhes) {
      const tableRows = Object.entries(mandado.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => {
          return new TableRow({
            children: [
              new TableCell({
                children: [new Paragraph({
                  children: [
                    new TextRun({ text: "■ ", color: "CCCCCC", bold: true }),
                    new TextRun({ text: `${translatePropToLabel(field.label || key).toUpperCase()}`, bold: true, color: "889EA3" }),
                    new TextRun({ text: ` | ${field.source?.map(translateSource).join(', ')}`, size: 16, color: "FE473C" }),
                  ]
                })],
                shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
              }),
              new TableCell({
                children: [new Paragraph(String(field.value))],
                shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
              }),
            ],
          });
        });

      if (tableRows.length > 0) {
        children.push(new Table({
          rows: tableRows,
          width: { size: 100, type: WidthType.PERCENTAGE },
          columnWidths: [3500, 5500],
        }));
      }
    }

    if (mandado.pessoa && mandado.pessoa.length > 0) {
        children.push(new Paragraph({ text: "PESSOAS", style: "subtitle" }));
        mandado.pessoa.forEach((item, index) => {
            if(item.is_deleted) return;
            children.push(new Paragraph({ children: [new TextRun({text: `PESSOA ${index + 1}`, bold: true})]}));
            const itemRows = Object.entries(item.value)
                .filter(([_, field]) => !field.is_deleted)
                .map(([key, field]) => new TableRow({
                    children: [
                        new TableCell({children: [new Paragraph(`${translatePropToLabel(field.label || key).toUpperCase()}`)]}),
                        new TableCell({children: [new Paragraph(String(field.value))]})
                    ]
                }));
            if(itemRows.length > 0) {
                children.push(new Table({ rows: itemRows, width: { size: 100, type: WidthType.PERCENTAGE }}));
            }
        });
    }

    if (mandado.processos && mandado.processos.length > 0) {
        children.push(new Paragraph({ text: "PROCESSOS", style: "subtitle" }));
        mandado.processos.forEach((item, index) => {
            if(item.is_deleted) return;
            children.push(new Paragraph({ children: [new TextRun({text: `PROCESSO ${index + 1}`, bold: true})]}));
            const itemRows = Object.entries(item.value)
                .filter(([_, field]) => !field.is_deleted)
                .map(([key, field]) => new TableRow({
                    children: [
                        new TableCell({children: [new Paragraph(`${translatePropToLabel(field.label || key).toUpperCase()}`)]}),
                        new TableCell({children: [new Paragraph(String(field.value))]})
                    ]
                }));
            if(itemRows.length > 0) {
                children.push(new Table({ rows: itemRows, width: { size: 100, type: WidthType.PERCENTAGE }}));
            }
        });
    }

    if (mandado.telefones && mandado.telefones.length > 0) {
        children.push(new Paragraph({ text: "TELEFONES", style: "subtitle" }));
        mandado.telefones.forEach((item, index) => {
            if(item.is_deleted) return;
            children.push(new Paragraph({ children: [new TextRun({text: `TELEFONE ${index + 1}`, bold: true})]}));
            const itemRows = Object.entries(item.value)
                .filter(([_, field]) => !field.is_deleted)
                .map(([key, field]) => new TableRow({
                    children: [
                        new TableCell({children: [new Paragraph(`${translatePropToLabel(field.label || key).toUpperCase()}`)]}),
                        new TableCell({children: [new Paragraph(String(field.value))]})
                    ]
                }));
            if(itemRows.length > 0) {
                children.push(new Table({ rows: itemRows, width: { size: 100, type: WidthType.PERCENTAGE }}));
            }
        });
    }

  });

  return { children };
};
