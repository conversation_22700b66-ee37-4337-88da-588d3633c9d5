<?xml version="1.0" encoding="UTF-8"?>
<infinispan
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="urn:infinispan:config:14.0 http://www.infinispan.org/schemas/infinispan-config-14.0.xsd"
        xmlns="urn:infinispan:config:14.0">

    <jgroups>
        <stack name="tcp-jdbc-ping" extends="tcp">
            <TCP bind_addr="site_local"/>
            <JDBC_PING
                connection_driver="org.postgresql.Driver"
                connection_url="${env.KC_DB_URL}"
                connection_username="${env.KC_DB_USERNAME}"
                connection_password="${env.KC_DB_PASSWORD}"
                initialize_sql="CREATE TABLE IF NOT EXISTS JGROUPSPING (own_addr VARCHAR(200) NOT NULL, cluster_name VARCHAR(200) NOT NULL, ping_data BYTEA, PRIMARY KEY (own_addr, cluster_name));"
                />
            <MERGE3/>
            <FD_SOCK/>
            <FD_ALL/>
            <VERIFY_SUSPECT/>
            <pbcast.NAKACK2/>
            <UNICAST3/>
            <pbcast.STABLE/>
            <pbcast.GMS/>
            <MFC/>
            <FRAG3/>
        </stack>
    </jgroups>

    <cache-container name="keycloak">
        <transport stack="tcp-jdbc-ping"/>
        
        <local-cache name="realms"/>
        <local-cache name="users"/>
        <local-cache name="authorization"/>
        <local-cache name="keys"/>

        <replicated-cache name="sessions"/>
        <replicated-cache name="authenticationSessions"/>
        <replicated-cache name="offlineSessions"/>
        <replicated-cache name="clientSessions"/>
        <replicated-cache name="offlineClientSessions"/>
        <replicated-cache name="loginFailures"/>
        <replicated-cache name="work"/>
        <distributed-cache name="actionTokens"/>
    </cache-container>
</infinispan>