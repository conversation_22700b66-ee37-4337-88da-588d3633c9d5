import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintDoacoesRecebidasProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      nome_completo?: ValueWithSource;
      razao_social?: ValueWithSource;
      vinculos?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      detalhes?: Record<string, ValueWithSource>;
      [key: string]: any;
    }>;
  };
}

export const renderDoacoesRecebidas = ({ section }: RenderPrintDoacoesRecebidasProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((doacao) => {
    if (doacao.nome_completo && !doacao.nome_completo.is_deleted) {
      children.push(new Paragraph({ children: [new TextRun({ text: `${(doacao.nome_completo.label || "Nome Completo").toUpperCase()}: ${doacao.nome_completo.value}`, bold: true, color: "FE473C" })] }));
    }
    if (doacao.razao_social && !doacao.razao_social.is_deleted) {
      children.push(new Paragraph({ children: [new TextRun({ text: `${(doacao.razao_social.label || "Razão Social").toUpperCase()}: ${doacao.razao_social.value}`, bold: true, color: "FE473C" })] }));
    }

    if (doacao.detalhes) {
      const tableRows = Object.entries(doacao.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => {
          return new TableRow({
            children: [
              new TableCell({ children: [new Paragraph(translatePropToLabel(field.label || key).toUpperCase())] }),
              new TableCell({ children: [new Paragraph(String(field.value))] }),
            ],
          });
        });
      if (tableRows.length > 0) {
        children.push(new Table({ rows: tableRows, width: { size: 100, type: WidthType.PERCENTAGE } }));
      }
    }

    if (doacao.vinculos && doacao.vinculos.length > 0) {
        children.push(new Paragraph({text: "DOAÇÕES", style: "subtitle"}));
        doacao.vinculos.filter(v => !v.is_deleted).forEach((vinculo, index) => {
            children.push(new Paragraph({text: `DOAÇÃO ${index + 1}`}));
            const vinculoRows = Object.entries(vinculo.value).filter(([_, f]) => !f.is_deleted).map(([key, field]) => new TableRow({children: [new TableCell({children: [new Paragraph(translatePropToLabel(field.label || key))]}), new TableCell({children: [new Paragraph(String(field.value))]})]}))
            if(vinculoRows.length > 0) {
                children.push(new Table({rows: vinculoRows, width: {size: 100, type: WidthType.PERCENTAGE}}));
            }
        });
    }

  });

  return { children };
};
