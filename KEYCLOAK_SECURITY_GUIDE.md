# Keycloak Security Configuration Guide

This guide outlines the security configuration and best practices implemented for <PERSON><PERSON><PERSON><PERSON> in this project.

## 🔒 Security Architecture

### Reverse Proxy Configuration

Following [Keycloak's reverse proxy guidelines](https://www.keycloak.org/server/reverseproxy), we've implemented:

#### Production Security (auth.reports.snapforensics.com)
- **Exposed Paths**: Only `/realms/` and `/resources/` as recommended
- **Blocked Paths**: `/admin/`, `/metrics/`, `/health/` (prevents attack vectors)
- **Security Headers**: Strict CSP, HSTS, X-Frame-Options=DENY
- **Rate Limiting**: 10 requests/minute for auth, 100 requests/minute general
- **SSL**: TLS 1.2+ only with secure cipher suites

#### Development Security (auth.reportsbeta.snapforensics.com)
- **Additional Access**: `/admin/` accessible for development
- **Relaxed Headers**: More permissive CSP for debugging
- **Enhanced Logging**: Detailed admin access logging
- **Rate Limiting**: More permissive limits for development

## 🌐 Hostname Configuration

### Production
```
KC_HOSTNAME_URL=https://auth.reports.snapforensics.com
KC_HOSTNAME_STRICT=true
KC_HOSTNAME_STRICT_HTTPS=true
```

### Development
```
KC_HOSTNAME_URL=https://auth.reportsbeta.snapforensics.com
KC_HOSTNAME_STRICT=false
KC_HOSTNAME_STRICT_HTTPS=false
```

## 🔐 Environment-Based Security

### Production Security Features
- **Strict HTTPS Enforcement**: All connections must use HTTPS
- **Trusted Proxy Validation**: Only specified proxy IPs accepted
- **Enhanced Cookie Security**: Strict SameSite, Secure flags
- **Resource Optimization**: 2 replicas, optimized JVM settings
- **Database Connection Pooling**: Larger pools for production load

### Development Security Features
- **Relaxed HTTPS**: Allows mixed content for debugging
- **Verbose Logging**: DEBUG level for troubleshooting
- **Admin Console Access**: Direct access to admin interfaces
- **Port Exposure**: Direct container access for debugging

## 🚀 Deployment Security

### Production Deployment
```bash
# Use production deployment script
./scripts/deploy-keycloak-production.sh
```

**Security Validations:**
- ✅ SSL certificates verified
- ✅ Database credentials secured
- ✅ Docker secrets created
- ✅ Health checks enabled
- ✅ Proxy configuration validated

### Development Deployment
```bash
# Use development deployment script
./scripts/deploy-keycloak-development.sh
```

## 🛡️ Security Headers Configuration

### Content Security Policy (CSP)

**Production:**
```
default-src 'self'; 
frame-src 'self'; 
frame-ancestors 'self'; 
object-src 'none'; 
script-src 'self'; 
style-src 'self' 'unsafe-inline'; 
img-src 'self' data:; 
font-src 'self' data:; 
connect-src 'self';
```

**Development:**
```
default-src 'self' 'unsafe-inline' 'unsafe-eval'; 
frame-src 'self'; 
frame-ancestors 'self'; 
object-src 'none';
```

### Other Security Headers
- **X-Frame-Options**: DENY (production), SAMEORIGIN (development)
- **X-Content-Type-Options**: nosniff
- **X-XSS-Protection**: 1; mode=block
- **Strict-Transport-Security**: max-age=31536000; includeSubDomains; preload
- **Referrer-Policy**: strict-origin-when-cross-origin

## 🔄 Proxy Security Configuration

### Trusted Proxy Addresses
```
KC_PROXY_TRUSTED_ADDRESSES=127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16
```

**Important**: Update with your actual proxy server IPs in production!

### Proxy Headers
```
KC_PROXY_HEADERS=xforwarded
```

Processes these headers:
- X-Forwarded-For
- X-Forwarded-Proto
- X-Forwarded-Host
- X-Forwarded-Port

## 📊 Monitoring and Health Checks

### Internal Monitoring (Not Publicly Exposed)
- **Health Endpoint**: `:9000/health` (management port)
- **Metrics Endpoint**: `:9000/metrics` (management port)
- **Ready Check**: `:9000/health/ready`

### Production Monitoring Access
- Only accessible from internal network/VPN
- Not exposed through reverse proxy
- Docker health checks configured

## 🔧 Database Security

### Connection Security
- **SSL Connections**: Enforced for database connections
- **Connection Pooling**: Optimized for production load
- **Credential Management**: Docker secrets in production

### JDBC_PING Clustering
- **Secure Clustering**: Database-based discovery
- **Connection Validation**: Validated connections
- **Data Isolation**: Cluster-specific data separation

## ⚠️ Security Checklist

### Pre-Production Checklist
- [ ] SSL certificates installed and valid
- [ ] Proxy trusted addresses configured with actual IPs
- [ ] Admin password changed from default
- [ ] Database credentials secured
- [ ] Docker secrets created
- [ ] Health checks responding
- [ ] Only `/realms/` and `/resources/` paths accessible
- [ ] Admin console not publicly accessible
- [ ] Security headers properly configured

### Regular Security Maintenance
- [ ] Monitor access logs for suspicious activity
- [ ] Update Keycloak to latest security patches
- [ ] Rotate admin credentials regularly
- [ ] Review and update trusted proxy addresses
- [ ] Test SSL certificate expiration monitoring
- [ ] Validate CSP policies don't break functionality

## 🚨 Security Incident Response

### If Admin Console is Compromised
1. Immediately block access at firewall level
2. Rotate all admin passwords
3. Review audit logs for unauthorized changes
4. Update trusted proxy addresses
5. Regenerate all client secrets

### If SSL Certificate is Compromised
1. Revoke compromised certificate
2. Generate new certificate
3. Update all configurations
4. Force password reset for all users
5. Review all active sessions

## 📚 Additional Resources

- [Keycloak Reverse Proxy Guide](https://www.keycloak.org/server/reverseproxy)
- [Keycloak Production Configuration](https://www.keycloak.org/server/configuration-production)
- [OWASP Security Headers](https://owasp.org/www-project-secure-headers/)
