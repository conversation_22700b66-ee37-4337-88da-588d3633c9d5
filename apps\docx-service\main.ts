import dotenv from "dotenv";
import path from "path";
import { initializeServer } from "./server";
import config from "./config/server";
import * as logger from "./utils/logger";
import { initializeMinioConnection } from "./config/minio";

if (process.env.NODE_ENV !== "production") {
  dotenv.config({ path: path.resolve(__dirname, ".env") });
}

/**
 * Initialize the Puppeteer browser and start both HTTP server and Kafka consumer
 */
async function main() {
  try {
    logger.info("Iniciando servidor docx...");

    // Start HTTP server
    initializeServer({ config });

    // Start Kafka consumer if enabled
    // const enableKafka = process.env.ENABLE_KAFKA_CONSUMER !== "false";
    // if (enableKafka) {
    //   logger.info("Initializing MinIO connection...");
    //   await initializeMinioConnection();
    //   logger.info("Starting Kafka consumer...");
    //   const { startKafkaConsumer } = await import("./service");
    //   await startKafkaConsumer();
    // }
  } catch (err) {
    logger.error("Failed to initialize browser or start server", {
      error: err,
    });
    process.exit(1);
  }
}

main().catch((err) => {
  logger.error("Unhandled error in main function", err);
  process.exit(1);
});

process.on("SIGINT", async () => {
  logger.info("Received SIGINT signal, shutting down...");
  process.exit(0);
});

process.on("SIGTERM", async () => {
  logger.info("Received SIGTERM signal, shutting down...");
  process.exit(0);
});
