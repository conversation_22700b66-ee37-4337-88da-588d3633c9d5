from enum import Enum
from typing import List

from reports_processor.constants import ReportKeys, razao_social_key, label_key, nome_completo_key, do_adicionais_key, \
    do_local_key, do_descricao_key, do_texto_key, pais_aplicacao_keys, partido_sigla_key, numero_mandado_key, \
    numero_processo_key, p_movimentacoes_key, nome_orgao_key, alias_key, primeiro_nome_key, sobrenome_key

front_entity_map = {ReportKeys.ENDERECO: 'enderecos',
                    ReportKeys.TELEFONE: 'telefones',
                    ReportKeys.EMAIL_ENTITY: 'emails',
                    ReportKeys.VINCULO: 'vinculos',
                    ReportKeys.PENA: 'pena',
                    # ReportKeys.ADVOGADO: 'advogado',
                    # ReportKeys.VITIMA: 'vitima',
                    # ReportKeys.REU: 'reu',
                    # ReportKeys.INTERESSADO: 'interessado',
                    # ReportKeys.RECORRIDO: 'recorrido',
                    # ReportKeys.OUTRO: 'outro',
                    # ReportKeys.RECORRENTRE
                    # ReportKeys.PAI: 'parentes',
                    # ReportKeys.PARTIDO_POLITICO: 'parentes',
                    # ReportKeys.CANDIDATO: 'parentes'
                    }


class FinalFrontFormatBasic:
    TITLE_FIELD = "title"
    SUBTITLE_FIELD = "subtitle"
    SUBSECTION_FIELD = 'subsection'
    SOURCES_FIELD = "sources"
    IS_DELETED = "is_deleted"
    DATA_COUNT_FIELD = "data_count"
    DATA_FIELD = "data"
    DETALHES_FIELD = "detalhes"

    def __init__(self, title, subtitle, entity_type, use_basic_value=None, count_level=0, subsection="", extra_data_to_move: List[str] = None, ignore_fields: List[str] = None):
        if extra_data_to_move is None:
            extra_data_to_move = []

        self.title = title
        self.subtitle = subtitle
        self.use_basic_value = use_basic_value
        self.count_level = count_level
        self.subsection = subsection
        self.extra_data_to_move = extra_data_to_move
        self.ignore_fields = ignore_fields or []
        self.entity_type = entity_type


class FinalFrontFormat(FinalFrontFormatBasic):
    def __init__(self, title, subtitle, entity_type, external_data_map, display_values, count_level=0, subsection="", extra_data_to_move: List[str] = None, ignore_fields: List[str] = None):
        super().__init__(title, subtitle, entity_type, count_level=count_level, subsection=subsection, extra_data_to_move=extra_data_to_move, ignore_fields=ignore_fields)
        self.external_data_map = external_data_map
        self.display_values = display_values or {}


front_vinculo_pessoais = FinalFrontFormat("Dados Pessoais", "Dados consultados na API SNAP.", [ReportKeys.PESSOA, ReportKeys.EMPRESA],
                                              {}, {}, count_level=1)
front_vinculo_empregaticio = FinalFrontFormat("Vínculos Empregatícios", "Dados consultados na API SNAP.", [ReportKeys.EMPRESA],
                                              {'empresa_pagadora': ['empresa pagadora', razao_social_key, 'razão social']},
                                              {'empresa_pagadora': 'Empresa Pagadora'})
front_telefones = FinalFrontFormatBasic("Telefones", "Dados consultados na API SNAP.", [ReportKeys.TELEFONE]) #, {'numero': ['numero']}, {'numero': 'Número'})
front_emails = FinalFrontFormatBasic("Emails", "Dados consultados na API SNAP.", [ReportKeys.EMAIL_ENTITY])
front_enderecos = FinalFrontFormatBasic("Endereços", "Dados consultados na API SNAP.", [ReportKeys.ENDERECO])
front_parentes = FinalFrontFormat("Parentes", "Dados consultados na API SNAP.", [ReportKeys.PESSOA], {'parentesco': [label_key]}, {'parentesco': 'Parentesco'})


front_outros_contatos = FinalFrontFormat("Possíveis Contatos", "Dados consultados na API SNAP.", [ReportKeys.PESSOA],
                                         {"nome_completo": [nome_completo_key]},
                                         {"nome_completo": "Nome"})
front_sociedades = FinalFrontFormat("Sociedades", "Dados consultados na API SNAP.", [ReportKeys.EMPRESA],
                                         {"razao_social": [razao_social_key, 'razão social']},
                                         {"razao_social": "Razão Social"})
front_socios = FinalFrontFormat("Sócios", "Dados consultados na API SNAP.", [ReportKeys.EMPRESA, ReportKeys.PESSOA],
                                {"razao_social": [razao_social_key, 'razão social'],
                                 "nome_completo": [nome_completo_key]},
                                {"razao_social": "Razão Social"})

front_do_nome = FinalFrontFormat("Diários Oficiais - Nome", "Dados consultados na API SNAP através do Nome da entidade.",
                                 [ReportKeys.DIARIO_OFICIAL],
                                         {"local": [do_adicionais_key, do_local_key]},
                                         {"local": "Local"},
                                 extra_data_to_move=[do_descricao_key, do_texto_key])
front_do_doc = FinalFrontFormat("Diários Oficiais - CPF", "Dados consultados na API SNAP através do CPF da entidade.",
                                [ReportKeys.DIARIO_OFICIAL],
                                {"local": [do_adicionais_key, do_local_key]},
                                {"local": "Local"},
                                extra_data_to_move=[do_descricao_key, do_texto_key])
front_pais = FinalFrontFormat("Possíveis Contas em Sites", "Dados consultados na API SNAP.",
                              [ReportKeys.PAI],
                              {'site': pais_aplicacao_keys}, {'site': 'Site'})
front_filiacao_partidaria = FinalFrontFormat("Filiação Partidária", "Dados consultados na API SNAP.",
                                             [ReportKeys.PARTIDO_POLITICO],
                              {'sigla': [partido_sigla_key]}, {'sigla': 'Sigla'})
front_doacoes_enviadas = FinalFrontFormat("Doações Enviadas Campanha", "Dados consultados na API SNAP.",
                                          [ReportKeys.PESSOA],
                              {'candidato': [nome_completo_key]}, {'candidato': 'Candidato'})
front_doacoes_recebidas = FinalFrontFormat("Doações Recebidas Campanha", "Dados consultados na API SNAP.",
                                           [ReportKeys.PESSOA, ReportKeys.EMPRESA],
                                           {"razao_social": [razao_social_key, 'razão social'],
                                                            "nome_completo": [nome_completo_key]},
                                            {"razao_social": "Razão Social"})
front_fornecimentos_enviados = FinalFrontFormat("Fornecimentos Enviados Campanha", "Dados consultados na API SNAP.",
                                                [ReportKeys.PESSOA, ReportKeys.EMPRESA],
                                                {"razao_social": [razao_social_key, 'razão social'],
                                                 "nome_completo": [nome_completo_key]},
                                                {"razao_social": "Razão Social"})
front_fornecimentos_recebidos = FinalFrontFormat("Fornecimentos Recebidos Campanha", "Dados consultados na API SNAP.",
                                                 [ReportKeys.PESSOA],
                                                 {'candidato': [nome_completo_key]}, {'candidato': 'Candidato'})
front_mandados = FinalFrontFormat("Mandados de Prisão", "Dados consultados no Banco Nacional de Monitoramento de Prisões (BNMP).",
                                  [ReportKeys.MANDADO],
                                                 {'numero': [numero_mandado_key]}, {'numero': 'Número do Mandado'})
front_processos = FinalFrontFormat("Processos", "Dados consultados na API SNAP.",
                                   [ReportKeys.PROCESSO],
                                                 {'numero': [numero_processo_key]}, {'numero': 'Número do Processo'},
                                   extra_data_to_move=[p_movimentacoes_key])
front_recursos_recebidos = FinalFrontFormat("Recursos Públicos Recebidos", "Dados consultados em portais de Transparência estaduais e federal.",
                                            [ReportKeys.RECURSOS_RECEBIDOS],
                                            {"orgao": [nome_orgao_key]},
                                            {"orgao": "Nome do Órgão"})
front_servico_publico = FinalFrontFormat("Serviço Público", "Dados consultados em portais de Transparência estaduais e federal.",
                                         [ReportKeys.PESSOA],
                                            {"nome_completo": [nome_completo_key]},
                                            {"nome_completo": "Nome Completo"}
                                            )

front_juntas_cmc = FinalFrontFormat("Juntas Comerciais", "Dados consultados na API SNAP.",
                                    [ReportKeys.EMPRESA],
                                    {"razao_social": [razao_social_key, 'razão social']},
                                    {"razao_social": "Razão Social"})

front_contatos_salvos = FinalFrontFormat("Contatos Salvos", "Dados consultados na API SNAP.",
                                         [ReportKeys.ALIAS],
                                    {"alias": [alias_key]},
                                    {"alias": "alias"})

front_empresas_relacionadas = FinalFrontFormat("Empresas Relacionadas", "Dados consultados na API SNAP.",
                                               [ReportKeys.EMPRESA],
                                    {"razao_social": [razao_social_key, 'razão social']},
                                    {"razao_social": "Razão Social"})

front_imagens = FinalFrontFormatBasic("Imagens", "Dados consultados na API SNAP.", [ReportKeys.IMAGEM])

front_nomes_usuario = FinalFrontFormatBasic("Nomes de Usuário", "Dados consultados na API SNAP.", [ReportKeys.ALIAS])

front_redes_sociais = FinalFrontFormatBasic("Perfis Redes Sociais", "Dados consultados na API SNAP.", [ReportKeys.GENERIC_REDE_SOCIAL], count_level=2)

front_vinculo_relacoes = FinalFrontFormat("Relações",
                                        "Dados consultados na API SNAP.",
                                          [ReportKeys.PESSOA, ReportKeys.EMPRESA],
                                          {"razao_social": [razao_social_key, 'razão social'],
                                                 "nome_completo": [nome_completo_key],
                             "tipo": [ReportKeys.TIPO],
                             "label default key": [label_key]},
                                          {"razao_social": "Razão Social"},
                                          ignore_fields=[sobrenome_key, primeiro_nome_key])


front_outras_urls = FinalFrontFormatBasic("Outras URLs", "Dados consultados na API SNAP.", [ReportKeys.URL])

front_p_pessoas_relacionadas = FinalFrontFormat("Possíveis Pessoas Relacionadas", "Dados consultados na API SNAP.",
                                                [ReportKeys.PESSOA],
                                                {},
                                                {})

front_vinculo_educacional = FinalFrontFormat("Vínculos Educacionais", "Dados consultados na API SNAP.",
                                             [ReportKeys.EMPRESA],
                                              {'empresa_educadora': [razao_social_key, 'razão social']},
                                              {'empresa_educadora': 'Empresa Educadora'})



class VinculoSection(Enum):
    """Enum representing all possible vinculos sections that can be processed"""
    DADOS_PESSOAIS = "dados_pessoais"
    MANDADOS = "mandados"
    PHONES = "phones"
    EMAILS = "emails"
    ENDERECOS = "enderecos"
    PARENTES = "parentes"
    SOCIEDADES = "sociedades"
    SOCIOS = "socios"
    JUNTAS_COMERCIAIS = "juntas_comerciais"
    VINCULOS_EMPREGATICIOS = "vinculos_empregaticios"
    VINCULOS_EDUCACIONAIS = "vinculos_educacionais"
    PROCESSOS = "processos"
    RECURSOS_RECEBIDOS = "recursos_recebidos"
    SERVICO_PUBLICO = "servico_publico"
    DIARIOS_OFICIAIS_DOC = "diarios_oficiais_doc"
    DIARIOS_OFICIAIS_NOME = "diarios_oficiais_nome"
    FILIACAO_PARTIDARIA = "f_partidaria"
    OUTROS_CONTATOS = "outros_contatos"
    PAIS = "provedores_apps"
    DOACOES_ELEITORAIS_ENVIADAS = "doacoes_eleitorais_enviadas"
    DOACOES_ELEITORAIS_RECEBIDAS = "doacoes_eleitorais_recebidas"
    FORNECIMENTOS_ELEITORAIS_ENVIADAS = "fornecimentos_eleitorais_enviadas"
    FORNECIMENTOS_ELEITORAIS_RECEBIDAS = "fornecimentos_eleitorais_recebidas"
    POSSIVEIS_PESSOAS_RELACIONADAS = "possiveis_pessoas_relacionadas"
    POSSIVEIS_EMPRESAS_RELACIONADAS = "possiveis_empresas_relacionadas"
    RELACOES = "relacoes"
    IMAGENS = "imagens"
    NOMES_USUARIO = "nomes_usuario"
    OUTRAS_URLS = "outras_urls"
    REDES_SOCIAIS = "redes_sociais"


    @staticmethod
    def front_format_section(section):
        """Returns the main entity type for this report"""
        return {
            VinculoSection.MANDADOS: front_mandados,
            VinculoSection.PHONES: front_telefones,
            VinculoSection.EMAILS: front_emails,
            VinculoSection.ENDERECOS: front_enderecos,
            VinculoSection.PARENTES: front_parentes,
            VinculoSection.SOCIEDADES: front_sociedades,
            VinculoSection.SOCIOS: front_socios,
            VinculoSection.JUNTAS_COMERCIAIS: front_juntas_cmc,
            VinculoSection.VINCULOS_EMPREGATICIOS: front_vinculo_empregaticio,
            VinculoSection.VINCULOS_EDUCACIONAIS: front_vinculo_educacional,
            VinculoSection.PROCESSOS: front_processos,
            VinculoSection.RECURSOS_RECEBIDOS: front_recursos_recebidos,
            VinculoSection.SERVICO_PUBLICO: front_servico_publico,
            VinculoSection.DIARIOS_OFICIAIS_DOC: front_do_doc,
            VinculoSection.DIARIOS_OFICIAIS_NOME: front_do_nome,
            VinculoSection.FILIACAO_PARTIDARIA: front_filiacao_partidaria,
            VinculoSection.OUTROS_CONTATOS: front_outros_contatos,
            VinculoSection.PAIS: front_pais,
            VinculoSection.DOACOES_ELEITORAIS_ENVIADAS: front_doacoes_enviadas,
            VinculoSection.DOACOES_ELEITORAIS_RECEBIDAS: front_doacoes_recebidas,
            VinculoSection.FORNECIMENTOS_ELEITORAIS_ENVIADAS: front_fornecimentos_enviados,
            VinculoSection.FORNECIMENTOS_ELEITORAIS_RECEBIDAS: front_fornecimentos_recebidos,
            VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS: front_p_pessoas_relacionadas,
            VinculoSection.POSSIVEIS_EMPRESAS_RELACIONADAS: front_empresas_relacionadas,
            VinculoSection.IMAGENS: front_imagens,
            VinculoSection.NOMES_USUARIO: front_nomes_usuario,
            VinculoSection.OUTRAS_URLS: front_outras_urls,
            VinculoSection.REDES_SOCIAIS: front_redes_sociais,
            VinculoSection.RELACOES: front_vinculo_relacoes,
            VinculoSection.DADOS_PESSOAIS: front_vinculo_pessoais,

        }[section]

