from sqlalchemy import Column, Text, ForeignKey, PrimaryKeyConstraint, Index
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID

from models.base import Base


class UserColumnsHmac(Base):
    __tablename__ = 'user_columns_hmac'
    __table_args__ = (
        PrimaryKeyConstraint("hmac", "column_name", "user_reports_id"),
        Index("idx_user_columns_hmac_user_reports_id", "user_reports_id"),  # Add this index
        {"schema": "reports"}
    )

    hmac = Column(Text, nullable=False)
    column_name = Column(Text, nullable=False)
    user_reports_id = Column(PostgresUUID, ForeignKey("reports.user_reports.user_reports_id"), nullable=False, index=True)

    def __repr__(self):
        return f"<UserColumnsHmac(hmac={self.hmac}, column_name={self.column_name}, user_reports_id={self.user_reports_id})>"
