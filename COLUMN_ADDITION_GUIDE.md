# Column Addition Guide

## Overview

When you add new columns to existing tables, they **will NOT be automatically created** in the database. You must follow a specific migration process to safely add columns to your tables in the `reports` schema.

## ⚠️ Important: Current Development vs Production

### Development Environment
- Currently uses `Base.metadata.create_all()` in `database/db.py`
- This creates tables from scratch if they don't exist
- **Does NOT add new columns to existing tables**
- **You must use Alembic migrations for column changes**

### Production Environment
- Should ONLY use Alembic migrations
- Never use `create_all()` in production
- All schema changes must be versioned and tracked

## 🔄 Column Addition Workflow

### Step 1: Modify Your SQLAlchemy Model

First, add the new column to your model:

```python
# Example: Adding 'status' column to users table
# File: models/user_model.py

class Users(Base):
    __tablename__ = 'users'
    __table_args__ = (
        # ... existing indexes ...
        {"schema": "reports"}
    )

    # ... existing columns ...

    # ✅ NEW COLUMN - Add here
    status = Column(Text, nullable=False, server_default='active')  # Note: provides default

    # OR for nullable columns
    description = Column(Text, nullable=True)  # Can be null, no default needed
```

### Step 2: Generate Migration Automatically

Alembic can auto-detect your model changes:

```bash
# Navigate to backend directory
cd apps/backend

# Generate migration with auto-detection
alembic revision --autogenerate -m "Add status column to users table"

# This creates a new file like: alembic/versions/002_add_status_column.py
```

### Step 3: Review the Generated Migration

**Always review the auto-generated migration before applying it:**

```python
# Example generated migration (review and customize if needed)
"""Add status column to users table

Revision ID: 002
Revises: 001
Create Date: 2025-09-25 11:00:00.000000
"""
from alembic import op
import sqlalchemy as sa

revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None

def upgrade() -> None:
    # Alembic auto-generated this:
    op.add_column('users',
        sa.Column('status', sa.Text(), server_default='active', nullable=False),
        schema='reports'
    )

def downgrade() -> None:
    # Alembic auto-generated this:
    op.drop_column('users', 'status', schema='reports')
```

### Step 4: Apply the Migration

```bash
# Apply the migration
alembic upgrade head

# Check migration status
alembic current
alembic history --verbose
```

### Step 5: Verify in Database

```sql
-- Connect to your database and verify
\d reports.users  -- Should show the new column
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_schema = 'reports' AND table_name = 'users'
ORDER BY ordinal_position;
```

## 🎯 Column Addition Patterns

### Pattern 1: Nullable Column (Safest)

```python
# Model change
new_field = Column(Text, nullable=True)

# Generated migration - safe for existing data
op.add_column('table_name',
    sa.Column('new_field', sa.Text(), nullable=True),
    schema='reports'
)
```

### Pattern 2: Non-Nullable with Default

```python
# Model change - MUST provide server_default
status = Column(Text, nullable=False, server_default='active')

# Generated migration
op.add_column('table_name',
    sa.Column('status', sa.Text(), server_default='active', nullable=False),
    schema='reports'
)
```

### Pattern 3: Foreign Key Column

```python
# Model change
category_id = Column(UUID, ForeignKey("reports.categories.category_id"), nullable=True)

# Generated migration
op.add_column('table_name',
    sa.Column('category_id', postgresql.UUID(), nullable=True),
    schema='reports'
)
op.create_foreign_key(
    'fk_table_name_category_id', 'table_name', 'categories',
    ['category_id'], ['category_id'],
    source_schema='reports', referent_schema='reports'
)
```

### Pattern 4: JSONB Column with Index

```python
# Model change
metadata = Column(JSONB, nullable=True)

# In __table_args__, add:
Index("idx_table_name_metadata", "metadata", postgresql_using="gin"),

# Generated migration will include both column and index
op.add_column('table_name',
    sa.Column('metadata', postgresql.JSONB(), nullable=True),
    schema='reports'
)
op.create_index('idx_table_name_metadata', 'table_name', ['metadata'],
                unique=False, schema='reports', postgresql_using='gin')
```

## ⚠️ Common Pitfalls and Solutions

### Pitfall 1: Non-Nullable Column Without Default

```python
# ❌ WRONG - This will fail if table has existing data
new_field = Column(Text, nullable=False)  # No default provided

# ✅ CORRECT - Provide a default value
new_field = Column(Text, nullable=False, server_default='default_value')

# ✅ ALTERNATIVE - Make it nullable initially, then update and make non-nullable
# Step 1: Add as nullable
new_field = Column(Text, nullable=True)
# Step 2: Later migration to populate values and make non-nullable
```

### Pitfall 2: Wrong Schema in Migration

```python
# ❌ WRONG - Missing schema specification
op.add_column('users', sa.Column('status', sa.Text()))

# ✅ CORRECT - Always specify schema='reports'
op.add_column('users', sa.Column('status', sa.Text()), schema='reports')
```

### Pitfall 3: Forgetting Index for Performance

```python
# If adding foreign keys or frequently queried columns:

# ✅ Add index in model
Index("idx_table_name_new_fk", "new_fk_column"),

# ✅ Or add manually in migration
op.create_index('idx_table_name_new_fk', 'table_name', ['new_fk_column'],
                unique=False, schema='reports')
```

## 🔧 Helper Commands

### Check Migration Status
```bash
# See current migration version
alembic current

# See migration history
alembic history --verbose

# See pending migrations
alembic show head
```

### Test Migrations
```bash
# Apply migration
alembic upgrade head

# Test rollback
alembic downgrade -1

# Reapply
alembic upgrade head
```

### Manual Migration Creation
```bash
# If auto-generate doesn't work properly
alembic revision -m "Manual migration description"
# Then edit the generated file manually
```

## 🎭 Example: Complete Column Addition

Let's say you want to add a `last_activity` timestamp to the `users` table:

### 1. Modify the Model

```python
# models/user_model.py
class Users(Base):
    # ... existing columns ...

    # ✅ Add new column
    last_activity = Column(
        DateTime(timezone=True),
        nullable=True,  # Nullable since existing users won't have this value
        index=True  # Add index for queries
    )
```

### 2. Update __table_args__ for Index

```python
class Users(Base):
    __table_args__ = (
        # ... existing indexes ...
        Index("idx_users_last_activity", "last_activity"),  # ✅ Add index
        {"schema": "reports"}
    )
```

### 3. Generate Migration

```bash
alembic revision --autogenerate -m "Add last_activity to users"
```

### 4. Review Generated Migration

```python
def upgrade() -> None:
    op.add_column('users',
        sa.Column('last_activity', sa.DateTime(timezone=True), nullable=True),
        schema='reports'
    )
    op.create_index('idx_users_last_activity', 'users', ['last_activity'],
                    unique=False, schema='reports')

def downgrade() -> None:
    op.drop_index('idx_users_last_activity', table_name='users', schema='reports')
    op.drop_column('users', 'last_activity', schema='reports')
```

### 5. Apply Migration

```bash
alembic upgrade head
```

### 6. Update Pydantic Schemas (if needed)

```python
# schemas/user_schema.py
class UserResponse(UserInDB):
    last_activity: Optional[datetime] = None  # ✅ Add to response schema
```

## 🚨 Emergency: Rollback Column Addition

If something goes wrong:

```bash
# Rollback to previous migration
alembic downgrade -1

# Or rollback to specific revision
alembic downgrade 001

# Check what changed
alembic history --verbose
```

## 🔍 Validation After Column Addition

Use the schema validator to check your changes:

```python
from database.schema_validator import SchemaValidator
from database.db import engine

validator = SchemaValidator(engine)
result = validator.validate_table("users")  # Replace with your table

if result.is_valid:
    print("✅ Column addition successful!")
else:
    print("❌ Issues found:")
    for error in result.errors:
        print(f"  - {error}")
```

## 📋 Column Addition Checklist

### Before Making Changes:
- [ ] Plan the column type and constraints
- [ ] Decide if nullable or provide default value
- [ ] Consider indexing requirements
- [ ] Check impact on existing queries
- [ ] Plan migration strategy for large tables

### During Implementation:
- [ ] Modify SQLAlchemy model
- [ ] Add index to `__table_args__` if needed
- [ ] Generate migration with `--autogenerate`
- [ ] Review generated migration carefully
- [ ] Test migration up and down

### After Implementation:
- [ ] Apply migration to development
- [ ] Verify column exists in database
- [ ] Test application functionality
- [ ] Update Pydantic schemas if needed
- [ ] Update API documentation if exposing column
- [ ] Plan production deployment

## 💡 Pro Tips

1. **Always use defaults for non-nullable columns** to avoid issues with existing data
2. **Test migrations on a copy of production data** before applying to production
3. **Keep migrations small and focused** - one column addition per migration is often better
4. **Document complex migrations** with comments explaining the reasoning
5. **Consider performance impact** - adding indexed columns to large tables can be slow

Remember: **Column additions are NOT automatic** - they require deliberate migration steps to ensure data integrity and system stability.