import path from 'path';
import fs from 'fs';

/**
 * Determines the correct assets path based on the environment.
 * Checks for the existence of the assets directory in multiple possible paths.
 * @returns The path to the assets directory
 */
export const determineAssetsPath = (): string => {
  const possiblePaths = [
    path.join(__dirname, '../assets'),           // Development
    path.join(__dirname, '../dist/assets'),      // Production build
    path.join(__dirname, './assets'),            // Same directory
    path.join(process.cwd(), 'assets'),          // Working directory
    path.join(process.cwd(), 'dist/assets'),     // Working directory dist
  ];
  
  for (const assetPath of possiblePaths) {
    if (fs.existsSync(assetPath)) {
      console.log(`Assets found at: ${assetPath}`);
      return assetPath;
    }
  }
  
  console.error('Assets directory not found in any of the expected paths:', possiblePaths);
  return possiblePaths[0]; // Fallback to dev path
};