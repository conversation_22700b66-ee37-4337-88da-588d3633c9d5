import React, { useMemo, useCallback } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { formatFieldValue, parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import {
  PossivelContato,
  PossiveisContatosSection,
  TestFunctions,
  TestFunction,
  SectionTestFunction,
  CalculateFunction,
  UpdaterFunction,
  ArrayKey
} from "../../model/PossiveisContatos";
import { ItemSeparator } from "../components/ItemSeparator";
import { useNestedRender } from "./NestedRenderContext";

export function useRenderPossiveisContatos(
  sectionTitle: string
): ArrayRenderStrategy<PossivelContato> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const includeKey = useCallback((isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted, [isTrash]);

  const testFunctions = useMemo((): TestFunctions => ({
    nomeCompleto: (e: PossivelContato) => e.nome_completo?.is_deleted === true,
    razaoSocial: (e: PossivelContato) => e.razao_social?.is_deleted === true,
    detalhes: (e: PossivelContato) => e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted === true)
      : false,
    pessoa: (e: PossivelContato) => e.pessoa
      ? e.pessoa.every((p: any) =>
        Object.values(p.value || {}).every((v: any) => v.is_deleted === true))
      : true,
    telefones: (e: PossivelContato) => e.telefones
      ? e.telefones.every((t: any) =>
        Object.values(t.value || {}).every((v: any) => v.is_deleted === true))
      : true,
    enderecos: (e: PossivelContato) => e.enderecos
      ? e.enderecos.every((end: any) =>
        Object.values(end.value || {}).every((v: any) => v.is_deleted === true))
      : true,
  }), []);

  const testEntryDeleted: TestFunction = useCallback((entry: PossivelContato): boolean => {
    const isNomeCompletoDeleted = testFunctions.nomeCompleto(entry);
    const isRazaoSocialDeleted = testFunctions.razaoSocial(entry);
    const areDetalhesDeleted = testFunctions.detalhes(entry);
    const arePessoaDeleted = testFunctions.pessoa(entry);
    const areTelefonesDeleted = testFunctions.telefones(entry);
    const areEnderecosDeleted = testFunctions.enderecos(entry);

    return (isNomeCompletoDeleted || isRazaoSocialDeleted) && areDetalhesDeleted && arePessoaDeleted && areTelefonesDeleted && areEnderecosDeleted;
  }, [testFunctions]);

  const testSectionDeleted: SectionTestFunction = useCallback((section: PossiveisContatosSection): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted),
    [testEntryDeleted]);

  const calculateDataCount: CalculateFunction = useCallback((section: PossiveisContatosSection): number => {
    if (!Array.isArray(section.data)) return 0;
    return section.data.reduce((count: number, entry: PossivelContato) => {
      return testEntryDeleted(entry) ? count : count + 1;
    }, 0);
  }, [testEntryDeleted]);

  const applyCascadingDeletion: (entry: PossivelContato, targetDeletedState: boolean) => void = useCallback((entry: PossivelContato, targetDeletedState: boolean) => {
    if (entry.detalhes) {
      Object.values(entry.detalhes).forEach((detalhe: any) => {
        if (detalhe) detalhe.is_deleted = targetDeletedState;
      });
    }

    (['pessoa', 'telefones', 'enderecos'] as const).forEach(key => {
      const field = entry[key];
      if (Array.isArray(field)) {
        field.forEach((item: any) => {
          item.is_deleted = targetDeletedState;
          if (item.value) {
            Object.values(item.value).forEach((campo: any) => {
              if (campo) campo.is_deleted = targetDeletedState;
            });
          }
        });
      }
    });
  }, []);

  const shouldShowEntry: TestFunction = useCallback((entry: PossivelContato): boolean => {
    const hasDeletedContent = (field: any, isArray = false): boolean => {
      if (!field) return false;
      if (isArray && Array.isArray(field)) {
        return field.some((item: any) =>
          item.is_deleted === true || Object.values(item.value || {}).some((v: any) => v.is_deleted === true)
        );
      }
      if (!isArray && typeof field === 'object') {
        return 'is_deleted' in field ? field.is_deleted === true :
          Object.values(field).some((v: any) => v.is_deleted === true);
      }
      return false;
    };

    const hasNonDeletedContent = (field: any, isArray = false): boolean => {
      if (!field) return false;
      if (isArray && Array.isArray(field)) {
        return field.some((item: any) =>
          item.is_deleted !== true || Object.values(item.value || {}).some((v: any) => v.is_deleted !== true)
        );
      }
      if (!isArray && typeof field === 'object') {
        return 'is_deleted' in field ? field.is_deleted !== true :
          Object.values(field).some((v: any) => v.is_deleted !== true);
      }
      return false;
    };

    const checkFunction = isTrash ? hasDeletedContent : hasNonDeletedContent;

    return checkFunction(entry.nome_completo) ||
      checkFunction(entry.razao_social) ||
      checkFunction(entry.detalhes) ||
      checkFunction(entry.pessoa, true) ||
      checkFunction(entry.telefones, true) ||
      checkFunction(entry.enderecos, true);
  }, [isTrash]);

  const shouldIncludeNestedBlock = useCallback((item: any) => {
    const vals = Object.values(item.value || {});
    return isTrash
      ? vals.some((v: any) => v.is_deleted === true)
      : vals.some((v: any) => v.is_deleted === false);
  }, [isTrash]);

  const shouldIncludeList = useCallback((arrayItems: any[]) => {
    if (!Array.isArray(arrayItems)) return false;
    return arrayItems.some((item: any) => shouldIncludeNestedBlock(item));
  }, [shouldIncludeNestedBlock]);

  const updateEntries = useCallback((
    updater: UpdaterFunction,
    testDeleted: TestFunction = testEntryDeleted,
    testSectionDel: SectionTestFunction = testSectionDeleted,
    calcDataCount: CalculateFunction = calculateDataCount
  ) => {
    actions.updateSectionEntries?.(
      sectionTitle,
      updater,
      testDeleted as (entry: Record<string, any>) => boolean,
      testSectionDel as (section: Record<string, any>) => boolean,
      calcDataCount as (section: Record<string, any>) => number
    );
  }, [actions, sectionTitle, testEntryDeleted, testSectionDeleted, calculateDataCount]);

  const calculateNestedDataCount = useCallback((nestedSection: PossiveisContatosSection): number => {
    if (!Array.isArray(nestedSection.data)) return 0;
    return nestedSection.data.reduce((count: number, entry: PossivelContato) => {
      const isEntryDeleted: boolean = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  }, [testEntryDeleted]);

  const NomeCompletoBlock = React.memo(({
    entry,
    index,
    sectionTitle
  }: {
    entry: PossivelContato;
    index: number;
    sectionTitle: string;
  }) => {
    const nested = useNestedRender();

    if (!entry?.nome_completo || !includeKey(entry.nome_completo.is_deleted || false)) return null;

    const handleToggleNomeCompleto = useCallback(() => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: PossiveisContatosSection) => {
          const targetEntry = nestedSection.data?.[index];
          if (targetEntry?.nome_completo) {
            targetEntry.nome_completo.is_deleted = !targetEntry.nome_completo.is_deleted;
            applyCascadingDeletion(targetEntry, targetEntry.nome_completo.is_deleted);
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: PossivelContato, i?: number) => {
        if (i === index && e.nome_completo) {
          e.nome_completo.is_deleted = !e.nome_completo.is_deleted;
          applyCascadingDeletion(e, e.nome_completo.is_deleted);
        }
      });
    }, [nested, index, updateEntries, applyCascadingDeletion]);

    return (
      <CustomGridContainer cols={1} key={`nome-completo-${index}`}>
        <CustomGridItem
          cols={1}
          className="mb-4"
          fullWidth
          onToggleField={handleToggleNomeCompleto}
        >
          <CustomReadOnlyInputField
            label={entry.nome_completo.label.toUpperCase()}
            colorClass="bg-primary"
            labelTextClass="text-accent"
            value={parseValue(formatFieldValue(entry.nome_completo.value))}
            tooltip={renderSourceTooltip(entry.nome_completo.source)}
          />
        </CustomGridItem>
      </CustomGridContainer>
    );
  });

  const RazaoSocialBlock = React.memo(({
    entry,
    index,
    sectionTitle
  }: {
    entry: PossivelContato;
    index: number;
    sectionTitle: string;
  }) => {
    const nested = useNestedRender();

    if (!entry?.razao_social || !includeKey(entry.razao_social.is_deleted || false)) return null;

    const handleToggleRazaoSocial = useCallback(() => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: PossiveisContatosSection) => {
          const targetEntry = nestedSection.data?.[index];
          if (targetEntry?.razao_social) {
            targetEntry.razao_social.is_deleted = !targetEntry.razao_social.is_deleted;
            applyCascadingDeletion(targetEntry, targetEntry.razao_social.is_deleted);
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: PossivelContato, i?: number) => {
        if (i === index && e.razao_social) {
          e.razao_social.is_deleted = !e.razao_social.is_deleted;
          applyCascadingDeletion(e, e.razao_social.is_deleted);
        }
      });
    }, [nested, index, updateEntries, applyCascadingDeletion]);

    return (
      <CustomGridContainer cols={1} key={`razao-social-${index}`}>
        <CustomGridItem
          cols={1}
          className="mb-6"
          fullWidth
          onToggleField={handleToggleRazaoSocial}
        >
          <CustomReadOnlyInputField
            label={entry.razao_social.label.toUpperCase()}
            colorClass="bg-primary"
            labelTextClass="text-accent"
            value={parseValue(formatFieldValue(entry.razao_social.value))}
            tooltip={renderSourceTooltip(entry.razao_social.source)}
            className="border-0 pb-0"
          />
        </CustomGridItem>
      </CustomGridContainer>
    );
  });

  const DetalhesBlock = React.memo(({
    entry,
    index,
    sectionTitle
  }: {
    entry: PossivelContato;
    index: number;
    sectionTitle: string;
  }) => {
    const nested = useNestedRender();

    if (!entry?.detalhes) return null;
    const subs = Object.entries(entry.detalhes).filter(([, v]) =>
      includeKey((v as any).is_deleted)
    );
    if (!subs.length) return null;

    const handleToggleDetalhe = useCallback((fieldKey: string) => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: PossiveisContatosSection) => {
          const targetEntry = nestedSection.data?.[index];
          const d = targetEntry?.detalhes?.[fieldKey];
          if (d) d.is_deleted = !d.is_deleted;
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: PossivelContato, i?: number) => {
        if (i === index) {
          const d = e.detalhes?.[fieldKey];
          if (d) d.is_deleted = !d.is_deleted;
        }
      });
    }, [nested, index, updateEntries]);

    return (
      <CustomGridContainer
        cols={2}
        columnFirst
        key={`detalhes-${index}`}
        className="mb-6"
      >
        {subs.map(([fieldKey, val]) => (
          <CustomGridItem
            key={fieldKey}
            cols={1}
            onToggleField={() => handleToggleDetalhe(fieldKey)}
          >
            <CustomReadOnlyInputField
              label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
              value={parseValue(formatFieldValue((val as any).value))}
              tooltip={renderSourceTooltip((val as any).source)}
            />
          </CustomGridItem>
        ))}
      </CustomGridContainer>
    );
  });

  const ArrayBlock = React.memo(({
    entry,
    index,
    sectionTitle,
    arrayKey,
    blockType,
  }: {
    entry: PossivelContato;
    index: number;
    sectionTitle: string;
    arrayKey: 'pessoa' | 'telefones' | 'enderecos';
    blockType: string;
  }) => {
    const mode = useReportMode();
    const isTrash = mode === "trash";
    const nested = useNestedRender();

    const arrayData = entry[arrayKey];
    if (!arrayData?.length || !shouldIncludeList(arrayData)) return null;

    const blocks = arrayData
      .map((item: any, i: number) => ({ bloco: item, idx: i }))
      .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

    if (!blocks.length) return null;

    const handleToggleListTitle = useCallback(() => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: PossiveisContatosSection) => {
          const targetEntry = nestedSection.data?.[index];
          const arrayItems = targetEntry?.[arrayKey];
          if (arrayItems && Array.isArray(arrayItems)) {
            const targetDeletedState = isTrash ? false : true;
            arrayItems.forEach((item: any) => {
              item.is_deleted = targetDeletedState;
              if (item.value) {
                Object.values(item.value).forEach((campo: any) => {
                  if (campo) campo.is_deleted = targetDeletedState;
                });
              }
            });
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: PossivelContato, i?: number) => {
        if (i === index) {
          const arrayItems = e[arrayKey];
          if (arrayItems && Array.isArray(arrayItems)) {
            const targetDeletedState = isTrash ? false : true;
            arrayItems.forEach((item: any) => {
              item.is_deleted = targetDeletedState;
              if (item.value) {
                Object.values(item.value).forEach((campo: any) => {
                  if (campo) campo.is_deleted = targetDeletedState;
                });
              }
            });
          }
        }
      });
    }, [nested, sectionTitle, index, arrayKey, isTrash]);

    const labelMap: Record<string, string> = {
      pessoa: "PESSOAS",
      telefones: "TELEFONES",
      enderecos: "ENDEREÇOS"
    };

    return (
      <CustomGridContainer cols={1} key={`${arrayKey}-${index}`} className="mb-4">
        <CustomGridItem
          cols={1}
          containerClassName="w-fit pr-12"
          onToggleField={handleToggleListTitle}
        >
          <ReportsCustomLabel
            label={labelMap[arrayKey]}
            colorClass="bg-primary"
          />
        </CustomGridItem>
        {arrayKey === 'telefones' ? (
          <CustomGridContainer cols={2}>
            {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
              <NestedArrayBlock
                key={`${arrayKey}-${index}-${origIdx}`}
                sectionTitle={sectionTitle}
                entryIdx={index}
                arrayKey={arrayKey}
                origIdx={origIdx}
                bloco={bloco}
                blockRenderIdx={blockRenderIdx}
                blockType={blockType}
              />
            ))}
          </CustomGridContainer>
        ) : (
          blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
            <NestedArrayBlock
              key={`${arrayKey}-${index}-${origIdx}`}
              sectionTitle={sectionTitle}
              entryIdx={index}
              arrayKey={arrayKey}
              origIdx={origIdx}
              bloco={bloco}
              blockRenderIdx={blockRenderIdx}
              blockType={blockType}
            />
          ))
        )}
      </CustomGridContainer>
    );
  });

  const NestedArrayBlock = React.memo(({
    sectionTitle,
    entryIdx,
    arrayKey,
    origIdx,
    bloco,
    blockRenderIdx,
    blockType
  }: {
    sectionTitle: string;
    entryIdx: number;
    arrayKey: string;
    origIdx: number;
    bloco: any;
    blockRenderIdx: number;
    blockType: string;
  }) => {
    const mode = useReportMode();
    const isTrash = mode === "trash";
    const nested = useNestedRender();

    const handleToggleBlock = useCallback(() => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: PossiveisContatosSection) => {
          const entry = nestedSection.data?.[entryIdx];
          const item = entry?.[arrayKey as ArrayKey]?.[origIdx];
          if (item?.value) {
            const targetDeletedState = isTrash ? false : true;
            item.is_deleted = targetDeletedState;
            Object.values(item.value).forEach((campo: any) => {
              if (campo) campo.is_deleted = targetDeletedState;
            });
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: PossivelContato, i?: number) => {
        if (i === entryIdx) {
          const item = e[arrayKey as ArrayKey]?.[origIdx];
          if (item?.value) {
            const targetDeletedState = isTrash ? false : true;
            item.is_deleted = targetDeletedState;
            Object.values(item.value).forEach((campo: any) => {
              if (campo) campo.is_deleted = targetDeletedState;
            });
          }
        }
      });
    }, [nested, sectionTitle, entryIdx, arrayKey, origIdx, isTrash]);

    const handleToggleField = useCallback((fieldKey: string) => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: PossiveisContatosSection) => {
          const entry = nestedSection.data?.[entryIdx];
          const item = entry?.[arrayKey as ArrayKey]?.[origIdx];
          if (item?.value?.[fieldKey]) {
            item.value[fieldKey].is_deleted = !item.value[fieldKey].is_deleted;
            item.is_deleted = Object.values(item.value).every((c: any) => c.is_deleted === true);
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: PossivelContato, i?: number) => {
        if (i === entryIdx) {
          const item = e[arrayKey as ArrayKey]?.[origIdx];
          if (item?.value?.[fieldKey]) {
            item.value[fieldKey].is_deleted = !item.value[fieldKey].is_deleted;
            item.is_deleted = Object.values(item.value).every((c: any) => c.is_deleted === true);
          }
        }
      });
    }, [nested, sectionTitle, entryIdx, arrayKey, origIdx]);

    return (
      <CustomGridContainer cols={1} key={`${arrayKey}-${origIdx}`} className="mb-4">
        <CustomGridItem
          cols={1}
          containerClassName="w-fit pr-12"
          onToggleField={handleToggleBlock}
        >
          <ReportsCustomLabel
            label={`${blockType.toUpperCase()} ${!isTrash ? blockRenderIdx + 1 : ""}`}
            colorClass="bg-primary"
          />
        </CustomGridItem>
        <div className="pl-5">
          <CustomGridContainer cols={blockType === "TELEFONE" ? 1 : 2}>
            {Object.entries(bloco.value)
              .filter(([_, v]) => (isTrash ? (v as any).is_deleted : !(v as any).is_deleted))
              .map(([fieldKey, fieldValue ]: any, index) => (
                <CustomGridItem key={`${arrayKey}-${origIdx}-${fieldKey}`} cols={1} className="py-1" onToggleField={() => handleToggleField(fieldKey)}>
                  <CustomReadOnlyInputField
                    label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                    colorClass="bg-border"
                    isFirstLabelList={index === 0}
                    icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                    value={parseValue(formatFieldValue(fieldValue.value))}
                    tooltip={renderSourceTooltip(fieldValue.source)}
                  />
                </CustomGridItem>
              ))}
          </CustomGridContainer>
        </div>
      </CustomGridContainer>
    );
  });

  const formatByKey: Record<
    string,
    (entry?: PossivelContato, index?: number) => React.ReactElement | null
  > = {
    nome_completo: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <NomeCompletoBlock entry={entry} index={index} sectionTitle={sectionTitle} />;
    },

    razao_social: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <RazaoSocialBlock entry={entry} index={index} sectionTitle={sectionTitle} />;
    },

    detalhes: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <DetalhesBlock entry={entry} index={index} sectionTitle={sectionTitle} />;
    },

    pessoa: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <ArrayBlock entry={entry} index={index} sectionTitle={sectionTitle} arrayKey="pessoa" blockType="PESSOA" />;
    },

    telefones: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <ArrayBlock entry={entry} index={index} sectionTitle={sectionTitle} arrayKey="telefones" blockType="TELEFONE" />;
    },

    enderecos: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <ArrayBlock entry={entry} index={index} sectionTitle={sectionTitle} arrayKey="enderecos" blockType="ENDEREÇO" />;
    },
  };

  const validateKeys = (keys: Array<keyof PossivelContato>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: PossivelContato, index?: number): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof PossivelContato>;

    if (!validateKeys(keys)) {
      console.warn(`[Seção ${sectionTitle}] Chaves inválidas:`, keys);
    }

    const orderedKeys: Array<keyof PossivelContato> = [
      'nome_completo',
      'razao_social',
      'detalhes',
      'pessoa',
      'telefones',
      'enderecos'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    const elements: React.ReactElement[] = [];

    filteredKeys.forEach((chave) => {
      const element = formatByKey[chave]?.(entry, index);
      if (element) elements.push(element);
    });

    return elements;
  };

  const render = (dataArray: PossivelContato[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn(`[Seção ${sectionTitle}] esperava array, mas recebeu:`, typeof dataArray);
      return [];
    }

    const filteredData = dataArray
      .map((entry, originalIndex) => ({ entry, originalIndex }))
      .filter(({ entry }) => shouldShowEntry(entry));

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach(({ entry, originalIndex }, filterIndex) => {
      const elements = renderSingleItem(entry, originalIndex);

      if (filteredData.length > 1) {
        const isLastItem = filterIndex === filteredData.length - 1;
        allElements.push(
          <div
            key={`possivel-contato-${originalIndex}`}
            className={`relative mb-8 pb-1 ${isLastItem ? '!mb-0 !pb-0' : ''}`}
          >
            {elements}
            {!isLastItem && <ItemSeparator />}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      ((entry: PossivelContato) => {
        if (entry.nome_completo) {
          entry.nome_completo.is_deleted = targetDeletedState;
        }
        if (entry.razao_social) {
          entry.razao_social.is_deleted = targetDeletedState;
        }
        applyCascadingDeletion(entry, targetDeletedState);
      }) as UpdaterFunction,
      testEntryDeleted as (entry: Record<string, any>) => boolean,
      testSectionDeleted as (section: Record<string, any>) => boolean,
      calculateDataCount as (section: Record<string, any>) => number
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted: testEntryDeleted as TestFunction,
    testSectionDeleted: testSectionDeleted as SectionTestFunction,
    deleteSectionEntries,
    calculateDataCount,
  } as ArrayRenderStrategy<PossivelContato> & { calculateDataCount: typeof calculateDataCount };
}