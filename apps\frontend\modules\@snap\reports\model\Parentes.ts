import { ValueWithSource } from "./ValueWithSource";
import { _Endereco } from "./Enderecos";
import { _Telefone } from "./Telefones";
import { ReportSection } from "../global";

export interface Parente {
    parentesco: ValueWithSource;
    detalhes: Record<
        string,
        ValueWithSource<string>
    >;
    pessoa?: Array<ValueWithSource>;
    telefones?: Array<ValueWithSource<_Telefone>>;
    enderecos?: Array<ValueWithSource<_Endereco>>;
}

export interface ParentesSection extends ReportSection {
    data: Parente[];
}

export interface TestFunctions {
    parentesco: (entry: Parente) => boolean;
    detalhes: (entry: Parente) => boolean;
    pessoa: (entry: Parente) => boolean;
    telefones: (entry: Parente) => boolean;
    enderecos: (entry: Parente) => boolean;
}

export type ArrayKey = 'pessoa' | 'telefones' | 'enderecos';
export type UpdaterFunction = (entry: Parente, index?: number) => void;
export type TestFunction = (entry: Parente) => boolean;
export type SectionTestFunction = (section: ParentesSection) => boolean;
export type CalculateFunction = (section: ParentesSection) => number;