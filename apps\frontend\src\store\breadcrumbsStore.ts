import { create } from "zustand";
import { BreadcrumbItem } from "~/types/global";

interface BreadcrumbsState {
  items: BreadcrumbItem[];
  directNavigationMode: boolean;
  actions: {
    setItems: (items: BreadcrumbItem[]) => void;
    addItem: (item: BreadcrumbItem) => void;
    removeItem: (index: number) => void;
    clearItems: () => void;
    setDirectNavigationMode: (isDirect: boolean) => void;
  };
}

const useBreadcrumbsStore = create<BreadcrumbsState>((set) => ({
  items: [],
  directNavigationMode: false,
  actions: {
    setItems: (items: BreadcrumbItem[]) => set({ items }),
    addItem: (item: BreadcrumbItem) =>
      set((state) => ({ items: [...state.items, item] })),
    removeItem: (index: number) =>
      set((state) => ({
        items: state.items.filter((_, idx) => idx !== index)
      })),
    clearItems: () => set({ items: [] }),
    setDirectNavigationMode: (isDirect: boolean) =>
      set({ directNavigationMode: isDirect }),
  },
}));

export const useBreadcrumbs = () =>
  useBreadcrumbsStore((state) => state.items);
export const useDirectNavigationMode = () =>
  useBreadcrumbsStore((state) => state.directNavigationMode);
export const useBreadcrumbsActions = () =>
  useBreadcrumbsStore((state) => state.actions);
export default useBreadcrumbsStore;
