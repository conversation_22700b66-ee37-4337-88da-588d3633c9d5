import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { getFieldLabel, getFieldValue, getSingular, translatePropToLabel, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintNomeUsuariosProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      detalhes?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
    }>;
  };
}

export const renderNomeUsuarios = ({ section }: RenderPrintNomeUsuariosProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  const allUsernames = section.data?.flatMap(entry =>
    entry.detalhes?.filter((detalhe: any) => !detalhe.is_deleted) || []
  ) || [];

  if (!allUsernames.length) {
    return { children };
  }

  const rows: TableRow[] = [];
  let cells: TableCell[] = [];

  allUsernames.forEach((detalhe, index) => {
    const validFields = Object.entries(detalhe.value).filter(([_, field]) => !field.is_deleted);
    if (validFields.length === 0) return;

    const cellParagraphs = [
        new Paragraph({children: [new TextRun({text: `${translatePropToLabel(getSingular(detalhe.label) || 'NOME DE USUÁRIO').toUpperCase()} ${index + 1}`, bold: true})]})];

    validFields.forEach(([key, fieldValue]) => {
        cellParagraphs.push(new Paragraph(`${translatePropToLabel(getFieldLabel(key, fieldValue)).toUpperCase()}: ${String(getFieldValue(fieldValue) || "")}`))
    });

    cells.push(new TableCell({children: cellParagraphs}));
    if(cells.length === 2) {
        rows.push(new TableRow({children: cells}));
        cells = [];
    }
  });

  if (cells.length > 0) {
    rows.push(new TableRow({ children: [...cells, new TableCell({ children: [new Paragraph("")] })] }));
  }

  if(rows.length > 0) {
      children.push(new Table({rows, width: {size: 100, type: WidthType.PERCENTAGE}, columnWidths: [4500, 4500]}));
  }

  return { children };
};
