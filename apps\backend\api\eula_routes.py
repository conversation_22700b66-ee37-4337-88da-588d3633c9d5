import logging
from fastapi import (APIRouter, Depends)
from sqlalchemy.ext.asyncio import AsyncSession

from database.db import get_db

from services.auth_service import auth_guard
from services.eula_service import EulaService

from models.eula_model import Eula


from utils.jwt_utils import JWTUtils

from core.constants import Roles

from exceptions.business_exceptions import AuthorizationError, FailToAccessEulaError

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/get_eula")
async def get_eula_endpoint(
    user: dict = Depends(auth_guard),
    db: AsyncSession = Depends(get_db)):
    
    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    logger.info(f"Eula get request received for user: {user_id}")

    if Roles.get_eula not in user_roles:
        raise AuthorizationError("User does not have get eula permission.")

    try:
        eula_service = EulaService(db=db, user_id=user_id)
        eula = await eula_service.get_eula()

        if eula is None:
            logger.warning(f"No active EULA found for user: {user_id}")
            return {
                "version": None,
                "content": None,
                "date": None
            }

        # Add defensive check to ensure eula is a proper object
        if not hasattr(eula, 'version') or not hasattr(eula, 'content') or not hasattr(eula, 'created_at'):
            logger.error(f"EULA object missing required attributes. Object: {eula}")
            logger.error(f"Object type: {type(eula)}, Available attributes: {dir(eula) if hasattr(eula, '__dict__') else 'No __dict__'}")
            raise FailToAccessEulaError(f"EULA object is malformed: {eula}")

        eula_dict = {
            "version": eula.version,
            "content": eula.content,
            "date": eula.created_at
        }

        logger.info(f"Successfully retrieved EULA version {eula.version} for user: {user_id}")
        return eula_dict
        
    except Exception as e:
        logger.error(f"Error retrieving EULA for user {user_id}: {str(e)}")
        logger.error(f"Exception type: {type(e)}")
        logger.error(f"Exception details: {e}", exc_info=True)
        raise FailToAccessEulaError(f"Error retrieving EULA for user {user_id}: {str(e)}")