import { CardFactory } from "~/containers/report/CardFactory";
import { ReportData, ReportMetadata, ReportSection } from "~/types/global";
import { FolderData } from "root/domain/entities/folder.model";
import { AnimatedFilledButton, Button, Icon, Text } from "@snap/design-system";
import { Pencil, Plus, Trash } from "lucide-react";
import MasonryLayout from "react-layout-masonry";
import { useEffect, useRef, useState } from "react";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { useReportListActions, useReportListLoadMore } from "~/store/reportListStore";
import EmptyList from "~/components/EmptyList";
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";
import { ReportModel } from "root/domain/entities/report.model";
import { FolderModel } from "root/domain/entities/folder.model";
import { toast } from "sonner";
import { useUserData } from "~/store/userStore";
import { base64ToUint8Array, useEncryption } from "~/hooks/useEncryption";
import { COLUMNS_BREAKPOINTS, REPORT_CONSTANTS, REPORT_SECTIONS, REPORT_TYPES } from "~/helpers/constants";
import { fetchReportById } from "~/services/gateways/report.gateway"
import { decryptReportPayload } from "~/helpers/encryption.helper"
import { createErrorHandler, getProfileImageFromSection } from "~/helpers";
import { useSecretKeyDialog } from "~/hooks/useSecretKeyDialog";
import { useNavigate, useParams } from "react-router";
import { useBreadcrumbsActions } from "~/store/breadcrumbsStore";
import { MdOutlineDriveFileMove } from "react-icons/md";
import { MoveFolderDialog } from "./MoveFolderDialog";
import { BiMerge } from "react-icons/bi";
import { MergeFoldersDialog } from "./MergeFoldersDialog";
import { useFolderCRUD } from "~/hooks/useFolderCRUD";
import { useDialogActions } from "~/store/dialogStore";
import { RenameFolderDialog } from "./RenameFolderDialog";
import { MoveReportToFolderDialog } from "./MoveReportToFolderDialog";
import { RenameReportDialog } from "./RenameReportDialog";
import { usePasswordCheck } from "~/hooks/usePasswordCheck";
import { usePassword } from "~/store/credentials";
import { MergeReportDialog } from "./MergeReportDialog";

interface ReportListProps {
  list: (ReportData | FolderData)[];
  isFetched: boolean;
  onNewReport: () => void;
}

export default function ReportsList({
  list,
  onNewReport,
  isFetched,
}: ReportListProps) {
  const { folderId } = useParams<{ folderId?: string }>();
  const { checkPermission } = usePermissionCheck();
  const canCreateReport = checkPermission(Permission.CREATE_REPORT);
  const { invalidateAllReports, generatePDFMutation, newReportMutation, deleteReportMutation, renameReportMutation } = useReportCRUD(folderId || null, false);
  const { deleteFolderMutation } = useFolderCRUD();
  const { openDialog, closeDialog } = useDialogActions();
  const { incrementPage } = useReportListActions();
  const loadMore = useReportListLoadMore();
  const [showLoadMore, setShowLoadMore] = useState(false);
  const [downloadingReports, setDownloadingReports] = useState<Set<string>>(new Set());
  const [deletingReports, setDeletingReports] = useState<Set<string>>(new Set());
  const [movingReports, setMovingReports] = useState<Set<string>>(new Set());
  const [retryingReports, setRetryingReports] = useState<Set<string>>(new Set());
  const [deletingFolders, setDeletingFolders] = useState<Set<string>>(new Set());
  const [movingFolders, setMovingFolders] = useState<Set<string>>(new Set());
  const [mergingFolders, setMergingFolders] = useState<Set<string>>(new Set());
  const [mergingReports, setMergingReports] = useState<Set<string>>(new Set());
  const sectionRef = useRef<HTMLElement>(null);
  const userData = useUserData();
  const organizationLogo = userData?.organization_logo as string;
  const shouldPrintSnapLogo = userData?.print_snap_logo as boolean;
  const { decryptData, _encryptData } = useEncryption();
  const navigate = useNavigate();
  const { resetPagination, addFolderToPath } = useReportListActions();
  const { addItem, setDirectNavigationMode } = useBreadcrumbsActions();
  const { openSecretKeyDialog } = useSecretKeyDialog();
  const { hasPasswordExpired } = usePasswordCheck();
  const secretKey = usePassword();
  const isUserAllowedToCombineReports = !!userData?.report_types.includes(REPORT_CONSTANTS.types.combinado);

  useEffect(() => {
    const section = sectionRef.current;
    if (section) {
      section.addEventListener("scroll", handleScroll);
      return () => section.removeEventListener("scroll", handleScroll);
    }
  }, [loadMore]);

  const loadMoreData = async () => {
    incrementPage()
  }

  const handleLoadMore = async () => {
    // SECRET KEY
    if (hasPasswordExpired) {
      openSecretKeyDialog(async () => {
        await loadMoreData();
        invalidateAllReports();
      });
    } else {
      await loadMoreData();
      invalidateAllReports();
    }
  };

  const handleScroll = () => {
    if (!sectionRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = sectionRef.current;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 100;
    const isAtTop = scrollTop === 0;
    setShowLoadMore(isAtBottom && !isAtTop);
  };

  const handleDownloadPDF = async (reportId: string) => {
    let toastId = null;
    try {
      setDownloadingReports(prev => new Set(prev).add(reportId));
      toastId = toast.loading("Gerando PDF...", {
        description: "Isso pode levar alguns minutos. Por favor, não feche esta aba até o download terminar.",
      });

      const reportDetailsData = await fetchReportById(reportId);
      const decryptedData = await decryptReportPayload(reportDetailsData as ReportData, decryptData);
      const { data: dataMap, ...meta } = decryptedData;
      const reportType = meta?.[REPORT_CONSTANTS.new_report.report_type] as string;
      const foundSections: ReportSection[] = dataMap?.[reportType as keyof typeof dataMap] || [];
      const filteredSections = foundSections.filter((sec) => sec.is_deleted !== true && sec.data_count > 0);
      const imagensSection = filteredSections.find(
        (section) => section.title === REPORT_SECTIONS.imagens
      );
      const profileImage = getProfileImageFromSection(imagensSection);

      const blobUrl = await generatePDFMutation.mutateAsync({
        sections: filteredSections,
        metadata: meta as ReportMetadata,
        profile_image: profileImage,
        should_print_snap_logo: shouldPrintSnapLogo,
        organization_logo: organizationLogo,
      });

      const reportName = meta?.[REPORT_CONSTANTS.new_report.report_name] as string || "relatorio";
      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = `${reportName}.pdf`;
      link.click();
      URL.revokeObjectURL(blobUrl);

      toast.dismiss(toastId);
      toast.success("PDF gerado com sucesso!");
      setDownloadingReports(prev => {
        const newSet = new Set(prev);
        newSet.delete(reportId);
        return newSet;
      });
    } catch (err) {
      console.error("Error in download process:", err);
      if (toastId) toast.dismiss(toastId);
      setDownloadingReports(prev => {
        const newSet = new Set(prev);
        newSet.delete(reportId);
        return newSet;
      });
      createErrorHandler(
        "Ocorreu um erro ao tentar gerar o PDF",
        "Erro ao gerar PDF"
      )(err);
    }
  };

  const handleOpenFolder = (folder: FolderModel) => {
    resetPagination();

    addFolderToPath({
      id: folder.id,
      name: folder.name
    });

    addItem({
      title: folder.name,
      onClick: () => navigate(`/pasta/${folder.id}`),
    });

    setDirectNavigationMode(false);

    navigate(`/pasta/${folder.id}`);
  };

  const handleMoveFolder = (folder: FolderModel) => {
    const handleOpenMoveFolderDialog = async () => {
      openDialog({
        title: "Mover pasta para outra pasta",
        icon: <MdOutlineDriveFileMove size={24} />,
        content: <MoveFolderDialog.Content currentFolderId={folder.parentId || null} folderId={folder.id}
          onMoveStart={() => setMovingFolders(prev => new Set(prev).add(folder.id))}
          onMoveEnd={() => setMovingFolders(prev => {
            const newSet = new Set(prev);
            newSet.delete(folder.id);
            return newSet;
          })}
        />,
        footer: (
          <MoveFolderDialog.Footer />
        ),
        className: "max-w-4xl",
      });
    };

    // SECRET KEY
    if (hasPasswordExpired) {
      openSecretKeyDialog(() => handleOpenMoveFolderDialog());
    } else {
      handleOpenMoveFolderDialog();
    }
  };

  const handleMergeFolder = (folder: FolderModel) => {
    const handleOpenMergeFolderDialog = async () => {
      openDialog({
        title: "Combinar pastas",
        icon: <BiMerge size={24} />,
        content: <MergeFoldersDialog.Content currentFolderId={folder.parentId || null} folderId={folder.id} folderName={folder.name}
          onMergeStart={() => setMergingFolders(prev => new Set(prev).add(folder.id))}
          onMergeEnd={() => setMergingFolders(prev => {
            const newSet = new Set(prev);
            newSet.delete(folder.id);
            return newSet;
          })}
        />,
        footer: (
          <MergeFoldersDialog.Footer
          />
        ),
        className: "max-w-4xl",
      });
    }
    // SECRET KEY
    if (hasPasswordExpired) {
      openSecretKeyDialog(() => handleOpenMergeFolderDialog());
    } else {
      handleOpenMergeFolderDialog();
    }
  };

  const handleConfirmDelete = (folder: FolderModel) => {
    openDialog({
      title: "Excluir pasta",
      icon: <Trash />,
      content: (
        <div>
          <Text variant="body-md">
            Tem certeza que deseja excluir a pasta <span className="font-bold text-accent uppercase">{folder.name}</span>?
          </Text>
          <Text variant="body-md" className="mt-2">
            Todos os <span className="font-bold">relatórios</span> e <span className="font-bold">pastas</span> dentro dela serão excluídos.
          </Text>
        </div>
      ),
      footer: (
        <div className="flex gap-3">
          <Button
            className="uppercase !bg-transparent"
            onClick={() => {
              closeDialog();
            }}
          >
            Cancelar
          </Button>
          <Button
            onClick={() => {
              setDeletingFolders(prev => new Set(prev).add(folder.id));
              deleteFolderMutation.mutate(folder.id, {
                onSettled: () => {
                  setDeletingFolders(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(folder.id);
                    return newSet;
                  });
                }
              });
            }}
            className="uppercase !bg-foreground !text-background !font-bold"
            icon={<Trash size={16} />}
            iconPosition="right"
          >
            Excluir
          </Button>
        </div>
      ),
    });
  };

  const handleRenameFolder = (folder: FolderModel) => {
    openDialog({
      title: "Renomear pasta",
      icon: <Pencil />,
      content: (
        <RenameFolderDialog
          folderId={folder.id}
          folderName={folder.name}
          onCancel={() => closeDialog()}
        />
      )
    });
  };

  const handleRetryReport = async (report: ReportModel) => {
    const isRelacoesType = report.reportType === REPORT_CONSTANTS.types.relacoes;
    const searchArgs = report.searchArgs as Record<string, string[]>;
    const firstValue = Object.values(searchArgs)[0];
    const valueString = Array.isArray(firstValue) ? firstValue[0] : firstValue;
    const plainTextValue = isRelacoesType ? searchArgs : valueString;

    try {
      setRetryingReports(prev => new Set(prev).add(report.id));

      const derivedKey = base64ToUint8Array(secretKey || "");
      const encryptedValue = await _encryptData(report.searchArgs, derivedKey.buffer);

      if (!encryptedValue.data) {
        console.error("Error encrypting searchArgs:", encryptedValue.error);
        setRetryingReports(prev => {
          const newSet = new Set(prev);
          newSet.delete(report.id);
          return newSet;
        });
        toast("Erro", {
          description: "Erro ao tentar refazer o relatório",
          action: {
            label: "Fechar",
            onClick: () => { },
          },
        });
        return;
      }

      await newReportMutation.mutateAsync({
        report_type: report.reportType,
        report_input_value: plainTextValue,
        report_search_args: plainTextValue,
        report_input_encrypted: encryptedValue.data,
        user_reports_id: report.id,
        parent_folder_id: report.parentFolderId,
      });

      setRetryingReports(prev => {
        const newSet = new Set(prev);
        newSet.delete(report.id);
        return newSet;
      });
    } catch (error) {
      console.error("Error processing newReportMutation:", error);
      setRetryingReports(prev => {
        const newSet = new Set(prev);
        newSet.delete(report.id);
        return newSet;
      });
      toast("Erro", {
        description: "Erro ao tentar refazer o relatório",
        action: {
          label: "Fechar",
          onClick: () => { },
        },
      });
    }
  };

  const handleConfirmDeleteReport = (report: ReportModel) => {
    openDialog({
      title: "Excluir relatório",
      icon: <Trash />,
      content: (
        <Text variant="body-md">
          Tem certeza que deseja excluir o relatório <span className="font-bold text-accent uppercase">{report.name}</span>?
        </Text>
      ),
      footer: (
        <div className="flex gap-3">
          <Button
            className="uppercase !bg-transparent"
            onClick={() => {
              closeDialog();
            }}
          >
            Cancelar
          </Button>
          <Button
            className="uppercase !bg-foreground !text-background !font-bold"
            icon={<Trash size={16} />}
            iconPosition="right"
            onClick={() => {
              setDeletingReports(prev => new Set(prev).add(report.id));
              deleteReportMutation.mutate(report.id, {
                onSettled: () => {
                  setDeletingReports(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(report.id);
                    return newSet;
                  });
                }
              });
            }}
          >
            Excluir
          </Button>
        </div>
      ),
      className: "max-w-lg",
    });
  };

  const handleMoveReport = (report: ReportModel) => {
    const handleOpenMoveReportDialog = () => {
      openDialog({
        title: "Mover relatório para pasta",
        icon: <MdOutlineDriveFileMove size={24} />,
        content: <MoveReportToFolderDialog.Content
          currentFolderId={report.parentFolderId || null}
          reportId={report.id}
          onMoveStart={() => setMovingReports(prev => new Set(prev).add(report.id))}
          onMoveEnd={() => setMovingReports(prev => {
            const newSet = new Set(prev);
            newSet.delete(report.id);
            return newSet;
          })}
        />,
        footer: (
          <MoveReportToFolderDialog.Footer />
        ),
        className: "max-w-4xl",
      });
    }
    // SECRET KEY
    if (hasPasswordExpired) {
      openSecretKeyDialog(() => handleOpenMoveReportDialog());
    } else {
      handleOpenMoveReportDialog();
    }

  };

  const handleRenameReport = (report: ReportModel) => {
    openDialog({
      title: "Renomear Relatório",
      icon: <Pencil />,
      content: (
        <RenameReportDialog
          reportId={report.id}
          reportName={report.name}
          onCancel={() => closeDialog()}
        />
      )
    });
  };

  const handleMergeReport = (report: ReportModel) => {
    const handleOpenMergeReportDialog = () => {
      openDialog({
        title: "Combinar relatórios",
        icon: <BiMerge size={24} />,
        content: <MergeReportDialog.Content
          currentFolderId={report.parentFolderId || null}
          reportId={report.id}
          reportName={report.name}
          onMergeStart={() => setMergingReports(prev => new Set(prev).add(report.id))}
          onMergeEnd={() => setMergingReports(prev => {
            const newSet = new Set(prev);
            newSet.delete(report.id);
            return newSet;
          })}
        />,
        footer: (
          <MergeReportDialog.Footer />
        ),
        className: "max-w-4xl",
      });
    }

    // SECRET KEY
    if (hasPasswordExpired) {
      openSecretKeyDialog(() => handleOpenMergeReportDialog());
    } else {
      handleOpenMergeReportDialog();
    }
  }

  return (
    <section
      ref={sectionRef}
      className="flex-1 w-full overflow-y-auto overflow-x-hidden min-h-0 max-h-[calc(100vh-232px)] px-8 pt-2 pb-8"
    >
      <MasonryLayout columns={COLUMNS_BREAKPOINTS} gap={20} className="relative">
        {canCreateReport ? (
          <AnimatedFilledButton
            data-testid="button-new-report"
            onClick={onNewReport}
            icon={<Icon src="/icons/icone_bigplus.svg" />}
          >
            <div className="flex flex-col items-start">
              <p className="text-2xl">Clique para criar:</p>
              <p className="text-2xl font-bold">Novo relatório</p>
              <p className="text-2xl font-bold">ou pasta</p>
              <div className="justify-self-end">
                <Plus size="20%" />
              </div>
            </div>
          </AnimatedFilledButton>
        ) : null}
        {isFetched &&
          list?.length > 0 &&
          list.map((item, index) => {
            const isFolder = 'folder_id' in item;
            const model = isFolder
              ? new FolderModel(item as FolderData)
              : new ReportModel(item as ReportData);
            return (
              <div className="z-[1]" key={index}>
                <CardFactory
                  item={model}
                  onDownloadPDF={handleDownloadPDF}
                  onOpenFolder={isFolder ? handleOpenFolder : undefined}
                  onMoveFolder={isFolder ? handleMoveFolder : undefined}
                  onMergeFolder={isFolder ? handleMergeFolder : undefined}
                  onRenameFolder={isFolder ? handleRenameFolder : undefined}
                  onDeleteFolder={isFolder ? handleConfirmDelete : undefined}
                  onMoveReport={handleMoveReport}
                  onRenameReport={handleRenameReport}
                  onDeleteReport={handleConfirmDeleteReport}
                  onMergeReports={handleMergeReport}
                  onRetry={!isFolder ? handleRetryReport : undefined}
                  isCreatingReport={retryingReports.has(model.id)}
                  isDeletingReport={deletingReports.has(model.id)}
                  isDownloadingPDF={downloadingReports.has(model.id)}
                  isDeletingFolder={deletingFolders.has(model.id)}
                  isMovingReport={movingReports.has(model.id)}
                  isMovingFolder={movingFolders.has(model.id)}
                  isMergingFolders={mergingFolders.has(model.id)}
                  hasPermissionToCreateCombinedReport={canCreateReport && isUserAllowedToCombineReports}
                />
              </div>
            );
          })}
      </MasonryLayout>
      {isFetched && list?.length === 0 && (
        <EmptyList onReload={() => { }} />
      )}

      {showLoadMore && loadMore && (
        <div className="w-full absolute bottom-2 left-0 flex justify-center py-4 px-8 z-[9999]">
          <Button
            variant="default"
            className="w-full uppercase"
            onClick={handleLoadMore}
          >
            Carregar mais resultados
          </Button>
        </div>
      )}
    </section>
  );
}
