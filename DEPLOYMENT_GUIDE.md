# 🚀 Updated Deployment Guide with Internal Keycloak

## Quick Start

Your `generate_deploy.sh` script has been updated to work with the new **internal-only Keycloak** configuration.

### 🏭 **Production Deployment**

```bash
./generate_deploy.sh \
  --profile prod \
  --keycloak-admin-password "YOUR_SECURE_PASSWORD" \
  --snap-api-client-secret "YOUR_API_SECRET" \
  --captcha-key "YOUR_CAPTCHA_KEY" \
  --client-id-google "YOUR_GOOGLE_CLIENT_ID" \
  --client-secret-google "YOUR_GOOGLE_SECRET" \
  --client-id-microsoft "YOUR_MS_CLIENT_ID" \
  --client-secret-microsoft "YOUR_MS_SECRET"
```

### 🧪 **Development Deployment**

```bash
./generate_deploy.sh \
  --profile dev \
  --keycloak-admin-password "admin123" \
  --snap-api-client-secret "dev-secret" \
  --captcha-key "dev-captcha-key" \
  --client-id-google "dev-google-id" \
  --client-secret-google "dev-google-secret"
```

### 🔄 **Backend-Only Development**

```bash
./generate_deploy.sh \
  --profile back-dev \
  --keycloak-admin-password "admin123" \
  --snap-api-client-secret "dev-secret" \
  --captcha-key "dev-captcha-key"
```

## 🔒 **What Changed for Keycloak Security**

### ✅ **Automatic Internal Configuration**
- **No external ports** - Keycloak completely internal
- **Internal URLs only** - `http://keycloak:8080`
- **VM-only admin access** - SSH required for management
- **Zero attack surface** - No external endpoints

### ✅ **Environment-Based Setup**
- **Production**: `KC_ENV=production`, strict security
- **Development**: `KC_ENV=development`, debug logging
- **Automatic configuration** based on `--profile`

### ✅ **Required Environment Variables**

The script automatically sets:
```bash
# Internal Keycloak URLs (no external domains)
KC_HOSTNAME_URL=http://keycloak:8080
KC_HOSTNAME_ADMIN_URL=http://keycloak:8080

# Database connection
DB_NAME=keycloak
DB_PASS=password  # Change this!

# Security settings per environment
KC_ENV=production|development
KC_LOG_LEVEL=WARN|DEBUG
```

## 📋 **Required Parameters**

### **Always Required:**
- `--keycloak-admin-password` - Admin password for Keycloak
- `--snap-api-client-secret` - Your API client secret
- `--captcha-key` - Captcha service key

### **OAuth Providers (at least one required):**
- `--client-id-google` + `--client-secret-google`
- `--client-id-microsoft` + `--client-secret-microsoft`

### **Optional:**
- `--profile` - `prod` (default), `dev`, `back-dev`, `prod-dev`
- `--keycloak-admin-user` - Admin username (default: `admin`)
- `--microsoft-tenant` - MS tenant (default: `common`)

## 🎯 **Environment Files Alternative**

Instead of command line args, you can use environment files:

### **For Development (.env):**
```bash
KEYCLOAK_ADMIN_PASSWORD=admin123
SNAP_API_CLIENT_SECRET=dev-secret
CAPTCHA_KEY=dev-captcha-key
CLIENT_ID_GOOGLE=your-google-id
CLIENT_SECRET_GOOGLE=your-google-secret
```

Then run:
```bash
./generate_deploy.sh --profile dev
```

### **For Production (.env.prod.dev):**
```bash
KEYCLOAK_ADMIN_PASSWORD=secure-password
SNAP_API_CLIENT_SECRET=production-secret
CAPTCHA_KEY=production-captcha
CLIENT_ID_GOOGLE=prod-google-id
CLIENT_SECRET_GOOGLE=prod-google-secret
```

Then run:
```bash
./generate_deploy.sh --profile prod-dev
```

## 🔧 **After Deployment**

### **1. Verify Keycloak is Running:**
```bash
./scripts/keycloak-internal-test.sh
```

### **2. Get Admin Access:**
```bash
./scripts/keycloak-admin-access.sh
```

### **3. SSH Tunnel for GUI (if needed):**
```bash
ssh -L 8080:localhost:8080 user@your-vm-ip
# Then access: http://localhost:8080/admin/
```

## 🛡️ **Security Benefits**

### **✅ Maximum Security**
- **Zero external exposure** - No attack surface
- **Internal communication only** - Container-to-container
- **VM-only admin access** - SSH required
- **Automatic secret management** - Docker secrets

### **✅ Simplified Management**
- **No SSL certificates needed** for auth
- **No reverse proxy config** for Keycloak
- **No external DNS** required
- **Automatic environment detection**

## 🚨 **Troubleshooting**

### **Script Fails:**
```bash
# Check required parameters
./generate_deploy.sh --help

# Verify Docker Swarm
docker swarm init

# Check logs
docker service logs mystack_keycloak
```

### **Keycloak Not Accessible:**
```bash
# Test internal connectivity
./scripts/keycloak-internal-test.sh

# Check container status
docker service ps mystack_keycloak

# View logs
docker service logs mystack_keycloak --follow
```

### **Admin Access Issues:**
```bash
# Get access information
./scripts/keycloak-admin-access.sh

# Verify secrets
docker secret ls | grep keycloak
```

## 📊 **Deployment Summary**

After running the script, you'll see:

```
🔒 Keycloak Internal Access Information
🏭 Production Keycloak (Internal Only):
  Internal URL: http://keycloak:8080
  Admin Access: VM only (SSH required)
  External Access: COMPLETELY BLOCKED

💡 Application Integration:
  Backend Services: Use KEYCLOAK_URL=http://keycloak:8080
  Frontend: Authenticate through backend API only
  Security: Zero external attack surface
```

Your Keycloak is now **fortress-level secure** with internal-only access! 🏰🔒
