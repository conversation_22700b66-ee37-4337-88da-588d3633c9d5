"""
Migration Templates for Table Creation in Reports Schema

This module provides standardized templates for creating new tables
following the established patterns in the codebase.
"""

STANDARD_TABLE_TEMPLATE = """\"\"\"Add {table_name} table

Revision ID: {revision_id}
Revises: {previous_revision}
Create Date: {create_date}

\"\"\"
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '{revision_id}'
down_revision = '{previous_revision}'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('{table_name}',
        sa.Column('{primary_key}', postgresql.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\\'UTC\\', now())'), nullable=False),
        sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\\'UTC\\', now())'), nullable=False),
        # Add your columns here
        sa.PrimaryKeyConstraint('{primary_key}'),
        schema='reports'
    )

    # Add indexes if needed
    # op.create_index('idx_{table_name}_column', '{table_name}', ['column'], unique=False, schema='reports')


def downgrade() -> None:
    op.drop_table('{table_name}', schema='reports')
"""

LOOKUP_TABLE_TEMPLATE = """\"\"\"Add {table_name} lookup table

Revision ID: {revision_id}
Revises: {previous_revision}
Create Date: {create_date}

\"\"\"
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '{revision_id}'
down_revision = '{previous_revision}'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('{table_name}',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('code', sa.String(50), nullable=False, unique=True),
        sa.Column('name', sa.Text(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('sort_order', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\\'UTC\\', now())'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('code'),
        schema='reports'
    )

    # Create indexes
    op.create_index('idx_{table_name}_code', '{table_name}', ['code'], unique=True, schema='reports')
    op.create_index('idx_{table_name}_active', '{table_name}', ['is_active'], unique=False, schema='reports')


def downgrade() -> None:
    op.drop_table('{table_name}', schema='reports')
"""

JUNCTION_TABLE_TEMPLATE = """\"\"\"Add {table_name} junction table

Revision ID: {revision_id}
Revises: {previous_revision}
Create Date: {create_date}

\"\"\"
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '{revision_id}'
down_revision = '{previous_revision}'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('{table_name}',
        sa.Column('{first_table}_id', postgresql.UUID(), nullable=False),
        sa.Column('{second_table}_id', postgresql.UUID(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\\'UTC\\', now())'), nullable=False),
        # Add additional columns if needed (e.g., role, status)
        sa.ForeignKeyConstraint(['{first_table}_id'], ['reports.{first_table}.{first_table}_id']),
        sa.ForeignKeyConstraint(['{second_table}_id'], ['reports.{second_table}.{second_table}_id']),
        sa.PrimaryKeyConstraint('{first_table}_id', '{second_table}_id'),
        schema='reports'
    )


def downgrade() -> None:
    op.drop_table('{table_name}', schema='reports')
"""

LOG_TABLE_TEMPLATE = """\"\"\"Add {table_name} log table

Revision ID: {revision_id}
Revises: {previous_revision}
Create Date: {create_date}

\"\"\"
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '{revision_id}'
down_revision = '{previous_revision}'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('{table_name}',
        sa.Column('log_id', postgresql.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('user_id', postgresql.UUID(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\\'UTC\\', now())'), nullable=False),
        sa.Column('action', sa.Text(), nullable=False),
        sa.Column('entity_type', sa.Text(), nullable=False),
        sa.Column('entity_id', sa.Text(), nullable=False),
        sa.Column('old_values', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('new_values', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('ip_address', sa.String(45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['reports.users.user_id']),
        sa.PrimaryKeyConstraint('log_id'),
        schema='reports'
    )

    # Create indexes for common queries
    op.create_index('idx_{table_name}_user_id', '{table_name}', ['user_id'], unique=False, schema='reports')
    op.create_index('idx_{table_name}_created_at', '{table_name}', ['created_at'], unique=False, schema='reports')
    op.create_index('idx_{table_name}_entity', '{table_name}', ['entity_type', 'entity_id'], unique=False, schema='reports')
    op.create_index('idx_{table_name}_action', '{table_name}', ['action'], unique=False, schema='reports')


def downgrade() -> None:
    op.drop_table('{table_name}', schema='reports')
"""

def generate_migration_file(table_name: str, template_type: str = "standard", **kwargs) -> str:
    """
    Generate a migration file based on the specified template.

    Args:
        table_name: Name of the table to create
        template_type: Type of template ('standard', 'lookup', 'junction', 'log')
        **kwargs: Additional template variables

    Returns:
        Generated migration file content
    """
    import uuid
    from datetime import datetime

    # Default template variables
    variables = {
        'table_name': table_name,
        'primary_key': f'{table_name}_id',
        'revision_id': str(uuid.uuid4())[:8],
        'previous_revision': kwargs.get('previous_revision', 'None'),
        'create_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'),
        **kwargs
    }

    templates = {
        'standard': STANDARD_TABLE_TEMPLATE,
        'lookup': LOOKUP_TABLE_TEMPLATE,
        'junction': JUNCTION_TABLE_TEMPLATE,
        'log': LOG_TABLE_TEMPLATE
    }

    if template_type not in templates:
        raise ValueError(f"Unknown template type: {template_type}")

    return templates[template_type].format(**variables)


# Common column definitions that can be reused
COMMON_COLUMNS = {
    'uuid_primary_key': "sa.Column('{name}', postgresql.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False)",
    'created_at': "sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\\'UTC\\', now())'), nullable=False)",
    'modified_at': "sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('timezone(\\'UTC\\', now())'), nullable=False)",
    'user_id_fk': "sa.Column('user_id', postgresql.UUID(), nullable=False)",
    'organization_id_fk': "sa.Column('organization_id', postgresql.UUID(), nullable=True)",
    'jsonb_data': "sa.Column('{name}', postgresql.JSONB(astext_type=sa.Text()), nullable=True)",
    'text_field': "sa.Column('{name}', sa.Text(), nullable=True)",
    'required_text': "sa.Column('{name}', sa.Text(), nullable=False)",
    'boolean_flag': "sa.Column('{name}', sa.Boolean(), nullable=False, server_default='{default}')",
    'status_enum': "sa.Column('{name}', postgresql.ENUM({values}, name='{enum_name}', create_type=True), nullable=False, server_default='{default}')"
}

# Common index patterns
INDEX_PATTERNS = {
    'gin_jsonb': "op.create_index('idx_{table}_{column}', '{table}', ['{column}'], unique=False, schema='reports', postgresql_using='gin')",
    'btree_single': "op.create_index('idx_{table}_{column}', '{table}', ['{column}'], unique=False, schema='reports')",
    'btree_composite': "op.create_index('idx_{table}_{columns}', '{table}', [{column_list}], unique=False, schema='reports')",
    'unique_constraint': "op.create_index('idx_{table}_{column}_unique', '{table}', ['{column}'], unique=True, schema='reports')"
}

# Common foreign key patterns
FOREIGN_KEY_PATTERNS = {
    'user_reference': "sa.ForeignKeyConstraint(['{column}'], ['reports.users.user_id'])",
    'organization_reference': "sa.ForeignKeyConstraint(['{column}'], ['reports.organizations.organization_id'])",
    'cascade_delete': "sa.ForeignKeyConstraint(['{column}'], ['reports.{table}.{reference}'], ondelete='CASCADE')"
}