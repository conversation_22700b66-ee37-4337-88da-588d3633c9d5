import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import NoResultFound
from models.report_executions_model import ReportExecutions
from schemas.report_executions_schema import ReportExecutionsSchema
from services.base_service import BaseService
from core.constants import SummaryReportStatus

from exceptions.business_exceptions import ReportOfThisProcessAlreadyRunning

logger = logging.getLogger(__name__)

class ReportExecutionsService(BaseService):
    def __init__(self, db: AsyncSession, user_id: str):
        super().__init__(db)
        self.user_id=user_id

        logger.info("[ReportExecutionsService][__init__] Service initialized with db session: %s", db)

    async def create_if_no_pending(self, **kwargs) -> ReportExecutions:
        logger.info("[ReportExecutionsService][create_if_no_pending] Checking for existing pending log for user_reports_id: %s", kwargs.get('user_reports_id'))
        q = await self.db.execute(
            select(ReportExecutions).where(
                ReportExecutions.user_reports_id == kwargs.get('user_reports_id'),
                ReportExecutions.user_id == self.user_id,
                ReportExecutions.status_tentativa == SummaryReportStatus.pending
            )
        )
        existing = q.scalar_one_or_none()
        if existing:
            logger.info("[ReportExecutionsService][create_if_no_pending] Pending log already exists for user_reports_id: %s", kwargs.get('user_reports_id'))
            raise ReportOfThisProcessAlreadyRunning()
        new_log = ReportExecutions(**kwargs)
        self.db.add(new_log)
        await self.db.commit()
        await self.db.refresh(new_log)
        logger.info("[ReportExecutionsService][create_if_no_pending] Created new report_executions with ID: %s", new_log.report_executions_id)
        return new_log

    async def update_columns(self, user_reports_id, updates: dict) -> ReportExecutions:
        logger.info("[ReportExecutionsService][update_columns] Updating columns %s for user_reports_id: %s, user_id: %s with values: %s", list(updates.keys()), user_reports_id, self.user_id, updates)
        q = await self.db.execute(
            select(ReportExecutions).where(
                ReportExecutions.user_reports_id == user_reports_id,
                ReportExecutions.user_id == self.user_id,
                ReportExecutions.status_tentativa == SummaryReportStatus.pending
            )
        )
        log = q.scalar_one_or_none()
        if not log:
            logger.error("[ReportExecutionsService][update_columns] No pending report_executions found for user_reports_id: {user_reports_id}, user_id: {self.user_id}")
            
            # raise NoResultFound(f"Pending ReportExecutions with user_reports_id {user_reports_id} and user_id {self.user_id} not found")
        for column_name, value in updates.items():
            if not hasattr(log, column_name):
                logger.info("[ReportExecutionsService][update_columns] Column '%s' does not exist on ReportExecutions", column_name)
                raise AttributeError(f"Column '{column_name}' does not exist on ReportExecutions")
            setattr(log, column_name, value)
        await self.db.commit()
        await self.db.refresh(log)
        logger.info("[ReportExecutionsService][update_columns] Updated columns %s for user_reports_id: %s, user_id: %s", list(updates.keys()), user_reports_id, self.user_id)
        return log

    async def update_columns_to_delete(self, user_reports_id, updates: dict) -> list[ReportExecutions]:
        logger.info("[ReportExecutionsService][update_columns_to_delete] Updating columns %s for user_reports_id: %s, user_id: %s with values: %s", list(updates.keys()), user_reports_id, self.user_id, updates)
        q = await self.db.execute(
            select(ReportExecutions).where(
                ReportExecutions.user_reports_id == user_reports_id,
                ReportExecutions.user_id == self.user_id
            )
        )
        logs = q.scalars().all()
        if not logs:
            logger.error("[ReportExecutionsService][update_columns_to_delete] No report_executions found for user_reports_id: %s, user_id: %s", user_reports_id, self.user_id)
            # raise NoResultFound(f"ReportExecutions with user_reports_id {user_reports_id} and user_id {self.user_id} not found")
        
        logger.info("[ReportExecutionsService][update_columns_to_delete] Found %d report_executions records to update for user_reports_id: %s, user_id: %s", len(logs), user_reports_id, self.user_id)
        
        updated_logs = []
        for log in logs:
            logger.info("[ReportExecutionsService][update_columns_to_delete] Updating report_executions_id: %s with columns: %s", log.report_executions_id, list(updates.keys()))
            for column_name, value in updates.items():
                if not hasattr(log, column_name):
                    logger.error("[ReportExecutionsService][update_columns_to_delete] Column {column_name} does not exist on pending to user_reports_id {user_reports_id} ReportExecutions to user_id {user_id}")
                    # raise AttributeError(f"Column '{column_name}' does not exist on ReportExecutions")
                setattr(log, column_name, value)
            updated_logs.append(log)
        
        await self.db.commit()
        
        for log in updated_logs:
            await self.db.refresh(log)
        
        logger.info("[ReportExecutionsService][update_columns_to_delete] Successfully updated columns %s for %d records with user_reports_id: %s, user_id: %s", list(updates.keys()), len(updated_logs), user_reports_id, self.user_id)
        return updated_logs

    # Backward compatibility: keep update_column as a wrapper
    async def update_column(self, user_reports_id, column_name: str, value) -> ReportExecutions:
        return await self.update_columns(user_reports_id, {column_name: value}) 