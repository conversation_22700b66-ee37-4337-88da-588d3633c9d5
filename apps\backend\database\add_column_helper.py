#!/usr/bin/env python3
"""
Column Addition Helper Script

This script helps you safely add new columns to existing tables
following established patterns and best practices.

Usage:
    python database/add_column_helper.py
"""

import os
import sys
import re
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Add the backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

def get_latest_revision() -> Optional[str]:
    """Get the latest migration revision ID"""
    versions_dir = Path("alembic/versions")
    if not versions_dir.exists():
        return None

    migration_files = [f for f in versions_dir.glob("*.py") if f.name != "__init__.py"]
    if not migration_files:
        return None

    # Get the most recent migration file
    latest_file = max(migration_files, key=lambda p: p.stat().st_mtime)

    # Extract revision ID from file
    try:
        with open(latest_file) as f:
            content = f.read()
            for line in content.split('\n'):
                if line.startswith('revision = '):
                    return line.split("'")[1]
    except Exception:
        pass

    return None

def get_table_models() -> Dict[str, str]:
    """Get available table models from the models directory"""
    models_dir = Path("models")
    tables = {}

    if not models_dir.exists():
        return tables

    for model_file in models_dir.glob("*_model.py"):
        if model_file.name == "__init__.py":
            continue

        try:
            with open(model_file) as f:
                content = f.read()
                # Find __tablename__ definitions
                matches = re.findall(r"__tablename__ = ['\"]([^'\"]+)['\"]", content)
                if matches:
                    table_name = matches[0]
                    model_name = model_file.stem.replace('_model', '')
                    tables[table_name] = model_name

                    # Validate that table uses reports schema
                    if '__table_args__' in content:
                        if "'schema': 'reports'" not in content and '"schema": "reports"' not in content:
                            print(f"⚠️  Warning: Table {table_name} may not be using reports schema")

        except Exception:
            continue

    return tables

def validate_schema_consistency() -> bool:
    """Validate that all models are using the reports schema consistently"""
    models_dir = Path("models")
    issues_found = []

    if not models_dir.exists():
        print("❌ Models directory not found")
        return False

    for model_file in models_dir.glob("*_model.py"):
        if model_file.name == "__init__.py":
            continue

        try:
            with open(model_file) as f:
                content = f.read()

                # Check if it has a table definition
                if "__tablename__" not in content:
                    continue

                table_matches = re.findall(r"__tablename__ = ['\"]([^'\"]+)['\"]", content)
                if not table_matches:
                    continue

                table_name = table_matches[0]

                # Check if it uses reports schema
                has_schema_arg = "'schema': 'reports'" in content or '"schema": "reports"' in content

                if not has_schema_arg:
                    issues_found.append(f"Table '{table_name}' in {model_file.name} missing schema='reports'")

        except Exception as e:
            issues_found.append(f"Error reading {model_file.name}: {e}")

    if issues_found:
        print("❌ Schema consistency issues found:")
        for issue in issues_found:
            print(f"  - {issue}")
        return False
    else:
        print("✅ All models are using reports schema consistently")
        return True

def validate_column_name(column_name: str) -> Tuple[bool, str]:
    """Validate column name follows conventions"""
    if not column_name:
        return False, "Column name cannot be empty"

    if not re.match(r'^[a-z][a-z0-9_]*$', column_name):
        return False, "Column name should start with lowercase letter and contain only lowercase, numbers, and underscores"

    if column_name in ['id', 'table', 'column', 'index', 'constraint', 'schema']:
        return False, f"'{column_name}' is a reserved word, choose a different name"

    return True, ""

def get_column_type_definition(col_type: str, **options) -> str:
    """Get SQLAlchemy column type definition"""
    type_map = {
        'Text': 'Text',
        'String': f'String({options.get("length", 255)})',
        'Integer': 'Integer',
        'BigInteger': 'BigInteger',
        'Boolean': 'Boolean',
        'DateTime': 'DateTime(timezone=True)',
        'Date': 'Date',
        'UUID': 'UUID',
        'JSONB': 'JSONB',
        'Float': 'Float',
        'Numeric': 'Numeric'
    }

    return type_map.get(col_type, 'Text')

def generate_column_definition(column_name: str, column_type: str, nullable: bool,
                             default_value: str = None, foreign_key: str = None,
                             index: bool = False) -> Dict[str, str]:
    """Generate complete column definition"""

    # Base column definition
    type_def = get_column_type_definition(column_type)
    parts = [f"'{column_name}'", type_def]

    # Foreign key
    if foreign_key:
        parts.append(f'ForeignKey("{foreign_key}")')

    # Nullable
    if not nullable:
        parts.append('nullable=False')
        if default_value:
            if column_type in ['String', 'Text']:
                parts.append(f"server_default='{default_value}'")
            else:
                parts.append(f"server_default={default_value}")
    else:
        parts.append('nullable=True')

    # Build column definition
    column_def = f"    {column_name} = Column({', '.join(parts)})"

    # Index definition if needed
    index_def = None
    if index:
        index_def = f'        Index("idx_{{table_name}}_{column_name}", "{column_name}"),'

    # Migration column definition
    migration_parts = [f"'{column_name}'", f"sa.{type_def}"]

    if foreign_key:
        migration_parts.append(f"sa.ForeignKey('{foreign_key}')")

    if not nullable:
        migration_parts.append('nullable=False')
        if default_value:
            if column_type in ['String', 'Text']:
                migration_parts.append(f"server_default='{default_value}'")
            else:
                migration_parts.append(f"server_default={default_value}")
    else:
        migration_parts.append('nullable=True')

    migration_def = f"    sa.Column({', '.join(migration_parts)})"

    return {
        'model_column': column_def,
        'model_index': index_def,
        'migration_column': migration_def
    }

def interactive_column_addition():
    """Interactive CLI for adding columns"""
    print("🔧 Database Column Addition Helper")
    print("=" * 40)

    # Validate schema consistency first
    print("\n🔍 Validating schema consistency...")
    if not validate_schema_consistency():
        print("\n⚠️  Schema consistency issues detected. Please fix these first.")
        response = input("\nContinue anyway? (y/N): ").lower()
        if not response.startswith('y'):
            print("❌ Column addition cancelled due to schema issues.")
            return

    # Get available tables
    tables = get_table_models()
    if not tables:
        print("❌ No table models found in models/ directory")
        return

    # Select table
    print("\n📋 Available tables:")
    table_list = list(tables.keys())
    for i, table in enumerate(table_list, 1):
        print(f"{i}. {table}")

    while True:
        try:
            choice = int(input(f"\nSelect table (1-{len(table_list)}): ")) - 1
            if 0 <= choice < len(table_list):
                selected_table = table_list[choice]
                break
            else:
                print("Invalid choice, please try again.")
        except ValueError:
            print("Please enter a valid number.")

    print(f"\n✅ Selected table: {selected_table}")

    # Column details
    while True:
        column_name = input("\n📝 Column name: ").strip()
        is_valid, error_msg = validate_column_name(column_name)
        if is_valid:
            break
        else:
            print(f"❌ {error_msg}")

    # Column type
    print("\n🎯 Select column type:")
    types = ['Text', 'String', 'Integer', 'BigInteger', 'Boolean', 'DateTime', 'Date', 'UUID', 'JSONB', 'Float']
    for i, col_type in enumerate(types, 1):
        print(f"{i}. {col_type}")

    while True:
        try:
            type_choice = int(input(f"Choice (1-{len(types)}): ")) - 1
            if 0 <= type_choice < len(types):
                column_type = types[type_choice]
                break
            else:
                print("Invalid choice, please try again.")
        except ValueError:
            print("Please enter a valid number.")

    # String length for String type
    length = None
    if column_type == 'String':
        length = input("String length (default: 255): ").strip()
        if length and not length.isdigit():
            print("Using default length 255")
            length = None

    # Nullable
    nullable = input(f"\n❓ Is '{column_name}' nullable? (y/n): ").lower().startswith('y')

    # Default value for non-nullable columns
    default_value = None
    if not nullable:
        default_value = input(f"Default value for '{column_name}': ").strip()
        if not default_value:
            print("❌ Non-nullable columns must have a default value!")
            return

    # Foreign key
    foreign_key = None
    if column_name.endswith('_id') and column_type == 'UUID':
        is_fk = input(f"\n🔗 Is '{column_name}' a foreign key? (y/n): ").lower().startswith('y')
        if is_fk:
            foreign_key = input("Foreign key reference (e.g., 'reports.users.user_id'): ").strip()
            if not foreign_key.startswith('reports.'):
                print("⚠️  Foreign key should start with 'reports.' for schema consistency")
                foreign_key = f"reports.{foreign_key}"

    # Index
    needs_index = False
    if foreign_key or column_name in ['email', 'api_key', 'status', 'type'] or column_type == 'JSONB':
        needs_index = input(f"\n📊 Add index for '{column_name}'? (recommended: y/n): ").lower().startswith('y')

    # Generate definitions
    column_def = generate_column_definition(
        column_name, column_type, nullable, default_value, foreign_key, needs_index
    )

    # Display summary
    print(f"\n📋 Column Addition Summary:")
    print(f"  Table: {selected_table}")
    print(f"  Column: {column_name}")
    print(f"  Type: {column_type}")
    print(f"  Nullable: {nullable}")
    print(f"  Default: {default_value or 'None'}")
    print(f"  Foreign Key: {foreign_key or 'None'}")
    print(f"  Index: {needs_index}")

    # Confirm
    if not input("\n❓ Proceed with column addition? (y/n): ").lower().startswith('y'):
        print("❌ Column addition cancelled.")
        return

    # Show next steps
    print(f"\n🚀 Next Steps:")
    print(f"1. Add this column to your model file (models/{tables[selected_table]}_model.py):")
    print(f"   {column_def['model_column']}")

    if column_def['model_index']:
        print(f"2. Add this index to __table_args__:")
        print(f"   {column_def['model_index']}")

    print(f"3. Generate migration:")
    print(f"   alembic revision --autogenerate -m 'Add {column_name} to {selected_table}'")

    print(f"4. Review the generated migration file")

    print(f"5. Apply migration:")
    print(f"   alembic upgrade head")

    # Option to generate migration automatically
    auto_migrate = input(f"\n🤖 Auto-generate migration now? (y/n): ").lower().startswith('y')

    if auto_migrate:
        print("\n⚠️  Please first add the column to your model file, then run:")
        print(f"alembic revision --autogenerate -m 'Add {column_name} to {selected_table}'")
        print("\nThis tool cannot modify your model files automatically for safety reasons.")

def check_alembic_setup():
    """Check if Alembic is properly set up"""
    if not Path("alembic.ini").exists():
        print("❌ alembic.ini not found. Please ensure you're in the backend directory.")
        return False

    if not Path("alembic/env.py").exists():
        print("❌ alembic/env.py not found. Alembic not properly initialized.")
        return False

    return True

def main():
    """Main entry point"""
    if not check_alembic_setup():
        return

    try:
        interactive_column_addition()
    except KeyboardInterrupt:
        print("\n\n👋 Column addition cancelled.")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")

if __name__ == "__main__":
    main()