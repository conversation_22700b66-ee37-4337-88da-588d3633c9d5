import os
import logging
from pydantic_settings import BaseSettings

logger = logging.getLogger(__name__)

class AlembicSettings(BaseSettings):
    """Simplified settings for Alembic migrations - only database-related config"""
    
    # Database configuration
    DB_HOST: str = os.getenv("DB_HOST", "postgres")
    DB_NAME: str = os.getenv("DB_NAME", "snap_reports")
    DB_USER: str = os.getenv("DB_USER", "postgres")
    DB_PASS: str = os.getenv("DB_PASS", "postgres")
    
    @property
    def DATABASE_URL(self):
        """Get database URL for Alembic migrations"""
        # Check if we're using the shared keycloak database
        if self.DB_NAME == "keycloak":
            logger.info("✅ Using shared Keycloak database with reports schema separation")

        logger.info("Connecting to database at %s:%s/%s with schema: reports", self.DB_HOST, 5432, self.DB_NAME)
        return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASS}@{self.DB_HOST}:5432/{self.DB_NAME}"
    
    model_config = {"env_file": ".env"}

# Instantiate settings
settings = AlembicSettings() 