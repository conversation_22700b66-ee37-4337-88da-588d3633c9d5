import { useCallback } from "react";
import { useSecretKeyDialog } from "~/hooks/useSecretKeyDialog";
import { usePasswordState } from "~/hooks/usePasswordState";

export const usePasswordCheck = () => {
  const { openSecretKeyDialog } = useSecretKeyDialog();
  const { getFreshPasswordState } = usePasswordState();

  const withPasswordCheck = useCallback((action: () => void) => {
    if (getFreshPasswordState()) {
      openSecretKeyDialog(() => {
        action();
      });
    } else {
      action();
    }
  }, [getFreshPasswordState, openSecretKeyDialog]);

  // Promise-based version for use in mutationFn
  const withPasswordCheckAsync = useCallback((action: () => Promise<any>): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (getFreshPasswordState()) {
        openSecretKeyDialog(async () => {
          try {
            const result = await action();
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });
      } else {
        action().then(resolve).catch(reject);
      }
    });
  }, [getFreshPasswordState, openSecretKeyDialog]);

  return {
    withPasswordCheck,
    withPasswordCheckAsync,
    hasPasswordExpired: getFreshPasswordState(),
  };
};
