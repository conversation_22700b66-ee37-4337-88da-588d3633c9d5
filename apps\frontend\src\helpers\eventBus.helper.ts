type EventCallback = (...args: any[]) => void;

class EventBus {
  private static instance: EventBus;
  private listeners: { [key: string]: EventCallback[] } = {};
  private logoutInProgress: boolean = false;

  private constructor() { }

  static getInstance(): EventBus {
    if (!EventBus.instance) {
      EventBus.instance = new EventBus();
    }
    return EventBus.instance;
  }

  subscribe(event: string, callback: EventCallback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);

    // Return unsubscribe function
    return () => {
      this.listeners[event] = this.listeners[event].filter(
        (cb) => cb !== callback
      );
    };
  }

  emit(event: string, ...args: any[]) {
    // Prevent multiple UNAUTHORIZED_ERROR events from triggering logout loops
    if (event === "UNAUTHORIZED_ERROR") {
      if (this.logoutInProgress) {
        console.log("[EventBus] Logout already in progress, ignoring duplicate UNAUTHORIZED_ERROR event");
        return;
      }
      this.logoutInProgress = true;

      // Reset the flag after a delay to allow future legitimate logout attempts
      setTimeout(() => {
        this.logoutInProgress = false;
      }, 5000);
    }

    if (this.listeners[event]) {
      this.listeners[event].forEach((callback) => callback(...args));
    }
  }
  resetLogoutFlag() {
    this.logoutInProgress = false;
  }
}

export const eventBus = EventBus.getInstance();
