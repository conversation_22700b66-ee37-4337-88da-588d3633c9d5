import { docxClient } from "../clients/docx.client";
import type { ReportDocumentProps } from "../../hooks/useReportPDF";

class DOCXGateway {
  async generateDOCX(props: ReportDocumentProps): Promise<string> {
    try {
      const response = await docxClient.post('/reports', props, {
        responseType: 'blob'
      });

      const docxBlob = response.data;
      const url = URL.createObjectURL(docxBlob);

      console.log('DOCX generated successfully by service');
      return url;
    } catch (error) {
      console.error('Error calling DOCX service:', error);
      throw error;
    }
  }
}

export const docxGateway = new DOCXGateway();