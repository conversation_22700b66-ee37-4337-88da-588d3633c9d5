import { useEffect, useState, useCallback } from "react";
import { docxGateway } from "../services";
import { ReportMetadata, ReportSection } from "../global";

export interface ReportDocumentProps {
  sections: ReportSection[];
  metadata: ReportMetadata;
  profile_image?: string;
  organization_logo?: string;
  should_print_snap_logo?: boolean;
}

export const useReportDOCX = () => {
  const [state, setState] = useState<{
    loading: boolean;
    error: Error | undefined;
    url: string | undefined;
  }>({
    loading: false,
    error: undefined,
    url: undefined,
  });

  const generateDOCX = useCallback(async (props: ReportDocumentProps) => {
    setState(prev => ({ ...prev, loading: true }));
    try {
      const url = await docxGateway.generateDOCX(props);
      setState({ loading: false, error: undefined, url });
      return url;
    } catch (error) {
      const err = error as Error;
      setState({ loading: false, error: err, url: undefined });
      throw err;
    }
  }, []);

  useEffect(() => {
    return () => {
      if (state.url) {
        URL.revokeObjectURL(state.url);
      }
    };
  }, [state.url]);

  return {
    generateDOCX,
    loading: state.loading,
    error: state.error,
    url: state.url
  };
};