from collections import defaultdict
from typing import Dict, List

from unidecode import unidecode

from reports_processor.constants import socio_key, qualificacao_societaria, parentes_vinculo, \
    outros_contatos_vinculo, ReportKeys, numero_processo_key, vinculo_autor_processo_nao_identificado_key, \
    pena_use_keys, recursos_recebidos_skip_label_keys, p_movimentacoes_key, parentes_mae_vinculo, parentes_pai_vinculo, \
    nome_completo_key, rede_social_key_prefix, educacional_vinculo, logger, ReportType, label_key, empregos_vinculo, \
    Constants
from reports_processor.formatters.formatters import map_to_front_format, merge_entity_lists
from reports_processor.utils import is_a_match, compare_and_update_vinculo, get_vinculo, is_front_formatted, \
    basic_property_dict, get_constant


class CallbackProcessors:
    """Separate class for callback processors to improve organization"""

    @staticmethod
    def filter_for_sociedades_vinculos(item: Dict, entity_type, search_value: str) -> List:
        return [
            x for x in item
            if (compare_and_update_vinculo(x, socio_key) or qualificacao_societaria in x)
        ]
    @staticmethod
    def filter_for_parentes_vinculos(item: Dict, entity_type, search_value: str) -> List:
        return [
            x for x in item
            if compare_and_update_vinculo(x, parentes_vinculo, full_match=False)
        ]

    @staticmethod
    def filter_for_not_parentes_vinculos(item: Dict, entity_type, search_value: str) -> List:
        return [
            x for x in item
            if not compare_and_update_vinculo(x, parentes_vinculo, full_match=False)
        ]

    @staticmethod
    def filter_for_not_educacional_or_empregaticio_vinculos(item: Dict, entity_type, search_value: str) -> List:

        return [
            x for x in item
            if not compare_and_update_vinculo(x, educacional_vinculo, full_match=True)
            and not compare_and_update_vinculo(x, empregos_vinculo, full_match=True)
        ]

    @staticmethod
    def filter_for_outros_contatos_vinculo(item: Dict, entity_type, search_value: str) -> List:
        return [
            x for x in item
            if compare_and_update_vinculo(x, outros_contatos_vinculo, full_match=False)
        ]

    @staticmethod
    def processos_extra_vinculos_callback(item: Dict, entity_type, search_value: str) -> Dict:
        """Process extra vinculos for processos"""

        if entity_type == ReportKeys.PESSOA:
            search_types = [ReportKeys.PESSOA, ReportKeys.ADVOGADO, ReportKeys.SERVIDOR]
        elif entity_type == ReportKeys.EMPRESA:
            search_types = [ReportKeys.EMPRESA]
        else:
            raise ValueError(f"Invalid entity type for processos search: {entity_type}")

        result = {}
        get_keys = [numero_processo_key]
        na_vinculo = vinculo_autor_processo_nao_identificado_key

        for key in search_types:
            entities = item.get(key, [])
            for entity_dict in entities:
                if is_a_match(entity_dict, entity_type, search_value):
                    vinculo = get_vinculo(entity_dict)
                    if vinculo:
                        vinculo = unidecode(vinculo).lower()
                        the_data = {k: item[k] for k in get_keys if k in item}
                        if the_data not in result.get(vinculo, []):
                            result.setdefault(vinculo, []).append(the_data)
        if not result:
            the_data = {k: item[k] for k in get_keys if k in item}
            if the_data not in result.get(na_vinculo, []):
                result.setdefault(na_vinculo, []).append(the_data)

        return result

    @staticmethod
    def mandados_item_callback(item: Dict, *_) -> Dict:
        """Process mandados items"""
        pena = [
            {k: p[k] for k in pena_use_keys if k in p}
            for p in item.get(ReportKeys.PESSOA, [])
        ]
        # TODO: ver de adicionar na pessoa principal depois
        item.pop(ReportKeys.PESSOA, None)
        item[ReportKeys.PENA] = pena
        return item

    @staticmethod
    def recursos_recebidos_item_callback(item: Dict, entity_type, search_value: str) -> Dict:
        """Process recursos recebidos items"""

        vinculo = get_vinculo(item)
        if vinculo and vinculo in recursos_recebidos_skip_label_keys:
            return {}
        return item

    @staticmethod
    def processos_item_callback(item: Dict, entity_type, search_value: str) -> Dict:
        """Process processos items"""
        # Process entity relationships
        for key in [ReportKeys.PESSOA, ReportKeys.EMPRESA]:
            for entity_dict in item.get(key, []):
                vinculo = get_vinculo(entity_dict)
                if vinculo:
                    vinculo = unidecode(vinculo).lower()
                    item.setdefault(vinculo, []).append(entity_dict)

            item.pop(key, None)

        # Process movimentacoes
        if p_movimentacoes_key in item:
            item[unidecode(p_movimentacoes_key).lower()] = [item.pop(p_movimentacoes_key)]

        return item

    @staticmethod
    def _process_parentes_data(cur_entity: Dict, entity_type, search_value: str) -> Dict:
        """Process parent/family relationship data"""
        if ReportKeys.PESSOA not in cur_entity:
            return cur_entity

        sub_parentes = [
            x for x in cur_entity[ReportKeys.PESSOA]
            if compare_and_update_vinculo(x, parentes_vinculo, full_match=False)
        ]

        # Extract parent information
        parent_configs = [
            (parentes_mae_vinculo, parentes_mae_vinculo.lower()),
            (parentes_pai_vinculo, parentes_pai_vinculo.lower())
        ]

        for parent_type, key in parent_configs:
            found = [x for x in sub_parentes if compare_and_update_vinculo(x, parent_type, full_match=False)]
            if found and nome_completo_key in found[0]:
                cur_entity[key] = found[0][nome_completo_key]

        del cur_entity[ReportKeys.PESSOA]
        return cur_entity

    @staticmethod
    def sociedade_data_processor_callback(base_data, sources):
        """Process sociedade data"""

        formatted_data = defaultdict(list)

        for idx, data in enumerate(base_data):
            source = sources[idx]
            cur_sociedades = []
            for cur_entity in data:
                # Extract related entities
                cur_entity_basic_data = {x: cur_entity[x] for x in ReportType.CNPJ.base_data_keys if x in cur_entity}

                related_people = cur_entity.pop(ReportKeys.PESSOA, [])
                related_companies = cur_entity.pop(ReportKeys.EMPRESA, [])

                # Add to formatted data
                cur_sociedades.append(
                    map_to_front_format(cur_entity, source, skip_lists=False)
                )

                for person in related_people:
                    this_person = person.copy()
                    the_company = cur_entity_basic_data.copy()
                    the_company[label_key] = this_person.pop(qualificacao_societaria, "")
                    this_person[ReportKeys.EMPRESA] = [the_company]

                    formatted_data["socios_p"] = merge_entity_lists(formatted_data["socios_p"], [map_to_front_format(this_person, source, skip_lists=False)])


                for company in related_companies:
                    this_company = company.copy()
                    the_company = cur_entity_basic_data.copy()
                    the_company[label_key] = this_company.pop(qualificacao_societaria, "")
                    this_company[ReportKeys.EMPRESA] = [the_company]

                    formatted_data["socios_e"] = merge_entity_lists(formatted_data["socios_e"], [map_to_front_format(this_company, source, skip_lists=False)])

            formatted_data["sociedades"] = merge_entity_lists(formatted_data["sociedades"],
                                                              cur_sociedades)

        return formatted_data

    @staticmethod
    def _flat_entities_iterative(data, use_keys):
        result = []
        base_entity = ReportKeys.PESSOA if ReportKeys.CPF in data else ReportKeys.EMPRESA
        stack = [(data, base_entity)]  # start with the root

        while stack:
            current, tipo = stack.pop()

            # Extract entity info (exclude children keys)
            entity = {k: v for k, v in current.items() if k not in use_keys}
            entity[ReportKeys.TIPO] = tipo
            result.append(entity)

            # Push children onto the stack (reversed so they stay in order)
            for key in use_keys:
                if key in current and isinstance(current[key], list):
                    for child in reversed(current[key]):
                        stack.append((child, key))

        return result

    @staticmethod
    def relacoes_data_processor_callback(base_data, sources):
        """Process relacoes data"""

        formatted_data = []

        for idx, data in enumerate(base_data):
            for cur_entity in data:
                formatted_data.extend(CallbackProcessors._flat_entities_iterative(cur_entity, use_keys=[ReportKeys.PESSOA, ReportKeys.EMPRESA]))

        formatted_data = [map_to_front_format(x, sources[0], skip_lists=True) for x in formatted_data]

        return formatted_data

    @staticmethod
    def filter_for_redes_sociais_vinculos(item: Dict, entity_type, search_value: str):

        if ReportKeys.GENERIC_REDE_SOCIAL in item:
            for idx, x in enumerate(item[ReportKeys.GENERIC_REDE_SOCIAL]):
                new_key = get_vinculo(x)
                if new_key:
                    if new_key in item:
                        item[new_key].append(x)
                    else:
                        item[new_key] = [x]
                else:
                    logger.warning(f"rede social nao encontrada for item with keys {item.keys()}")
                    return dict()
            del item[ReportKeys.GENERIC_REDE_SOCIAL]

        else:
            it_keys = list(item.keys())
            for key in it_keys:
                item[key[len(rede_social_key_prefix):]] = item.pop(key)

        return item

    @staticmethod
    def filter_for_vinculos_educacionais(item: Dict, entity_type, search_value: str):
        return [
            x for x in item
            if compare_and_update_vinculo(x, educacional_vinculo, full_match=True)
        ]

    @staticmethod
    def filter_for_vinculos_empregaticio(item: Dict, entity_type, search_value: str):
        return [
            x for x in item
            if compare_and_update_vinculo(x, empregos_vinculo, full_match=True)
        ]

    @staticmethod
    def vinculos_educacionais_item_callback(item: Dict, *_):
        """Process items"""

        vinculos = item.get(ReportKeys.VINCULO)
        merged_items = []

        if isinstance(vinculos, list) and all(isinstance(v, dict) for v in vinculos):
            for vinculo in vinculos:
                merged_items.append({**item, **vinculo})  # shallow merge
                merged_items[-1].pop(ReportKeys.VINCULO, None)

        elif isinstance(vinculos, dict) and 'value' in vinculos and all(isinstance(v, dict) for v in vinculos['value']):
            for vinculo in vinculos['value']:
                if is_front_formatted(vinculo):
                    merged_items.append({**item, **vinculo})
                else:
                    tmp = {k: basic_property_dict(v, get_constant(Constants.PropToLabel, k, k), vinculos['source']) for k, v in vinculo.items()}
                    merged_items.append({**item, **tmp})
                merged_items[-1].pop(ReportKeys.VINCULO, None)

        return merged_items

