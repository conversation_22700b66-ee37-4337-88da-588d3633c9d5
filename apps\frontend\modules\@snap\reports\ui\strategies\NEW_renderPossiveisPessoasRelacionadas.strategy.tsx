import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { formatFieldValue, parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { isBase64Image, isValidUrl, renderSourceTooltip } from "./helpers.strategy";
import { ValidatedImage } from "../components/ValidateImage";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { PossivelPessoaRelacionada } from "../../model/PossiveisPessoasRelacionadas";
import { ItemSeparator } from "../components/ItemSeparator";

export function useRenderPossiveisPessoasRelacionadas(
  sectionTitle: string
): ArrayRenderStrategy<PossivelPessoaRelacionada> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<PossivelPessoaRelacionada, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  const testNomeCompletoDeleted = (e: any) => e.nome_completo?.is_deleted === true;
  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted === true)
      : true;
  const testTelefonesDeleted = (e: any) =>
    e.telefones
      ? e.telefones.every((t: any) =>
        Object.values(t.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testEmailsDeleted = (e: any) =>
    e.emails
      ? e.emails.every((email: any) =>
        Object.values(email.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testEnderecosDeleted = (e: any) =>
    e.enderecos
      ? e.enderecos.every((end: any) =>
        Object.values(end.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testImagensDeleted = (e: any) =>
    e.imagens
      ? e.imagens.every((img: any) =>
        Object.values(img.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testRedesSociaisDeleted = (e: any) =>
    e.redes_sociais
      ? e.redes_sociais.every((redesEntry: any) =>
        redesEntry.detalhes
          ? redesEntry.detalhes.every((detalhe: any) => {
            if (detalhe.value) {
              return Object.values(detalhe.value).every((platform: any) => {
                if (Array.isArray(platform)) {
                  return platform.every((profile: any) =>
                    Object.values(profile).every((field: any) => field.is_deleted === true)
                  );
                }
                return true;
              });
            }
            return detalhe.is_deleted === true;
          })
          : true
      )
      : true;

  const testGenericListsDeleted = (e: any) => {
    const genericListKeys = Object.keys(e).filter(key =>
      key !== 'nome_completo' &&
      key !== 'detalhes' &&
      key !== 'telefones' &&
      key !== 'emails' &&
      key !== 'enderecos' &&
      key !== 'imagens' &&
      key !== 'redes_sociais' &&
      Array.isArray(e[key])
    );

    if (genericListKeys.length === 0) return true;

    return genericListKeys.every(key => {
      const list = e[key] as any[];
      return list.every((item: any) => {
        if (item.value && typeof item.value === 'object') {
          return Object.values(item.value).every((field: any) => field.is_deleted === true);
        }
        return item.is_deleted === true;
      });
    });
  };

  const testEntryDeleted = (entry: any): boolean => {
    const isNomeCompletoDeleted = testNomeCompletoDeleted(entry);
    const areDetalhesDeleted = testDetalhesDeleted(entry);
    const areTelefonesDeleted = testTelefonesDeleted(entry);
    const areEmailsDeleted = testEmailsDeleted(entry);
    const areEnderecosDeleted = testEnderecosDeleted(entry);
    const areImagensDeleted = testImagensDeleted(entry);
    const areRedesSociaisDeleted = testRedesSociaisDeleted(entry);
    const areGenericListsDeleted = testGenericListsDeleted(entry);

    return isNomeCompletoDeleted && areDetalhesDeleted && areTelefonesDeleted && areEnderecosDeleted && areImagensDeleted && areRedesSociaisDeleted && areEmailsDeleted && areGenericListsDeleted;
  };

  const testSectionDeleted = (section: any): boolean => {
    if (!section?.data?.length) return true;
    return section.data.every((entry: any) => testEntryDeleted(entry));
  };

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      // Count non-deleted entries (whole objects)
      // An entry is considered deleted only when ALL its parts are deleted
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  };

  const onToggleTelefonesField = (idx: number, telefoneIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        // Only update the specific entry
        if (entryIndex === idx) {
          const telefone = entry.telefones?.[telefoneIdx];
          if (telefone?.value?.[fieldKey]) {
            telefone.value[fieldKey].is_deleted = !telefone.value[fieldKey].is_deleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleEmailsField = (idx: number, emailIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        // Only update the specific entry
        if (entryIndex === idx) {
          const email = entry.emails?.[emailIdx];
          if (email?.value?.[fieldKey]) {
            email.value[fieldKey].is_deleted = !email.value[fieldKey].is_deleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleEnderecosField = (idx: number, enderecoIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        // Only update the specific entry
        if (entryIndex === idx) {
          const endereco = entry.enderecos?.[enderecoIdx];
          if (endereco?.value?.[fieldKey]) {
            endereco.value[fieldKey].is_deleted = !endereco.value[fieldKey].is_deleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleImagensField = (idx: number, imagemIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        // Only update the specific entry
        if (entryIndex === idx) {
          const imagem = entry.imagens?.[imagemIdx];
          if (imagem?.value?.[fieldKey]) {
            imagem.value[fieldKey].is_deleted = !imagem.value[fieldKey].is_deleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const shouldIncludePlatform = (profiles: any[]) => {
    return profiles.some(profile =>
      Object.values(profile).some((field: any) => includeKey(field.is_deleted))
    );
  };

  const shouldIncludeProfile = (profile: any) => {
    return Object.values(profile).some((field: any) => includeKey(field.is_deleted));
  };

  const onTogglePlatform = (idx: number, detalheIdx: number, platform: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        // Only update the specific entry
        if (entryIndex === idx) {
          const redesEntry = entry.redes_sociais?.[0]; // Assuming single redes_sociais entry
          const detalhe = redesEntry?.detalhes?.[detalheIdx];
          if (detalhe?.value?.[platform]) {
            const profiles = detalhe.value[platform];
            // Determine the target state based on current mode
            const targetDeletedState = isTrash ? false : true;

            // Set all profiles in this platform to the target state
            profiles.forEach((profile: any) => {
              Object.values(profile).forEach((field: any) => {
                if (field && typeof field === 'object' && 'is_deleted' in field) {
                  field.is_deleted = targetDeletedState;
                }
              });
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleProfile = (idx: number, detalheIdx: number, platform: string, profileIdx: number) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        // Only update the specific entry
        if (entryIndex === idx) {
          const redesEntry = entry.redes_sociais?.[0];
          const profile = redesEntry?.detalhes?.[detalheIdx]?.value?.[platform]?.[profileIdx];
          if (profile) {
            // Determine the target state based on current mode
            const targetDeletedState = isTrash ? false : true;

            Object.values(profile).forEach((field: any) => {
              if (field && typeof field === 'object' && 'is_deleted' in field) {
                field.is_deleted = targetDeletedState;
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onTogglePlatformField = (idx: number, detalheIdx: number, platform: string, profileIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        // Only update the specific entry
        if (entryIndex === idx) {
          const redesEntry = entry.redes_sociais?.[0];
          const field = redesEntry?.detalhes?.[detalheIdx]?.value?.[platform]?.[profileIdx]?.[fieldKey];
          if (field) {
            field.is_deleted = !field.is_deleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleGenericListTitle = (idx: number, listKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        // Only update the specific entry
        if (entryIndex === idx) {
          const list = entry[listKey] as any[];
          if (Array.isArray(list)) {
            // Determine the target state based on current mode
            const targetDeletedState = isTrash ? false : true;

            // Apply to all items and their fields in the list
            list.forEach((item: any) => {
              item.is_deleted = targetDeletedState;
              if (item.value && typeof item.value === 'object') {
                Object.values(item.value).forEach((field: any) => {
                  if (field && typeof field === 'object' && 'is_deleted' in field) {
                    field.is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleGenericListItem = (idx: number, listKey: string, itemIdx: number) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        // Only update the specific entry
        if (entryIndex === idx) {
          const item = entry[listKey]?.[itemIdx];
          if (item) {
            if (item.value && typeof item.value === 'object') {
              // Determine the target state based on current mode
              const targetDeletedState = isTrash ? false : true;

              // Set the item's is_deleted state
              item.is_deleted = targetDeletedState;

              // Set all fields within the item to the same state
              Object.values(item.value).forEach((field: any) => {
                if (field && typeof field === 'object' && 'is_deleted' in field) {
                  field.is_deleted = targetDeletedState;
                }
              });
            } else {
              item.is_deleted = !item.is_deleted;
            }
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleGenericListField = (idx: number, listKey: string, itemIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        // Only update the specific entry
        if (entryIndex === idx) {
          const field = entry[listKey]?.[itemIdx]?.value?.[fieldKey];
          if (field) {
            field.is_deleted = !field.is_deleted;

            // Update item's is_deleted based on all fields
            const item = entry[listKey]?.[itemIdx];
            if (item?.value) {
              const allFieldsDeleted = Object.values(item.value).every((f: any) => f.is_deleted === true);
              item.is_deleted = allFieldsDeleted;
            }
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const formatByKey: Record<
    string,
    (entry?: PossivelPessoaRelacionada) => React.ReactElement | null
  > = {
    nome_completo: (entry) => {
      if (!entry?.nome_completo || !includeKey(entry.nome_completo.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer cols={1} key={`nome-completo-${idx}`}>
          <CustomGridItem
            fullWidth
            className="mb-6"
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === idx && e.nome_completo) {
                    e.nome_completo.is_deleted = !e.nome_completo.is_deleted;

                    // Cascading deletion logic: when nome_completo is deleted, delete all other fields
                    // When nome_completo is restored, restore all other fields
                    const targetDeletedState = e.nome_completo.is_deleted;

                    // Apply to all detalhes
                    if (e.detalhes) {
                      Object.values(e.detalhes).forEach((campo: any) => {
                        if (campo) campo.is_deleted = targetDeletedState;
                      });
                    }

                    // Apply to telefones array
                    if (e.telefones) {
                      e.telefones.forEach((t: any) => {
                        t.is_deleted = targetDeletedState;
                        if (t.value) {
                          Object.values(t.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    if (e.emails) {
                      e.emails.forEach((email: any) => {
                        email.is_deleted = targetDeletedState;
                        if (email.value) {
                          Object.values(email.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    // Apply to enderecos array
                    if (e.enderecos) {
                      e.enderecos.forEach((end: any) => {
                        end.is_deleted = targetDeletedState;
                        if (end.value) {
                          Object.values(end.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    // Apply to imagens array
                    if (e.imagens) {
                      e.imagens.forEach((img: any) => {
                        img.is_deleted = targetDeletedState;
                        if (img.value) {
                          Object.values(img.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    // Apply to redes_sociais array
                    if (e.redes_sociais) {
                      e.redes_sociais.forEach((redesEntry: any) => {
                        if (redesEntry.detalhes) {
                          redesEntry.detalhes.forEach((detalhe: any) => {
                            detalhe.is_deleted = targetDeletedState;
                            if (detalhe.value) {
                              Object.values(detalhe.value).forEach((platform: any) => {
                                if (Array.isArray(platform)) {
                                  platform.forEach((profile: any) => {
                                    Object.values(profile).forEach((field: any) => {
                                      if (field) field.is_deleted = targetDeletedState;
                                    });
                                  });
                                }
                              });
                            }
                          });
                        }
                      });
                    }

                    // Apply to generic lists (following participantKeys pattern from NEW_renderProcessos.strategy.tsx)
                    const genericListKeys = Object.keys(e).filter(key =>
                      key !== 'nome_completo' &&
                      key !== 'detalhes' &&
                      key !== 'telefones' &&
                      key !== 'emails' &&
                      key !== 'enderecos' &&
                      key !== 'imagens' &&
                      key !== 'redes_sociais' &&
                      Array.isArray(e[key])
                    );

                    genericListKeys.forEach(key => {
                      if (Array.isArray(e[key])) {
                        e[key].forEach((item: any) => {
                          item.is_deleted = targetDeletedState;
                          if (item.value && typeof item.value === 'object') {
                            Object.keys(item.value).forEach(fieldKey => {
                              if (item.value[fieldKey]) {
                                item.value[fieldKey].is_deleted = targetDeletedState;
                              }
                            });
                          }
                        });
                      }
                    });
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={entry.nome_completo.label.toUpperCase()}
              colorClass="bg-primary"
              labelTextClass="text-accent"
              value={parseValue(formatFieldValue(entry.nome_completo.value))}
              tooltip={renderSourceTooltip(entry.nome_completo.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    detalhes: (entry) => {
      if (!entry?.detalhes) return null;
      const subs = Object.entries(entry.detalhes).filter(([, v]) =>
        includeKey((v as any).is_deleted)
      );
      if (!subs.length) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer
          cols={2}
          columnFirst
          key={`detalhes-${idx}`}
          className="mb-6"
        >
          {subs.map(([fieldKey, val]) => (
            <CustomGridItem
              key={fieldKey}
              cols={1}
              onToggleField={() =>
                actions.updateSectionEntries!(
                  sectionTitle,
                  (e: any, i?: number) => {
                    if (i === idx) {
                      const d = e.detalhes?.[fieldKey];
                      if (d) d.is_deleted = !d.is_deleted;
                    }
                  },
                  testEntryDeleted,
                  testSectionDeleted,
                  calculateDataCount
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
                value={parseValue(formatFieldValue((val as any).value))}
                tooltip={renderSourceTooltip((val as any).source)}
              />
            </CustomGridItem>
          ))}
        </CustomGridContainer>
      );
    },

    telefones: (entry) => {
      if (!entry?.telefones?.length) return null;
      const idx = idxMap.get(entry)!;

      const filteredTelefones = entry.telefones.filter((telefone: any) => {
        if (telefone.value) {
          return Object.values(telefone.value).some((field: any) => includeKey(field.is_deleted));
        }
        return includeKey(telefone.is_deleted);
      });

      if (filteredTelefones.length === 0) return null;

      return (
        <CustomGridContainer cols={1} key={`telefones-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => {
              // Toggle all telefones
              const updater = actions.updateSectionEntries;
              if (!updater) return;

              // Determine the target state based on current mode
              const targetDeletedState = isTrash ? false : true;

              updater(
                sectionTitle,
                (entryToUpdate, entryIndex) => {
                  // Only update the specific entry
                  if (entryIndex === idx && entryToUpdate.telefones) {
                    entryToUpdate.telefones.forEach((telefone: any) => {
                      telefone.is_deleted = targetDeletedState;
                      if (telefone.value) {
                        Object.values(telefone.value).forEach((field: any) => {
                          if (field && typeof field === 'object' && 'is_deleted' in field) {
                            field.is_deleted = targetDeletedState;
                          }
                        });
                      }
                    });
                  }
                },
                testEntryDeleted as any,
                testSectionDeleted,
                calculateDataCount
              );
            }}
          >
            <ReportsCustomLabel
              label="TELEFONES"
              colorClass="bg-primary"
            />
          </CustomGridItem>
          <CustomGridContainer cols={2}>
            {filteredTelefones.map((telefone: any, renderIdx: number) => {
              const originalIdx = entry.telefones!.indexOf(telefone);

              if (telefone.value) {
                return (
                  <div key={`tel-${idx}-${originalIdx}`} className="mb-4">
                    <CustomGridItem
                      cols={1}
                      containerClassName="w-fit pr-12 pb-2"
                      onToggleField={() => {
                        // Toggle this specific telefone
                        const updater = actions.updateSectionEntries;
                        if (!updater) return;

                        // Determine the target state based on current mode
                        const targetDeletedState = isTrash ? false : true;

                        updater(
                          sectionTitle,
                          (entryToUpdate, entryIndex) => {
                            // Only update the specific entry
                            if (entryIndex === idx) {
                              const telefoneToUpdate = entryToUpdate.telefones?.[originalIdx];
                              if (telefoneToUpdate) {
                                telefoneToUpdate.is_deleted = targetDeletedState;
                                if (telefoneToUpdate.value) {
                                  Object.values(telefoneToUpdate.value).forEach((field: any) => {
                                    if (field && typeof field === 'object' && 'is_deleted' in field) {
                                      field.is_deleted = targetDeletedState;
                                    }
                                  });
                                }
                              }
                            }
                          },
                          testEntryDeleted as any,
                          testSectionDeleted,
                          calculateDataCount
                        );
                      }}
                    >
                      <ReportsCustomLabel
                        label={`TELEFONE ${!isTrash ? renderIdx + 1 : ""}`}
                        colorClass="bg-border"
                      />
                    </CustomGridItem>
                    <div className="pl-5">
                      <CustomGridContainer cols={1}>
                        {Object.entries(telefone.value)
                          .filter(([_, fieldValue]: any) => includeKey(fieldValue.is_deleted))
                          .map(([fieldKey, fieldValue]: any, index) => (
                            <CustomGridItem
                              key={`telefone-${originalIdx}-${fieldKey}`}
                              cols={1}
                              className="py-1"
                              onToggleField={() => onToggleTelefonesField(idx, originalIdx, fieldKey)}
                            >
                              <CustomReadOnlyInputField
                                label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                value={parseValue(formatFieldValue(fieldValue.value))}
                                isFirstLabelList={index === 0}
                                icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                                tooltip={renderSourceTooltip(fieldValue.source)}
                              />
                            </CustomGridItem>
                          ))}
                      </CustomGridContainer>
                    </div>
                  </div>
                );
              }
              return null;
            })}
          </CustomGridContainer>
        </CustomGridContainer>
      );
    },

    emails: (entry) => {
      if (!entry?.emails?.length) return null;
      const idx = idxMap.get(entry)!;

      const filteredEmails = entry.emails.filter((email: any) => {
        if (email.value) {
          return Object.values(email.value).some((field: any) => includeKey(field.is_deleted));
        }
        return includeKey(email.is_deleted);
      });

      if (filteredEmails.length === 0) return null;

      return (
        <CustomGridContainer cols={1} key={`emails-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => {
              // Toggle all emails
              const updater = actions.updateSectionEntries;
              if (!updater) return;

              // Determine the target state based on current mode
              const targetDeletedState = isTrash ? false : true;

              updater(
                sectionTitle,
                (entryToUpdate, entryIndex) => {
                  // Only update the specific entry
                  if (entryIndex === idx && entryToUpdate.emails) {
                    entryToUpdate.emails.forEach((email: any) => {
                      email.is_deleted = targetDeletedState;
                      if (email.value) {
                        Object.values(email.value).forEach((field: any) => {
                          if (field && typeof field === 'object' && 'is_deleted' in field) {
                            field.is_deleted = targetDeletedState;
                          }
                        });
                      }
                    });
                  }
                },
                testEntryDeleted as any,
                testSectionDeleted,
                calculateDataCount
              );
            }}
          >
            <ReportsCustomLabel
              label="EMAILS"
              colorClass="bg-primary"
            />
          </CustomGridItem>
          <CustomGridContainer cols={2}>
            {filteredEmails.map((email: any, renderIdx: number) => {
              const originalIdx = entry.emails!.indexOf(email);

              if (email.value) {
                return (
                  <div key={`email-${idx}-${originalIdx}`} className="mb-4">
                    <CustomGridItem
                      cols={1}
                      containerClassName="w-fit pr-12 pb-2"
                      onToggleField={() => {
                        const updater = actions.updateSectionEntries;
                        if (!updater) return;

                        // Determine the target state based on current mode
                        const targetDeletedState = isTrash ? false : true;

                        updater(
                          sectionTitle,
                          (entryToUpdate, entryIndex) => {
                            // Only update the specific entry
                            if (entryIndex === idx) {
                              const emailToUpdate = entryToUpdate.emails?.[originalIdx];
                              if (emailToUpdate) {
                                emailToUpdate.is_deleted = targetDeletedState;
                                if (emailToUpdate.value) {
                                  Object.values(emailToUpdate.value).forEach((field: any) => {
                                    if (field && typeof field === 'object' && 'is_deleted' in field) {
                                      field.is_deleted = targetDeletedState;
                                    }
                                  });
                                }
                              }
                            }
                          },
                          testEntryDeleted as any,
                          testSectionDeleted,
                          calculateDataCount
                        );
                      }}
                    >
                      <ReportsCustomLabel
                        label={`EMAIL ${!isTrash ? renderIdx + 1 : ""}`}
                        colorClass="bg-border"
                      />
                    </CustomGridItem>
                    <div className="pl-5">
                      <CustomGridContainer cols={1}>
                        {Object.entries(email.value)
                          .filter(([_, fieldValue]: any) => includeKey(fieldValue.is_deleted))
                          .map(([fieldKey, fieldValue]: any, index) => (
                            <CustomGridItem
                              key={`email-${originalIdx}-${fieldKey}`}
                              cols={1}
                              className="py-1"
                              onToggleField={() => onToggleEmailsField(idx, originalIdx, fieldKey)}
                            >
                              <CustomReadOnlyInputField
                                label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                value={parseValue(formatFieldValue(fieldValue.value))}
                                isFirstLabelList={index === 0}
                                icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                                tooltip={renderSourceTooltip(fieldValue.source)}
                              />
                            </CustomGridItem>
                          ))}
                      </CustomGridContainer>
                    </div>
                  </div>
                );
              }
              return null;
            })}
          </CustomGridContainer>
        </CustomGridContainer>
      );
    },

    enderecos: (entry) => {
      if (!entry?.enderecos?.length) return null;
      const idx = idxMap.get(entry)!;

      const filteredEnderecos = entry.enderecos.filter((endereco: any) => {
        if (endereco.value) {
          return Object.values(endereco.value).some((field: any) => includeKey(field.is_deleted));
        }
        return includeKey(endereco.is_deleted);
      });

      if (filteredEnderecos.length === 0) return null;

      return (
        <CustomGridContainer cols={1} key={`enderecos-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => {
              // Toggle all enderecos
              const updater = actions.updateSectionEntries;
              if (!updater) return;

              // Determine the target state based on current mode
              const targetDeletedState = isTrash ? false : true;

              updater(
                sectionTitle,
                (entryToUpdate, entryIndex) => {
                  // Only update the specific entry
                  if (entryIndex === idx && entryToUpdate.enderecos) {
                    entryToUpdate.enderecos.forEach((endereco: any) => {
                      endereco.is_deleted = targetDeletedState;
                      if (endereco.value) {
                        Object.values(endereco.value).forEach((field: any) => {
                          if (field && typeof field === 'object' && 'is_deleted' in field) {
                            field.is_deleted = targetDeletedState;
                          }
                        });
                      }
                    });
                  }
                },
                testEntryDeleted as any,
                testSectionDeleted,
                calculateDataCount
              );
            }}
          >
            <ReportsCustomLabel
              label="ENDEREÇOS"
              colorClass="bg-primary"
            />
          </CustomGridItem>
          {filteredEnderecos.map((endereco: any, renderIdx: number) => {
            const originalIdx = entry.enderecos!.indexOf(endereco);

            if (endereco.value) {
              return (
                <div key={`end-${idx}-${originalIdx}`} className="mb-4">
                  <CustomGridItem
                    cols={1}
                    containerClassName="w-fit pr-12 pb-2"
                    onToggleField={() => {
                      // Toggle this specific endereco
                      const updater = actions.updateSectionEntries;
                      if (!updater) return;

                      // Determine the target state based on current mode
                      const targetDeletedState = isTrash ? false : true;

                      updater(
                        sectionTitle,
                        (entryToUpdate, entryIndex) => {
                          // Only update the specific entry
                          if (entryIndex === idx) {
                            const enderecoToUpdate = entryToUpdate.enderecos?.[originalIdx];
                            if (enderecoToUpdate) {
                              enderecoToUpdate.is_deleted = targetDeletedState;
                              if (enderecoToUpdate.value) {
                                Object.values(enderecoToUpdate.value).forEach((field: any) => {
                                  if (field && typeof field === 'object' && 'is_deleted' in field) {
                                    field.is_deleted = targetDeletedState;
                                  }
                                });
                              }
                            }
                          }
                        },
                        testEntryDeleted as any,
                        testSectionDeleted,
                        calculateDataCount
                      );
                    }}
                  >
                    <ReportsCustomLabel
                      label={`ENDEREÇO ${!isTrash ? renderIdx + 1 : ""}`}
                      colorClass="bg-border"
                    />
                  </CustomGridItem>
                  <div className="pl-5">
                    <CustomGridContainer cols={2}>
                      {Object.entries(endereco.value)
                        .filter(([_, fieldValue]: any) => includeKey(fieldValue.is_deleted))
                        .map(([fieldKey, fieldValue]: any, index) => (
                          <CustomGridItem
                            key={`endereco-${originalIdx}-${fieldKey}`}
                            cols={1}
                            className="py-1"
                            onToggleField={() => onToggleEnderecosField(idx, originalIdx, fieldKey)}
                          >
                            <CustomReadOnlyInputField
                              label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                              value={parseValue(formatFieldValue(fieldValue.value))}
                              isFirstLabelList={index === 0}
                              icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                              tooltip={renderSourceTooltip(fieldValue.source)}
                            />
                          </CustomGridItem>
                        ))}
                    </CustomGridContainer>
                  </div>
                </div>
              );
            }
            return null;
          })}
        </CustomGridContainer>
      );
    },

    imagens: (entry) => {
      if (!entry?.imagens?.length) return null;
      const idx = idxMap.get(entry)!;

      const filteredImagens = entry.imagens.filter((imagem: any) => {
        if (imagem.value) {
          return Object.values(imagem.value).some((field: any) => includeKey(field.is_deleted));
        }
        return includeKey(imagem.is_deleted);
      });

      if (filteredImagens.length === 0) return null;

      return (
        <CustomGridContainer cols={1} key={`imagens-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => {
              // Toggle all imagens
              const updater = actions.updateSectionEntries;
              if (!updater) return;

              // Determine the target state based on current mode
              const targetDeletedState = isTrash ? false : true;

              updater(
                sectionTitle,
                (entryToUpdate, entryIndex) => {
                  // Only update the specific entry
                  if (entryIndex === idx && entryToUpdate.imagens) {
                    entryToUpdate.imagens.forEach((imagem: any) => {
                      imagem.is_deleted = targetDeletedState;
                      if (imagem.value) {
                        Object.values(imagem.value).forEach((field: any) => {
                          if (field && typeof field === 'object' && 'is_deleted' in field) {
                            field.is_deleted = targetDeletedState;
                          }
                        });
                      }
                    });
                  }
                },
                testEntryDeleted as any,
                testSectionDeleted,
                calculateDataCount
              );
            }}
          >
            <ReportsCustomLabel
              label="IMAGENS"
              colorClass="bg-primary"
            />
          </CustomGridItem>

          <CustomGridContainer cols={4}>
            {(() => {
              let imageCounter = 1;

              return filteredImagens.map((imagem: any) => {
                const originalIdx = entry.imagens!.indexOf(imagem);

                if (imagem.value) {
                  return Object.entries(imagem.value)
                    .filter(([_, fieldValue]: any) => includeKey(fieldValue.is_deleted))
                    .map(([fieldKey, fieldValue]: any) => {
                      const currentImageNumber = imageCounter++;

                      return (
                        <CustomGridItem
                          key={`imagem-${originalIdx}-${fieldKey}`}
                          cols={1}
                          onToggleField={() => onToggleImagensField(idx, originalIdx, fieldKey)}
                        >
                          <div className="py-2 group">
                            <CustomReadOnlyInputField
                              label={`IMAGEM ${currentImageNumber}`}
                              value={parseValue(formatFieldValue(fieldValue.value))}
                              tooltip={renderSourceTooltip(fieldValue.source)}
                            />
                            <ValidatedImage
                              src={String(fieldValue.value)}
                              alt={`Imagem ${currentImageNumber}`}
                              className="w-full max-w-full h-48 mx-auto mt-2 bg-background/40 rounded-sm"
                            />
                          </div>
                        </CustomGridItem>
                      );
                    });
                }
                return null;
              });
            })()}
          </CustomGridContainer>
        </CustomGridContainer>
      );
    },

    redes_sociais: (entry) => {
      if (!entry?.redes_sociais?.length) return null;
      const idx = idxMap.get(entry)!;

      const redesEntry = entry.redes_sociais[0]; // Assuming single redes_sociais entry
      if (!redesEntry?.detalhes?.length) return null;

      const platformElements: React.ReactElement[] = [];

      // First, collect all platform elements
      redesEntry.detalhes.forEach((detalhe: any, detalheIdx: number) => {
        if (!detalhe.value) return;

        // Process each platform in this detalhe
        Object.entries(detalhe.value).forEach(([platform, profiles]) => {
          if (!Array.isArray(profiles) || !shouldIncludePlatform(profiles)) return;

          // Format platform name for display
          let platformName = platform.charAt(0).toUpperCase() + platform.slice(1);
          if (platform === "x (twitter)") platformName = "X (Twitter)";

          // Filter profiles to show
          const filteredProfiles = profiles
            .map((profile: any, profileIdx: number) => ({ profile, originalIdx: profileIdx }))
            .filter(({ profile }) => shouldIncludeProfile(profile));

          if (filteredProfiles.length === 0) return;

          platformElements.push(
            <CustomGridContainer cols={1} key={`platform-${platform}-${detalheIdx}`} className="mb-4">
              <CustomGridItem
                cols={1}
                containerClassName="w-fit pr-12"
                onToggleField={() => onTogglePlatform(idx, detalheIdx, platform)}
              >
                <ReportsCustomLabel
                  label={platformName.toUpperCase()}
                  colorClass="bg-primary"
                />
              </CustomGridItem>
              {filteredProfiles.map(({ profile, originalIdx }, renderIdx) => (
                <div key={`${platform}-profile-${originalIdx}`} className="mb-4">
                  <CustomGridItem
                    cols={1}
                    className="py-1"
                    containerClassName="w-fit pr-12 pb-2"
                    onToggleField={() => onToggleProfile(idx, detalheIdx, platform, originalIdx)}
                  >
                    <ReportsCustomLabel
                      label={`${platformName.toUpperCase()} ${!isTrash ? renderIdx + 1 : ""}`}
                      colorClass="bg-border"
                    />
                  </CustomGridItem>
                  <div className="pl-5">
                    <CustomGridContainer cols={2}>
                      {Object.entries(profile)
                        .filter(([_, fieldValue]: any) =>
                          isTrash ? fieldValue.is_deleted : !fieldValue.is_deleted
                        )
                        .map(([fieldKey, fieldValue]: any, index) => {
                          const isImageValue = !Array.isArray(fieldValue.value) &&
                            (isValidUrl(fieldValue.value) || isBase64Image(fieldValue.value));
                          if (isImageValue) {
                            return (
                              <CustomGridItem
                                key={`${platform}-${originalIdx}-${fieldKey}`}
                                cols={1}
                                className="py-1"
                                onToggleField={() => onTogglePlatformField(idx, detalheIdx, platform, originalIdx, fieldKey)}
                              >
                                <div className="py-2 group">
                                  <CustomReadOnlyInputField
                                    label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                    value={parseValue(formatFieldValue(fieldValue.value))}
                                    isFirstLabelList={index === 0}
                                    icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                                    tooltip={renderSourceTooltip(fieldValue.source)}
                                  />
                                  <ValidatedImage
                                    src={String(fieldValue.value)}
                                    alt={`Imagem ${renderIdx + 1}`}
                                    className="w-full max-w-full h-48 mx-auto mt-2 bg-background/40 rounded-sm"
                                  />
                                </div>
                              </CustomGridItem>
                            );
                          }

                          return (
                            <CustomGridItem
                              key={`${platform}-${originalIdx}-${fieldKey}`}
                              cols={1}
                              className="py-1"
                              onToggleField={() => onTogglePlatformField(idx, detalheIdx, platform, originalIdx, fieldKey)}
                            >
                              <CustomReadOnlyInputField
                                label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                value={parseValue(formatFieldValue(fieldValue.value))}
                                isFirstLabelList={index === 0}
                                icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                                tooltip={renderSourceTooltip(fieldValue.source)}
                              />
                            </CustomGridItem>
                          );
                        })}
                    </CustomGridContainer>
                  </div>
                </div>
              ))}
            </CustomGridContainer>
          );
        });
      });

      // Only show the section if there are platform elements to display
      if (platformElements.length === 0) return null;

      const allElements: React.ReactElement[] = [];

      // Add main title for Redes Sociais section
      allElements.push(
        <CustomGridContainer cols={1} key={`redes-sociais-title-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => {
              // Toggle all redes_sociais
              const updater = actions.updateSectionEntries;
              if (!updater) return;

              // Determine the target state based on current mode
              const targetDeletedState = isTrash ? false : true;

              updater(
                sectionTitle,
                (entryToUpdate, entryIndex) => {
                  // Only update the specific entry
                  if (entryIndex === idx && entryToUpdate.redes_sociais) {
                    entryToUpdate.redes_sociais.forEach((redesEntry: any) => {
                      if (redesEntry.detalhes) {
                        redesEntry.detalhes.forEach((detalhe: any) => {
                          detalhe.is_deleted = targetDeletedState;
                          if (detalhe.value) {
                            Object.values(detalhe.value).forEach((platform: any) => {
                              if (Array.isArray(platform)) {
                                platform.forEach((profile: any) => {
                                  Object.values(profile).forEach((field: any) => {
                                    if (field && typeof field === 'object' && 'is_deleted' in field) {
                                      field.is_deleted = targetDeletedState;
                                    }
                                  });
                                });
                              }
                            });
                          }
                        });
                      }
                    });
                  }
                },
                testEntryDeleted as any,
                testSectionDeleted,
                calculateDataCount
              );
            }}
          >
            <ReportsCustomLabel
              label="REDES SOCIAIS"
              colorClass="bg-primary"
            />
          </CustomGridItem>
        </CustomGridContainer>
      );

      // Add all the platform elements
      allElements.push(...platformElements);

      return allElements.length > 0 ? <>{allElements}</> : null;
    },
  };

  const renderGenericList = (entry: PossivelPessoaRelacionada, listKey: string): React.ReactElement | null => {
    const list = (entry as any)[listKey];
    if (!Array.isArray(list) || list.length === 0) return null;

    const idx = idxMap.get(entry)!;

    const filteredItems = list.filter((item: any) => {
      if (item.value && typeof item.value === 'object') {
        return Object.values(item.value).some((field: any) => includeKey(field.is_deleted));
      }
      return includeKey(item.is_deleted);
    });

    if (filteredItems.length === 0) return null;

    const displayTitle = listKey
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    return (
      <CustomGridContainer cols={1} key={`${listKey}-${idx}`} className="mb-4">
        <CustomGridItem
          cols={1}
          containerClassName="w-fit pr-12"
          onToggleField={() => onToggleGenericListTitle(idx, listKey)}
        >
          <ReportsCustomLabel
            label={displayTitle.toUpperCase()}
            colorClass="bg-primary"
          />
        </CustomGridItem>
        {filteredItems.map((item: any, renderIdx: number) => {
          const originalIdx = list.indexOf(item);

          if (item.value && typeof item.value === 'object') {
            return (
              <div key={`${listKey}-${idx}-${originalIdx}`} className="mb-4">
                <CustomGridItem
                  cols={1}
                  containerClassName="w-fit pr-12 pb-2"
                  onToggleField={() => onToggleGenericListItem(idx, listKey, originalIdx)}
                >
                  <ReportsCustomLabel
                    label={`${displayTitle.toUpperCase()} ${!isTrash ? renderIdx + 1 : ""}`}
                    colorClass="bg-border"
                  />
                </CustomGridItem>
                <div className="pl-5">
                  <CustomGridContainer cols={2}>
                    {Object.entries(item.value)
                      .filter(([_, fieldValue]: any) => includeKey(fieldValue.is_deleted))
                      .map(([fieldKey, fieldValue]: any, index) => {

                        return (
                          <CustomGridItem
                            key={`${listKey}-${originalIdx}-${fieldKey}`}
                            cols={1}
                            className="py-1"
                            onToggleField={() => onToggleGenericListField(idx, listKey, originalIdx, fieldKey)}
                          >
                            <CustomReadOnlyInputField
                              label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                              value={parseValue(formatFieldValue(fieldValue.value))}
                              isFirstLabelList={index === 0}
                              icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                              tooltip={renderSourceTooltip(fieldValue.source)}
                            />
                          </CustomGridItem>
                        );
                      })}
                  </CustomGridContainer>
                </div>
              </div>
            );
          }
          return null;
        })}
      </CustomGridContainer>
    );
  };

  const validateKeys = (keys: Array<keyof PossivelPessoaRelacionada>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: PossivelPessoaRelacionada): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof PossivelPessoaRelacionada>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Possíveis Pessoas Relacionadas] Chaves inválidas:", keys);
    }

    const orderedKeys: Array<keyof PossivelPessoaRelacionada> = [
      'nome_completo',
      'detalhes',
      'telefones',
      'emails',
      'enderecos',
      'imagens',
      'redes_sociais'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    const genericListKeys = keys.filter(key =>
      !orderedKeys.includes(key) && Array.isArray((entry as any)[key])
    );

    const elements: React.ReactElement[] = [];

    // Render known keys first
    filteredKeys.forEach((chave) => {
      const element = formatByKey[chave]?.(entry);
      if (element) elements.push(element);
    });

    // Render generic lists
    genericListKeys.forEach((listKey) => {
      const element = renderGenericList(entry, listKey as string);
      if (element) elements.push(element);
    });

    return elements;
  };

  const render = (dataArray: PossivelPessoaRelacionada[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Possíveis Pessoas Relacionadas] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      const elements = renderSingleItem(entry);
      return elements.length > 0;
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (filteredData.length > 1) {
        const isLastItem = index === filteredData.length - 1;
        allElements.push(
          <div
            key={`pessoa-relacionada-${index}`}
            className={`relative mb-8 pb-1 ${isLastItem ? '!mb-0 !pb-0' : ''}`}
          >
            {elements}
            {!isLastItem && <ItemSeparator />}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      (entry: any) => {
        if (entry.nome_completo) {
          entry.nome_completo.is_deleted = targetDeletedState;
        }

        if (entry.detalhes && typeof entry.detalhes === 'object') {
          Object.values(entry.detalhes).forEach((field: any) => {
            if (field && typeof field === 'object' && 'is_deleted' in field) {
              field.is_deleted = targetDeletedState;
            }
          });
        }

        [entry.telefones, entry.emails, entry.enderecos, entry.imagens].forEach(property => {
          if (Array.isArray(property)) {
            property.forEach((item: any) => {
              if (item.value && typeof item.value === 'object') {
                Object.values(item.value).forEach((field: any) => {
                  if (field && typeof field === 'object' && 'is_deleted' in field) {
                    field.is_deleted = targetDeletedState;
                  }
                });
              }
              if (item && typeof item === 'object' && 'is_deleted' in item) {
                item.is_deleted = targetDeletedState;
              }
            });
          }
        });

        if (entry.redes_sociais) {
          entry.redes_sociais.forEach((redesEntry: any) => {
            if (redesEntry.detalhes) {
              redesEntry.detalhes.forEach((detalhe: any) => {
                if (detalhe.value) {
                  Object.values(detalhe.value).forEach((platform: any) => {
                    if (Array.isArray(platform)) {
                      platform.forEach((profile: any) => {
                        Object.values(profile).forEach((field: any) => {
                          if (field && typeof field === 'object' && 'is_deleted' in field) {
                            field.is_deleted = targetDeletedState;
                          }
                        });
                      });
                    }
                  });
                }
              });
            }
          });
        }

        const genericListKeys = Object.keys(entry).filter(key =>
          key !== 'nome_completo' &&
          key !== 'detalhes' &&
          key !== 'telefones' &&
          key !== 'emails' &&
          key !== 'enderecos' &&
          key !== 'imagens' &&
          key !== 'redes_sociais' &&
          Array.isArray(entry[key])
        );

        genericListKeys.forEach(key => {
          if (Array.isArray(entry[key])) {
            entry[key].forEach((item: any) => {
              item.is_deleted = targetDeletedState;
              if (item.value && typeof item.value === 'object') {
                Object.values(item.value).forEach((field: any) => {
                  if (field && typeof field === 'object' && 'is_deleted' in field) {
                    field.is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        });
      },
      testEntryDeleted as any,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
