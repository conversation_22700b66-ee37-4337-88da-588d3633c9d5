import logging
import json
import time
import asyncio
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
import redis.asyncio as redis
from core.config import settings

logger = logging.getLogger(__name__)

class RedisService:
    """Redis service for managing token caching and distributed locking."""
    
    def __init__(self):
        self._redis: Optional[redis.Redis] = None
        self._lock: Optional[asyncio.Lock] = None
    
    async def initialize(self):
        """Initialize Redis connection."""
        if self._redis is not None:
            return
        
        try:
            logger.info("[RedisService] Initializing Redis connection to %s:%s", 
                       settings.REDIS_HOST, settings.REDIS_PORT)
            
            # Create Redis connection pool
            self._redis = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True,
                max_connections=settings.REDIS_MAX_CONNECTIONS
            )
            
            # Test connection
            await self._redis.ping()
            logger.info("[RedisService] Successfully connected to Redis")
            
        except Exception as e:
            logger.error("[RedisService] Failed to connect to Redis: %s", str(e))
            raise
    
    async def close(self):
        """Close Redis connection."""
        if self._redis:
            await self._redis.close()
            self._redis = None
            logger.info("[RedisService] Redis connection closed")
    
    def _get_user_cache_key(self, user_id: str) -> str:
        """Generate cache key for user tokens."""
        return f"user_tokens:{user_id}"
    
    def _get_user_lock_key(self, user_id: str) -> str:
        """Generate lock key for user refresh operations."""
        return f"user_refresh_lock:{user_id}"
    
    def _get_user_failure_key(self, user_id: str) -> str:
        """Generate failure tracking key for user."""
        return f"user_refresh_failure:{user_id}"
    

    
    def _calculate_cache_ttl(self, access_token: str, fallback_ttl: int = 1800) -> int:
        """
        Calculate the TTL for caching. Uses a fixed 30-minute TTL by default.
        
        Args:
            access_token: The JWT access token (not used for TTL calculation)
            fallback_ttl: Default TTL in seconds (30 minutes = 1800 seconds)
            
        Returns:
            TTL in seconds for Redis cache
        """
        logger.info("[RedisService] Using fixed cache TTL: %s seconds (30 minutes)", fallback_ttl)
        return fallback_ttl
    
    async def get_cached_tokens(self, user_id: str) -> Optional[Tuple[str, str]]:
        """
        Get cached tokens for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Tuple of (access_token, refresh_token) or None if not found
        """
        if not self._redis:
            await self.initialize()
        
        try:
            cache_key = self._get_user_cache_key(user_id)
            logger.info("[RedisService] Getting cached tokens for user: %s, cache_key: %s", user_id, cache_key)
            
            cached_data = await self._redis.get(cache_key)
            logger.info("[RedisService] Raw cached data for user %s: %s", user_id, "Found" if cached_data else "Not found")
            
            if cached_data:
                token_data = json.loads(cached_data)
                access_token = token_data.get("access_token")
                refresh_token = token_data.get("refresh_token")
                
                logger.info("[RedisService] Parsed token data for user %s - access_token: %s, refresh_token: %s", 
                           user_id, "Present" if access_token else "Missing", "Present" if refresh_token else "Missing")
                
                # Check if tokens are still valid
                expire_at = token_data.get("expire_at")
                current_time = time.time()
                logger.info("[RedisService] Token expiration check for user %s - expire_at: %s, current_time: %s, valid: %s", 
                           user_id, expire_at, current_time, expire_at and current_time < expire_at)
                
                if expire_at and current_time < expire_at:
                    logger.info("[RedisService] Found valid cached tokens for user: %s", user_id)
                    return access_token, refresh_token
                else:
                    logger.info("[RedisService] Cached tokens expired for user: %s, clearing cache", user_id)
                    deleted = await self._redis.delete(cache_key)
                    logger.info("[RedisService] Deleted expired tokens: %s", deleted)
                    return None
            
            logger.info("[RedisService] No cached tokens found for user: %s", user_id)
            return None
            
        except Exception as e:
            logger.error("[RedisService] Error getting cached tokens for user %s: %s", user_id, str(e))
            logger.error("[RedisService] Exception type: %s", type(e).__name__)
            import traceback
            logger.error("[RedisService] Full traceback: %s", traceback.format_exc())
            return None
    
    async def set_cached_tokens(self, user_id: str, access_token: str, refresh_token: str, 
                               ttl_seconds: int = None) -> bool:
        """
        Cache tokens for a user.
        If an existing item exists, it will be removed first before inserting the new one.
        
        Args:
            user_id: User identifier
            access_token: Access token
            refresh_token: Refresh token
            ttl_seconds: Time to live in seconds (if None, will use default 30 minutes)
            
        Returns:
            True if successful, False otherwise
        """
        if not self._redis:
            await self.initialize()
        
        try:
            cache_key = self._get_user_cache_key(user_id)
            logger.info("[RedisService] Setting cached tokens for user: %s, cache_key: %s", user_id, cache_key)
            
            # Check if an existing item exists and remove it
            existing_item = await self._redis.get(cache_key)
            if existing_item:
                logger.info("[RedisService] Found existing cached tokens for user: %s. Removing before inserting new ones.", user_id)
                deleted = await self._redis.delete(cache_key)
                logger.info("[RedisService] Deleted existing item: %s", deleted)
            
            # Use default 30-minute TTL if not provided
            if ttl_seconds is None:
                ttl_seconds = self._calculate_cache_ttl(access_token)
            
            # Prepare token data
            token_data = {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "expire_at": time.time() + ttl_seconds,
                "user_id": user_id,
                "created_at": time.time(),
                "last_updated": time.time()
            }
            
            logger.info("[RedisService] Prepared token data for user: %s, TTL: %s seconds", user_id, ttl_seconds)
            
            # Store in Redis with TTL
            result = await self._redis.setex(
                cache_key,
                ttl_seconds,
                json.dumps(token_data)
            )
            
            logger.info("[RedisService] Redis setex result: %s", result)
            
            if result:
                logger.info("[RedisService] Successfully cached tokens for user: %s. TTL: %s seconds", 
                           user_id, ttl_seconds)
                
                # Verify the data was stored
                verification = await self._redis.get(cache_key)
                if verification:
                    logger.info("[RedisService] ✅ Verification successful - data found in Redis after setex")
                else:
                    logger.error("[RedisService] ❌ Verification failed - data NOT found in Redis after setex")
                
                return True
            else:
                logger.error("[RedisService] Redis setex returned False for user: %s", user_id)
                return False
            
        except Exception as e:
            logger.error("[RedisService] Error caching tokens for user %s: %s", user_id, str(e))
            logger.error("[RedisService] Exception type: %s", type(e).__name__)
            import traceback
            logger.error("[RedisService] Full traceback: %s", traceback.format_exc())
            return False
    
    async def update_user_tokens(self, user_id: str, new_access_token: str, new_refresh_token: str, 
                                ttl_seconds: int = None) -> bool:
        """
        Update user tokens by replacing the existing cache entry.
        This ensures proper token rotation and prevents cache pollution.
        If an existing item exists, it will be removed first before inserting the new one.
        
        Args:
            user_id: User identifier
            new_access_token: New access token
            new_refresh_token: New refresh token
            ttl_seconds: Time to live in seconds (if None, will use default 30 minutes)
            
        Returns:
            True if successful, False otherwise
        """
        if not self._redis:
            await self.initialize()
        
        try:
            # Simply replace the existing cache entry (set_cached_tokens already handles removal)
            success = await self.set_cached_tokens(user_id, new_access_token, new_refresh_token, ttl_seconds)
            
            if success:
                logger.info("[RedisService] Successfully updated tokens for user: %s", user_id)
            else:
                logger.error("[RedisService] Failed to update tokens for user: %s", user_id)
            
            return success
            
        except Exception as e:
            logger.error("[RedisService] Error updating tokens for user %s: %s", user_id, str(e))
            return False
    
    async def acquire_lock(self, user_id: str, timeout_seconds: int = 30) -> bool:
        """
        Acquire a distributed lock for user token refresh.
        
        Args:
            user_id: User identifier
            timeout_seconds: Lock timeout in seconds
            
        Returns:
            True if lock acquired, False otherwise
        """
        if not self._redis:
            await self.initialize()
        
        try:
            lock_key = self._get_user_lock_key(user_id)
            lock_value = f"locked_at_{int(time.time())}"
            
            # Try to set the lock with NX (only if not exists) and EX (expiration)
            acquired = await self._redis.set(lock_key, lock_value, ex=timeout_seconds, nx=True)
            
            if acquired:
                logger.info("[RedisService] Successfully acquired refresh lock for user: %s", user_id)
            else:
                logger.info("[RedisService] Failed to acquire refresh lock for user: %s (already locked)", user_id)
            
            return bool(acquired)
            
        except Exception as e:
            logger.error("[RedisService] Error acquiring lock for user %s: %s", user_id, str(e))
            return False
    
    async def release_lock(self, user_id: str) -> bool:
        """
        Release the distributed lock for user token refresh.
        
        Args:
            user_id: User identifier
            
        Returns:
            True if lock released, False otherwise
        """
        if not self._redis:
            await self.initialize()
        
        try:
            lock_key = self._get_user_lock_key(user_id)
            released = await self._redis.delete(lock_key)
            
            if released:
                logger.info("[RedisService] Successfully released refresh lock for user: %s", user_id)
            else:
                logger.info("[RedisService] No lock found to release for user: %s", user_id)
            
            return bool(released)
            
        except Exception as e:
            logger.error("[RedisService] Error releasing lock for user %s: %s", user_id, str(e))
            return False
    
    async def is_refresh_failing(self, user_id: str) -> bool:
        """
        Check if refresh has been failing recently for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            True if refresh is failing, False otherwise
        """
        if not self._redis:
            await self.initialize()
        
        try:
            failure_key = self._get_user_failure_key(user_id)
            failure_count = await self._redis.get(failure_key)
            
            if failure_count:
                count = int(failure_count)
                logger.info("[RedisService] User %s has %s recent refresh failures", user_id, count)
                return count >= 3  # Consider failing after 3 attempts
            
            return False
            
        except Exception as e:
            logger.error("[RedisService] Error checking refresh failure for user %s: %s", user_id, str(e))
            return False
    
    async def record_refresh_failure(self, user_id: str) -> bool:
        """
        Record a refresh failure for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            True if successful, False otherwise
        """
        if not self._redis:
            await self.initialize()
        
        try:
            failure_key = self._get_user_failure_key(user_id)
            
            # Increment failure count with expiration (reset after 1 hour)
            await self._redis.incr(failure_key)
            await self._redis.expire(failure_key, 3600)  # 1 hour TTL
            
            logger.info("[RedisService] Recorded refresh failure for user: %s", user_id)
            return True
            
        except Exception as e:
            logger.error("[RedisService] Error recording refresh failure for user %s: %s", user_id, str(e))
            return False
    
    async def clear_refresh_failure(self, user_id: str) -> bool:
        """
        Clear failure record when refresh succeeds for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            True if successful, False otherwise
        """
        if not self._redis:
            await self.initialize()
        
        try:
            failure_key = self._get_user_failure_key(user_id)
            cleared = await self._redis.delete(failure_key)
            
            if cleared:
                logger.info("[RedisService] Cleared failure record for user: %s", user_id)
            else:
                logger.info("[RedisService] No failure record found for user: %s", user_id)
            
            return bool(cleared)
            
        except Exception as e:
            logger.error("[RedisService] Error clearing refresh failure for user %s: %s", user_id, str(e))
            return False
    
    async def clear_user_cache(self, user_id: str) -> bool:
        """
        Clear all cached data for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            True if successful, False otherwise
        """
        if not self._redis:
            await self.initialize()
        
        try:
            # Clear tokens, lock, and failure records
            cache_key = self._get_user_cache_key(user_id)
            lock_key = self._get_user_lock_key(user_id)
            failure_key = self._get_user_failure_key(user_id)
            
            await self._redis.delete(cache_key, lock_key, failure_key)
            
            logger.info("[RedisService] Cleared all cached data for user: %s", user_id)
            return True
            
        except Exception as e:
            logger.error("[RedisService] Error clearing cache for user %s: %s", user_id, str(e))
            return False
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the Redis cache.
        
        Returns:
            Dictionary with cache statistics
        """
        if not self._redis:
            await self.initialize()
        
        try:
            # Get all keys matching our patterns
            user_token_keys = await self._redis.keys("user_tokens:*")
            user_lock_keys = await self._redis.keys("user_refresh_lock:*")
            user_failure_keys = await self._redis.keys("user_refresh_failure:*")
            
            stats = {
                "total_users_with_tokens": len(user_token_keys),
                "total_active_locks": len(user_lock_keys),
                "total_users_with_failures": len(user_failure_keys),
                "timestamp": time.time()
            }
            
            # Get some sample user IDs for debugging
            if user_token_keys:
                sample_users = [key.split(":")[1] for key in user_token_keys[:5]]
                stats["sample_users"] = sample_users
            
            return stats
            
        except Exception as e:
            logger.error("[RedisService] Error getting cache stats: %s", str(e))
            return {"error": str(e), "timestamp": time.time()}

# Global instance
redis_service = RedisService()
