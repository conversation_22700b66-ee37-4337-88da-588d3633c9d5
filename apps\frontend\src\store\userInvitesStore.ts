import { create } from "zustand";
import { UserInviteResponse, UserInvitesFilters } from "~/types/global";

interface UserInvitesStoreActions {
  setInvitesList: (list: UserInviteResponse[]) => void;
  appendInvitesList: (list: UserInviteResponse[]) => void;
  setPage: (page: number) => void;
  incrementPage: () => void;
  setLimit: (limit: number) => void;
  setStatusFilter: (status: string) => void;
  setTypeFilter: (type: string) => void;
  setDateFilter: (date: string) => void;
  setSearchEmailFilter: (email: string) => void;
  setOrder: (order: string) => void;
  setColumnOrder: (column: string) => void;
  clearFilters: () => void;
  resetPagination: () => void;
  setTotalItems: (total: number) => void;
}

interface UserInvitesStoreState {
  invitesList: UserInviteResponse[];
  filters: UserInvitesFilters;
  totalItems: number;
  actions: UserInvitesStoreActions;
}

const defaultFilters: UserInvitesFilters = {
  status_invite: "",
  type_invite: "",
  specific_date: "",
  order: "desc",
  column_order: "sent_at",
  limit: 10,
  page: 1,
  search_email: ""
};

const useUserInvitesStore = create<UserInvitesStoreState>((set) => ({
  invitesList: [],
  filters: defaultFilters,
  totalItems: 0,
  actions: {
    setInvitesList: (list: UserInviteResponse[]) => set({ invitesList: list }),
    appendInvitesList: (list: UserInviteResponse[]) =>
      set(state => ({ invitesList: [...state.invitesList, ...list] })),
    setPage: (page: number) =>
      set(state => ({ filters: { ...state.filters, page } })),
    incrementPage: () =>
      set(state => ({ filters: { ...state.filters, page: state.filters.page! + 1 } })),
    setLimit: (limit: number) =>
      set(state => ({ filters: { ...state.filters, limit } })),
    setStatusFilter: (status_invite: string) =>
      set(state => ({ filters: { ...state.filters, status_invite, page: 1 } })),
    setTypeFilter: (type_invite: string) =>
      set(state => ({ filters: { ...state.filters, type_invite, page: 1 } })),
    setDateFilter: (specific_date: string) =>
      set(state => ({ filters: { ...state.filters, specific_date, page: 1 } })),
    setSearchEmailFilter: (search_email: string) =>
      set(state => ({ filters: { ...state.filters, search_email, page: 1 } })),
    setOrder: (order: string) =>
      set(state => ({ filters: { ...state.filters, order, page: 1 } })),
    setColumnOrder: (column_order: string) =>
      set(state => ({ filters: { ...state.filters, column_order, page: 1 } })),
    clearFilters: () =>
      set({ filters: defaultFilters }),
    resetPagination: () =>
      set(state => ({ filters: { ...state.filters, page: 1 } })),
    setTotalItems: (totalItems: number) => set({ totalItems }),
  },
}));

export const useUserInvitesList = () => useUserInvitesStore(state => state.invitesList);
export const useUserInvitesFilters = () => useUserInvitesStore(state => state.filters);
export const useUserInvitesTotalItems = () => useUserInvitesStore(state => state.totalItems);
export const useUserInvitesActions = () => useUserInvitesStore(state => state.actions);

export default useUserInvitesStore;
