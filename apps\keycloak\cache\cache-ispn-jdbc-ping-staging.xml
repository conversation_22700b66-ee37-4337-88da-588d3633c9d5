<?xml version="1.0" encoding="UTF-8"?>
<infinispan
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="urn:infinispan:config:14.0 http://www.infinispan.org/schemas/infinispan-config-14.0.xsd"
        xmlns="urn:infinispan:config:14.0">

    <jgroups>
        <stack name="tcp-jdbc-ping" extends="tcp">
            <TCP bind_addr="site_local"/>
            <JDBC_PING
                connection_driver="org.postgresql.Driver"
                connection_url="${env.KC_DB_URL}"
                connection_username="${env.KC_DB_USERNAME}"
                connection_password="${env.KC_DB_PASSWORD}"
                initialize_sql="CREATE TABLE IF NOT EXISTS JGROUPSPING (own_addr VARCHAR(200) NOT NULL, cluster_name VARCHAR(200) NOT NULL, ping_data BYTEA, PRIMARY KEY (own_addr, cluster_name));"
                />
            <MERGE3/>
            <FD_SOCK/>
            <FD_ALL/>
            <VERIFY_SUSPECT/>
            <pbcast.NAKACK2/>
            <UNICAST3/>
            <pbcast.STABLE/>
            <pbcast.GMS/>
            <MFC/>
            <FRAG3/>
        </stack>
    </jgroups>

    <cache-container name="keycloak" statistics="true">
        <transport stack="tcp-jdbc-ping"/>
        
        <!-- Local caches with staging-optimized settings (smaller) -->
        <local-cache name="realms" statistics="true">
            <memory max-count="5000" when-full="REMOVE"/>
            <expiration max-idle="900000" lifespan="1800000"/>   <!-- 15min idle, 30min total -->
        </local-cache>
        
        <local-cache name="users" statistics="true">
            <memory max-count="10000" when-full="REMOVE"/>
            <expiration max-idle="600000" lifespan="3600000"/>   <!-- 10min idle, 1h total -->
        </local-cache>
        
        <local-cache name="authorization" statistics="true">
            <memory max-count="5000" when-full="REMOVE"/>
            <expiration max-idle="600000" lifespan="1200000"/>   <!-- 10min idle, 20min total -->
        </local-cache>
        
        <local-cache name="keys" statistics="true">
            <memory max-count="500" when-full="REMOVE"/>
            <expiration max-idle="3600000" lifespan="43200000"/> <!-- 1h idle, 12h total -->
        </local-cache>

        <!-- Session caches with moderate capacity -->
        <replicated-cache name="sessions" statistics="true">
            <memory max-count="10000" when-full="REMOVE"/>
            <expiration max-idle="1800000"/>   <!-- 30 minutes idle -->
        </replicated-cache>
        
        <replicated-cache name="authenticationSessions" statistics="true">
            <memory max-count="2000" when-full="REMOVE"/>
            <expiration max-idle="300000"/>    <!-- 5 minutes idle -->
        </replicated-cache>
        
        <replicated-cache name="offlineSessions" statistics="true">
            <memory max-count="5000" when-full="REMOVE"/>
            <expiration max-idle="604800000"/> <!-- 7 days idle -->
        </replicated-cache>
        
        <replicated-cache name="clientSessions" statistics="true">
            <memory max-count="10000" when-full="REMOVE"/>
            <expiration max-idle="1800000"/>   <!-- 30 minutes idle -->
        </replicated-cache>
        
        <replicated-cache name="offlineClientSessions" statistics="true">
            <memory max-count="5000" when-full="REMOVE"/>
            <expiration max-idle="604800000"/>  <!-- 7 days idle -->
        </replicated-cache>
        
        <replicated-cache name="loginFailures" statistics="true">
            <memory max-count="2000" when-full="REMOVE"/>
            <expiration max-idle="900000"/>     <!-- 15 minutes idle -->
        </replicated-cache>
        
        <replicated-cache name="work" statistics="true">
            <memory max-count="500" when-full="REMOVE"/>
            <expiration max-idle="60000"/>      <!-- 1 minute idle -->
        </replicated-cache>
        
        <distributed-cache name="actionTokens" owners="1" statistics="true">
            <memory max-count="2000" when-full="REMOVE"/>
            <expiration max-idle="300000" lifespan="300000"/>   <!-- 5 minutes -->
        </distributed-cache>
    </cache-container>
</infinispan>