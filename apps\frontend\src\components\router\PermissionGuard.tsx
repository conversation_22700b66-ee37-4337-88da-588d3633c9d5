import { ReactNode } from "react";
import { Navigate, useLocation } from "react-router";
import { usePermissionManager } from "~/services/permissionManager";
import { Permission, SPECIAL_ROUTES } from "~/helpers/permissions.helper";
import { useUserData } from "~/store/userStore";
import { Loading } from "@snap/design-system";

interface PermissionGuardProps {
  children: ReactNode;
  requiredPermission: Permission;
  fallbackPath?: string;
}

/**
 * Componente que protege rotas baseado em permissões específicas
 * Redireciona para página de sem permissão se o usuário não tiver acesso
 * Aguarda o carregamento dos dados do usuário antes de verificar permissões
 */
export const PermissionGuard = ({
  children,
  requiredPermission,
  fallbackPath = SPECIAL_ROUTES.NO_PERMISSION
}: PermissionGuardProps) => {
  const location = useLocation();
  const userData = useUserData();
  const { checkPermission } = usePermissionManager();

  if (!userData) {
    return (
      <div className="flex items-center justify-center h-32 w-full">
        <Loading size="default" />
      </div>
    );
  }

  // Verifica se o usuário tem a permissão necessária usando o método genérico
  const hasRequiredPermission = checkPermission(requiredPermission);

  // Se não tem permissão, redireciona para página de acesso negado
  if (!hasRequiredPermission) {
    return (
      <Navigate
        to={fallbackPath}
        replace
        state={{
          from: location,
          requiredPermission,
          message: `Permissão necessária: ${requiredPermission}`
        }}
      />
    );
  }

  return <>{children}</>;
};

export const usePermissionCheck = () => {
  const { checkPermission } = usePermissionManager();
  return { checkPermission };
};

export default PermissionGuard;
