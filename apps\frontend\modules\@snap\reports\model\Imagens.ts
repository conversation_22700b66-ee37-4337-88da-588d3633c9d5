import { ValueWithSource } from "./ValueWithSource";
import { ReportSection } from "../global";

export interface _Imagem {
    url: ValueWithSource;
}

export interface Imagem {
    detalhes: Array<ValueWithSource<string>>;
}

export type UpdaterFunction = (entry: Imagem, index?: number) => void;
export type TestFunction = (entry: Imagem) => boolean;
export type SectionTestFunction = (section: ImagensSection) => boolean;
export type CalculateFunction = (section: ImagensSection) => number;

export interface ImagensSection extends ReportSection {
  data: Imagem[];
  data_count: number;
}

export interface TestFunctions {
  detalhes: (e: Imagem) => boolean;
}
