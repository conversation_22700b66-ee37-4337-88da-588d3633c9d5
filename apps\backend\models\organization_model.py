from sqlalchemy import Column, DateTime, Text, Boolean, Index, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, ENUM as PGEnum
from models.base import Base
from models.enums import organization_status  # your Enum

class Organizations(Base):
    __tablename__ = "organizations"
    __table_args__ = (
        Index("idx_organizations_api_key", "api_key"),
        Index("idx_organizations_status", "status_organization"),
        {"schema": "reports"},
    )

    organization_id = Column(PostgresUUID, primary_key=True, server_default=func.gen_random_uuid())
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.timezone("UTC", func.now()))
    name = Column(Text, nullable=False)
    image_logo = Column(Text, nullable=True)
    api_key = Column(Text, nullable=True)
    status_organization = Column(
        PGEnum(organization_status, name="organization_status", schema="reports", create_type=True),
        nullable=False,
        server_default='ativo'
    )
    print_snap_logo = Column(Boolean, nullable=True, server_default='true')

    users = relationship("OrganizationUsers", back_populates="organization")
    invites = relationship("Invite", back_populates="organization") 
    folders = relationship("Folder", back_populates="organization")

    def __repr__(self):
        return f"<Organization(organization_id={self.organization_id}, name={self.name})>"
