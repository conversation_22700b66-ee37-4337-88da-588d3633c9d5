import { ReportData, EncryptedData, Ngrams, EncryptedPayload } from "~/types/global";
import { CryptoResult } from "~/workers/crypto/interfaces";
import { REPORT_CONSTANTS } from "./constants";

export interface CombinedSearchArgs {
  [key: string]: string | number | object;
}

type KeyCounters = Record<string, number>;

type DecryptFunction = (encryptedData: EncryptedPayload, password?: string) => Promise<CryptoResult<any>>;

const searchArgs = REPORT_CONSTANTS.new_report.report_search_args

/**
 * Handles the case where a key already exists in the combinedArgs object.
 * It renames the existing key and adds the new key with a suffix.
 *
 * @param combinedArgs - The object containing all combined search arguments
 * @param keyCounters - An object tracking the count of each key
 * @param key - The key to handle
 * @param value - The value associated with the key
 */
export const handleExistingKey = (
  combinedArgs: CombinedSearchArgs,
  keyCounters: KeyCounters,
  key: string,
  value: string | number | object
): void => {
  if (keyCounters[key] === undefined) {
    // Pega a primeira chave duplicada a adiciona o sufixo
    const existingValue = combinedArgs[key];
    delete combinedArgs[key];
    combinedArgs[`${key}_1`] = existingValue;
    keyCounters[key] = 1;
  }

  keyCounters[key]++;
  combinedArgs[`${key}_${keyCounters[key]}`] = value;
};

const processReportSearchArgs = (
  report: ReportData,
  combinedArgs: CombinedSearchArgs,
  keyCounters: KeyCounters
): void => {

  const reportSearchArgs = report?.[searchArgs];
  const isNotObject = typeof reportSearchArgs !== "object";
  const isNull = reportSearchArgs === null;

  if (!reportSearchArgs || isNotObject || isNull) return;

  if ('encrypted' in reportSearchArgs && 'iv' in reportSearchArgs) {
    return;
  }

  const searchArgsObj = reportSearchArgs as Record<string, string | number | object>;

  Object.entries(searchArgsObj).forEach(([key, value]) => {
    if (combinedArgs[key] !== undefined) {
      handleExistingKey(combinedArgs, keyCounters, key, value);
    } else {
      combinedArgs[key] = value;
    }
  });
};

export const combineReportSearchArgs = (reportsDetails: ReportData[]): CombinedSearchArgs => {
  if (!reportsDetails || reportsDetails.length === 0) {
    return {};
  }

  const combinedReportSearchArgs: CombinedSearchArgs = {};
  const keyCounters: Record<string, number> = {};

  reportsDetails.forEach((report) => {
    processReportSearchArgs(report, combinedReportSearchArgs, keyCounters);
  });

  return combinedReportSearchArgs;
};

/**
 * Fetches and decrypts reports based on their IDs.
 */
export const fetchAndDecryptReports = async (
  reportIds: string[],
  fetchReportById: (id: string) => Promise<unknown>,
  decryptReportPayload: (report: ReportData, decryptFn: DecryptFunction) => Promise<any>,
  decryptData: DecryptFunction
): Promise<ReportData[]> => {

  const reportsDetailsPromises = reportIds.map((reportId) =>
    fetchReportById(reportId).then((report) =>
      decryptReportPayload(report as ReportData, decryptData)
    )
  );
  const results = await Promise.all(reportsDetailsPromises);
  return results as ReportData[];
};

/**
 * Combines ngrams from multiple reports.
 */
export const combineNgramsFromReports = async (
  reportsDetails: ReportData[],
  encryptNgrams: (data: { report_search_args: unknown }) => Promise<Ngrams>
): Promise<string[]> => {
  const allNgrams: string[] = [];
  const ngramsPromises = reportsDetails.map(async (report) => {
    const ngrams = await encryptNgrams({
      report_search_args: report[searchArgs],
    });
    if (ngrams[searchArgs]) {
      allNgrams.push(...ngrams[searchArgs]);
    }
    return ngrams;
  });

  await Promise.all(ngramsPromises);
  return allNgrams;
};