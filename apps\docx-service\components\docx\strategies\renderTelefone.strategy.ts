import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, getSingular, translateSource, getFieldLabel, getFieldValue } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintTelefoneProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      detalhes?: Array<{
        value: Record<string, {
          value: string;
          label: string;
          source: string[];
          is_deleted: boolean;
        }>;
        label: string;
        source: string[];
        is_deleted: boolean;
      }>
    }>
  };
}

export const renderTelefone = ({ section }: RenderPrintTelefoneProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  const allPhones = section.data?.flatMap(entry =>
    entry.detalhes?.filter((detalhe: ValueWithSource) => !detalhe.is_deleted) || []
  ) || [];

  if (!allPhones.length) {
    return { children: [createSectionTitle(section.title), new Paragraph("Nenhuma informação encontrada.")] };
  }

  const rows: TableRow[] = [];
  let cells: TableCell[] = [];

  allPhones.forEach((detalhe, index) => {
    const validFields = Object.entries(detalhe.value).filter(([_, field]) => !field.is_deleted);
    if (validFields.length === 0) return;

    const cellParagraphs: Paragraph[] = [
        new Paragraph({
            children: [
                new TextRun({ text: `${translatePropToLabel(getSingular(detalhe.label) || 'TELEFONE').toUpperCase()} ${index + 1}`, bold: true, color: "889EA3"}),
                new TextRun({ text: ` | ${detalhe.source?.map(translateSource).join(', ')}`, size: 16, color: "FE473C" })
            ]
        })
    ];

    validFields.forEach(([key, fieldValue]) => {
        cellParagraphs.push(new Paragraph({
            children: [
                new TextRun({ text: `${translatePropToLabel(getFieldLabel(key, fieldValue)).toUpperCase()}: `, bold: true }),
                new TextRun(String(getFieldValue(fieldValue) || "")),
            ]
        }));
    });

    cells.push(new TableCell({ children: cellParagraphs }));

    if (cells.length === 2) {
        rows.push(new TableRow({ children: cells }));
        cells = [];
    }
  });

  if (cells.length > 0) {
    rows.push(new TableRow({ children: [...cells, new TableCell({ children: [new Paragraph("")] })] }));
  }

  if (rows.length > 0) {
    children.push(new Table({ rows, width: { size: 100, type: WidthType.PERCENTAGE }, columnWidths: [4500, 4500] }));
  }

  return { children };
};
