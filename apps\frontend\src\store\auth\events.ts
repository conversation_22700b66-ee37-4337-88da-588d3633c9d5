import { useAuthStore } from "./store";
import { AuthState } from "./interface";
import { AUTH_INITIAL_STATE } from "./state";

interface NewSessionInputDTO {
  user: AuthState["authentication"]["user"];
}

// Always access mutations inside the function body to avoid stale closures
export const startNewAuthenticatedSession = (input: NewSessionInputDTO) => {
  useAuthStore.getState().actions.setUser(input.user);
  useAuthStore.getState().actions.setIsAuthenticated(true);
};

export const endAuthenticatedSession = () => {
  useAuthStore.getState().actions.clearAuthentication();
};

export const updateUser = (user: AuthState["authentication"]["user"]) => {
  useAuthStore.getState().actions.setUser(user);
};

export const startNewAuthorizedSession = (
  permissions: AuthState["authorization"]["permissions"]
) => {
  useAuthStore.getState().actions.setPermissions(permissions);
  useAuthStore.getState().actions.setIsAuthorized(true);
};

export const endAuthorizedSession = () => {
  useAuthStore.getState().actions.clearAuthorization();
};

export const updatePermissions = (
  permissions: AuthState["authorization"]["permissions"]
) => {
  useAuthStore.getState().actions.setPermissions(permissions);
};

// Full store reset (authentication + authorization)
export const resetAuthStore = () => {
  useAuthStore.setState({
    ...AUTH_INITIAL_STATE,
  });
};
