# Test script for Cover.tsx Component
# This script tests the actual Cover.tsx component

Write-Host "Testing Cover.tsx Component..." -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green

# Check if Node.js is available
try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: Node.js is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Check dependencies
Write-Host "Checking dependencies..."
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    pnpm install
    Write-Host "✅ Dependencies installed" -ForegroundColor Green
} else {
    Write-Host "✅ Dependencies already installed" -ForegroundColor Green
}

# Install Chrome
Write-Host "Installing Chrome for Puppeteer..."
try {
    npx puppeteer browsers install chrome
    Write-Host "✅ Chrome installed" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Chrome installation failed" -ForegroundColor Yellow
}

# Build the project if needed
Write-Host "Building TypeScript..."
try {
    pnpm build
    Write-Host "✅ Build completed" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Build failed, trying ts-node..." -ForegroundColor Yellow
}

Write-Host ""

# Run the test
Write-Host "Running Cover.tsx test..." -ForegroundColor Cyan
Write-Host "This will:"
Write-Host "  1. Use the actual Cover.tsx component"
Write-Host "  2. Generate HTML with React SSR"
Write-Host "  3. Create PDF from the component"
Write-Host "  4. Save as test-cover-tsx-output.pdf"
Write-Host ""

try {
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    # Try ts-node first, fallback to compiled version
    try {
        npx ts-node __tests__/test-cover-tsx.ts
    } catch {
        Write-Host "ts-node failed, trying compiled version..." -ForegroundColor Yellow
        node dist/__tests__/test-cover-tsx.js
    }
    
    $stopwatch.Stop()
    $totalTime = $stopwatch.Elapsed.TotalSeconds
    
    Write-Host ""
    Write-Host "Test completed in $($totalTime)s" -ForegroundColor Green
    
    # Check if PDF was created
    if (Test-Path "__tests__/test-cover-tsx-output.pdf") {
        $fileInfo = Get-Item "__tests__/test-cover-tsx-output.pdf"
        Write-Host "✅ PDF file created successfully!" -ForegroundColor Green
        Write-Host "📄 File: __tests__/test-cover-tsx-output.pdf" -ForegroundColor Green
        Write-Host "📊 Size: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Green
        Write-Host ""
        Write-Host "This PDF was generated using the actual Cover.tsx component!" -ForegroundColor Yellow
    } else {
        Write-Host "❌ PDF file was not created" -ForegroundColor Red
    }
    
} catch {
    Write-Host ""
    Write-Host "❌ Error running test: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Cover.tsx test completed!" -ForegroundColor Green