import { REPORT_CONSTANTS, REPORT_SECTIONS } from "../../../../config/constants";
import { useLocation } from "react-router";
import { ReportMetadata, ReportType } from "../../../../global";
/* mocks cpf*/
import { processosMock } from "root/modules/@snap/reports/mock/processos.mock";
import { dadosPessoaisMock } from "root/modules/@snap/reports/mock/dados_pessoais.mock";
import { dadosEmpresaMock } from "root/modules/@snap/reports/mock/dados_empresa.mock";
import { enderecoMock } from "root/modules/@snap/reports/mock/enderecos.mock";
import { telefonesMock } from "root/modules/@snap/reports/mock/telefones.mock";
import { emailsMock } from "root/modules/@snap/reports/mock/emails.mock";
import { mandadosMock } from "root/modules/@snap/reports/mock/mandados.mock";
import { parentesMock } from "root/modules/@snap/reports/mock/parentes.mock";
import { sociedadesMock } from "root/modules/@snap/reports/mock/sociedades.mock";
import { sociosCPFMock } from "root/modules/@snap/reports/mock/socios_cpf";
import { vinculosEmpregaticiosMock } from "root/modules/@snap/reports/mock/vinculos_empregaticios.mock";
import { recursosPublicosMock } from "root/modules/@snap/reports/mock/recursos_publicos.mock";
import { servicosPublicosMock } from "root/modules/@snap/reports/mock/servicos_publicos.mock";
import { diariosOficiaisCPFMock } from "root/modules/@snap/reports/mock/diarios_oficiais_cpf.mock";
import { diariosOficiaisNOMEMock } from "root/modules/@snap/reports/mock/diarios_oficiais_nome.mock";
import { filiacaoPartidariaMock } from "root/modules/@snap/reports/mock/filiacao_partidaria.mock";
import { possiveisContatosMock } from "root/modules/@snap/reports/mock/possiveis_contatos.mock";
import { possiveisContasMock } from "root/modules/@snap/reports/mock/possiveis_contas.mock";
import { doacoesEnviadasMock } from "root/modules/@snap/reports/mock/doacoes_enviadas.mock";
import { doacoesRecebidasMock } from "root/modules/@snap/reports/mock/doacoes_recebidas.mock";
import { fornecimentosEnviadosMock } from "root/modules/@snap/reports/mock/fornecimentos_enviados.mock";
import { fornecimentosRecebidosMock } from "root/modules/@snap/reports/mock/fornecimentos_recebidos.mock";
/* mocks cnpj*/
import { telefonesMockCNPJ } from "root/modules/@snap/reports/mock/telefones_cnpj.mock";
import { emailsMockCNPJ } from "root/modules/@snap/reports/mock/emails_cnpj.mock";
import { enderecoMockCNPJ } from "root/modules/@snap/reports/mock/enderecos_cnpj.mock";
import { sociosMock } from "root/modules/@snap/reports/mock/socios.mock";
import { juntasComerciaisMock } from "root/modules/@snap/reports/mock/juntas_comerciais.mock";
import { diariosOficiaisCNPJMock } from "root/modules/@snap/reports/mock/diarios_oficiais_cnpj.mock";
import { diariosOficiaisNomeCNPJMock } from "root/modules/@snap/reports/mock/diarios_oficiais_nome_cnpj.mock";
import { dadosPessoaisTelefoneMock } from "root/modules/@snap/reports/mock/dados_pessoais_telefone.mock";
import { contatosSalvosMock } from "root/modules/@snap/reports/mock/contatos_salvos.mock";
import { emailsMockTelefone } from "root/modules/@snap/reports/mock/emails_telefone.mock";
import { enderecoMockTelefone } from "root/modules/@snap/reports/mock/enderecos_telefone.mock";
import { parentesTelefoneMock } from "root/modules/@snap/reports/mock/parentes_telefone.mock";
import { empresasRelacionadasMock } from "root/modules/@snap/reports/mock/empresas_relacionadas.mock";
/* mocks telefone */
import { telefonesMockTelefone } from "root/modules/@snap/reports/mock/telefones_telefone.mock";
import { possiveisContatosMockTelefone } from "root/modules/@snap/reports/mock/possiveis_contatos_telefone.mock";
import { possiveisPessoasRelacionadasMock } from "root/modules/@snap/reports/mock/possiveis_pessoas_relacionadas.mock";
import { imagensMock } from "root/modules/@snap/reports/mock/imagens.mock";
import { possiveisContasMockTelefone } from "root/modules/@snap/reports/mock/possiveis_contas_telefone.mock";
import { urlsMock } from "root/modules/@snap/reports/mock/outrasUrls.mock";
import { perfisRedesSOciaisMock } from "root/modules/@snap/reports/mock/perfis_redes_sociais.mock";
import { nomeUsuarioMock } from "root/modules/@snap/reports/mock/nome_usuario.mock";
/* cpf result mock */
import mockResultCpf  from "root/modules/@snap/reports/mock/cpf_result_mock.json";

export const getMockedData = () => {
    const location = useLocation();
    const reportType: ReportType | string = location.pathname.split("/")[2];

    /* CPF */
    const detailsMockCPF = [
        {
            title: REPORT_SECTIONS.dados_pessoais,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP"],
            data_count: Object.keys(dadosPessoaisMock.detalhes).length,
            data: [dadosPessoaisMock],
        },
        {
            title: REPORT_SECTIONS.mandados_de_prisao,
            subtitle: "Dados consultados no Banco Nacional de Monitoramento de Prisões (BNMP).",
            source: ["BancoNacionalDeMonitoramentoDePrisoes"],
            data_count: mandadosMock.length,
            data: mandadosMock,
        },
        {
            title: REPORT_SECTIONS.telefones,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP"],
            data_count: Object.keys(telefonesMock.detalhes).length,
            data: [telefonesMock],
        },
        {
            title: REPORT_SECTIONS.emails,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP"],
            data_count: Object.keys(emailsMock.detalhes).length,
            data: [emailsMock],
        },
        {
            title: REPORT_SECTIONS.enderecos,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP"],
            data_count: Object.keys(enderecoMock.detalhes).length,
            data: [enderecoMock],
        },
        {
            title: REPORT_SECTIONS.parentes,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP", "BancoNacionalDeMonitoramentoDePrisoes"],
            data_count: parentesMock.length,
            data: parentesMock,
        },
        {
            title: REPORT_SECTIONS.sociedades,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP", "CadastroNacionalPJ", "SintegraMTCPF", "SintegraMA", "SintegraMGCPF"],
            data_count: sociedadesMock.length,
            data: sociedadesMock,
        },
        {
            title: REPORT_SECTIONS.socios,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP"],
            data_count: Object.keys(sociosCPFMock.detalhes).length,
            data: [sociosCPFMock],
        },
        {
            title: REPORT_SECTIONS.vinculos_empregaticios,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP"],
            data_count: vinculosEmpregaticiosMock.length,
            data: vinculosEmpregaticiosMock,
        },
        {
            title: REPORT_SECTIONS.processos,
            subtitle: "Dados consultados na API SNAP.",
            source: ["Escavador"],
            data_count: processosMock.length,
            data: processosMock,
        },
        {
            title: REPORT_SECTIONS.recursos_publicos_recebidos,
            subtitle: "Dados consultados em portais de Transparência estaduais e federal.",
            source: ["PortalDaTransparenciaDeMinasGerais", "PortalDaTransparenciaDoAmazonas", "TransparenciaSC", "TransparenciaManausCPF", "TransparenciaDF"],
            data_count: recursosPublicosMock.length,
            data: recursosPublicosMock,
        },
        {
            title: REPORT_SECTIONS.servico_publico,
            subtitle: "Dados consultados em portais de Transparência estaduais e federal.",
            source: ["TransparenciaPRNome"],
            data_count: servicosPublicosMock.length,
            data: servicosPublicosMock,
        },
        {
            title: REPORT_SECTIONS.diarios_oficiais_cpf,
            subtitle: "Dados consultados na API SNAP através do CPF de entrada.",
            source: ["EscavadorDOCPF", "QueridoDiarioCPF"],
            data_count: diariosOficiaisCPFMock.length,
            data: diariosOficiaisCPFMock,
        },
        {
            title: REPORT_SECTIONS.diarios_oficiais_nome,
            subtitle: "Dados consultados na API SNAP através do NOME da pessoa.",
            source: ["EscavadorDONome", "QueridoDiarioNome"],
            data_count: diariosOficiaisNOMEMock.length,
            data: diariosOficiaisNOMEMock,
        },
        {
            title: REPORT_SECTIONS.filiacao_partidaria,
            subtitle: "Dados consultados na API SNAP.",
            source: ["TSEFiliacaoPartidaria"],
            data_count: filiacaoPartidariaMock.length,
            data: filiacaoPartidariaMock,
        },
        {
            title: REPORT_SECTIONS.possiveis_contatos,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP"],
            data_count: possiveisContatosMock.length,
            data: possiveisContatosMock,
        },
        {
            title: REPORT_SECTIONS.possiveis_contas_em_sites,
            subtitle: "Dados consultados na API SNAP.",
            source: ["PAIscpf", "ProvedorDeAplicacacaoDaInternetcpf"],
            data_count: possiveisContasMock.length,
            data: possiveisContasMock,
        },
        {
            title: REPORT_SECTIONS.doacoes_enviadas_campanha,
            subtitle: "Dados consultados na API SNAP.",
            source: ["TSEDoacoes"],
            data_count: doacoesEnviadasMock.length,
            data: doacoesEnviadasMock,
        },
        {
            title: REPORT_SECTIONS.doacoes_recebidas_campanha,
            subtitle: "Dados consultados na API SNAP.",
            source: ["TSEDoacoes"],
            data_count: doacoesRecebidasMock.length,
            data: doacoesRecebidasMock,
        },
        {
            title: REPORT_SECTIONS.fornecimentos_enviados_campanha,
            subtitle: "Dados consultados na API SNAP.",
            source: ["TSEFornecimento"],
            data_count: fornecimentosEnviadosMock.length,
            data: fornecimentosEnviadosMock,
        },
        {
            title: REPORT_SECTIONS.fornecimentos_recebidos_campanha,
            subtitle: "Dados consultados na API SNAP.",
            source: ["TSEFornecimento"],
            data_count: fornecimentosRecebidosMock.length,
            data: fornecimentosRecebidosMock,
        },
    ];

    const metadataMockCpf: ReportMetadata = {
        [REPORT_CONSTANTS.new_report.report_id]: "fea0a198-6110-4621-9ef0-16b667317492",
        [REPORT_CONSTANTS.new_report.report_status]: "success",
        [REPORT_CONSTANTS.new_report.report_type]: "cpf",
        [REPORT_CONSTANTS.new_report.report_search_args]: { cpf: "12345678910" },
        [REPORT_CONSTANTS.new_report.report_name]: "cpf 12345678910",
        [REPORT_CONSTANTS.new_report.creation_at]: "2025-05-19T20:20:56.920580+00:00",
        [REPORT_CONSTANTS.new_report.modified_at]: "2025-05-19T20:20:56.920580+00:00",
        [REPORT_CONSTANTS.new_report.subject_name]: "João da Silva",
        [REPORT_CONSTANTS.new_report.subject_mother_name]: "Maria da Silva",
        [REPORT_CONSTANTS.new_report.subject_age]: 35,
        [REPORT_CONSTANTS.new_report.subject_sex]: "M",
    }

    /* CNPJ */
    const detailsMockCNPJ = [
        {
            title: REPORT_SECTIONS.dados_empresa,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP", "CadastroNacionalPJ"],
            data_count: Object.keys(dadosEmpresaMock.detalhes).length,
            data: [dadosEmpresaMock],
        },
        {
            title: REPORT_SECTIONS.telefones,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP", "SintegraMACNPJ", "CadastroNacionalPJ", "SintegraPBCNPJ"],
            data_count: Object.keys(telefonesMockCNPJ.detalhes).length,
            data: [telefonesMockCNPJ],
        },
        {
            title: REPORT_SECTIONS.emails,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP", "CadastroNacionalPJ"],
            data_count: Object.keys(emailsMockCNPJ.detalhes).length,
            data: [emailsMockCNPJ],
        },
        {
            title: REPORT_SECTIONS.enderecos,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP", "CadastroNacionalPJ", "SintegraMTCNPJ", "SintegraMACNPJ", "SintegraDeSaoPauloCNPJ", "SintegraPBCNPJ", "SintegraSECNPJ", "SintegraMGCNPJ", "SintegraCECNPJ", "SintegraPRCNPJ"],
            data_count: Object.keys(enderecoMockCNPJ.detalhes).length,
            data: [enderecoMockCNPJ],
        },
        {
            title: REPORT_SECTIONS.socios,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP", "CadastroNacionalPJ"],
            data_count: Object.keys(sociosMock.detalhes).length,
            data: [sociosMock],
        },
        {
            title: REPORT_SECTIONS.juntas_comerciais,
            subtitle: "Dados consultados na API SNAP.",
            source: ["JUCESP"],
            data_count: juntasComerciaisMock.length,
            data: juntasComerciaisMock,
        },
        {
            title: REPORT_SECTIONS.processos,
            subtitle: "Dados consultados na API SNAP.",
            source: ["Escavador"],
            data_count: processosMock.length,
            data: processosMock,
        },
        {
            title: REPORT_SECTIONS.diarios_oficiais_cnpj,
            subtitle: "Dados consultados na API SNAP através do CNPJ de entrada.",
            source: ["EscavadorDOCNPJ"],
            data_count: diariosOficiaisCNPJMock.length,
            data: diariosOficiaisCNPJMock,
        },
        {
            title: REPORT_SECTIONS.diarios_oficiais_nome,
            subtitle: "Dados consultados na API SNAP através do NOME da empresa.",
            source: ["EscavadorDONome"],
            data_count: diariosOficiaisNomeCNPJMock.length,
            data: diariosOficiaisNomeCNPJMock,
        },
        {
            title: REPORT_SECTIONS.recursos_publicos_recebidos,
            subtitle: "Dados consultados em portais de Transparência estaduais e federal.",
            source: ["PortalDaTransparenciaDeMinasGeraisCNPJ", "TransparenciaDFCNPJ", "PortalDaTransparenciaDoAmazonasCNPJ", "TransparenciaPRCNPJ", "TransparenciaManausCNPJ"],
            data_count: recursosPublicosMock.length,
            data: recursosPublicosMock,
        },
        {
            title: REPORT_SECTIONS.doacoes_enviadas_campanha,
            subtitle: "Dados consultados na API SNAP.",
            source: ["TSEDoacoes"],
            data_count: doacoesEnviadasMock.length,
            data: doacoesEnviadasMock,
        },
        {
            title: REPORT_SECTIONS.doacoes_recebidas_campanha,
            subtitle: "Dados consultados na API SNAP.",
            source: ["TSEDoacoes"],
            data_count: doacoesRecebidasMock.length,
            data: doacoesRecebidasMock,
        },
        {
            title: REPORT_SECTIONS.fornecimentos_enviados_campanha,
            subtitle: "Dados consultados na API SNAP.",
            source: ["TSEFornecimento"],
            data_count: fornecimentosEnviadosMock.length,
            data: fornecimentosEnviadosMock,
        },
        {
            title: REPORT_SECTIONS.fornecimentos_recebidos_campanha,
            subtitle: "Dados consultados na API SNAP.",
            source: ["TSEFornecimento"],
            data_count: fornecimentosRecebidosMock.length,
            data: fornecimentosRecebidosMock,
        }
    ]

    const metadataMockCNPJ: ReportMetadata = {
        [REPORT_CONSTANTS.new_report.report_id]: "6f6e06e9-af51-4067-a53f-03c8c17c4da8",
        [REPORT_CONSTANTS.new_report.report_status]: "success",
        [REPORT_CONSTANTS.new_report.report_type]: "cnpj",
        [REPORT_CONSTANTS.new_report.report_search_args]: { cnpj: "16670085000155" },
        [REPORT_CONSTANTS.new_report.report_name]: "cnpj 16670085000155",
        [REPORT_CONSTANTS.new_report.creation_at]: "2025-05-19T20:20:56.920580+00:00",
        [REPORT_CONSTANTS.new_report.modified_at]: "2025-05-19T20:20:56.920580+00:00",
        [REPORT_CONSTANTS.new_report.subject_name]: "TECHBIZ FORENSE DIGITAL LTDA",
        [REPORT_CONSTANTS.new_report.subject_mother_name]: "",
        [REPORT_CONSTANTS.new_report.subject_age]: null,
        [REPORT_CONSTANTS.new_report.subject_sex]: "",
    }

    /* TELEFONE */
    const detailsMockTelefone = [
        {
            title: REPORT_SECTIONS.contatos_salvos,
            subtitle: "Dados consultados na API SNAP.",
            source: ["IRBIS"],
            data_count: contatosSalvosMock.detalhes.length,
            data: [contatosSalvosMock],
        },
        {
            title: REPORT_SECTIONS.dados_pessoais,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP"],
            data_count: Object.keys(dadosPessoaisTelefoneMock.detalhes).length,
            data: [dadosPessoaisTelefoneMock],
        },
        {
            title: REPORT_SECTIONS.imagens,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP"],
            data_count: imagensMock.detalhes.length,
            data: [imagensMock],
        },
        {
            title: REPORT_SECTIONS.emails,
            subtitle: "Dados consultados na API SNAP.",
            source: ["IRBIS", "IRBISLuna"],
            data_count: Object.keys(emailsMockTelefone.detalhes).length,
            data: [emailsMockTelefone],
        },
        {
            title: REPORT_SECTIONS.enderecos,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP", "IRBISLuna"],
            data_count: Object.keys(enderecoMockTelefone.detalhes).length,
            data: [enderecoMockTelefone],
        },
        {
            title: REPORT_SECTIONS.parentes,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP"],
            data_count: parentesTelefoneMock.length,
            data: parentesTelefoneMock,
        },
        {
            title: REPORT_SECTIONS.empresas_relacionadas,
            subtitle: "Dados consultados na API SNAP.",
            source: ["IRBISLuna"],
            data_count: empresasRelacionadasMock.length,
            data: empresasRelacionadasMock,
        },
        {
            title: REPORT_SECTIONS.nomes_usuario,
            subtitle: "Dados consultados na API SNAP.",
            source: ["IRBISLuna"],
            data_count: nomeUsuarioMock.detalhes.length,
            data: [nomeUsuarioMock],
        },
        {
            title: REPORT_SECTIONS.telefones,
            subtitle: "Dados consultados na API SNAP.",
            source: ["IRBISLuna"],
            data_count: Object.keys(telefonesMockTelefone.detalhes).length,
            data: [telefonesMockTelefone],
        },
        {
            title: REPORT_SECTIONS.perfis_redes_sociais,
            subtitle: "Dados consultados na API SNAP.",
            source: ["IRBIS", "IRBISLuna"],
            data_count: perfisRedesSOciaisMock.length,
            data: perfisRedesSOciaisMock,
        },
        {
            title: REPORT_SECTIONS.outras_urls,
            subtitle: "Dados consultados na API SNAP.",
            source: ["IRBISLuna"],
            data_count: urlsMock.detalhes.length,
            data: [urlsMock],
        },
        {
            title: REPORT_SECTIONS.possiveis_contatos,
            subtitle: "Dados consultados na API SNAP.",
            source: ["SNAP", "IRBISLuna"],
            data_count: possiveisContatosMockTelefone.length,
            data: possiveisContatosMockTelefone,
        },
        {
            title: REPORT_SECTIONS.possiveis_pessoas_relacionadas,
            subtitle: "Dados consultados na API SNAP.",
            source: ["IRBISLuna"],
            data_count: possiveisPessoasRelacionadasMock.length,
            data: possiveisPessoasRelacionadasMock,
        },
        {
            title: REPORT_SECTIONS.possiveis_contas_em_sites,
            subtitle: "Dados consultados na API SNAP.",
            source: ["ProvedorDeAplicacacaoDaInternettelefone"],
            data_count: possiveisContasMockTelefone.length,
            data: possiveisContasMockTelefone,
        }
    ]

    const metadataMockTelefone: ReportMetadata = {
        [REPORT_CONSTANTS.new_report.report_id]: "6f6e06e9-af51-4067-a53f-03c8c17c4da8",
        [REPORT_CONSTANTS.new_report.report_status]: "success",
        [REPORT_CONSTANTS.new_report.report_type]: "telefone",
        [REPORT_CONSTANTS.new_report.report_search_args]: { telefone: "21999891661" },
        [REPORT_CONSTANTS.new_report.report_name]: "telefone 21999891661",
        [REPORT_CONSTANTS.new_report.creation_at]: "2025-05-19T20:20:56.920580+00:00",
        [REPORT_CONSTANTS.new_report.modified_at]: "2025-05-19T20:20:56.920580+00:00",
        [REPORT_CONSTANTS.new_report.subject_name]: "BIANCA PARREIRA CABRAL-LOPES",
        [REPORT_CONSTANTS.new_report.subject_mother_name]: "NORMA PERREIRA",
        [REPORT_CONSTANTS.new_report.subject_age]: null,
        [REPORT_CONSTANTS.new_report.subject_sex]: "F",
    }

    const teste = mockResultCpf
    
    switch (reportType) {
        case "cnpj":
            return { ...metadataMockCNPJ, data: { "cnpj": detailsMockCNPJ } };
        case "telefone":
            return { ...metadataMockTelefone, data: { "telefone": detailsMockTelefone } };
        case "cpf":
        default:
            return { ...metadataMockCpf, data: { "cpf": teste } };
    }
}
