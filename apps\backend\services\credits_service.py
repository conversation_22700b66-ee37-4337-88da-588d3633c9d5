import logging
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import H<PERSON>PEx<PERSON>, status
from datetime import datetime, timezone
import pytz
import asyncio
import httpx

from core.constants import Fields, UserFields, CustomErrorMessages, Endpoints

from models.user_model import Users

from services.base_service import BaseService
from exceptions.business_exceptions import (
    UserNotFoundError,
    UserLicenseExpiredError,
    UserCreditsDateMissingError,
    UpdateMonthlyUserCreditsError,
    ApiCreditsFetchError,
    ApiCreditsServiceUnavailableError,
    SpendQuotaFailedError
)

logger = logging.getLogger(__name__)


class CreditsService(BaseService):

    def __init__(self, db: AsyncSession, user_id: str) -> None:
        super().__init__(db)
        self.user_id=user_id
        self.api_key=None
        self.api_credits=None
        self.api_next_reset_credits=None


    async def compare_user_credits_with_api_credits(self, user_data: dict) -> int:
        logger.info("[compare_user_credits_with_api_credits][user(%s)] Called", self.user_id)

        if not user_data:
             logger.warning("[compare_user_credits_with_api_credits][user(%s)] No user data provided.", self.user_id)
             raise UserNotFoundError(self.user_id)

        logger.info("[compare_user_credits_with_api_credits][user(%s)] user_data: %s", self.user_id, user_data)
        user_credits = user_data.get(Fields.total_credits)
        next_reset_credits = user_data.get(UserFields.next_reset_credits)
        logger.info("[compare_user_credits_with_api_credits][user(%s)] user_credits: %s, next_reset_credits: %s", self.user_id, user_credits, next_reset_credits)
        
        # if getattr(self, 'api_credits', None) is None and getattr(self, 'api_next_reset_credits', None) is None:
        logger.info("[compare_user_credits_with_api_credits][user(%s)] api_credits and api_next_reset_credits are None, fetching from API", self.user_id)
        
        self.api_credits , self.api_next_reset_credits = await self.get_api_credits()
        logger.info("[compare_user_credits_with_api_credits][user(%s)] api_credits: %s, api_next_reset_credits: %s", self.user_id, self.api_credits, self.api_next_reset_credits)
        # now_utc = datetime.now(timezone.utc)
        now_br = datetime.now(pytz.timezone('UTC'))
        #UPDATING MONTHLY USER CREDITS ON OUR DB
        if self.api_next_reset_credits is not None:
            logger.info("[compare_user_credits_with_api_credits][user(%s)] Checking if next_reset_credits < api_next_reset_credits: %s < %s", self.user_id, next_reset_credits, self.api_next_reset_credits)

            if now_br>self.api_next_reset_credits:
                logger.error("[compare_user_credits_with_api_credits][user(%s)] api_next_reset_credits is before now_br, but next_reset_credits is None. This should not happen.", self.user_id)
                raise UserLicenseExpiredError()


            elif next_reset_credits is None or (next_reset_credits<self.api_next_reset_credits and now_br>=next_reset_credits): 

                logger.info("[compare_user_credits_with_api_credits][user(%s)] next_reset_credits is before api_next_reset_credits, updating monthly user credits", self.user_id)
                #necessary to update the user credits and date of when reset
                user_monthly_credits = user_data.get(UserFields.credits_monthly)
                logger.info("[compare_user_credits_with_api_credits][user(%s)] user_monthly_credits: %s", self.user_id, user_monthly_credits)

                user_credits = await self.update_monthly_user_credits(user_credits_monthly=user_monthly_credits)
                logger.info("[compare_user_credits_with_api_credits][user(%s)] Updated user_credits to: %s", self.user_id, user_credits)

                
        else:
            logger.warning("[compare_user_credits_with_api_credits][user(%s)] Missing self.api_next_reset_credits on db: %s", self.user_id, self.api_next_reset_credits)
            logger.error("[compare_user_credits_with_api_credits][user(%s)] Raising HTTP 404 due to missing dates", self.user_id)
            raise UserCreditsDateMissingError()
        
        if self.api_credits is not None:
            logger.info("[compare_user_credits_with_api_credits][user(%s)] API credits available: %s", self.user_id, self.api_credits)
            minimum_credit = self.api_credits
            db_credits=False
            if user_credits is not None:
                logger.info("[compare_user_credits_with_api_credits][user(%s)] User credits available: %s, calculating minimum", self.user_id, user_credits)
                minimum_credit = min(self.api_credits, user_credits)
                db_credits=True
                logger.info("[compare_user_credits_with_api_credits][user(%s)] Minimum credit calculated: %s", self.user_id, minimum_credit)
            else:
                logger.info("[compare_user_credits_with_api_credits][user(%s)] User credits is None, using API credits as minimum: %s", self.user_id, minimum_credit)
        
        logger.info("[compare_user_credits_with_api_credits][user(%s)] Returning minimum credit: %s", self.user_id, minimum_credit)
        return minimum_credit, db_credits
        
        
    async def update_monthly_user_credits(self, user_credits_monthly: int):
        logger.info("[update_monthly_user_credits][user(%s)] Called. Updating credits to %s valid until %s.", self.user_id, user_credits_monthly, self.api_next_reset_credits)
        update_data={UserFields.next_reset_credits: self.api_next_reset_credits,
                     UserFields.credits_monthly: user_credits_monthly,
                     Fields.total_credits: user_credits_monthly}   
        
        logger.info("[update_monthly_user_credits][user(%s)] update_data: %s", self.user_id, update_data)
        try:
            logger.info("[update_monthly_user_credits][user(%s)] Executing update.", self.user_id)
            await self.db.execute(
                update(Users).where(Users.user_id==self.user_id)
                .values(**update_data)
            )
            await self.db.commit()
            logger.info("[update_monthly_user_credits][user(%s)] User credits updated successfully.", self.user_id)
            return user_credits_monthly
        except Exception as e:
            logger.error("[update_monthly_user_credits][user(%s)] Error: %s", self.user_id, e)
            raise UpdateMonthlyUserCreditsError()
      

    async def get_api_credits(self):
        logger.info("[get_api_credits][user(%s)] Fetching API credits.", self.user_id)
        if not getattr(self, 'api_key', None):
            logger.warning("[get_api_credits][user(%s)] self.api_key is None, cannot fetch API credits.", self.user_id)
            return self.api_credits, self.api_next_reset_credits

        headers = {
            'Ocp-Apim-Subscription-Key': self.api_key,
            'Accept': 'application/json'
        }
        
        max_retries = 3
        for attempt in range(1, max_retries + 1):
            try:
                async with httpx.AsyncClient() as client:
                    logger.info(f"[get_api_credits][user({self.user_id})] Attempt {attempt}/{max_retries}: Sending GET request to: {Endpoints.credits_report_endpoint}")
                    results_response = await client.get(
                        Endpoints.credits_report_endpoint,
                        headers=headers
                    )
                    logger.info(f"[get_api_credits][user({self.user_id})] Response status: {results_response.status_code}")

                    if results_response.status_code == 200:
                        logger.info(f"[get_api_credits][user({self.user_id})] Successfully fetched API credits on attempt {attempt}.")
                        
                        results_str = results_response.text
                        logger.info("[get_api_credits][user(%s)] Parsing response: %s", self.user_id, results_str)
                        parts = [part.strip() for part in results_str.split(',')]
                        values = {}
                        for part in parts:
                            if ': ' in part:
                                key, value = part.split(': ', 1)
                                values[key.lower()] = value

                        self.api_credits = int(values.get("remaining", 0))
                        api_next_reset_credits_str = values.get("next_reset")

                        if api_next_reset_credits_str:
                            date_str_clean = api_next_reset_credits_str.replace(" ", "")
                            try:
                                self.api_next_reset_credits = datetime.strptime(date_str_clean, "%Y-%m-%d").replace(tzinfo=pytz.timezone('UTC'))
                                logger.info("[get_api_credits][user(%s)] api_next_reset_credits parsed as datetime: %s", self.user_id, self.api_next_reset_credits)
                            except Exception as e:
                                logger.error("[get_api_credits][user(%s)] Invalid date format for api_next_reset_credits: %s, Error: %s", self.user_id, api_next_reset_credits_str, e)
                        
                        return self.api_credits, self.api_next_reset_credits

                    elif results_response.status_code >= 500:
                        logger.warning(f"[get_api_credits][user({self.user_id})] Attempt {attempt} failed with server error: {results_response.status_code}. Retrying...")
                    
                    else:
                        error_text = results_response.text
                        logger.error(f"[get_api_credits][user({self.user_id})] Non-retryable client error {results_response.status_code}: {error_text}")
                        raise ApiCreditsFetchError(results_response.status_code, error_text)

            except httpx.RequestError as e:
                logger.warning(f"[get_api_credits][user({self.user_id})] Attempt {attempt} failed with network error: {e}. Retrying...")
            
            except Exception as e:
                logger.error(f"[get_api_credits][user({self.user_id})] Attempt {attempt} failed with unexpected error: {e}", exc_info=True)
                if attempt >= max_retries:
                    raise ApiCreditsFetchError(500, str(e))

            if attempt < max_retries:
                await asyncio.sleep(attempt)

        logger.error(f"[get_api_credits][user({self.user_id})] All {max_retries} retry attempts failed.")
        raise ApiCreditsServiceUnavailableError()


    async def change_user_credits(self, credit_delta: int):
        logger.info("[change_user_credits][user(%s)] Called. Changing credits by delta: %s.", self.user_id, credit_delta)
        try:
            query = select(Users).where(Users.user_id == self.user_id)
            logger.debug("[change_user_credits][user(%s)] Executing select query for user.", self.user_id)
            result = await self.db.execute(query)
            user = result.scalars().first()
            logger.debug("[change_user_credits][user(%s)] User fetched: %s", self.user_id, user)
            if not user:
                logger.error("[change_user_credits][user(%s)] User not found.", self.user_id)
                raise UserNotFoundError(self.user_id)
            available_credits = getattr(user, Fields.total_credits, 0)
            new_credits_total = available_credits + credit_delta
            logger.info("[change_user_credits][user(%s)] available_credits: %s, new_credits_total: %s", self.user_id, available_credits, new_credits_total)
            if new_credits_total<0:
                logger.error("[change_user_credits][user(%s)] Attempted to set negative credits. available_credits: %s, credit_delta: %s", self.user_id, available_credits, credit_delta)
                raise SpendQuotaFailedError()
            logger.info("[change_user_credits][user(%s)] Changing user from %s to %s.", self.user_id, available_credits, new_credits_total)
            await self.db.execute(
                update(Users)
                .where(Users.user_id == self.user_id)
                .values({Fields.total_credits: new_credits_total})
            )
            logger.info("[change_user_credits][user(%s)] Update query executed.", self.user_id)
            await self.db.commit()
            logger.info("[change_user_credits][user(%s)] Updated credits. New balance: %s", self.user_id, new_credits_total)
        except Exception as e:
            logger.error("[change_user_credits][user(%s)] Error: %s", self.user_id, e)
            raise SpendQuotaFailedError()
        