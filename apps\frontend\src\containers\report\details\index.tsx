import { useEffect, useMemo } from "react";
import { Loading } from "@snap/design-system";
import { useSecretKeyDialog } from "~/hooks/useSecretKeyDialog";
import { Navigate, useParams } from "react-router";
import { useUserData } from "~/store/userStore";
import { REPORT_DETAIL_STORE_INSTANCE } from "~/store/details";
import { useReportActionsWithAutoSave } from "~/hooks/useReportActionsWithAutoSave";
import { CombinadoFilterOption, ReportMetadata, ReportSection } from "~/types/global";
import { __PersonDetailsPage } from "root/modules/@snap/reports/ui/containers/report/";
import TabContainer from "../TabContainer";
import { toast } from "sonner";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";
import { AUTH_STORE_INSTANCE } from "~/store/auth";
import { useIsPasswordExpired } from "~/store/credentials";
import { MAX_PAGE_HEIGHT } from "~/helpers/constants";
import { REPORT_CONSTANTS } from "~/helpers/constants";

const ReportDetailsContainer = () => {
  const {
    selectors: {
      useReportSections,
      useReportMetadata,
      useReportProfileImage,
      useReportType,
      useReportReloadTrigger,
      useIsActionLoading,
      useSelectedCombinedFilter,
      useCombinedReportSources
    },
    events: {
      setReportSections,
      setReportMetadata,
      setReportType,
      setSelectedCombinedFilter,
      setCombinedReportSources
    }
  } = REPORT_DETAIL_STORE_INSTANCE;

  const reportSections = useReportSections();
  const metadata = useReportMetadata();
  const profileImage = useReportProfileImage();
  const reportType = useReportType();
  const reloadTrigger = useReportReloadTrigger();
  const isActionLoading = useIsActionLoading();
  const selectedCombinedFilter = useSelectedCombinedFilter();
  const combinedReportSources = useCombinedReportSources();
  const { openSecretKeyDialog } = useSecretKeyDialog();
  const userData = useUserData();
  const { id, type } = useParams<{ type: string; id: string }>();
  const actions = useReportActionsWithAutoSave()
  const { reportDetailsQuery } = useReportCRUD();
  const { checkPermission } = usePermissionCheck();
  const canUpdateReport = checkPermission(Permission.UPDATE_REPORT);
  const organizationLogo = userData?.organization_logo as string;
  const { data: reportDetailsData, isLoading, isError, error } = reportDetailsQuery(id!);
  const shouldPrintSnapLogo = userData?.print_snap_logo as boolean;

  const {
    selectors: { useIsAuthorized },
  } = AUTH_STORE_INSTANCE;
  const isUserAuthorized = useIsAuthorized();
  const isPasswordExpired = useIsPasswordExpired();

  const storeProps = useMemo(() => ({
    sections: reportSections,
    metadata: metadata as ReportMetadata,
    image: profileImage as string,
    organizationLogo: organizationLogo,
    shouldPrintSnapLogo: shouldPrintSnapLogo,
    reportType: reportType,
    isTrashEnabled: canUpdateReport,
    isPrintEnabled: true,
    isActionLoading: isActionLoading,
    selectedCombinedFilter: selectedCombinedFilter,
    combinedReportSources: combinedReportSources,
    actions: {
      ...actions,
      setSelectedCombinedFilter,
      setCombinedReportSources
    }
  }),
  [
    reportSections,
    metadata,
    profileImage,
    organizationLogo,
    shouldPrintSnapLogo,
    reportType,
    canUpdateReport,
    isActionLoading,
    selectedCombinedFilter,
    combinedReportSources,
    actions,
    setSelectedCombinedFilter,
    setCombinedReportSources
  ]);

  useEffect(() => {
    const loadReportData = () => {
      if (!reportDetailsData || isLoading) return;

      const { data: dataMap, ...meta } = reportDetailsData;
      const typeKey = type as keyof typeof dataMap;
      const foundSections: ReportSection[] = dataMap?.[typeKey] || [];

      setReportSections(foundSections);
      setReportMetadata(meta as ReportMetadata);
      setReportType(String(typeKey));

      if (typeKey === REPORT_CONSTANTS.types.combinado) {
        let reportSourcesUsed = (meta as ReportMetadata).report_sources_used;
        if (!Array.isArray(reportSourcesUsed)) {
          reportSourcesUsed = [];
        }

        setCombinedReportSources(reportSourcesUsed as CombinadoFilterOption[]);
        setSelectedCombinedFilter('combined_data');
      }
    };

    loadReportData();
  }, 
  [
    reportDetailsData,
    isLoading,
    setReportSections,
    setReportMetadata,
    setReportType,
    setSelectedCombinedFilter,
    setCombinedReportSources,
    type,
  ]);

  useEffect(() => {
    if (!isUserAuthorized || isPasswordExpired) {
      openSecretKeyDialog();
    }
  }, [isUserAuthorized, openSecretKeyDialog, isPasswordExpired]);

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className={`flex items-center justify-center h-screen w-full ${MAX_PAGE_HEIGHT}`}>
          <Loading size="lg" />
        </div>
      );
    }

    if (isError) {
      console.error("Error fetching report:", error);
      toast.error("Ocorreu um erro ao tentar carregar o relatório");
      return <Navigate to="/" replace />;
    };

    return (
      <div className={`flex px-8 flex-col lg:flex-row gap-8 w-full opacity-0 ${!isLoading && "opacity-100"} transition-opacity duration-200`}>
        <div className="flex-1 md:flex-1/4">
          <TabContainer />
        </div>
        <div className={`flex-1 md:flex-3/4 overflow-y-auto overflow-x-hidden [scrollbar-gutter:stable] ${MAX_PAGE_HEIGHT}`} id="report-content">
          <__PersonDetailsPage store={storeProps} />
        </div>
      </div>
    )
  }

  return renderContent();
};

export default ReportDetailsContainer;