"""
Column-Specific Migration Templates

This module provides templates for common column addition scenarios
to ensure consistent and safe database modifications.
"""

from datetime import datetime
from typing import Dict, Optional, List

NULLABLE_COLUMN_TEMPLATE = """\"\"\"Add {column_name} column to {table_name}

Revision ID: {revision_id}
Revises: {previous_revision}
Create Date: {create_date}

\"\"\"
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision = '{revision_id}'
down_revision = '{previous_revision}'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add nullable column - safe for existing data
    op.add_column('{table_name}',
        sa.Column('{column_name}', {column_type}, nullable=True),
        schema='reports'
    )

    # Add index if specified
    {index_creation}


def downgrade() -> None:
    # Drop index first if exists
    {index_removal}

    # Drop column
    op.drop_column('{table_name}', '{column_name}', schema='reports')
"""

NON_NULLABLE_COLUMN_TEMPLATE = """\"\"\"Add {column_name} column to {table_name}

Revision ID: {revision_id}
Revises: {previous_revision}
Create Date: {create_date}

\"\"\"
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision = '{revision_id}'
down_revision = '{previous_revision}'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add non-nullable column with default value
    op.add_column('{table_name}',
        sa.Column('{column_name}', {column_type}, nullable=False, server_default='{default_value}'),
        schema='reports'
    )

    # Add index if specified
    {index_creation}


def downgrade() -> None:
    # Drop index first if exists
    {index_removal}

    # Drop column
    op.drop_column('{table_name}', '{column_name}', schema='reports')
"""

FOREIGN_KEY_COLUMN_TEMPLATE = """\"\"\"Add {column_name} column to {table_name}

Revision ID: {revision_id}
Revises: {previous_revision}
Create Date: {create_date}

\"\"\"
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision = '{revision_id}'
down_revision = '{previous_revision}'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add foreign key column
    op.add_column('{table_name}',
        sa.Column('{column_name}', postgresql.UUID(), nullable={nullable}),
        schema='reports'
    )

    # Create foreign key constraint
    op.create_foreign_key(
        'fk_{table_name}_{column_name}',
        '{table_name}', '{referenced_table}',
        ['{column_name}'], ['{referenced_column}'],
        source_schema='reports', referent_schema='reports'
    )

    # Add index for foreign key
    op.create_index('idx_{table_name}_{column_name}', '{table_name}', ['{column_name}'],
                    unique=False, schema='reports')


def downgrade() -> None:
    # Drop index
    op.drop_index('idx_{table_name}_{column_name}', table_name='{table_name}', schema='reports')

    # Drop foreign key constraint
    op.drop_constraint('fk_{table_name}_{column_name}', '{table_name}', type_='foreignkey', schema='reports')

    # Drop column
    op.drop_column('{table_name}', '{column_name}', schema='reports')
"""

JSONB_COLUMN_TEMPLATE = """\"\"\"Add {column_name} JSONB column to {table_name}

Revision ID: {revision_id}
Revises: {previous_revision}
Create Date: {create_date}

\"\"\"
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision = '{revision_id}'
down_revision = '{previous_revision}'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add JSONB column
    op.add_column('{table_name}',
        sa.Column('{column_name}', postgresql.JSONB(astext_type=sa.Text()), nullable={nullable}),
        schema='reports'
    )

    # Add GIN index for JSONB queries
    op.create_index('idx_{table_name}_{column_name}', '{table_name}', ['{column_name}'],
                    unique=False, schema='reports', postgresql_using='gin')


def downgrade() -> None:
    # Drop GIN index
    op.drop_index('idx_{table_name}_{column_name}', table_name='{table_name}', schema='reports')

    # Drop column
    op.drop_column('{table_name}', '{column_name}', schema='reports')
"""

ENUM_COLUMN_TEMPLATE = """\"\"\"Add {column_name} enum column to {table_name}

Revision ID: {revision_id}
Revises: {previous_revision}
Create Date: {create_date}

\"\"\"
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision = '{revision_id}'
down_revision = '{previous_revision}'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create enum type if it doesn't exist
    {enum_creation}

    # Add enum column
    op.add_column('{table_name}',
        sa.Column('{column_name}', postgresql.ENUM({enum_values}, name='{enum_name}', create_type=False),
                  nullable={nullable}, server_default='{default_value}'),
        schema='reports'
    )

    # Add index if specified
    {index_creation}


def downgrade() -> None:
    # Drop index if exists
    {index_removal}

    # Drop column
    op.drop_column('{table_name}', '{column_name}', schema='reports')

    # Note: Enum type is not dropped automatically to avoid affecting other tables
    # If this is the only table using this enum, manually drop with:
    # op.execute('DROP TYPE IF EXISTS {enum_name} CASCADE')
"""

TWO_STEP_COLUMN_TEMPLATE = """\"\"\"Add {column_name} column to {table_name} (Step {step} of 2)

Revision ID: {revision_id}
Revises: {previous_revision}
Create Date: {create_date}

This is a two-step migration for adding a non-nullable column to a table with existing data.
Step 1: Add nullable column and populate with default values
Step 2: Make column non-nullable (separate migration)

\"\"\"
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision = '{revision_id}'
down_revision = '{previous_revision}'
branch_labels = None
depends_on = None


def upgrade() -> None:
    if '{step}' == '1':
        # Step 1: Add as nullable column
        op.add_column('{table_name}',
            sa.Column('{column_name}', {column_type}, nullable=True),
            schema='reports'
        )

        # Populate existing rows with default value
        op.execute(\"\"\"
            UPDATE reports.{table_name}
            SET {column_name} = '{default_value}'
            WHERE {column_name} IS NULL
        \"\"\")

    else:
        # Step 2: Make column non-nullable
        op.alter_column('{table_name}', '{column_name}',
                       existing_type={column_type},
                       nullable=False,
                       server_default='{default_value}',
                       schema='reports')

        # Add index if specified
        {index_creation}


def downgrade() -> None:
    if '{step}' == '2':
        # Step 2 downgrade: Make column nullable again
        {index_removal}

        op.alter_column('{table_name}', '{column_name}',
                       existing_type={column_type},
                       nullable=True,
                       server_default=None,
                       schema='reports')

    else:
        # Step 1 downgrade: Drop column entirely
        op.drop_column('{table_name}', '{column_name}', schema='reports')
"""

class ColumnMigrationGenerator:
    """Generate column migration files based on column requirements"""

    def __init__(self):
        self.templates = {
            'nullable': NULLABLE_COLUMN_TEMPLATE,
            'non_nullable': NON_NULLABLE_COLUMN_TEMPLATE,
            'foreign_key': FOREIGN_KEY_COLUMN_TEMPLATE,
            'jsonb': JSONB_COLUMN_TEMPLATE,
            'enum': ENUM_COLUMN_TEMPLATE,
            'two_step': TWO_STEP_COLUMN_TEMPLATE
        }

    def generate_column_migration(self, migration_type: str, **kwargs) -> str:
        """
        Generate a column migration based on the specified type and parameters.

        Args:
            migration_type: Type of migration ('nullable', 'non_nullable', 'foreign_key', etc.)
            **kwargs: Template variables

        Returns:
            Generated migration file content
        """
        if migration_type not in self.templates:
            raise ValueError(f"Unknown migration type: {migration_type}")

        template = self.templates[migration_type]
        return template.format(**self._prepare_template_vars(kwargs))

    def _prepare_template_vars(self, kwargs: Dict) -> Dict:
        """Prepare template variables with defaults"""
        import uuid

        defaults = {
            'revision_id': str(uuid.uuid4())[:8],
            'previous_revision': 'None',
            'create_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'),
            'nullable': 'True',
            'index_creation': '# No index specified',
            'index_removal': '# No index to remove',
            'enum_creation': '# Enum type should already exist'
        }

        # Update with provided kwargs
        defaults.update(kwargs)

        # Handle special formatting for specific fields
        if defaults.get('needs_index'):
            column_name = defaults['column_name']
            table_name = defaults['table_name']

            if defaults.get('is_jsonb'):
                defaults['index_creation'] = f"""op.create_index('idx_{table_name}_{column_name}', '{table_name}', ['{column_name}'],
                        unique=False, schema='reports', postgresql_using='gin')"""
                defaults['index_removal'] = f"""op.drop_index('idx_{table_name}_{column_name}', table_name='{table_name}', schema='reports')"""
            else:
                defaults['index_creation'] = f"""op.create_index('idx_{table_name}_{column_name}', '{table_name}', ['{column_name}'],
                        unique=False, schema='reports')"""
                defaults['index_removal'] = f"""op.drop_index('idx_{table_name}_{column_name}', table_name='{table_name}', schema='reports')"""

        return defaults

    def determine_migration_type(self, column_info: Dict) -> str:
        """
        Determine the appropriate migration type based on column information.

        Args:
            column_info: Dictionary with column details

        Returns:
            Migration type string
        """
        if column_info.get('is_foreign_key'):
            return 'foreign_key'
        elif column_info.get('column_type') == 'JSONB':
            return 'jsonb'
        elif column_info.get('is_enum'):
            return 'enum'
        elif not column_info.get('nullable', True):
            if column_info.get('use_two_step', False):
                return 'two_step'
            else:
                return 'non_nullable'
        else:
            return 'nullable'

    def generate_column_types(self, column_type: str, **options) -> str:
        """Generate SQLAlchemy column type definition for migrations"""
        type_mapping = {
            'Text': 'sa.Text()',
            'String': f'sa.String({options.get("length", 255)})',
            'Integer': 'sa.Integer()',
            'BigInteger': 'sa.BigInteger()',
            'Boolean': 'sa.Boolean()',
            'DateTime': 'sa.DateTime(timezone=True)',
            'Date': 'sa.Date()',
            'UUID': 'postgresql.UUID()',
            'JSONB': 'postgresql.JSONB(astext_type=sa.Text())',
            'Float': 'sa.Float()',
            'Numeric': 'sa.Numeric()'
        }

        return type_mapping.get(column_type, 'sa.Text()')

# Utility functions for common column patterns
def create_timestamp_column_migration(table_name: str, column_name: str = 'last_updated') -> str:
    """Create migration for adding a timestamp column"""
    generator = ColumnMigrationGenerator()
    return generator.generate_column_migration(
        'nullable',
        table_name=table_name,
        column_name=column_name,
        column_type='sa.DateTime(timezone=True)',
        nullable='True'
    )

def create_status_column_migration(table_name: str, default_status: str = 'active') -> str:
    """Create migration for adding a status column"""
    generator = ColumnMigrationGenerator()
    return generator.generate_column_migration(
        'non_nullable',
        table_name=table_name,
        column_name='status',
        column_type='sa.Text()',
        nullable='False',
        default_value=default_status,
        needs_index=True
    )

def create_user_reference_migration(table_name: str, nullable: bool = True) -> str:
    """Create migration for adding user_id foreign key"""
    generator = ColumnMigrationGenerator()
    return generator.generate_column_migration(
        'foreign_key',
        table_name=table_name,
        column_name='user_id',
        column_type='postgresql.UUID()',
        nullable=str(nullable),
        referenced_table='users',
        referenced_column='user_id'
    )

def create_jsonb_data_migration(table_name: str, column_name: str = 'data') -> str:
    """Create migration for adding JSONB data column"""
    generator = ColumnMigrationGenerator()
    return generator.generate_column_migration(
        'jsonb',
        table_name=table_name,
        column_name=column_name,
        nullable='True',
        is_jsonb=True,
        needs_index=True
    )

# Common column patterns that can be reused
COMMON_COLUMN_PATTERNS = {
    'audit_columns': {
        'created_by': {
            'type': 'UUID',
            'nullable': True,
            'foreign_key': 'reports.users.user_id'
        },
        'modified_by': {
            'type': 'UUID',
            'nullable': True,
            'foreign_key': 'reports.users.user_id'
        }
    },
    'soft_delete': {
        'deleted_at': {
            'type': 'DateTime',
            'nullable': True
        },
        'is_deleted': {
            'type': 'Boolean',
            'nullable': False,
            'default': 'false'
        }
    },
    'versioning': {
        'version': {
            'type': 'Integer',
            'nullable': False,
            'default': '1'
        }
    }
}