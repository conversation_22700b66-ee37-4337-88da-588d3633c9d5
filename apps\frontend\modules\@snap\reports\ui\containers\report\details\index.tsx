import React, { memo } from 'react';
import ReportDetailComponent from '../../../components/ReportDetailComponent';
import ReportProfileHeader from '../../../components/ReportProfileHeader';
import { ReportDetailStore, ReportMetadata } from '../../../../global';
import { ReportProvider, RenderMode } from '../../../../context/ReportContext';
import { TooltipProvider } from "../../../components/base/tooltip";
import { ModalInstance } from "@snap/design-system"
import RenderRelacoes from '../../../components/RenderRelacoes';
import DocumentButtons from '../../../components/DocumentButtons';
import { hasRenderableContents } from '../../../../helpers';

export interface PersonDetailsPageProps {
  store: ReportDetailStore;
  renderMode?: RenderMode;
}



export const __PersonDetailsPage: React.FC<PersonDetailsPageProps> = memo(({ store, renderMode = 'default' }) => {
  const metadata = store?.metadata || ({} as ReportMetadata)
  const sections = store?.sections || [];
  const reportType = store?.reportType || '';
  const isTrashEnabled = store?.isTrashEnabled || false;
  const isPrintEnabled = store?.isPrintEnabled || false;
  const isActionLoading = store?.isActionLoading || false;
  const profileImage = store?.image;
  const organizationLogo = store?.organizationLogo;
  const shouldPrintSnapLogo = store?.shouldPrintSnapLogo ?? true;
  const selectedCombinedFilter = store?.selectedCombinedFilter || 'combined_data';
  const combinedReportSources = store?.combinedReportSources || [];
  const isRelacoes = reportType === 'relacoes';

  const renderContent = () => {
    if (!metadata || !sections) {
      console.error('Metadata or sections are not defined');
      return <div>Sem dados para exibir</div>;
    }

    return (
      <div className="relative">
        <DocumentButtons
          showPdfButton={isPrintEnabled && hasRenderableContents(sections) && !isRelacoes}
          showDocxButton={isPrintEnabled && hasRenderableContents(sections) && !isRelacoes}
        />
        {
          renderMode === 'default' && (
            <div className="pb-4">
              <ReportProfileHeader />
            </div>
          )}
        {!isRelacoes ? <ReportDetailComponent /> : <RenderRelacoes />}
      </div>
    )
  };

  return (
    <ReportProvider
      sections={sections}
      metadata={metadata}
      reportType={reportType}
      isTrashEnabled={isTrashEnabled}
      isPrintEnabled={isPrintEnabled}
      isActionLoading={isActionLoading}
      image={profileImage}
      shouldPrintSnapLogo={shouldPrintSnapLogo}
      organizationLogo={organizationLogo}
      renderMode={renderMode}
      selectedCombinedFilter={selectedCombinedFilter}
      combinedReportSources={combinedReportSources}
      actions={store.actions}
    >
      <TooltipProvider>
        <ModalInstance />
        {renderContent()}
      </TooltipProvider>
    </ReportProvider >
  );
});

__PersonDetailsPage.displayName = 'PersonDetailsPage';