import { Socio } from "../model/Socios";
import { ValueWithSource } from "../model/ValueWithSource";

/**
 * Data Transfer Object for Socio model
 * Provides validation and transformation methods to ensure data conforms to the Socio model
 */
export class SocioDTO {
  /**
   * Validates if the provided data conforms to the Socio model structure
   * @param data Any data to validate
   * @returns Boolean indicating if the data is valid
   */
  static validate(data: any): boolean {
    if (!data || typeof data !== 'object') return false;
    
    // Check if at least one of the main properties exists and has the correct structure
    const hasValidMainProperty = 
      (data.nome_completo && SocioDTO.isValidValueWithSource(data.nome_completo)) || 
      (data.razao_social && SocioDTO.isValidValueWithSource(data.razao_social));
    
    // Check if at least one of the detail properties exists with correct structure
    const hasValidDetailProperty = 
      (data.detalhes && SocioDTO.isValidDetailsObject(data.detalhes)) ||
      (data.empresa && SocioDTO.isValidEmpresaArray(data.empresa));
    
    return hasValidMainProperty && hasValidDetailProperty;
  }

  /**
   * Transforms the provided data to conform to the Socio model
   * @param data Any data to transform
   * @returns A valid Socio object
   */
  static transform(data: any): Socio {
    if (!data || typeof data !== 'object') {
      return {};
    }

    const result: Socio = {};
    
    // Transform main properties
    if (data.nome_completo && SocioDTO.isValidValueWithSource(data.nome_completo)) {
      result.nome_completo = data.nome_completo;
    }
    
    if (data.razao_social && SocioDTO.isValidValueWithSource(data.razao_social)) {
      result.razao_social = data.razao_social;
    }
    
    // Transform details
    if (data.detalhes && typeof data.detalhes === 'object') {
      result.detalhes = SocioDTO.transformDetails(data.detalhes);
    }
    
    // Transform empresa array
    if (data.empresa && Array.isArray(data.empresa)) {
      result.empresa = SocioDTO.transformEmpresa(data.empresa);
    }
    
    return result;
  }

  /**
   * Validates if a value conforms to the ValueWithSource structure
   */
  private static isValidValueWithSource(value: any): boolean {
    return (
      value &&
      typeof value === 'object' &&
      'value' in value &&
      'label' in value &&
      typeof value.label === 'string' &&
      'source' in value &&
      Array.isArray(value.source)
    );
  }

  /**
   * Validates if an object is a valid details object
   */
  private static isValidDetailsObject(details: any): boolean {
    if (!details || typeof details !== 'object') return false;
    
    // Check if at least one property in details is a valid ValueWithSource
    return Object.values(details).some(value => 
      SocioDTO.isValidValueWithSource(value)
    );
  }

  /**
   * Validates if an array is a valid empresa array
   */
  private static isValidEmpresaArray(empresa: any[]): boolean {
    if (!Array.isArray(empresa) || empresa.length === 0) return false;
    
    // Check if at least one item in the array has a valid structure
    return empresa.some(item => 
      item && 
      typeof item === 'object' && 
      'value' in item && 
      typeof item.value === 'object' &&
      Object.values(item.value).some(v => SocioDTO.isValidValueWithSource(v as any))
    );
  }

  /**
   * Transforms a details object to ensure all values are valid
   */
  private static transformDetails(details: Record<string, any>): Record<string, ValueWithSource> {
    const result: Record<string, ValueWithSource> = {};
    
    Object.entries(details).forEach(([key, value]) => {
      if (SocioDTO.isValidValueWithSource(value)) {
        result[key] = value;
      }
    });
    
    return result;
  }

  /**
   * Transforms an empresa array to ensure all items are valid
   */
  private static transformEmpresa(empresa: any[]): Array<ValueWithSource> {
    return empresa
      .filter(item => 
        item && 
        typeof item === 'object' && 
        'value' in item && 
        typeof item.value === 'object'
      )
      .map(item => ({
        ...item,
        value: Object.entries(item.value).reduce((acc, [key, val]) => {
          if (SocioDTO.isValidValueWithSource(val)) {
            acc[key] = val;
          }
          return acc;
        }, {} as Record<string, any>)
      }));
  }

  /**
   * Validates an array of data items
   * @param dataArray Array of items to validate
   * @returns Boolean indicating if the array contains at least one valid item
   */
  static validateArray(dataArray: any[]): boolean {
    if (!Array.isArray(dataArray)) return false;
    return dataArray.some(item => SocioDTO.validate(item));
  }

  /**
   * Transforms an array of data items
   * @param dataArray Array of items to transform
   * @returns Array of valid Socio objects
   */
  static transformArray(dataArray: any[]): Socio[] {
    if (!Array.isArray(dataArray)) return [];
    return dataArray
      .filter(item => SocioDTO.validate(item))
      .map(item => SocioDTO.transform(item));
  }
}