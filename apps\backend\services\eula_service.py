import logging

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession


from services.base_service import BaseService


from models.eula_model import Eula

logger = logging.getLogger(__name__)


class EulaService(BaseService):
    def __init__(self, db: AsyncSession, user_id: str) -> None:
        super().__init__(db)
        self.user_id=user_id

    async def get_eula(self):
        logger.info(f"Fetching active EULA for user: {self.user_id}")
        
        try:
            # Use select(Eula) to get the full Eula object
            logger.info("Executing EULA query with select(Eula)...")
            query = select(Eula).where(Eula.valid_until.is_(None)).order_by(Eula.created_at.desc())
            logger.debug(f"Executing EULA query: {query}")
            
            # Log the compiled SQL for debugging
            compiled_query = query.compile(compile_kwargs={"literal_binds": True})
            logger.info(f"Compiled SQL query: {compiled_query}")
            
            # Execute the query once
            result = await self.db.execute(query)
            logger.info(f"Query result type: {type(result)}")
            
            # Use scalars().first() to get the Eula object directly
            # This is the correct way when using select(Eula)
            eula = result.scalars().first()
            logger.info(f"Eula object from scalars().first(): {eula}")
            logger.info(f"Eula object type from scalars().first(): {type(eula)}")
            
            if eula is None:
                logger.warning(f"No active EULA found in database")
                return None
            
            # Verify we got a proper Eula object
            if hasattr(eula, 'version') and hasattr(eula, 'content') and hasattr(eula, 'created_at'):
                logger.info(f"Found active EULA version: {eula.version}")
                return eula
            else:
                logger.warning(f"Eula object missing required attributes. Object: {eula}, Type: {type(eula)}")
                # Log the actual object structure for debugging
                logger.error(f"Eula object dir(): {dir(eula)}")
                if hasattr(eula, '__dict__'):
                    logger.error(f"Eula object __dict__: {eula.__dict__}")
                return None
            
        except Exception as e:
            logger.error(f"Error fetching EULA from database: {str(e)}")
            raise