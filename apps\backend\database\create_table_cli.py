#!/usr/bin/env python3
"""
Interactive CLI tool for creating new database tables

This script provides an interactive interface for creating new tables
following established patterns and best practices.

Usage:
    python database/create_table_cli.py
"""

import os
import sys
import json
from pathlib import Path
from typing import List, Dict, Optional

# Add the backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

def interactive_table_creation():
    """Interactive CLI for creating a new table"""
    print("🗄️  Database Table Creation Wizard")
    print("=" * 50)

    # Basic information
    table_name = input("📝 Table name (lowercase with underscores): ").strip()
    if not table_name:
        print("❌ Table name is required!")
        return

    # Validate table name
    if not table_name.islower() or not table_name.replace('_', '').replace('-', '').isalnum():
        print("⚠️  Warning: Table name should be lowercase with underscores only")

    description = input(f"📄 Brief description of {table_name}: ").strip()

    # Table type
    print("\n🏗️  Select table type:")
    print("1. Standard table (with UUID primary key)")
    print("2. Lookup/Reference table (with integer primary key)")
    print("3. Junction table (many-to-many relationship)")
    print("4. Audit/Log table")

    table_type_choice = input("Choice (1-4): ").strip()
    table_types = {
        '1': 'standard',
        '2': 'lookup',
        '3': 'junction',
        '4': 'log'
    }
    table_type = table_types.get(table_type_choice, 'standard')

    # Relationships
    print("\n🔗 Common relationships:")
    has_user_relation = input("Does this table relate to users? (y/n): ").lower().startswith('y')
    has_org_relation = input("Does this table relate to organizations? (y/n): ").lower().startswith('y')

    # Additional columns
    print("\n📋 Additional columns (press Enter to skip, 'done' to finish):")
    additional_columns = []
    while True:
        col_name = input("Column name: ").strip()
        if not col_name or col_name.lower() == 'done':
            break

        col_type = input(f"Type for {col_name} (Text/String/Integer/Boolean/JSONB/DateTime): ").strip() or 'Text'
        nullable = input(f"Is {col_name} nullable? (y/n): ").lower().startswith('y')

        additional_columns.append({
            'name': col_name,
            'type': col_type,
            'nullable': nullable
        })

    # Generate files
    print(f"\n🚀 Creating table '{table_name}'...")

    try:
        # Generate model
        model_content = generate_model_file(
            table_name, table_type, has_user_relation, has_org_relation,
            additional_columns, description
        )

        # Write model file
        model_file = Path(f"models/{table_name}_model.py")
        with open(model_file, 'w') as f:
            f.write(model_content)
        print(f"✅ Created model file: {model_file}")

        # Generate migration
        migration_content = generate_migration_file(table_name, table_type, additional_columns)
        migration_file = Path(f"alembic/versions/{get_next_revision()}_{table_name}.py")
        with open(migration_file, 'w') as f:
            f.write(migration_content)
        print(f"✅ Created migration file: {migration_file}")

        # Generate basic schema
        schema_content = generate_schema_file(table_name, additional_columns)
        schema_file = Path(f"schemas/{table_name}_schema.py")
        with open(schema_file, 'w') as f:
            f.write(schema_content)
        print(f"✅ Created schema file: {schema_file}")

        print(f"\n🎉 Successfully created table '{table_name}'!")
        print("\n📝 Next steps:")
        print(f"1. Review generated files and customize as needed")
        print(f"2. Add import to models/__init__.py:")
        print(f"   from models.{table_name}_model import {to_pascal_case(table_name)}")
        print(f"3. Test migration: alembic upgrade head")
        print(f"4. Validate schema using schema_validator.py")

    except Exception as e:
        print(f"❌ Error creating table: {e}")

def generate_model_file(table_name: str, table_type: str, has_user: bool, has_org: bool,
                       additional_columns: List[Dict], description: str) -> str:
    """Generate SQLAlchemy model file content"""

    class_name = to_pascal_case(table_name)
    pk_name = f"{table_name}_id" if table_type != 'lookup' else 'id'
    pk_type = 'UUID' if table_type != 'lookup' else 'Integer'

    imports = [
        "from sqlalchemy import Column, DateTime, Text, String, Integer, BigInteger, Boolean, Index, ForeignKey",
        "from sqlalchemy.dialects.postgresql import UUID, JSONB",
        "from sqlalchemy.sql import func",
        "from sqlalchemy.orm import relationship",
        "",
        "from models.base import Base"
    ]

    # Build columns
    columns = []
    indexes = []
    relationships = []

    # Primary key
    if table_type == 'lookup':
        columns.append(f"    {pk_name} = Column(Integer, primary_key=True)")
    else:
        columns.append(f"    {pk_name} = Column(UUID, primary_key=True, server_default=func.gen_random_uuid())")

    # Foreign keys
    if has_user and table_type != 'junction':
        columns.append('    user_id = Column(UUID, ForeignKey("reports.users.user_id"), nullable=False)')
        indexes.append('        Index("idx_{}_user_id", "user_id"),'.format(table_name))
        relationships.append('    user = relationship("Users", back_populates="{}")'.format(table_name + 's'))

    if has_org:
        columns.append('    organization_id = Column(UUID, ForeignKey("reports.organizations.organization_id"), nullable=True)')
        indexes.append('        Index("idx_{}_organization_id", "organization_id"),'.format(table_name))
        relationships.append('    organization = relationship("Organizations", back_populates="{}")'.format(table_name + 's'))

    # Timestamps
    if table_type != 'lookup':
        columns.append("    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.timezone('UTC', func.now()))")
        if table_type not in ['log', 'junction']:
            columns.append("    modified_at = Column(DateTime(timezone=True), nullable=False, server_default=func.timezone('UTC', func.now()))")
    else:
        columns.append("    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.timezone('UTC', func.now()))")

    # Additional columns
    for col in additional_columns:
        nullable_str = 'True' if col['nullable'] else 'False'

        if col['type'] == 'String':
            col_def = f"    {col['name']} = Column(String(255), nullable={nullable_str})"
        elif col['type'] == 'Boolean':
            col_def = f"    {col['name']} = Column(Boolean, nullable={nullable_str}, server_default='false')"
        elif col['type'] == 'JSONB':
            col_def = f"    {col['name']} = Column(JSONB, nullable={nullable_str})"
            indexes.append(f'        Index("idx_{table_name}_{col["name"]}", "{col["name"]}", postgresql_using="gin"),')
        else:
            col_def = f"    {col['name']} = Column({col['type']}, nullable={nullable_str})"

        columns.append(col_def)

    # Lookup table specific columns
    if table_type == 'lookup':
        columns.extend([
            '    code = Column(String(50), nullable=False, unique=True)',
            '    name = Column(Text, nullable=False)',
            '    description = Column(Text, nullable=True)',
            '    is_active = Column(Boolean, nullable=False, server_default="true")',
            '    sort_order = Column(Integer, nullable=True)'
        ])
        indexes.extend([
            '        Index("idx_{}_code", "code", unique=True),'.format(table_name),
            '        Index("idx_{}_active", "is_active"),'.format(table_name)
        ])

    # Build the complete model
    model_parts = [
        '"""',
        f'{class_name} Model',
        '',
        description or f'Model for {table_name} table',
        '"""',
        '',
        '\n'.join(imports),
        '',
        '',
        f'class {class_name}(Base):',
        f'    __tablename__ = "{table_name}"',
        '    __table_args__ = ('
    ]

    if indexes:
        model_parts.extend(indexes)

    model_parts.extend([
        '        {"schema": "reports"}',
        '    )',
        '',
        '    # Columns'
    ])

    model_parts.extend(columns)

    if relationships:
        model_parts.extend(['', '    # Relationships'] + relationships)

    model_parts.extend([
        '',
        '    def __repr__(self):',
        f'        return f"<{class_name}({pk_name}={{self.{pk_name}}})>"'
    ])

    return '\n'.join(model_parts)

def generate_migration_file(table_name: str, table_type: str, additional_columns: List[Dict]) -> str:
    """Generate Alembic migration file content"""
    revision = get_next_revision()

    return f'''"""Add {table_name} table

Revision ID: {revision}
Revises: {get_previous_revision()}
Create Date: {get_current_timestamp()}

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision = '{revision}'
down_revision = '{get_previous_revision()}'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # TODO: Customize this migration based on your specific table structure
    # This is a basic template - modify as needed
    op.create_table('{table_name}',
        # Add your columns here based on the model you created
        schema='reports'
    )


def downgrade() -> None:
    op.drop_table('{table_name}', schema='reports')
'''

def generate_schema_file(table_name: str, additional_columns: List[Dict]) -> str:
    """Generate Pydantic schema file content"""
    class_name = to_pascal_case(table_name)

    return f'''"""
Pydantic schemas for {class_name}

These schemas define the structure for API requests and responses
for the {table_name} table.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel


class {class_name}Base(BaseModel):
    """Base schema with common fields"""
    pass


class {class_name}Create({class_name}Base):
    """Schema for creating new {table_name} records"""
    pass


class {class_name}Update(BaseModel):
    """Schema for updating {table_name} records"""
    pass


class {class_name}InDB({class_name}Base):
    """Schema for {table_name} records from database"""
    {table_name}_id: UUID
    created_at: datetime

    class Config:
        from_attributes = True


class {class_name}Response({class_name}InDB):
    """Schema for {table_name} API responses"""
    pass
'''

def to_pascal_case(snake_str: str) -> str:
    """Convert snake_case to PascalCase"""
    return ''.join(word.capitalize() for word in snake_str.split('_'))

def get_next_revision() -> str:
    """Get next revision number"""
    import uuid
    return str(uuid.uuid4())[:8]

def get_previous_revision() -> str:
    """Get the previous revision ID"""
    # This would need to read from existing migration files
    # For now, return a placeholder
    return "001"

def get_current_timestamp() -> str:
    """Get current timestamp for migration"""
    from datetime import datetime
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')

def main():
    """Main entry point"""
    try:
        interactive_table_creation()
    except KeyboardInterrupt:
        print("\n\n👋 Table creation cancelled.")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")

if __name__ == "__main__":
    main()