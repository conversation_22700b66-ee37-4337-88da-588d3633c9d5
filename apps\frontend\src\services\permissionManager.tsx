import { Permission } from "~/helpers/permissions.helper";
import { useUserData } from "~/store/userStore";

export const usePermissionManager = () => {
  const userData = useUserData();
  const usersPermissions =
    userData?.roles || [];
  const permissions = [...usersPermissions];

  const permissionsDict = permissions.reduce((acc: Record<string, boolean>, curr: string) => {
    acc[curr] = true;
    return acc;
  }, {});

  const hasPermission = (role: string) => {
    try {
      if (!userData) {
        return false;
      }
      const hasPermission = permissionsDict[role];
      return !!hasPermission;
    } catch (error) {
      console.error(error);
      return false;
    }
  };

  const getCurrentPermissions = () => permissions;

  const checkPermission = (permission: Permission) => hasPermission(permission);
  /* relatórios */
  const canCreateReport = () => hasPermission(Permission.CREATE_REPORT);
  const canViewReportList = () => hasPermission(Permission.VIEW_REPORT_LIST);
  const canViewReportDetails = () => hasPermission(Permission.VIEW_REPORT_DETAILS);
  const canUpdateReport = () => hasPermission(Permission.UPDATE_REPORT);
  /* convites */
  const canInviteUser = () => hasPermission(Permission.INVITE_USER);
  const canCancelInvite = () => hasPermission(Permission.CANCEL_INVITE);
  const canGetUserInvite = () => hasPermission(Permission.GET_USER_INVITE);
  const canGetDataFromUserInvite = () => hasPermission(Permission.GET_DATA_FROM_USER_INVITE);
  const canGetInviteDetails = () => hasPermission(Permission.GET_INVITE_DETAILS);
  const canAnswerInvite = () => hasPermission(Permission.ANSWER_INVITE);
  /* usuários */
  const canEditUser = () => hasPermission(Permission.EDIT_USER);
  const canGetUserLogs = () => hasPermission(Permission.GET_USER_LOGS);
  /* organizações */
  const canGetOrganizationLogs = () => hasPermission(Permission.GET_ORGANIZATION_LOGS);
  const canGetOrganizationInvite = () => hasPermission(Permission.GET_ORGANIZATION_INVITE);
  const canUpdatePrintSnapLogo = () => hasPermission(Permission.PRINT_SNAP_LOGO);
  /* api key */
  const canAddApiKey = () => hasPermission(Permission.ADD_API_KEY);

  return {
    checkPermission,
    canCreateReport,
    canViewReportList,
    canViewReportDetails,
    canUpdateReport,
    canInviteUser,
    canCancelInvite,
    canGetUserInvite,
    canGetDataFromUserInvite,
    canGetInviteDetails,
    canAnswerInvite,
    canEditUser,
    canGetUserLogs,
    canGetOrganizationLogs,
    canGetOrganizationInvite,
    canAddApiKey,
    getCurrentPermissions,
    canUpdatePrintSnapLogo,
  };
};
