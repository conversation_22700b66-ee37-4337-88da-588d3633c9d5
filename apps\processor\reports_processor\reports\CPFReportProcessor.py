from typing import Dict, Any

from .BaseReportProcessor import BaseReportProcessor, ReportTypeConfig
from ..constants import ReportKeys
from ..FormatFrontFormat import VinculoSection
from ..EntityExtractor import VinculoConfig, ExtractionResult
from ..CallbackProcessors import CallbackProcessors

class CPFReportProcessor(BaseReportProcessor):
    """Processor for CPF reports"""

    def get_config(self) -> ReportTypeConfig:
        return ReportTypeConfig(
            do_filter_doc='CPF',
            do_filter_name='Nome',
            do_doc = {'title': 'Diários Oficiais - CPF'},  # TODO: pode dar merda
            dados_pessoais={'title': 'Dados Pessoais'},  # TODO: pode dar merda
            possiveis_pessoas_relacionadas={'title': 'Possíveis Pessoas Relacionadas'},
            possiveis_empresas_relacionadas={'title': 'Empresas Relacionadas'},
            enabled_sections=[
                VinculoSection.MANDADOS, VinculoSection.PHONES, VinculoSection.EMAILS,
                VinculoSection.ENDERECOS, VinculoSection.PARENTES, VinculoSection.SOCIEDADES,
                VinculoSection.SOCIOS, VinculoSection.VINCULOS_EMPREGATICIOS, VinculoSection.PROCESSOS,
                VinculoSection.RECURSOS_RECEBIDOS, VinculoSection.SERVICO_PUBLICO,
                VinculoSection.DIARIOS_OFICIAIS_DOC, VinculoSection.DIARIOS_OFICIAIS_NOME,
                VinculoSection.FILIACAO_PARTIDARIA, VinculoSection.OUTROS_CONTATOS,
                VinculoSection.PAIS, VinculoSection.DOACOES_ELEITORAIS_ENVIADAS,
                VinculoSection.DOACOES_ELEITORAIS_RECEBIDAS, VinculoSection.FORNECIMENTOS_ELEITORAIS_ENVIADAS,
                VinculoSection.FORNECIMENTOS_ELEITORAIS_RECEBIDAS, VinculoSection.IMAGENS, VinculoSection.NOMES_USUARIO,
                VinculoSection.OUTRAS_URLS, VinculoSection.REDES_SOCIAIS, VinculoSection.VINCULOS_EDUCACIONAIS
            ]
        )

    def _get_custom_societario(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[
        VinculoSection, VinculoConfig]:
        # Extract societario data for CPF
        societario = self.extractor.extract_vinculos_genericos(
            other_result_data, entity_type, search_value,
            VinculoConfig(extract_type=ReportKeys.EMPRESA,
                          filter_base_data_callback=CallbackProcessors.filter_for_sociedades_vinculos,
                          replace_data_processor=CallbackProcessors.sociedade_data_processor_callback)
        )

        if not societario.data:
            return {}

        return {
            VinculoSection.SOCIEDADES: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.SOCIEDADES),
                extract_func=lambda *args, **kwargs: ExtractionResult(societario.data.get('sociedades', []), societario.sources),
                extract_type=ReportKeys.EMPRESA
            ),
            VinculoSection.SOCIOS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.SOCIOS),
                extract_func=lambda *args, **kwargs: ExtractionResult(societario.data.get('socios_p', []) + societario.data.get('socios_e', []),
                                         societario.sources),
                extract_type=ReportKeys.EMPRESA
            )
        }

    def get_custom_vinculos(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[
        Any, VinculoConfig]:

        custom_dict = self._get_custom_societario(other_result_data, entity_type, search_value)
        custom_dict.update(self._get_custom_processos(other_result_data, entity_type, search_value))
        custom_dict.update(self._get_custom_fornecimentos_campanha(other_result_data, entity_type, search_value))
        custom_dict.update(self._get_custom_doacoes_campanha(other_result_data, entity_type, search_value))
        return custom_dict

    def adjust_metadata(self, processed_result, metadata):
        self._adjust_metadata_personal_data(processed_result, metadata)
        self._adjust_metadata_age(processed_result, metadata)