import { <PERSON>rowser } from "puppeteer";
import { IGenerateReportsPDFInput, IGenerateReportsHTMLOutput } from "../global";
import * as logger from '../utils/logger';

export const CreateGenerateReportsPDF = (browserRef: Browser) => {
  const _state = Object.freeze({
    browser: browserRef
  });
  const { browser } = _state

  if (!browser) {
    logger.error('Browser not initialized, cannot generate PDF');
    throw new Error('Browser not initialized');
  }

  return async ({
    header,
    content,
    footer,
    hasOrganizationLogo,
    should_print_snap_logo
  }: IGenerateReportsHTMLOutput): Promise<Uint8Array<ArrayBufferLike>> => {

    try {
      const page = await browser.newPage();
      logger.info('Starting PDF generation process');
      const startTime = Date.now();


      await page.setViewport({ width: 794, height: 1123 });
      logger.debug('Viewport set to A4 dimensions');

      logger.debug('Content details', {
        headerLength: header?.length || 0,
        footerLength: footer?.length || 0
      });

      await page.setContent(content, {
        waitUntil: ['domcontentloaded'],
      });
      // margem superior de acordo com config do header
      const topMargin = hasOrganizationLogo ? '112' : should_print_snap_logo ? '72' : '62';

      const pdfBuffer = await page.pdf({
        format: 'A4',
        displayHeaderFooter: true,
        headerTemplate: header,
        footerTemplate: footer,
        printBackground: true,
        margin: { top: topMargin, right: '20', bottom: '74', left: '20' },
        preferCSSPageSize: true, // Use CSS @page size
        timeout: 600000, // 10 minutos
      });
      await page.close();

      const generationTime = Date.now() - startTime;
      logger.info('PDF generation completed', {
        duration: generationTime,
        size: pdfBuffer.length
      });

      return pdfBuffer;

    } catch (err) {
      logger.error('PDF generation error', {
        error: err as Error
      });
      throw new Error((err as Error).message);
    }
  }
}

export async function GenerateReportsPdf({
  browserRef: browser,
  ...params
}: IGenerateReportsPDFInput): Promise<Uint8Array<ArrayBufferLike>> {
  if (!browser) {
    logger.error('Browser not initialized, cannot generate PDF');
    throw new Error('Browser not initialized');
  }

  try {
    logger.info('Starting PDF generation process');
    const startTime = Date.now();
    const page = await browser.newPage();
    logger.debug('New browser page created');

    await page.setViewport({ width: 794, height: 1123 });
    logger.debug('Viewport set to A4 dimensions');

    logger.debug('Content details', {
      headerLength: params.header?.length || 0,
      footerLength: params.footer?.length || 0
    });

    await page.setContent(params.content, {
      waitUntil: ['domcontentloaded'],
      timeout: 300000,
    });
    // margem superior de acordo com config do header
    const topMargin = params.hasOrganizationLogo ? '112' : params.should_print_snap_logo ? '72' : '62';

    const pdfBuffer = await page.pdf({
      format: 'A4',
      displayHeaderFooter: true,
      headerTemplate: params.header,
      footerTemplate: params.footer,
      printBackground: true,
      margin: { top: topMargin, right: '20', bottom: '74', left: '20' },
      preferCSSPageSize: true, // Use CSS @page size
      timeout: 600000, // 10 minutos
    });
    // await page.close();

    const generationTime = Date.now() - startTime;
    logger.info('PDF generation completed', {
      duration: generationTime,
      size: pdfBuffer.length
    });

    return pdfBuffer;

  } catch (err) {
    logger.error('PDF generation error', {
      error: err as Error
    });
    throw new Error((err as Error).message);
  }
}
