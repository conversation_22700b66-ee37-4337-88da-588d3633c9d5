import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { formatFieldValue, parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip, getIconImage } from "./helpers.strategy";
import { GridItem } from "@snap/design-system";
import { ContatoSalvo } from "../../model/ContatosSalvos";
import { useNestedRender } from "./NestedRenderContext";

export function useRenderContatosSalvos(
  sectionTitle: string
): ArrayRenderStrategy<ContatoSalvo> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";
  const nested = useNestedRender();

  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<ContatoSalvo, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? e.detalhes.every((contato: any) =>
        contato.alias?.is_deleted === true &&
        Object.values(contato.detalhes || {}).every((v: any) => v.is_deleted === true)
      )
      : true;

  const testEntryDeleted = (entry: any): boolean => {
    return testDetalhesDeleted(entry);
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes || !Array.isArray(entry.detalhes)) return count;

      // Count individual contacts that should be included in the current view
      const activeContacts = entry.detalhes.filter((contato: any) => {
        const isAliasDeleted = contato.alias?.is_deleted === true;
        // In data count, we count contacts that would be visible in default view
        // Count contacts where alias is NOT deleted
        return !isAliasDeleted;
      });

      return count + activeContacts.length;
    }, 0);
  };

  const shouldIncludeContato = (contato: any) => {
    const isAliasDeleted = contato.alias?.is_deleted === true;
    const hasDeletedDetalhes = Object.values(contato.detalhes || {}).some((v: any) => v.is_deleted === true);

    if (isTrash) {
      return isAliasDeleted || hasDeletedDetalhes;
    } else {
      return !isAliasDeleted;
    }
  };

  const onToggleContato = (entryIdx: number, contatoIdx: number) => {
    if (nested?.isNested) {
      nested.updateNestedSection(sectionTitle, (nestedSection) => {
        const entry = nestedSection.data?.[0];
        const contato = entry?.detalhes?.[contatoIdx];
        if (contato) {
          const currentAliasDeleted = contato.alias?.is_deleted || false;
          const targetDeletedState = !currentAliasDeleted;
          if (contato.alias) contato.alias.is_deleted = targetDeletedState;
          if (contato.detalhes) {
            Object.values(contato.detalhes).forEach((detalhe: any) => { if (detalhe) detalhe.is_deleted = targetDeletedState; });
          }
        }
        const dataArr = Array.isArray(nestedSection.data) ? nestedSection.data : [];
        nestedSection.data_count = dataArr.reduce((acc: number, ent: any) => {
          const contatos = ent?.detalhes?.filter((c: any) => !(c?.alias?.is_deleted === true)) ?? [];
          return acc + contatos.length;
        }, 0);
      });
      return;
    }

    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const contato = e.detalhes?.[contatoIdx];
          if (contato) {
            const currentAliasDeleted = contato.alias?.is_deleted || false;
            const targetDeletedState = !currentAliasDeleted;

            if (contato.alias) {
              contato.alias.is_deleted = targetDeletedState;
            }

            if (contato.detalhes) {
              Object.values(contato.detalhes).forEach((detalhe: any) => {
                if (detalhe) detalhe.is_deleted = targetDeletedState;
              });
            }
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleContatoField = (entryIdx: number, contatoIdx: number, fieldKey: string) => {
    if (nested?.isNested) {
      nested.updateNestedSection(sectionTitle, (nestedSection) => {
        const entry = Array.isArray(nestedSection.data) ? nestedSection.data[entryIdx] : undefined;
        const contato = entry?.detalhes?.[contatoIdx];
        if (contato?.detalhes?.[fieldKey]) {
          contato.detalhes[fieldKey].is_deleted = !contato.detalhes[fieldKey].is_deleted;
        }
        const dataArr = Array.isArray(nestedSection.data) ? nestedSection.data : [];
        nestedSection.data_count = dataArr.reduce((acc: number, ent: any) => {
          const contatos = ent?.detalhes?.filter((c: any) => !(c?.alias?.is_deleted === true)) ?? [];
          return acc + contatos.length;
        }, 0);
      });
      return;
    }

    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const contato = e.detalhes?.[contatoIdx];
          if (contato?.detalhes?.[fieldKey]) {
            contato.detalhes[fieldKey].is_deleted = !contato.detalhes[fieldKey].is_deleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const formatByKey: Record<
    string,
    (entry?: ContatoSalvo) => React.ReactElement | null
  > = {
    detalhes: (entry) => {
      if (!entry?.detalhes?.length) return null;
      const idx = idxMap.get(entry)!;

      const visibleContatos = entry.detalhes
        .map((contato, i) => ({ contato, idx: i }))
        .filter(({ contato }) => shouldIncludeContato(contato));

      if (!visibleContatos.length) return null;

      return (
        <CustomGridContainer cols={2} key={`detalhes-${idx}`} className="mb-6">
          {visibleContatos.map(({ contato, idx: contatoIdx }) => {
            const handleToggleAlias = () => onToggleContato(idx, contatoIdx);
            const handleToggleContatoField = (fieldKey: string) => onToggleContatoField(idx, contatoIdx, fieldKey);

            return (
              <GridItem key={`contato-${idx}-${contatoIdx}`} cols={1}>
                {includeKey(contato.alias?.is_deleted || false) && (
                  <CustomGridItem
                    cols={1}
                    className="mb-1"
                    onToggleField={handleToggleAlias}
                  >
                    <CustomReadOnlyInputField
                      label={contato.alias.label.toUpperCase()}
                      colorClass="bg-primary"
                      labelTextClass="text-accent"
                      value={parseValue(formatFieldValue(contato.alias.value))}
                      tooltip={renderSourceTooltip(contato.alias.source)}
                      className="border-0 pb-0"
                    />
                  </CustomGridItem>
                )}

                {Object.entries(contato.detalhes)
                  .filter(([, v]) => includeKey(v.is_deleted))
                  .map(([fieldKey, fieldValue]) => {
                    const isOriginField = fieldKey === 'origin';
                    const fieldIconSrc = isOriginField && fieldValue.value ? getIconImage(fieldValue.value) : null;

                    return (
                      <CustomGridItem
                        key={`contato-${idx}-${contatoIdx}-${fieldKey}`}
                        fullWidth
                        className="py-1"
                        onToggleField={() => handleToggleContatoField(fieldKey)}
                      >
                        <div className="flex items-baseline gap-2 w-full">
                          {fieldIconSrc && (
                            <img
                              src={fieldIconSrc}
                              alt={fieldValue.value}
                              className="w-4 h-4 object-contain flex-shrink-0"
                              onError={(e) => {
                                e.currentTarget.style.display = 'none';
                              }}
                            />
                          )}
                          <CustomReadOnlyInputField
                            label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                            colorClass="bg-border"
                            icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                            value={formatFieldValue(fieldValue.value || "")}
                            tooltip={renderSourceTooltip(fieldValue.source)}
                            className="flex-1"
                          />
                        </div>
                      </CustomGridItem>
                    );
                  })}
              </GridItem>
            );
          })}
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof ContatoSalvo>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: ContatoSalvo): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof ContatoSalvo>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Contatos Salvos] Chaves inválidas:", keys);
    }

    const orderedKeys: Array<keyof ContatoSalvo> = ['detalhes'];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    return filteredKeys
      .map((chave) => formatByKey[chave]?.(entry))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: ContatoSalvo[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Contatos Salvos] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (!entry.detalhes || !Array.isArray(entry.detalhes)) return false;

      if (isTrash) {
        return entry.detalhes.some((contato: any) =>
          contato.alias?.is_deleted === true ||
          Object.values(contato.detalhes || {}).some((v: any) => v.is_deleted === true)
        );
      } else {
        return entry.detalhes.some((contato: any) => !contato.alias?.is_deleted);
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`contato-salvo-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        if (entry.detalhes) {
          entry.detalhes.forEach((contato: any) => {
            if (contato.alias) {
              contato.alias.is_deleted = targetDeletedState;
            }
            if (contato.detalhes) {
              Object.values(contato.detalhes).forEach((detalhe: any) => {
                if (detalhe) detalhe.is_deleted = targetDeletedState;
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}