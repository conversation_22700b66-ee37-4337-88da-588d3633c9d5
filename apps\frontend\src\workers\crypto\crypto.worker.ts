/**
 * @file Este script é um Web Worker que carrega e executa um módulo WebAssembly (WASM)
 * gerado a partir de código Rust. Ele lida com operações criptográficas em uma thread separada
 * para não bloquear a thread principal da interface do usuário.
 */

// Importa as funções do módulo WASM gerado pelo wasm-bindgen.
// O `init` é uma função especial que carrega e inicializa o módulo WASM.
import init, {
  init_panic_hook,
  derive_key,
  aes_gcm_encrypt,
  aes_gcm_decrypt,
  hmac_sha256,
} from "_@snap/crypto";

/**
 * Adiciona um event listener para receber mensagens da thread principal.
 * A estrutura da mensagem esperada é um objeto com uma propriedade `operacao` que define a ação a ser executada.
 */
self.addEventListener("message", async (e) => {
  const { id, operacao, ...data } = e.data;

  // Helper: Uint8Array <-> base64
  function toBase64(bytes: Uint8Array) {
    return btoa(String.fromCharCode(...bytes));
  }
  function fromBase64(b64: string) {
    const bin = atob(b64);
    return new Uint8Array([...bin].map((c) => c.charCodeAt(0)));
  }
  try {
    if (operacao === "INICIALIZAR") {
      await init();
      init_panic_hook();
      postMessage({ id, operacao, success: true });
    } else if (operacao === "KEY_DERIVATION") {
      const key = derive_key(data.password, data.salt, 2, 1024, 1);
      postMessage({ id, operacao, success: true, key });
    } else if (operacao === "ENCRYPTION") {
      const iv = new Uint8Array(12);
      crypto.getRandomValues(iv);
      // Always JSON encode plaintext
      let plaintextBytes;
      if (typeof data.plaintext === "string") {
        plaintextBytes = new TextEncoder().encode(data.plaintext);
      } else if (data.plaintext instanceof Uint8Array) {
        plaintextBytes = data.plaintext;
      } else {
        plaintextBytes = new TextEncoder().encode(
          JSON.stringify(data.plaintext)
        );
      }
      const encrypted = aes_gcm_encrypt(data.key, iv, plaintextBytes, null);
      postMessage({
        id,
        operacao,
        success: true,
        encrypted: toBase64(encrypted),
        iv: toBase64(iv),
      });
    } else if (operacao === "DECRYPTION") {
      try {
        const ciphertext =
          typeof data.ciphertext === "string"
            ? fromBase64(data.ciphertext)
            : data.ciphertext;
        const iv = typeof data.iv === "string" ? fromBase64(data.iv) : data.iv;
        const decrypted = aes_gcm_decrypt(data.key, iv, ciphertext, null);
        const plaintext = new TextDecoder().decode(decrypted);
        let result;
        try {
          result = JSON.parse(plaintext);
        } catch {
          result = plaintext;
        }
        postMessage({ id, operacao, success: true, decrypted: result });
      } catch (error) {
        postMessage({ id, operacao, success: false, error: error.toString() });
      }
    } else if (operacao === "HMAC") {
      const hmac = hmac_sha256(data.key, data.message);
      postMessage({ id, operacao, success: true, hmac });
    } else if (operacao === "PERFORMANCE") {
      const start = performance.now();
      const operations = 100;
      const key = new TextEncoder().encode("testkey");
      for (let i = 0; i < operations; i++) {
        hmac_sha256(key, new TextEncoder().encode(`message${i}`));
      }
      const end = performance.now();
      const totalTime = end - start;
      const opsPerSecond = Math.round((operations / totalTime) * 1000);
      postMessage({
        id,
        operacao,
        success: true,
        operations,
        totalTime: Math.round(totalTime),
        opsPerSecond,
      });
    }
  } catch (error) {
    postMessage({
      id,
      operacao,
      success: false,
      error: error.toString(),
    });
  }
});

export {}; // Garante que este arquivo seja tratado como um módulo.
