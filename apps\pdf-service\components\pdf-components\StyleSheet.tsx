import React from 'react';

/**
 * Converts React PDF specific style properties to standard CSS properties
 */
function convertPDFStyleToCSS(style: PDFCSSProperties): React.CSSProperties {
  const {
    paddingHorizontal,
    paddingVertical,
    marginHorizontal,
    marginVertical,
    ...restStyle
  } = style;

  const convertedStyle: React.CSSProperties = { ...restStyle };

  // Convert paddingHorizontal to paddingLeft and paddingRight
  if (paddingHorizontal !== undefined) {
    convertedStyle.paddingLeft = paddingHorizontal;
    convertedStyle.paddingRight = paddingHorizontal;
  }

  // Convert paddingVertical to paddingTop and paddingBottom
  if (paddingVertical !== undefined) {
    convertedStyle.paddingTop = paddingVertical;
    convertedStyle.paddingBottom = paddingVertical;
  }

  // Convert marginHorizontal to marginLeft and marginRight
  if (marginHorizontal !== undefined) {
    convertedStyle.marginLeft = marginHorizontal;
    convertedStyle.marginRight = marginHorizontal;
  }

  // Convert marginVertical to marginTop and marginBottom
  if (marginVertical !== undefined) {
    convertedStyle.marginTop = marginVertical;
    convertedStyle.marginBottom = marginVertical;
  }

  return convertedStyle;
}

// Extended CSS properties to include React PDF specific properties
export interface PDFCSSProperties extends React.CSSProperties {
  paddingHorizontal?: number | string;
  paddingVertical?: number | string;
  marginHorizontal?: number | string;
  marginVertical?: number | string;
  textTransform?: 'none' | 'capitalize' | 'uppercase' | 'lowercase';
}

export interface Styles {
  [key: string]: PDFCSSProperties;
}

/**
 * StyleSheet utility that replicates the create function from @react-pdf/renderer StyleSheet.
 * This allows for creating and managing styles in a way that's compatible with both
 * PDF rendering and HTML rendering.
 */
export const StyleSheet = {
  /**
   * Creates a stylesheet object from the provided styles.
   * Converts React PDF specific properties to standard CSS properties.
   *
   * @param styles - An object containing style definitions
   * @returns The styles object with PDF properties converted to CSS
   */
  create: <T extends Styles>(styles: T): T => {
    const convertedStyles = {} as T;

    for (const [key, style] of Object.entries(styles)) {
      convertedStyles[key as keyof T] = convertPDFStyleToCSS(style) as T[keyof T];
    }

    return convertedStyles;
  },

  /**
   * Combines multiple style objects into a single style object.
   * This is useful when you need to merge styles from different sources.
   * 
   * @param styles - Array of style objects to combine
   * @returns Combined style object
   */
  compose: (...styles: (React.CSSProperties | undefined)[]): React.CSSProperties => {
    const result: React.CSSProperties = {};
    for (const style of styles) {
      if (style) {
        Object.assign(result, style);
      }
    }
    return result;
  },

  /**
   * Flattens an array of styles into a single style object.
   * This handles the case where styles might be passed as an array.
   *
   * @param styles - Style or array of styles
   * @returns Flattened style object
   */
  flatten: (styles: React.CSSProperties | React.CSSProperties[]): React.CSSProperties => {
    if (Array.isArray(styles)) {
      return styles.reduce((acc, style) => ({ ...acc, ...(style || {}) }), {});
    }
    return styles || {};
  },

  /**
   * Resolves a style value, handling both direct values and style references.
   * This is useful for dynamic style resolution.
   * 
   * @param style - Style value or reference
   * @returns Resolved style value
   */
  resolve: (style: any): React.CSSProperties => {
    if (typeof style === 'object' && style !== null) {
      return style;
    }
    return {};
  }
};

export default StyleSheet;
