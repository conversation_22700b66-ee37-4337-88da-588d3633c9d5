
services:
  ngrok:
    image: ngrok/ngrok:latest
    networks:
      - mystack-net
    environment:
      NGROK_AUTHTOKEN: ${NGROK_AUTHTOKEN}
    command: http --domain=${KC_HOSTNAME_URL} keycloak:8080
    depends_on:
      - backend
    deploy:
      resources:
        limits:
          cpus: "0.1"
          memory: "128M"
        reservations:
          cpus: "0.05"
          memory: "32M"
    restart: unless-stopped
