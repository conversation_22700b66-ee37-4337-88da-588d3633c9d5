import React from 'react';

export interface TextProps {
  className?: string;
  id?: string;
  style?: React.CSSProperties | React.CSSProperties[];
  /**
   * Render component in all wrapped pages.
   */
  fixed?: boolean;
  /**
   * Force the wrapping algorithm to start a new page when rendering the
   * element.
   */
  break?: boolean;
  /**
   * Hint that no page wrapping should occur between all sibling elements following the element within n points
   */
  minPresenceAhead?: number;
  /**
   * Enable/disable page wrapping for element.
   */
  wrap?: boolean;
  /**
   * Enables debug mode on page bounding box.
   */
  debug?: boolean;
  render?: (props: {
    pageNumber: number;
    totalPages: number;
    subPageNumber: number;
    subPageTotalPages: number;
  }) => React.ReactNode;
  /**
   * Override the default hyphenation-callback
   */
  hyphenationCallback?: (word: string) => string[];
  /**
   * Specifies the minimum number of lines in a text element that must be shown at the bottom of a page or its container.
   */
  orphans?: number;
  /**
   * Specifies the minimum number of lines in a text element that must be shown at the top of a page or its container.
   */
  widows?: number;
  children?: React.ReactNode;
}

/**
 * A React component for displaying text. Text supports nesting of other
 * Text or Link components to create inline styling.
 */
export const Text: React.FC<TextProps> = ({
  id,
  style,
  fixed,
  break: pageBreak,
  minPresenceAhead,
  wrap,
  debug,
  render,
  hyphenationCallback,
  orphans,
  widows,
  children,
}) => {
  // Convert style array to single style object if needed
  const combinedStyle = Array.isArray(style) 
    ? style.reduce((acc, s) => ({ ...acc, ...s }), {})
    : style;

  // Base text styles that mimic PDF text behavior
  const textStyle: React.CSSProperties = {
    display: 'inline-block',
    fontFamily: 'Helvetica, Arial, sans-serif',
    fontSize: '12px',
    lineHeight: '1.2',
    color: '#000',
    margin: 0,
    padding: 0,
    border: debug ? '1px solid green' : undefined,
    orphans: orphans,
    widows: widows,
    ...combinedStyle,
  };

  // Handle fixed positioning
  if (fixed) {
    textStyle.position = 'absolute';
  }

  // Handle page break
  if (pageBreak) {
    textStyle.pageBreakBefore = 'always';
  }

  // For page numbering, we'll use CSS counters in print media
  // This is a mock context for rendering, but actual page numbers will be handled by CSS
  const pageContext = {
    pageNumber: 1, // Will be replaced by CSS counter in print
    totalPages: 1, // Will be calculated during PDF generation
    subPageNumber: 1,
    subPageTotalPages: 1,
  };

  // Check if this is a page number text (used in footer)
  const isPageNumber = render && 
    (combinedStyle?.color === '#FFFFFF' || combinedStyle?.fontWeight === 'normal') &&
    fixed;

  // Determine the appropriate HTML element based on style
  const isBlock = combinedStyle?.display === 'block' || 
                  combinedStyle?.display === 'flex' ||
                  combinedStyle?.position === 'absolute';

  const Element = isBlock ? 'div' : 'span';

  return (
    <Element 
      id={id}
      className={`pdf-text ${isPageNumber ? 'pdf-page-number' : ''}`}
      style={textStyle}
      data-wrap={wrap}
      data-debug={debug}
      data-fixed={fixed}
      data-min-presence-ahead={minPresenceAhead}
      data-orphans={orphans}
      data-widows={widows}
      data-is-page-number={isPageNumber || undefined}
    >
      {render ? render(pageContext) : children}
    </Element>
  );
};

export default Text;
