import { ValueWithSource } from "./ValueWithSource";

export interface PossivelContato {
    nome_completo?: ValueWithSource<string>;
    razao_social?: ValueWithSource<string>;
    detalhes?: Record<string, ValueWithSource>;
    pessoa?: Array<ValueWithSource>;
    telefones?: Array<ValueWithSource>;
    enderecos?: Array<ValueWithSource>;
}

export type UpdaterFunction = (entry: PossivelContato, index?: number) => void;
export type TestFunction = (entry: PossivelContato) => boolean;
export type SectionTestFunction = (section: PossiveisContatosSection) => boolean;
export type CalculateFunction = (section: PossiveisContatosSection) => number;
export type ArrayKey = 'pessoa' | 'telefones' | 'enderecos';

export interface PossiveisContatosSection {
    data: PossivelContato[];
    data_count: number;
}

export interface TestFunctions {
    nomeCompleto: TestFunction;
    razaoSocial: TestFunction;
    detalhes: TestFunction;
    pessoa: TestFunction;
    telefones: TestFunction;
    enderecos: TestFunction;
}
