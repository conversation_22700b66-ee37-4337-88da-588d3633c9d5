import UserMenu from "~/components/UserMenu";
import { useAuth } from "~/hooks/useAuth";
import { useNavigate } from "react-router";
import { MenuItem } from "@snap/design-system";
import UserMenuContent from "./UserMenuContent";
import { useDialogActions } from "~/store/dialogStore";
import { MailCheck, MailWarning } from "lucide-react";
import { InviteListDialog } from "~/containers/account/configuration/InviteListDialog";
import { Badge } from "~/components/ui/badge";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "~/components/ui/tooltip";
import { useUserCRUD } from "~/hooks/useUserCRUD";
import { USER_CONSTANTS } from "~/helpers/constants";
import { usePermissionCheck } from "./router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";

export default function UserNavbar() {
  const { openDialog } = useDialogActions();
  const navigate = useNavigate();
  const {
    logoutMutation: { mutateAsync: logout },
  } = useAuth();
  const { userInviteQuery } = useUserCRUD();
  const enviadoStatus = USER_CONSTANTS.status_invite.enviado;
  const { data: invitesData } = userInviteQuery(enviadoStatus);
  const inviteCount = invitesData?.length || 0;
  const { checkPermission } = usePermissionCheck();
  const canViewInvites = checkPermission(Permission.GET_USER_INVITE);

  const handleLogout = () => {
    logout();
  };

  const handleNavigateConfig = () => {
    const path = `/conta/configuracoes`;
    navigate(path);
  };

  const handleNavigateSupport = () => {
    const path = `/conta/suporte`;
    navigate(path);
  };

  const handleOpenInvitesDialog = () => {
    openDialog({
      title: "Convites",
      icon: <MailCheck />,
      content: <InviteListDialog.Content/>,
      className: "max-w-4xl",
    });
  };

  const notificationAction = {
    label: "NOTIFICAÇÕES",
    icon: <div className="h-3 w-3 bg-border" />,
    onClick: handleOpenInvitesDialog,
  };
  const configurationAction = {
    label: "CONFIGURAÇÕES",
    icon: <div className="h-3 w-3 bg-border" />,
    onClick: handleNavigateConfig,
  };
  const logoutAction = {
    label: "SAIR",
    icon: <div className="h-3 w-3 bg-border" />,
    onClick: handleLogout,
  };
  const supportAction = {
    label: "SUPORTE",
    icon: <div className="h-3 w-3 bg-border" />,
    onClick: handleNavigateSupport,
  };

  const menuActions: MenuItem[] = [
    configurationAction,
    ...(canViewInvites ? [notificationAction] : []),
    supportAction,
    logoutAction,
  ]

  const InviteBadge = () => {
    if (inviteCount === 0) return null;

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant="default"
              className="absolute -top-2 -right-2 bg-primary text-primary-foreground cursor-pointer hover:bg-primary/80 min-w-[20px] h-5 rounded-full flex items-center justify-center gap-1 text-xs z-10 px-2"
              onClick={handleOpenInvitesDialog}
            >
              <MailWarning size={12} />
              {inviteCount}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p>{inviteCount === 1 ? "Convite" : "Convites"}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  return (
    <div className="relative">
      <UserMenu userMenuContent={<UserMenuContent menuActions={menuActions} />} />
      {canViewInvites && <InviteBadge />}
    </div>
  );
}
