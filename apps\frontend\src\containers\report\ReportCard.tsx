import { getInitials, getTypeIcon, isReportSubjectCompany, maskDocumentNumber, translatePropToLabel } from "~/helpers";
import { <PERSON><PERSON><PERSON>, LuRedo } from "react-icons/lu";
import {
  SubjectCard,
  Separator,
  Avatar,
  Button,
  StandardList,
  ListItem,
  Text,
} from "@snap/design-system";
import { useNavigate } from "react-router";
import { MdOutlineDriveFileMove } from "react-icons/md";
import { ArrowDown, ArrowRight, Download, Pencil, Trash, X } from "lucide-react";
import { BiMerge } from "react-icons/bi";
import { isErrorReport, isPendingReport } from "~/helpers/reportStatus.helper";
import { ReportModel } from "root/domain/entities/report.model";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { FaUsers } from "react-icons/fa6";
import { TbBuildings } from "react-icons/tb";
import { AiOutlineLoading3Quarters } from "react-icons/ai";

export default function ReportCard({
  report,
  onRetry,
  onMoveReport,
  onRenameReport,
  onDeleteReport,
  onDownloadPDF,
  onMergeReports,
  isDownloadingPDF = false,
  isCreatingReport = false,
  isDeletingReport = false,
  isRenamingReport = false,
  isMovingReport = false,
  hasPermissionToCreateCombinedReport = false
}: {
  report: ReportModel;
  onRetry?: (report: ReportModel) => void;
  onMoveReport?: (report: ReportModel) => void;
  onRenameReport?: (report: ReportModel) => void;
  onDeleteReport?: (report: ReportModel) => void;
  onDownloadPDF?: (reportId: string) => void;
  onMergeReports?: (report: ReportModel) => void;
  isDownloadingPDF?: boolean;
  isCreatingReport?: boolean;
  isDeletingReport?: boolean;
  isRenamingReport?: boolean;
  isMovingReport?: boolean;
  hasPermissionToCreateCombinedReport: boolean;
}) {
  const navigate = useNavigate();
  const listData = [...report.searchItems];
  const initials = getInitials(report.subjectName);
  const subjectNameRelatedInitials = getInitials(report.subjectNameRelated)
  const defaultIcon = <div className="size-3 bg-border" />;
  const redirectTo = `/report/${report.reportType}/${report.id}`;
  const isMultipleSubjects = report.subjectName === REPORT_CONSTANTS.multiplos_registros_encontrados;
  const isCombinado = report.reportType === REPORT_CONSTANTS.types.combinado;
  const isRelacoes = report.reportType === REPORT_CONSTANTS.types.relacoes;
  const entryText = report.combinedReportsCount && report.combinedReportsCount > 2 ? "entradas" : "entrada"
  const multipleIcon = report.subjectPersonCount ? <FaUsers className="size-5" /> : <TbBuildings className="size-5" />;
  const isCompany = isReportSubjectCompany(
    report.reportType,
    report.subjectAge,
    report.subjectSex,
    report.subjectMotherName
  );
  const cardCommunClassNames = {
    headerClassName: "p-0 !bg-background",
    menuWrapperClassName: "top-0 right-0 bg-neutral-500",
    cardClassName: "max-w-[282px] !bg-background",
    footerClassName: "[&_p]:text-sm !bg-background",
    contentClassName: "!bg-background",
    menuTriggerType: "click",
    borderBoxClassName: "border-1 rounded-[2px]",
    markShadowClassName: "!bg-transparent",
  }

  const moveFolderMenuItem = {
    label: "mover para pasta",
    icon: isMovingReport ? (
      <AiOutlineLoading3Quarters size={16} className="animate-spin" />
    ) : (
      <MdOutlineDriveFileMove className="size-4" />
    ),
    onClick: () => onMoveReport?.(report),
    disabled: isMovingReport,
  };

  const mergeReportMenuItem = {
    label: "combinar relatório",
    icon: <BiMerge className="size-4" />,
    onClick: () => onMergeReports?.(report),
  };

  const renameFolderMenuItem = {
    label: "renomear relatório",
    icon: isRenamingReport ? (
      <AiOutlineLoading3Quarters size={16} className="animate-spin" />
    ) : (
      <Pencil className="size-4" />
    ),
    onClick: () => onRenameReport?.(report),
    disabled: isRenamingReport,
  };

  const downloadPDFMenuItem = {
    label: isDownloadingPDF ? "Gerando PDF..." : "Exportar PDF",
    icon: isDownloadingPDF ? (
      <AiOutlineLoading3Quarters size={16} className="animate-spin" />
    ) : (
      <Download className="size-4" />
    ),
    onClick: () => onDownloadPDF?.(report.id),
    disabled: !onDownloadPDF || isDownloadingPDF,
  };

  const deleteFolderMenuItem = {
    label: "Excluir relatório",
    icon: isDeletingReport ? (
      <AiOutlineLoading3Quarters size={16} className="animate-spin" />
    ) : (
      <Trash className="size-4" />
    ),
    onClick: () => onDeleteReport?.(report),
    disabled: isDeletingReport,
  };

  if (report.subjectMotherName && !isCombinado) {
    listData.unshift({
      label: "MÃE",
      value: report.subjectMotherName,
      icon: defaultIcon,
    });
  }

  if (report.subjectAge != null && report.subjectAge !== "" && !isCombinado) {
    const ageLabel = isCompany ? "DATA DE FUNDAÇÃO" : "IDADE";
    listData.push({
      label: ageLabel,
      value: report.subjectAge,
      icon: defaultIcon,
    });
  }

  if (report.subjectSex && !isCombinado) {
    listData.push({
      label: "SEXO",
      value: report.subjectSex,
      icon: defaultIcon,
    });
  }

  if (report.subjectPersonCount) {
    listData.push({
      label: "PESSOAS ENCONTRADAS",
      value: report.subjectPersonCount,
      icon: defaultIcon,
    });
  }

  if (report.subjectCompanyCount) {
    listData.push({
      label: "EMPRESAS ENCONTRADAS",
      value: report.subjectCompanyCount,
      icon: defaultIcon,
    });
  }

  if (report.subjectNameRelated) {
    listData.unshift({
      label: "RELACIONADO",
      value: report.subjectNameRelated,
      icon: defaultIcon,
    });
  }

  const getAvatarDisplayName = (): string => {
    if (isCombinado) {
      const firstEntry = Object.entries(report.searchArgs)[0]; // TODO - entender se a lógica pode ficar nessa de pegar o primeiro report
      if (!firstEntry) return '';
      const [key, value] = firstEntry;
      const maskedValue = maskDocumentNumber(value as string, key)
      return `${key || ''}: ${maskedValue || ''}`;
    }
    return report.subjectName;
  };

  const getAvatarSecondaryText = (): string => {
    if (isCombinado && report.combinedReportsCount) {
      return `+ ${report.combinedReportsCount - 1} ${entryText}`;
    }
    return '';
  };

  const getAvatarFallback = (): string => {
    if (isCombinado) {
      return String(report.combinedReportsCount || 0);
    }
    return initials;
  };

  const renderMultipleSubjectsAvatar = () => (
    <div className="flex items-center justify-between gap-2">
      <div className="border-border border-4 rounded-full p-3">
        {multipleIcon}
      </div>
      <Text variant="body-lg" className="uppercase text-left">
        {report.subjectName}
      </Text>
    </div>
  );

  const renderRelationsAvatar = () => {
    const relatedName = report.subjectNameRelated || "Não informado";
    const relatedFallback = subjectNameRelatedInitials || "N/A";

    return (
      <div className="flex flex-col justify-between gap-2">
        <Avatar
          name={report.subjectName}
          fallback={initials}
          size="md"
          textAlign="right"
          textClassName="text-md uppercase"
        />
        <div className="flex items-center gap-2 justify-between">
          <Separator className="flex-1/3" />
          <ArrowDown size={16} />
          <Separator className="flex-1/3" />
        </div>
        <Avatar
          name={relatedName}
          fallback={relatedFallback}
          size="md"
          textAlign="right"
          textClassName="text-md uppercase"
        />
      </div>
    );
  };

  const renderStandardAvatar = () => (
    <Avatar
      name={getAvatarDisplayName()}
      subtext={getAvatarSecondaryText()}
      fallback={getAvatarFallback()}
      size="md"
      textAlign="right"
      textClassName="text-lg uppercase"
      className="!bg-transparent"
    />
  );

  const renderHeaderAvatar = (): React.ReactElement => {
    if (isMultipleSubjects) return renderMultipleSubjectsAvatar();
    if (isRelacoes) return renderRelationsAvatar();

    return renderStandardAvatar();
  };

  const pendingHeader = (
    <div className="flex items-center justify-between bg-neutral-500 gap-3 p-3 w-full">
      {getTypeIcon(report.reportType, 32)}
      <div>
        <Text className="uppercase">
          {`Relatório ${translatePropToLabel(report.reportType)}`}
        </Text>
      </div>
    </div>
  );

  const header = (
    <>
      <div
        className="flex items-center justify-between bg-neutral-500 rouded-[2pz] gap-3 p-3 w-full cursor-pointer hover:bg-neutral-400 transition-colors duration-200 group"
        onClick={() => navigate(redirectTo)}
      >
        {getTypeIcon(report.reportType, 32)}
        <span className="uppercase line-clamp-2 text-ellipsis max-w-4/5 group-hover:underline text-right flex-1">
          {report.name}
        </span>
      </div>
      <div className="p-3">
        {renderHeaderAvatar()}
      </div>
    </>
  );

  const content = (
    <StandardList withSeparator>
      {listData.map(({ label, value, icon, highlight }) => (
        <ListItem
          key={`${label}-${value}`}
          icon={icon}
          label={label}
          value={value}
          highlight={highlight}
          className="text-foreground"
          labelClassName="text-sm"
          valueClassName="text-sm"
        />
      ))}
    </StandardList>
  );
  const actions = (
    <Button
      variant="outline"
      size="sm"
      iconPosition="right"
      icon={<ArrowRight className="size-4" />}
      className="w-full"
      onClick={() => navigate(redirectTo)}
    >
      Acessar
    </Button>
  );
  const defaultMenu = [
    moveFolderMenuItem,
    ...(hasPermissionToCreateCombinedReport ? [mergeReportMenuItem] : []),
    renameFolderMenuItem,
    downloadPDFMenuItem,
    deleteFolderMenuItem,
  ];

  if (isPendingReport(report.raw)) {
    return (
      <SubjectCard
        header={
          pendingHeader
        }
        content={
          <div className="relative flex flex-col gap-2">
            <div className="relative flex flex-col justify-center items-center p-4">
              <LuClock className="w-12 h-12 text-brand-primary animate-spin" />
              <Text variant="body-md" align="center" className="mt-2">Gerando seu relatório</Text>
            </div>
            {content}
          </div>
        }
        footer={{
          createdAt: report.createdAt,
          updatedAt: report.modifiedAt,
        }}
        menu={[deleteFolderMenuItem]}
        {...cardCommunClassNames as Object}
      />
    );
  }

  if (isErrorReport(report.raw)) {
    return (
      <SubjectCard
        header={
          pendingHeader
        }
        content={
          <div className="relative flex flex-col gap-2">
            <div className="relative flex flex-col justify-center items-center p-4">
              <X className="w-12 h-12 text-primary" />
              <Text variant="body-md" align="center" className="mt-2">Ocorreu um erro ao gerar o relatório</Text>
            </div>
            {content}
          </div>
        }
        footer={{
          createdAt: report.createdAt,
          updatedAt: report.modifiedAt,
        }}
        actions={
          <Button
            variant="outline"
            onClick={() => onRetry?.(report)}
            disabled={isCreatingReport}
          >
            Refazer {isCreatingReport ? <AiOutlineLoading3Quarters size={16} /> : <LuRedo />}
          </Button>
        }
        menu={[deleteFolderMenuItem]}
        {...cardCommunClassNames as Object}
      />
    );
  }

  return (
    <SubjectCard
      header={header}
      content={content}
      actions={actions}
      footer={{
        createdAt: report.createdAt,
        updatedAt: report.modifiedAt,
      }}
      menu={defaultMenu}
      {...cardCommunClassNames as Object}
    />
  );
}