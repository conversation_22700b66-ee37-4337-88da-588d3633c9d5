import logging
import os
from datetime import datetime
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from starlette.requests import Request

# Import settings and Keycloak client functions
from core.config import settings
from keycloak_client import initialize_keycloak_admin_client, shutdown_keycloak_admin_client
from middleware.error_middleware import ErrorHandlingMiddleware
from exceptions.business_exceptions import RequestBodyTooLargeError
import pytz

LOG_LEVEL = settings.LOG_LEVEL
os.makedirs("logs", exist_ok=True)

today = datetime.now(pytz.timezone('UTC')).strftime("%Y-%m-%d")
log_file_path = f"logs/{today}.log"


# Create formatter with custom converter
formatter = logging.Formatter(
    fmt="%(asctime)s [%(levelname)s] [%(name)s]: %(message)s",
    datefmt="%d/%m/%Y %H:%M:%S"
)

# Create handlers
stream_handler = logging.StreamHandler()
stream_handler.setFormatter(formatter)

file_handler = logging.FileHandler(log_file_path, mode='a')
file_handler.setFormatter(formatter)

# Get root logger and remove any default handlers
logger = logging.getLogger()
logger.setLevel(LOG_LEVEL)
logger.handlers = []  # Remove any default handlers

# Add your handlers
logger.addHandler(stream_handler)
logger.addHandler(file_handler)

# Now use logger as usual
app_logger = logging.getLogger(__name__)
app_logger.info("App started.")

from fastapi import FastAPI, WebSocket
from starlette.middleware.cors import CORSMiddleware
from api.routes import router
from typing import Dict, List
# import asyncio
from database.db import init_db
from database.auto_init import auto_initialize_database, should_auto_initialize
from core.startup_validation import validate_startup_or_fail
from contextlib import asynccontextmanager
import asyncio
from kafka_consumer import start_minio_event_consumer,  _pdf_response_consumer
from core.startup import initialize_redis, shutdown_redis



@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup validation and initialization
    logger.info("[lifespan] Starting application initialization...")

    try:
        # Run comprehensive startup validation
        logger.info("[lifespan] Running startup validation checks...")
        await validate_startup_or_fail()
        logger.info("[lifespan] ✅ All startup validation checks passed")

        # Initialize Database with automatic detection
        logger.info("[lifespan] Starting database initialization...")

        if should_auto_initialize():
            logger.info("[lifespan] Auto-initialization enabled, detecting database state...")
            init_result = await auto_initialize_database()

            if init_result['errors']:
                logger.error("[lifespan] Database initialization failed!")
                for error in init_result['errors']:
                    logger.error(f"[lifespan] Error: {error}")
                raise RuntimeError("Database initialization failed")
            else:
                logger.info("[lifespan] ✅ Database initialization completed successfully")
                if init_result['is_first_run']:
                    logger.info("[lifespan] 🚀 First run detected - database schema created")
                else:
                    logger.info("[lifespan] 🔄 Database schema updated/validated")
        else:
            logger.info("[lifespan] Auto-initialization disabled, using fallback method...")
            await init_db()
            logger.info("[lifespan] Database initialized using fallback method")

    except Exception as e:
        logger.error(f"[lifespan] Critical startup failure: {e}")
        logger.error("[lifespan] Application cannot start safely - shutting down")
        raise

    # Initialize Keycloak Admin Client
    logger.info("[lifespan] Initializing Keycloak Admin client...")
    await initialize_keycloak_admin_client()
    logger.info("[lifespan] Keycloak Admin client initialized.")

    # Initialize Redis
    logger.info("[lifespan] Initializing Redis connection...")
    await initialize_redis()
    logger.info("[lifespan] Redis connection initialized.")

    # Start Kafka consumers
    logger.info("[lifespan] Starting Kafka consumers...")
    
    # Start MinIO event consumer
    minio_consumer_task = asyncio.create_task(start_minio_event_consumer(app))
    logger.info("[lifespan] MinIO event consumer started.")
    
    # Start PDF event consumer
    # pdf_consumer_task = asyncio.create_task(start_pdf_event_consumer(app))
    # logger.info("[lifespan] PDF event consumer started.")
    
    yield # The application will run here

    # On shutdown
    logger.info("[lifespan] Shutting down application...")
    
    # Cancel Kafka consumer tasks
    logger.info("[lifespan] Cancelling Kafka consumer tasks...")
    minio_consumer_task.cancel()
    
    # Wait for tasks to complete cancellation
    try:
        await asyncio.wait_for(asyncio.gather(minio_consumer_task, return_exceptions=True), timeout=5.0)
        logger.info("[lifespan] Kafka consumer tasks cancelled successfully.")
    except asyncio.TimeoutError:
        logger.warning("[lifespan] Timeout waiting for Kafka consumer tasks to cancel.")
    
    # Shutdown PDF response consumer if it exists
    if _pdf_response_consumer is not None:
        try:
            logger.info("[lifespan] Stopping PDF response consumer...")
            await _pdf_response_consumer.stop()
            logger.info("[lifespan] PDF response consumer stopped.")
        except Exception as e:
            logger.warning("[lifespan] Error stopping PDF response consumer: %s", e)
    
    # Shutdown Keycloak Admin Client
    await shutdown_keycloak_admin_client()
    logger.info("[lifespan] Keycloak Admin client shut down.")

    # Shutdown Redis
    await shutdown_redis()
    logger.info("[lifespan] Redis connection shut down.")


app = FastAPI(lifespan=lifespan)
app.state.running_snap_tasks = {}

origins = [
    "http://***************:3000",
    "http://***************:8000",
    "http://***************:8080",
    "http://***************:5432",
    "https://sturgeon-big-tapir.ngrok-free.app",
    "https://modest-shrew-urgently.ngrok-free.app",
    "https://supreme-macaw-picked.ngrok-free.app",
    "https://mature-worm-jointly.ngrok-free.app",
    "https://real-bee-suddenly.ngrok-free.app",
    "https://genuinely-larine-claire.ngrok-free.app",
    "http://***************",
    "http://***************",
    "https://***************",
    "https://***************",
    "http://localhost",
    "http://localhost:3000",
    "https://localhost",
    "https://localhost:3000",
    "https://localhost:8000",
    ""
]




class LimitRequestSizeMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, max_body_size: int):
        super().__init__(app)
        self.max_body_size = max_body_size

    async def dispatch(self, request: Request, call_next):
        body = await request.body()
        if len(body) > self.max_body_size:
            raise RequestBodyTooLargeError()
        return await call_next(request)


class NoCacheMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        response: Response = await call_next(request)
        response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, max-age=0"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        return response

app.add_middleware(NoCacheMiddleware)
app.add_middleware(LimitRequestSizeMiddleware, max_body_size=100 * 1024 * 1024)  # 100MB


class TokenCookieSyncMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        response: Response = await call_next(request)
        tokens = getattr(request.state, "refreshed_tokens", None)
        if tokens:
            domain = None
            # Force cross-site compatible cookies during development
            if "localhost" in settings.FRONTEND_REDIRECT_URL:
                is_secure = True
                same_site = "none"
            elif "189" in settings.FRONTEND_REDIRECT_URL or "reportsbeta" in settings.FRONTEND_REDIRECT_URL:
                is_secure = True
                same_site = "lax"
                domain=".reportsbeta.snapforensics.com"
            else:
                is_secure = False
                same_site = "lax"

            try:
                # Log presence and sizes without leaking contents
                access_len = len(tokens.get("access_token") or "")
                refresh_len = len(tokens.get("refresh_token") or "")
                app_logger.info(
                    "[TokenCookieSyncMiddleware] Syncing cookies from request.state. access.len=%s refresh.len=%s secure=%s samesite=%s",
                    access_len,
                    refresh_len,
                    is_secure,
                    same_site,
                    domain
                )
            except Exception as e:
                app_logger.warning("[TokenCookieSyncMiddleware] Could not compute token sizes: %s", e)

            try:
                # Note: Setting cookie only when access_len == 0 might be intentional
                # if this middleware only handles cases where tokens are empty/invalid
                if access_len == 0:
                    response.set_cookie(
                        "access_token",
                        tokens.get("access_token"),
                        httponly=True,
                        secure=is_secure,
                        samesite=same_site,
                        path="/",
                        domain=domain
                    )
                    app_logger.info("[TokenCookieSyncMiddleware] access_token cookie set on response.")
            except Exception as e:
                app_logger.warning("[TokenCookieSyncMiddleware] Failed to set access_token cookie: %s", e)
            
            try:
                # Note: Setting cookie only when refresh_len == 0 might be intentional
                # if this middleware only handles cases where tokens are empty/invalid
                if refresh_len == 0:
                    response.set_cookie(
                        "refresh_token",
                        tokens.get("refresh_token"),
                        httponly=True,
                        secure=is_secure,
                        samesite=same_site,
                        path="/",   
                        domain=domain
                    )
                    app_logger.info("[TokenCookieSyncMiddleware] refresh_token cookie set on response.")
            except Exception as e:
                app_logger.warning("[TokenCookieSyncMiddleware] Failed to set refresh_token cookie: %s", e)
            try:
                set_cookie_headers_count = sum(1 for (k, _) in ((k.decode().lower(), v.decode()) for k, v in getattr(response, "raw_headers", [])) if k == "set-cookie")
                app_logger.info("[TokenCookieSyncMiddleware] Set-Cookie headers count after sync: %s", set_cookie_headers_count)
            except Exception as e:
                app_logger.warning("[TokenCookieSyncMiddleware] Could not count response Set-Cookie headers: %s", e)
        return response

app.add_middleware(TokenCookieSyncMiddleware)
app.add_middleware(ErrorHandlingMiddleware)
logger.info("[setup] Registering routers...")

logger.info("[setup] Configuring CORS middleware...")
logger.info("[setup] Allowed origins for CORS: %s", origins)
logger.info("[setup] CORS allow_credentials: True")
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

app.include_router(router)

@app.get("/")
async def root():
    logger.info("[root] Health check endpoint called")
    return {"message": "FastAPI backend running"}

# You can keep your ConnectionManager as is, it's not directly related to Keycloak Admin client init
class ConnectionManager:
    def __init__(self):
        logger.info("[ConnectionManager] Initializing Connection Manager...")
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        logger.info(f"[ConnectionManager.connect] Connecting WebSocket for user {user_id}")
        await websocket.accept()
        self.active_connections[user_id] = websocket
        logger.info("[ConnectionManager.connect] Total connections: %s", len(self.active_connections))

    async def disconnect(self, user_id: str):
        logger.info(f"[ConnectionManager.disconnect] Disconnecting WebSocket for user {user_id}")
        websocket = self.active_connections.pop(user_id, None)
        if websocket: # Ensure websocket exists before closing
            await websocket.close(code=1001, reason="Server is closing the connection")
        logger.info("[ConnectionManager.disconnect] Total connections after disconnect: %s", len(self.active_connections))
        self.log_connections()

    def get_connection(self, user_id: str):
        websocket = self.active_connections.get(user_id)
        if websocket:
            logger.info("[ConnectionManager.get_connection] WebSocket found for user %s", user_id)
        else:
            logger.warning("[ConnectionManager.get_connection] No WebSocket found for user %s", user_id)
        return websocket

    async def send_to_user(self, user_id: str, message: str):
        websocket = self.get_connection(user_id)
        if websocket:
            try:
                await websocket.send_text(message)
                logger.info(f"[ConnectionManager.send_to_user] Message sent to user {user_id}")
            except Exception as e:
                logger.warning(f"[ConnectionManager.send_to_user] Failed to send message to user {user_id}: {e}")
                await self.disconnect(user_id)
    
    def list_connections(self) -> List[str]:
        return list(self.active_connections.keys())
    
    def log_connections(self):
        if self.active_connections:
            logger.info("[ConnectionManager] Active connections: %s", self.list_connections())
        else:
            logger.info("[ConnectionManager] No active connections.")

app.state.connection_manager = ConnectionManager()
logger.info("[setup] ConnectionManager instance created and assigned.")

