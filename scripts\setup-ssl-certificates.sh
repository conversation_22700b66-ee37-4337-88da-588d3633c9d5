#!/bin/bash
# SSL Certificate Setup Script for VM *************
# Handles both Let's Encrypt and self-signed certificate generation

set -euo pipefail

DOMAIN_MAIN="reportsbeta.snapforensics.com"
# No admin domain needed - admin console access via VM/VPN only
CERT_DIR="./config/nginx/certs"
VM_IP="*************"

print_title() {
    echo ""
    echo "===================="
    echo "$1"
    echo "===================="
}

print_success() {
    echo "✅ $1"
}

print_info() {
    echo "ℹ️ $1"
}

print_warning() {
    echo "⚠️ $1"
}

print_error() {
    echo "❌ $1"
}

check_dns() {
    print_title "Checking DNS Configuration"

    print_info "Checking DNS for $DOMAIN_MAIN..."

    if command -v dig &> /dev/null; then
        ip=$(dig +short "$DOMAIN_MAIN" | tail -n1)
    elif command -v nslookup &> /dev/null; then
        ip=$(nslookup "$DOMAIN_MAIN" | grep "Address:" | tail -n1 | awk '{print $2}')
    else
        print_warning "dig or nslookup not available, skipping DNS check"
        return 0
    fi

    if [ "$ip" = "$VM_IP" ]; then
        print_success "$DOMAIN_MAIN points to $VM_IP ✓"
    else
        print_warning "$DOMAIN_MAIN points to $ip (expected $VM_IP)"
        print_info "You may need to update DNS records before obtaining certificates"
    fi
}

check_ports() {
    print_title "Checking Port Availability"

    for port in 80 443; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            print_warning "Port $port is already in use"
            print_info "You may need to stop other services before obtaining certificates"
        else
            print_success "Port $port is available ✓"
        fi
    done
}

setup_letsencrypt() {
    print_title "Setting Up Let's Encrypt Certificates"

    if ! command -v certbot &> /dev/null; then
        print_info "Installing certbot..."
        sudo apt update
        sudo apt install -y certbot
    fi

    print_info "Obtaining certificate for $DOMAIN_MAIN..."
    sudo certbot certonly --standalone -d "$DOMAIN_MAIN" --non-interactive --agree-tos --email "admin@$DOMAIN_MAIN" || {
        print_error "Failed to obtain certificate for $DOMAIN_MAIN"
        return 1
    }

    print_info "Copying certificates to $CERT_DIR..."
    sudo cp "/etc/letsencrypt/live/$DOMAIN_MAIN/fullchain.pem" "$CERT_DIR/$DOMAIN_MAIN.crt"
    sudo cp "/etc/letsencrypt/live/$DOMAIN_MAIN/privkey.pem" "$CERT_DIR/$DOMAIN_MAIN.key"

    # Set proper permissions
    sudo chown $USER:$USER "$CERT_DIR"/*.crt "$CERT_DIR"/*.key
    chmod 644 "$CERT_DIR"/*.crt
    chmod 600 "$CERT_DIR"/*.key

    print_success "Let's Encrypt certificates installed successfully!"
}

setup_selfsigned() {
    print_title "Setting Up Self-Signed Certificates (Development Only)"
    print_warning "Self-signed certificates are not trusted by browsers and should only be used for development"

    # Generate certificate for main domain
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout "$CERT_DIR/$DOMAIN_MAIN.key" \
        -out "$CERT_DIR/$DOMAIN_MAIN.crt" \
        -subj "/CN=$DOMAIN_MAIN/O=Snap Reports Dev/C=US" \
        -addext "subjectAltName=DNS:$DOMAIN_MAIN"

    # Set proper permissions
    chmod 644 "$CERT_DIR"/*.crt
    chmod 600 "$CERT_DIR"/*.key

    print_success "Self-signed certificates generated!"
    print_info "To trust these certificates in your browser, add them to your trusted certificate store"
}

setup_renewal() {
    print_title "Setting Up Certificate Renewal"

    # Create renewal script
    cat > ./scripts/renew-certificates.sh << 'EOF'
#!/bin/bash
# Certificate renewal script

echo "Renewing certificates..."
sudo certbot renew --quiet

if [ $? -eq 0 ]; then
    echo "Certificates renewed successfully"

    # Copy renewed certificates
    sudo cp /etc/letsencrypt/live/reportsbeta.snapforensics.com/fullchain.pem ./config/nginx/certs/reportsbeta.snapforensics.com.crt
    sudo cp /etc/letsencrypt/live/reportsbeta.snapforensics.com/privkey.pem ./config/nginx/certs/reportsbeta.snapforensics.com.key
    sudo cp /etc/letsencrypt/live/admin.reportsbeta.snapforensics.com/fullchain.pem ./config/nginx/certs/admin.reportsbeta.snapforensics.com.crt
    sudo cp /etc/letsencrypt/live/admin.reportsbeta.snapforensics.com/privkey.pem ./config/nginx/certs/admin.reportsbeta.snapforensics.com.key

    # Fix permissions
    sudo chown $USER:$USER ./config/nginx/certs/*.crt ./config/nginx/certs/*.key
    chmod 644 ./config/nginx/certs/*.crt
    chmod 600 ./config/nginx/certs/*.key

    # Reload nginx in Docker
    docker service update --force mystack_nginx || echo "Failed to update nginx service"

    echo "Certificate renewal completed"
else
    echo "Certificate renewal failed"
    exit 1
fi
EOF

    chmod +x ./scripts/renew-certificates.sh

    print_info "Setting up cron job for automatic renewal..."
    (crontab -l 2>/dev/null; echo "0 2 * * 1 $(pwd)/scripts/renew-certificates.sh >> $(pwd)/logs/cert-renewal.log 2>&1") | crontab -

    print_success "Certificate renewal configured!"
    print_info "Certificates will be automatically renewed every Monday at 2:00 AM"
}

main() {
    print_title "SSL Certificate Setup for Snap Reports"
    print_info "VM IP: $VM_IP"
    print_info "Domain: $DOMAIN_MAIN (Admin console: VM/VPN access only)"

    # Create certificate directory if it doesn't exist
    mkdir -p "$CERT_DIR"

    # Check prerequisites
    check_dns
    check_ports

    echo ""
    echo "Choose certificate type:"
    echo "1) Let's Encrypt (Production - requires valid DNS)"
    echo "2) Self-signed (Development only)"
    echo "3) Skip certificate setup"

    read -p "Enter your choice (1-3): " choice

    case $choice in
        1)
            setup_letsencrypt
            setup_renewal
            ;;
        2)
            setup_selfsigned
            ;;
        3)
            print_info "Skipping certificate setup"
            print_warning "You'll need to manually add certificates to $CERT_DIR/"
            ;;
        *)
            print_error "Invalid choice"
            exit 1
            ;;
    esac

    print_title "Certificate Setup Complete"
    print_info "Next steps:"
    echo "1. Ensure DNS points to $VM_IP for $DOMAIN_MAIN"
    echo "2. Run your deployment script: ./generate_deploy.sh --profile prod ..."
    echo "3. Test HTTPS access: https://$DOMAIN_MAIN/health"
    echo "4. Test admin access: SSH tunnel to VM (http://localhost:8080/admin/)"
}

main "$@"