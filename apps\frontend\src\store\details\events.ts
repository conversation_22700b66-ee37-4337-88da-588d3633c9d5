import { useReportDetailStore } from "./store";
import { RelacoesSectionListItem, SectionListRecordsItem, UpdateFnPromise } from "./interface";
import { ReportMetadata, ReportSection } from "~/types/global";
import { REPORT_DETAIL_INITIAL_STATE } from "./state";
import { REPORT_SECTIONS } from "~/helpers/constants";
import { getProfileImageFromSection } from "~/helpers";

const getStore = () => useReportDetailStore;

const calculateSectionListRecords = (sections: ReportSection[]): SectionListRecordsItem[] => {
  return sections
    .filter(section => {
      if (section.subsection) return false;
      if (section.is_deleted) return false;
      if (section.data_count > 0) return true;

      // Algumas seções vazias vem com a chave "detalhes" vazia dentro de "data", por isso a verificação abaixo
      return Array.isArray(section.data) &&
        section.data.some((item) =>
          Array.isArray((item as any).detalhes) &&
          (item as any).detalhes.length > 0
        );
    })
    .map((s) => ({ title: s.title, data_count: s.data_count }));
};

const calculateTotalRecords = (sectionListRecords: SectionListRecordsItem[]): number => {
  return sectionListRecords.reduce((sum, section) => {
    const count = Number(section.data_count) || 0;
    return sum + count;
  }, 0);
};

const calculateRelacoesSectionList = (sections: ReportSection[]): RelacoesSectionListItem[] => {
  const relacoesSectionList: RelacoesSectionListItem[] = [];
  const relacoesSection = sections.find(section => section.title === "Relações");
  if (relacoesSection && Array.isArray(relacoesSection.data)) {
    const ordinalLabels = [
      "primeiro vínculo",
      "segundo vínculo",
      "terceiro vínculo",
      "quarto vínculo",
      "quinto vínculo",
      "sexto vínculo",
      "sétimo vínculo",
      "oitavo vínculo",
      "nono vínculo",
      "décimo vínculo"
    ];

    relacoesSection.data.forEach((item, index) => {
      if (index < ordinalLabels.length) {
        relacoesSectionList.push({
          title: ordinalLabels[index],
          data_count: 1,
          index: index
        });
      }
    });
  }
  return relacoesSectionList;
};

export const updateSectionListRecords = () => {
  const store = getStore().getState();
  const sections = store.data.sections;

  const sectionListRecords = calculateSectionListRecords(sections);
  const totalRecords = calculateTotalRecords(sectionListRecords);
  const relacoesSectionList = calculateRelacoesSectionList(sections);

  store.actions.setSectionListRecords(sectionListRecords);
  store.actions.setTotalRecords(totalRecords);
  store.actions.setRelacoesSectionList(relacoesSectionList);
};

export const setReportSections = (sections: ReportSection[]) => {
  const useReportDetailStore = getStore();
  const store = useReportDetailStore.getState();

  // Extrair imagem de perfil da seção imagens
  const imagensSection = sections.find(
    (section) => section.title === REPORT_SECTIONS.imagens
  );
  const profileImage = getProfileImageFromSection(imagensSection);

  // Calcular todos os valores relacionados às seções usando os helpers
  const sectionListRecords = calculateSectionListRecords(sections);
  const totalRecords = calculateTotalRecords(sectionListRecords);
  const relacoesSectionList = calculateRelacoesSectionList(sections);

  store.actions.setReportSections(sections);
  store.actions.setProfileImage(profileImage);
  store.actions.setSectionListRecords(sectionListRecords);
  store.actions.setRelacoesSectionList(relacoesSectionList);
  store.actions.setTotalRecords(totalRecords);
};

export const setDeletedSections = (deletedSections: ReportSection[]) => {
  getStore().getState().actions.setDeletedSections(deletedSections);
};

export const setReportMetadata = (metadata: ReportMetadata) => {
  getStore().getState().actions.setMetadata(metadata);
};

export const setReportType = (reportType: string) => {
  getStore().getState().actions.setReportType(reportType);
};

export const setSelectedCombinedFilter = (filterId: string) => {
  getStore().getState().actions.setSelectedCombinedFilter(filterId);
};

export const setCombinedReportSources = (sources: Array<{
  report_type: string;
  report_input_value: string;
  user_reports_id: string;
  report_name: string;
}>) => {
  getStore().getState().actions.setCombinedReportSources(sources);
};

export const setProfileImage = (profileImage: string) => {
  getStore().getState().actions.setProfileImage(profileImage);
};

export const forceReload = () => {
  getStore().getState().actions.forceReload();
};

export const updateSectionEntries = (
  sectionTitle: string,
  updaterFn: UpdateFnPromise,
  testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
  testSectionDeletedFn: (section: ReportSection) => boolean,
  calculateDataCountFn?: (section: ReportSection) => number,
  includeSubsections?: boolean,
  crossSectionUpdate?: { matchingProp: string; updaterFn: UpdateFnPromise }
) => {
  getStore().getState().actions.updateSectionEntries(
    sectionTitle,
    updaterFn,
    testEntryDeletedFn,
    testSectionDeletedFn,
    calculateDataCountFn,
    includeSubsections,
    crossSectionUpdate
  );
};

export const updateSubsectionWithMainSection = (
  sectionTitle: string,
  subsectionName: string,
  matchingProp: string,
  updaterFn: UpdateFnPromise,
  testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
  testSectionDeletedFn: (section: ReportSection) => boolean,
  calculateDataCountFn?: (section: ReportSection) => number
) => {
  getStore().getState().actions.updateSubsectionWithMainSection(
    sectionTitle,
    subsectionName,
    matchingProp,
    updaterFn,
    testEntryDeletedFn,
    testSectionDeletedFn,
    calculateDataCountFn
  );
};

export const scheduleAutoSave = () => {
  getStore().getState().actions.scheduleAutoSave();
};

export const cancelAutoSave = () => {
  getStore().getState().actions.cancelAutoSave();
};

export const setAutoSaveMutation = (mutation: any) => {
  getStore().getState().actions.setAutoSaveMutation(mutation);
};

export const useActionLoading = () => {
  const useReportDetailStore = getStore();
  return useReportDetailStore((state) => state.ui.isActionLoading);
};

export const resetReportDetailStore = () => {
  getStore().setState({
    ...REPORT_DETAIL_INITIAL_STATE,
  });
};