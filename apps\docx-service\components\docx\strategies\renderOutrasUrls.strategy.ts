import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, WidthType } from "docx";import { ReportSection, ValueWithSource } from "../../../global";import { translateSource } from "../../../helpers";import { createSectionTitle } from "./utils";interface RenderPrintOutrasUrlsProps {  section: Omit<ReportSection, 'data'> & {    data: Array<{      detalhes?: Array<ValueWithSource<Record<string, ValueWithSource>>>;    }>;  };}export const renderOutrasUrls = ({ section }: RenderPrintOutrasUrlsProps): ISectionOptions => {  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];  const allUrls = section.data?.flatMap(entry =>    entry.detalhes?.filter(detalhe => !detalhe.is_deleted) || []  ) || [];  if (!allUrls.length) {    return { children };  }  const rows: TableRow[] = [];  let cells: TableCell[] = [];  allUrls.forEach((detalhe, index) => {    const urlEntry = Object.entries(detalhe.value).find(([_, valueObj]) => !valueObj.is_deleted);    if (!urlEntry) return;    const [_, valueObj] = urlEntry;    const urlValue = valueObj.value;    const sourceValue = valueObj.source;    const cellContent = new Paragraph({        children: [            new TextRun({ text: `URL ${index + 1}`, bold: true, color: "889EA3" }),            new TextRun({ text: ` | ${sourceValue?.map(translateSource).join(', ')}`, size: 16, color: "FE473C" }),            new TextRun({ text: `
${urlValue}`, break: 1 }),        ]    });    cells.push(new TableCell({ children: [cellContent] }));    if (cells.length === 2) {        rows.push(new TableRow({ children: cells }));        cells = [];    }  });  if (cells.length > 0) {    rows.push(new TableRow({ children: [...cells, new TableCell({ children: [new Paragraph("")] })] }));  }  if(rows.length > 0) {      children.push(new Table({rows, width: {size: 100, type: WidthType.PERCENTAGE}, columnWidths: [4500, 4500]}));  }  return { children };};