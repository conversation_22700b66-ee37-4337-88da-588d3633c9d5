from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from copy import copy

from ..CallbackProcessors import CallbackProcessors
from ..EntityExtractor import EntityExtractor, VinculoConfig, ExtractionResult
from ..constants import ReportKeys, rede_social_key_prefix, age_fields, nome_completo_key, primeiro_nome_key, \
    sobrenome_key, sexo_key, parentes_mae_vinculo, parentes_vinculo, razao_social_key, OtherResultDataTypes
from ..FormatFrontFormat import VinculoSection, front_vinculo_pessoais, front_p_pessoas_relacionadas, \
    front_empresas_relacionadas, front_parentes, front_vinculo_relacoes
from datetime import datetime
import logging


from dataclasses import dataclass, field
from typing import List, Optional, Union, Dict, Any

SectionValue = Union[str, Dict[str, str]]


@dataclass
class ReportTypeConfig:
    # private backing fields (not part of the public init)
    _do_filter_doc: Optional[str] = field(default=None, init=False, repr=False)
    _do_filter_name: Optional[str] = field(default=None, init=False, repr=False)

    _do_doc: Optional[SectionValue] = field(default=None, init=False, repr=False)
    _enabled_sections: List["VinculoSection"] = field(default_factory=list, init=False, repr=False)
    _dados_pessoais: Optional[SectionValue] = field(default=None, init=False, repr=False)
    _possiveis_pessoas_relacionadas: Optional[SectionValue] = field(default=None, init=False, repr=False)
    _possiveis_empresas_relacionadas: Optional[SectionValue] = field(default=None, init=False, repr=False)

    def __init__(
        self,
        do_filter_doc: Optional[SectionValue] = None,
        do_filter_name: Optional[str] = None,
        do_doc: Optional[SectionValue] = None,
        enabled_sections: Optional[List["VinculoSection"]] = None,
        dados_pessoais: Optional[SectionValue] = None,
        possiveis_pessoas_relacionadas: Optional[SectionValue] = None,
        possiveis_empresas_relacionadas: Optional[SectionValue] = None,
    ):
        # initial backing values
        self._do_filter_doc = None
        self._do_filter_name = None
        self._do_doc = None
        self._enabled_sections = enabled_sections or []
        self._dados_pessoais = None
        self._possiveis_pessoas_relacionadas = None
        self._possiveis_empresas_relacionadas = None

        # set via properties so setters perform side-effects
        self.do_filter_doc = do_filter_doc
        self.do_filter_name = do_filter_name

        self.do_doc = do_doc
        self.enabled_sections = enabled_sections or []
        self.dados_pessoais_title = dados_pessoais
        self.possiveis_pessoas_relacionadas_title = possiveis_pessoas_relacionadas
        self.possiveis_empresas_relacionadas_title = possiveis_empresas_relacionadas

    # ----------------- helper ----------------- #
    def _apply_section(self, section_type: str, value: SectionValue) -> None:
        """Apply value (str or dict with title/subtitle) to VinculoSection.front_format_section."""
        if value is None:
            return
        section = VinculoSection.front_format_section(section_type)

        if isinstance(value, str):
            section.title = value
        elif isinstance(value, dict):
            if "title" in value and value["title"] is not None:
                section.title = value["title"]
            if "subtitle" in value:
                section.subtitle = value["subtitle"]
        else:
            # fallback: convert to str
            section.title = str(value)

    @property
    def do_doc(self) -> Optional[str]:
        return self._do_doc

    @do_doc.setter
    def do_doc(self, value: Optional[str]) -> None:
        self._do_doc = value

        if value is not None:
            self._apply_section(VinculoSection.DIARIOS_OFICIAIS_DOC, value)

    @property
    def enabled_sections(self) -> List["VinculoSection"]:
        return self._enabled_sections

    @enabled_sections.setter
    def enabled_sections(self, value: List["VinculoSection"]) -> None:
        self._enabled_sections = value or []

    @property
    def dados_pessoais_title(self) -> Optional[SectionValue]:
        return self._dados_pessoais

    @dados_pessoais_title.setter
    def dados_pessoais_title(self, value: Optional[SectionValue]) -> None:
        self._dados_pessoais = value
        if value is not None:
            self._apply_section(VinculoSection.DADOS_PESSOAIS, value)

    @property
    def possiveis_pessoas_relacionadas_title(self) -> Optional[SectionValue]:
        return self._possiveis_pessoas_relacionadas

    @possiveis_pessoas_relacionadas_title.setter
    def possiveis_pessoas_relacionadas_title(self, value: Optional[SectionValue]) -> None:
        self._possiveis_pessoas_relacionadas = value
        if value is not None:
            self._apply_section(VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS, value)

    @property
    def possiveis_empresas_relacionadas_title(self) -> Optional[SectionValue]:
        return self._possiveis_empresas_relacionadas

    @possiveis_empresas_relacionadas_title.setter
    def possiveis_empresas_relacionadas_title(self, value: Optional[SectionValue]) -> None:
        self._possiveis_empresas_relacionadas = value
        if value is not None:
            self._apply_section(VinculoSection.POSSIVEIS_EMPRESAS_RELACIONADAS, value)


#
# from dataclasses import dataclass
# from typing import List, Optional
#
#
# def update_section_titles(section_attr: str, section_type: str, update_subtitle: bool = False):
#     """Decorator to update section title (and optionally subtitle) when a property changes."""
#     def decorator(func):
#         name = func.__name__
#
#         @property
#         def prop(self):
#             return getattr(self, section_attr)
#
#         @prop.setter
#         def prop(self, value):
#             setattr(self, section_attr, value)
#             if value is not None:
#                 section = VinculoSection.front_format_section(section_type)
#                 # Always update title
#                 section.title = value.get("title") if isinstance(value, dict) else value
#                 # Optionally update subtitle if provided
#                 if update_subtitle and isinstance(value, dict) and "subtitle" in value:
#                     section.subtitle = value["subtitle"]
#
#         return prop
#     return decorator
#
#
# @dataclass
# class ReportTypeConfig:
#     _do_filter_doc: str
#     _do_filter_name: str
#     _title_format: str
#     _enabled_sections: List["VinculoSection"]
#     _dados_pessoais: Optional[dict] = None  # can be {"title": ..., "subtitle": ...}
#     _possiveis_pessoas_relacionadas: Optional[dict] = None
#     _possiveis_empresas_relacionadas: Optional[dict] = None
#
#     @update_section_titles("_do_filter_doc", VinculoSection.DIARIOS_OFICIAIS_DOC)
#     def do_filter_doc(self): pass
#
#     @update_section_titles("_do_filter_doc", VinculoSection.DIARIOS_OFICIAIS_NOME)
#     def do_filter_nome(self):
#         pass
#
#     @property
#     def title_format(self):
#         return self._title_format
#
#     @title_format.setter
#     def title_format(self, value):
#         self._title_format = value
#         # Update the front_do_doc title formatting if it's a string
#         if self._do_filter_doc:
#             section = VinculoSection.front_format_section(VinculoSection.DIARIOS_OFICIAIS_DOC)
#             if isinstance(self._do_filter_doc, str):
#                 section.title = section.title.format(value)
#             elif isinstance(self._do_filter_doc, dict) and "title" in self._do_filter_doc:
#                 section.title = self._do_filter_doc["title"].format(value)
#
#     @update_section_titles("_dados_pessoais_title", "DADOS_PESSOAIS", update_subtitle=True)
#     def dados_pessoais_title(self): pass
#
#     @update_section_titles("_possiveis_pessoas_relacionadas_title", "POSSIVEIS_PESSOAS_RELACIONADAS", update_subtitle=True)
#     def possiveis_pessoas_relacionadas_title(self): pass
#
#     @update_section_titles("_possiveis_empresas_relacionadas_title", "POSSIVEIS_EMPRESAS_RELACIONADAS", update_subtitle=True)
#     def possiveis_empresas_relacionadas_title(self): pass
#
#     @property
#     def enabled_sections(self):
#         return self._enabled_sections
#
#     @enabled_sections.setter
#     def enabled_sections(self, value):
#         self._enabled_sections = value



# @dataclass
# class ReportTypeConfig:
#     """Configuration for a specific report type"""
#     do_filter_doc: str
#     do_filter_name: str
#     title_format: str
#     enabled_sections: List[VinculoSection]
#     dados_pessoais_title: str = None
#     possiveis_pessoas_relacionadas_title: str = None
#     possiveis_empresas_relacionadas_title: str = None
#
#     def __post_init__(self):
#         # Set the front_do_doc title format
#         if self.do_filter_doc:
#             front_do_doc = VinculoSection.front_format_section(VinculoSection.DIARIOS_OFICIAIS_DOC)
#             front_do_doc.title = front_do_doc.title.format(self.title_format)
#
#         if self.dados_pessoais_title:
#             front_dados_pessoais = VinculoSection.front_format_section(VinculoSection.DADOS_PESSOAIS)
#             front_dados_pessoais.title = front_dados_pessoais.title = self.dados_pessoais_title
#
#         if self.possiveis_pessoas_relacionadas_title:
#             front_possiveis_pessoas = VinculoSection.front_format_section(VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS)
#             front_possiveis_pessoas.title = front_possiveis_pessoas.title = self.possiveis_pessoas_relacionadas_title

class BaseReportProcessor(ABC):
    """Base class for report processors"""

    def __init__(self, extractor: EntityExtractor):
        self.extractor = extractor


    def make_vinculo_redes_sociais(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.REDES_SOCIAIS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=None,
            extract_type_starts_with=rede_social_key_prefix,
            filter_base_data_callback=CallbackProcessors.filter_for_redes_sociais_vinculos,
            skip_lists=False,
            try_merging_lists=True
        )

    def make_vinculo_educacionais(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.VINCULOS_EDUCACIONAIS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.EMPRESA,
            filter_base_data_callback=CallbackProcessors.filter_for_vinculos_educacionais,
            item_callback=CallbackProcessors.vinculos_educacionais_item_callback,
        )

    def make_vinculo_outras_urls(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.OUTRAS_URLS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.URL,
        )

    def make_vinculo_nomes_usuario(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.NOMES_USUARIO),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.ALIAS,
        )

    def make_vinculo_imagens(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.IMAGENS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.IMAGEM,
        )

    def make_vinculo_juntas_comerciais(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.JUNTAS_COMERCIAIS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.EMPRESA_JUCESP,
        )

    def make_vinculo_phones(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.PHONES),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.TELEFONE,
        )

    def make_vinculo_emails(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.EMAILS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.EMAIL_ENTITY,
        )

    def make_vinculo_enderecos(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.ENDERECOS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.ENDERECO,
        )

    def make_vinculo_mandados(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.MANDADOS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.MANDADO,
            skip_lists=False,
            # item_callback=CallbackProcessors.mandados_item_callback
        )

    def make_vinculo_parentes(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.PARENTES),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.PESSOA,
            skip_lists=False,
            try_merge=True,
            filter_base_data_callback=CallbackProcessors.filter_for_parentes_vinculos,
        )

    def make_vinculo_empregaticios(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.VINCULOS_EMPREGATICIOS),
            extract_func=self.extractor.extract_vinculos_empregaticios,
            extract_type=ReportKeys.REMUNERACAO,
        )

    def make_vinculo_recursos_recebidos(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.RECURSOS_RECEBIDOS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.RECURSOS_RECEBIDOS,
            skip_lists=True,
            item_callback=CallbackProcessors.recursos_recebidos_item_callback,
        )

    def make_vinculo_servico_publico(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.SERVICO_PUBLICO),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.SERVIDOR,
            skip_lists=False,
        )

    def make_vinculo_diarios_oficiais_doc(self, config=None) -> VinculoConfig:
        if config is None:
            config = self.get_config()

        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.DIARIOS_OFICIAIS_DOC),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.DIARIO_OFICIAL,
            filter_source_name=config.do_filter_doc,
            try_merge=False,
        )

    def make_vinculo_diarios_oficiais_nome(self, config=None) -> VinculoConfig:
        if config is None:
            config = self.get_config()

        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.DIARIOS_OFICIAIS_NOME),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.DIARIO_OFICIAL,
            filter_source_name=config.do_filter_name,
            try_merge=False,
        )

    def make_vinculo_filiacao_partidaria(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.FILIACAO_PARTIDARIA),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.PARTIDO_POLITICO,
            try_merge=True,
            skip_lists=False,
        )

    def make_vinculo_outros_contatos(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.OUTROS_CONTATOS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.PESSOA,
            try_merge=True,
            skip_lists=False,
            filter_base_data_callback=CallbackProcessors.filter_for_outros_contatos_vinculo,
        )

    def make_vinculo_pais(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.PAIS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.PAI,
            try_merge=False,
        )

    def make_vinculo_possiveis_pessoas(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.PESSOA,
            skip_lists=False,
            filter_base_data_callback=CallbackProcessors.filter_for_not_parentes_vinculos,
            other_result_keys=[OtherResultDataTypes.other],
        )

    def make_vinculo_possiveis_empresas(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.POSSIVEIS_EMPRESAS_RELACIONADAS),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=ReportKeys.EMPRESA,
            skip_lists=False,
            #filter_base_data_callback=CallbackProcessors.filter_for_not_educacional_or_empregaticio_vinculos,
        )

    def make_vinculo_relacoes(self) -> VinculoConfig:
        return VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.RELACOES),
            extract_func=self.extractor.extract_vinculos_genericos,
            extract_type=[ReportKeys.PESSOA, ReportKeys.EMPRESA],
            skip_lists=False,
            replace_data_processor=CallbackProcessors.relacoes_data_processor_callback,
        )

    @abstractmethod
    def get_config(self) -> ReportTypeConfig:
        """Get the configuration for this report type"""
        pass

    @abstractmethod
    def get_custom_vinculos(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[
        str, VinculoConfig]:
        pass

    def _get_custom_processos(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[Any, VinculoConfig]:
        # Extract complex relationships first
        basic_processos = self.extractor.extract_vinculos_genericos(
            other_result_data, entity_type, search_value,
            VinculoConfig(extract_type=ReportKeys.PROCESSO,
                          skip_lists=False,
                          item_callback=CallbackProcessors.processos_item_callback,
                          extra_data_callback=CallbackProcessors.processos_extra_vinculos_callback)
        )

        if not basic_processos.data:
            return {}

        custom_processos = {
            VinculoSection.PROCESSOS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.PROCESSOS),
                extract_func=lambda *args, **kwargs: ExtractionResult(basic_processos.data['base'], basic_processos.sources),
                extract_type=ReportKeys.PROCESSO
            )
        }

        # Add process-specific vinculos
        if hasattr(basic_processos.data, 'get') and 'extra' in basic_processos.data:
            basic_processos_vinculos = basic_processos.data.get('extra', {})
            for p_key in basic_processos_vinculos.keys():
                new_p = copy(VinculoSection.front_format_section(VinculoSection.PROCESSOS))
                new_p.subsection = p_key

                custom_processos[f'processos_{p_key}'] = VinculoConfig(
                    new_p,
                    extract_func=lambda *args, key=p_key, **kwargs: ExtractionResult(basic_processos_vinculos[key], basic_processos.sources),
                    extract_type=ReportKeys.PROCESSO
                )

        return custom_processos


    def _get_custom_fornecimentos_campanha(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[VinculoSection, VinculoConfig]:

        fornecimentos = self.extractor.extract_vinculos_campanha(other_result_data, entity_type, None,
                                                            VinculoConfig(reverse=True))

        if not fornecimentos.data:
            return {}

        return {
            VinculoSection.FORNECIMENTOS_ELEITORAIS_ENVIADAS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.FORNECIMENTOS_ELEITORAIS_ENVIADAS),
                extract_func=lambda *args, **kwargs: ExtractionResult(fornecimentos.data['enviada'], fornecimentos.sources),
                extract_type="FORNECIMENTOS"
            ),
            VinculoSection.FORNECIMENTOS_ELEITORAIS_RECEBIDAS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.FORNECIMENTOS_ELEITORAIS_RECEBIDAS),
                extract_func=lambda *args, **kwargs: ExtractionResult(fornecimentos.data['recebida'], fornecimentos.sources),
                extract_type="FORNECIMENTOS"
            )
        }

    def _get_custom_doacoes_campanha(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[VinculoSection, VinculoConfig]:
        # Extract complex relationships first

        doacoes = self.extractor.extract_vinculos_campanha(other_result_data, entity_type, 'Doacoes',
                                                           VinculoConfig())
        if not doacoes.data:
            return {}

        return {
        VinculoSection.DOACOES_ELEITORAIS_ENVIADAS: VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.DOACOES_ELEITORAIS_ENVIADAS),
            extract_func=lambda *args, **kwargs: ExtractionResult(doacoes.data['enviada'], doacoes.sources),
            extract_type="DOACOES"
        ),
        VinculoSection.DOACOES_ELEITORAIS_RECEBIDAS: VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.DOACOES_ELEITORAIS_RECEBIDAS),
            extract_func=lambda *args, **kwargs: ExtractionResult(doacoes.data['recebida'], doacoes.sources),
            extract_type="DOACOES",
        )}

    def get_section_vinculo_config(self, other_result_data: Dict, entity_type: str, search_value: str, has_main_data: bool = True, skip_custom_vinculos: bool = False) -> Dict[
        Any, VinculoConfig]:
        config = self.get_config()
        vinculos = self._get_basic_section_vinculo_config(config, has_main_data)
        if not skip_custom_vinculos:
            custom_vinculos = self.get_custom_vinculos(other_result_data, entity_type, search_value)
            vinculos.update(custom_vinculos)

        result = {x: vinculos[x] for x in config.enabled_sections if x in vinculos}
        for x, v in vinculos.items():
            if x not in result and not isinstance(x, VinculoSection):
                result[x] = v

        return result

    def _get_basic_section_vinculo_config(self, config=None, has_main_data: bool = True):
        if config is None:
            config = self.get_config()

        if has_main_data:

            vinculos = {
                VinculoSection.REDES_SOCIAIS: self.make_vinculo_redes_sociais(),
                VinculoSection.VINCULOS_EDUCACIONAIS: self.make_vinculo_educacionais(),
                VinculoSection.OUTRAS_URLS: self.make_vinculo_outras_urls(),
                VinculoSection.NOMES_USUARIO: self.make_vinculo_nomes_usuario(),
                VinculoSection.IMAGENS: self.make_vinculo_imagens(),
                VinculoSection.JUNTAS_COMERCIAIS: self.make_vinculo_juntas_comerciais(),
                VinculoSection.PHONES: self.make_vinculo_phones(),
                VinculoSection.EMAILS: self.make_vinculo_emails(),
                VinculoSection.ENDERECOS: self.make_vinculo_enderecos(),
                VinculoSection.MANDADOS: self.make_vinculo_mandados(),
                VinculoSection.PARENTES: self.make_vinculo_parentes(),
                VinculoSection.VINCULOS_EMPREGATICIOS: self.make_vinculo_empregaticios(),
                VinculoSection.RECURSOS_RECEBIDOS: self.make_vinculo_recursos_recebidos(),
                VinculoSection.SERVICO_PUBLICO: self.make_vinculo_servico_publico(),
                VinculoSection.DIARIOS_OFICIAIS_DOC: self.make_vinculo_diarios_oficiais_doc(config),
                VinculoSection.DIARIOS_OFICIAIS_NOME: self.make_vinculo_diarios_oficiais_nome(config),
                VinculoSection.FILIACAO_PARTIDARIA: self.make_vinculo_filiacao_partidaria(),
                VinculoSection.OUTROS_CONTATOS: self.make_vinculo_outros_contatos(),
                VinculoSection.PAIS: self.make_vinculo_pais(),
                VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS: self.make_vinculo_possiveis_pessoas(),
                VinculoSection.POSSIVEIS_EMPRESAS_RELACIONADAS: self.make_vinculo_possiveis_empresas(),
            }

        else:
            vinculos = {
                VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS: self.make_vinculo_possiveis_pessoas(),
                VinculoSection.POSSIVEIS_EMPRESAS_RELACIONADAS: self.make_vinculo_possiveis_empresas(),
                VinculoSection.REDES_SOCIAIS: self.make_vinculo_redes_sociais(),
                VinculoSection.IMAGENS: self.make_vinculo_imagens(),
                VinculoSection.PHONES: self.make_vinculo_phones(),
                VinculoSection.ENDERECOS: self.make_vinculo_enderecos(),
                VinculoSection.PAIS: self.make_vinculo_pais(),
            }
        return vinculos

    def adjust_metadata(self, processed_result, metadata):
        return


    @classmethod
    def find_section_in_final_result(cls, section, processed_result):
        r_section = [data for data in processed_result if data['title'] == section.title]
        return r_section[0] if r_section else None

    def _adjust_metadata_age(self, processed_result, metadata):
        personal_data = self.find_section_in_final_result(front_vinculo_pessoais, processed_result)
        if personal_data['data_count'] <= 0:
            return

        for p_age in age_fields:
            if p_age in personal_data['data'][0]['detalhes']:
                tmp_data = personal_data['data'][0]['detalhes'][p_age]['value']
                try:
                    tmp_data = datetime.strptime(tmp_data, "%d/%m/%Y").date()
                    metadata['subject_age'] = tmp_data.isoformat()
                    break
                except Exception:
                    logging.debug(f"Failed to process date {personal_data['data'][0]['detalhes'][p_age]['value']}")
                    continue
        return

    def _adjust_metadata_for_multiple_results(self, processed_result, metadata):
        personal_data = self.find_section_in_final_result(front_vinculo_pessoais, processed_result)
        if personal_data['data_count'] > 0:
            return

        possible_person = self.find_section_in_final_result(front_p_pessoas_relacionadas, processed_result)
        possible_person_count = possible_person['data_count']

        possible_company = self.find_section_in_final_result(front_empresas_relacionadas, processed_result)
        possible_company_count = possible_company['data_count']

        if possible_person_count > 0 or possible_company_count > 0:
            name = "Multiplos registros encontrados"

        else:
            name = "Nenhum registro encontrado"

        metadata['subject_name'] = name
        metadata['subject_mother_name'] = None
        metadata['subject_age'] = None
        metadata['subject_sex'] = None
        metadata['subject_person_count'] = possible_person_count
        metadata['subject_company_count'] = possible_company_count
        # todo: imagem
        return

    def _adjust_metadata_for_mother_name(self, processed_result, metadata):
        parentes_data = self.find_section_in_final_result(front_parentes, processed_result)
        mother_name = None

        if not parentes_data or parentes_data['data_count'] == 0:
            return


        for parent in parentes_data['data']:
            parentesco = parent.get('parentesco', None)
            if not parentesco or parentesco['value'].lower() != f"{parentes_vinculo} {parentes_mae_vinculo}".lower():
                continue

            if 'detalhes' in parent:
                mother_name = self._get_name_value(parent['detalhes'])
                if mother_name:
                    break

        metadata['subject_mother_name'] = mother_name


    def _get_name_value(self, location):

        name = location.get(nome_completo_key, '')
        if name:
            name = name['value']

        else:
            first_name = location.get(primeiro_nome_key, '')
            sobrenome = location.get(sobrenome_key, '')

            if first_name:
                first_name = first_name['value']

            if sobrenome:
                sobrenome = sobrenome['value']

            if first_name:
                name = f"{first_name} {sobrenome}".strip()

        if not name:
            name = location.get(razao_social_key, None)
            if not name:
                name = location.get('razão social', None)

            if name:
                name = name['value']

        return name


    def _adjust_metadata_personal_data(self, processed_result, metadata):
        personal_data = self.find_section_in_final_result(front_vinculo_pessoais, processed_result)
        if personal_data['data_count'] == 0:
            # poderia resetar os metadados aqui
            return

        self._adjust_metadata_for_mother_name(processed_result, metadata)

        try:
            base_personal_data = personal_data['data'][0]['detalhes']
        except Exception:
            return

        name = self._get_name_value(base_personal_data)

        sex = base_personal_data.get(sexo_key, None)
        if sex:
            sex = sex['value']

        metadata['subject_name'] = name
        metadata['subject_sex'] = sex
        metadata['subject_person_count'] = 0
        metadata['subject_company_count'] = 0
        # todo: imagem
        return

    def _adjust_metadata_relacoes(self, processed_result, metadata):
        relacoes_data = self.find_section_in_final_result(front_vinculo_relacoes, processed_result)
        metadata['number_of_relations'] = relacoes_data['data_count']
        return
