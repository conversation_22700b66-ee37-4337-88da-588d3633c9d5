import { Button, Input, Text, Avatar, Loading } from "@snap/design-system";
import { Check, X } from "lucide-react";
import { useEffect } from "react";
import { toast } from "sonner";
import { Checkbox } from "~/components/ui/checkbox";
import { Label } from "~/components/ui/label";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { REPORT_TYPES } from "~/helpers/constants";
import { useUserCRUD } from "~/hooks/useUserCRUD";
import { useDialogActions } from "~/store/dialogStore";
import { ReportCredits, UserInviteResponse, UserData } from "~/types/global";
import { getInitials, parseValue } from "~/helpers";
import {
  useEditUserActions,
  useEditUserName,
  useEditUserEmailInvited,
  useEditUserTypeInvite,
  useEditUserReportTypes,
  useEditUserCreditsSent,
  useEditUserSelectedUser,
} from "~/store/editUserStore";
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";

interface EditUserDialogContentProps {
  selectedRow: UserInviteResponse;
}

export function EditUserDialogContent({ selectedRow }: EditUserDialogContentProps) {
  const name = useEditUserName();
  const email_invited = useEditUserEmailInvited();
  const type_invite = useEditUserTypeInvite();
  const report_types = useEditUserReportTypes();
  const credits_sent = useEditUserCreditsSent();
  const { setSelectedUser, setTypeInvite, setReportTypes, setCreditsSent } = useEditUserActions();
  const { userFromInviteQuery } = useUserCRUD();
  const userFromInviteData = userFromInviteQuery(selectedRow?.invite_id || "");
  const { checkPermission } = usePermissionCheck();
  const canEditUser = checkPermission(Permission.EDIT_USER);

  useEffect(() => {
    if (userFromInviteData?.data) {
      const apiData = userFromInviteData.data as UserData;

      const normalizedUser: UserInviteResponse = {
        user_id: apiData.user_id,
        name: apiData.name || "",
        email_invited: apiData.email,
        type_invite: apiData.role,
        report_types: apiData.report_types || [],
        credits_sent: apiData.credits_monthly?.toString(),
      };

      setSelectedUser(normalizedUser);
    }
  }, [userFromInviteData?.data, selectedRow?.invite_id, userFromInviteData.data?.user_id]);

  const options = Object.values(REPORT_TYPES as ReportCredits | object).map(
    ([value]) => ({
      value: value,
      label: parseValue(value).toUpperCase(),
    })
  );

  const initials = getInitials(name);

  const handleReportTypeChange = (type: string, checked: boolean) => {
    setReportTypes({ ...report_types, [type]: checked });
  };

  const getValidImageUrl = (image: string | null | undefined) => {
    return image || undefined;
  };

  if (userFromInviteData.isLoading) {
    return (
      <div className="flex flex-col gap-6 p-4">
        <div className="flex items-center justify-center py-8">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  if (userFromInviteData.error) {
    return (
      <div className="flex flex-col gap-6 p-4">
        <div className="flex items-center justify-center py-8 text-accent">
          <Text>Erro ao carregar dados.</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6 p-4">
      <div className="flex items-center gap-4 p-4 border border-border rounded-md">
        <Avatar
          size="md"
          className="size-22"
          src={getValidImageUrl(undefined)}
          fallback={initials || "NA"}
          textAlign="left"
        />
        <div className="flex flex-col gap-2 flex-1">
          <div>
            <label className="block text-xs mb-1 font-semibold">Nome:</label>
            <Input
              type="text"
              placeholder="Nome do usuário"
              variant="filled"
              value={name}
              disabled
              className="rounded-none border-0 py-1.5 bg-background"
              wrapperClassName="border-1 border-foreground"
            />
          </div>
          <div>
            <label className="block text-xs mb-1 font-semibold">Email:</label>
            <Input
              type="email"
              placeholder="Email do usuário"
              variant="filled"
              value={email_invited}
              disabled
              className="rounded-none border-0 py-1.5 bg-background"
              wrapperClassName="border-1 border-foreground"
            />
          </div>
        </div>
      </div>

      <div className="flex gap-4 border border-border rounded-md p-4">
        <div className="flex items-center flex-1 gap-4">
          <Text variant="body-md" className="font-semibold">
            Tipo de Perfil:
          </Text>
          <RadioGroup
            value={type_invite}
            onValueChange={setTypeInvite}
            className="flex flex-1 gap-8"
            disabled={!canEditUser}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="investigador"
                id="investigador"
                className="border-white"
              />
              <Label htmlFor="investigador" className="text-[16px]">
                Investigador
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="administrador"
                id="administrador"
                className="border-white"
              />
              <Label htmlFor="administrador" className="text-[16px]">
                Administrador
              </Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      <div className="space-y-4">
        <Text variant="body-md" className="font-semibold">
          Marque os <span className="text-accent">tipos de relatório </span> que esse usuário terá acesso:
        </Text>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {options.map((item) => {
            const checked = report_types[item.value as keyof typeof report_types];
            return (
              <label
                key={item.value}
                htmlFor={`edit-${item.value}`}
                className={`
                  flex items-center justify-between gap-2
                  bg-neutral-600 py-2 px-3 rounded
                  hover:opacity-100
                  ${checked ? "" : "opacity-80"}
                  cursor-pointer
                `}
              >
                <span className="font-mono text-[16px]">
                  {item.label}
                </span>

                <Checkbox
                  id={`edit-${item.value}`}
                  checked={checked}
                  onCheckedChange={(c) =>
                    handleReportTypeChange(item.value, c as boolean)
                  }
                  className="border-foreground data-[state=checked]:bg-white rounded-none"
                  disabled={!canEditUser}
                />
              </label>
            );
          })}
        </div>
      </div>

      <div className="flex items-center justify-between gap-8 border-t border-b border-dashed border-border py-4">
        <Text variant="body-md" className="font-semibold">
          Defina a quantidade de <span className="text-accent">consultas</span> para uso desse usuário:
        </Text>

        <Input
          type="number"
          variant="filled"
          min={0}
          value={credits_sent}
          onChange={(e) => setCreditsSent(Number(e.target.value))}
          className="rounded-none border-0 py-1.5 bg-background"
          wrapperClassName="flex-1 max-w-[140px] border-1 border-foreground"
          disabled={!canEditUser}
        />
      </div>
    </div>
  );
}

export function EditUserDialogFooter() {
  const { editInvitedUserMutation, userFromInviteQuery } = useUserCRUD();
  const { closeDialog } = useDialogActions();
  const selectedUser = useEditUserSelectedUser();
  const type_invite = useEditUserTypeInvite();
  const report_types = useEditUserReportTypes();
  const credits_sent = useEditUserCreditsSent();
  const { checkPermission } = usePermissionCheck();
  const canEditUser = checkPermission(Permission.EDIT_USER);

  const userFromInviteData = userFromInviteQuery(selectedUser?.invite_id || "");

  const handleSaveUser = () => {
    if (!selectedUser) {
      toast.error("Erro ao salvar usuário!", {
        description: "Usuário não selecionado.",
      });
      return;
    }

    console.log("selectedUser: ", selectedUser);
    console.log("userFromInviteData: ", selectedUser.user_id);

    if (!credits_sent || !type_invite || !Object.values(report_types).some((type) => type)) {
      toast.error("Erro ao salvar usuário!", {
        description: "Campos tipo de usuário, tipo de relatório e créditos não podem estar vazios.",
      });
      return;
    }

    if (!selectedUser.user_id) {
      toast.error("Erro ao salvar usuário!", {
        description: "Dados do usuário não encontrados.",
      });
      return;
    }

    const reportTypesArray = Object.keys(report_types).filter(
      (type) => report_types[type as keyof typeof report_types]
    );

    editInvitedUserMutation.mutate({
      user_id: selectedUser.user_id,
      report_types: reportTypesArray,
      role: type_invite,
      credits_monthly: credits_sent,
    }, {
      onSuccess: () => {
        closeDialog();
      }
    });
  };

  return (
    <div className="flex gap-4">
      <Button
        className="uppercase"
        onClick={closeDialog}
      >
        Fechar <X />
      </Button>
      {canEditUser ? (
        <Button
          className="uppercase"
          onClick={handleSaveUser}
          disabled={editInvitedUserMutation.isPending || userFromInviteData.isLoading || userFromInviteData.isFetching}
        >
          {editInvitedUserMutation.isPending ? "Salvando..." : "Salvar Alterações"} <Check />
        </Button>) : null
      }
    </div>
  );
}

export const EditUserDialog = {
  Content: EditUserDialogContent,
  Footer: EditUserDialogFooter,
};
