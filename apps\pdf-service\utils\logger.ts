import { format } from 'util';

export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  error?: Error;
  stack?: string;
  errorMessage?: string;
}

/**
 * Formats a log entry as a JSON string
 */
function formatLogEntry(entry: LogEntry): string {
  const { error, ...logData } = entry;
  
  if (error) {
    logData['errorMessage'] = error.message;
    logData['stack'] = error.stack;
  }
  
  return JSON.stringify(logData);
}

/**
 * Creates a log entry with the current timestamp
 */
function createLogEntry(level: LogLevel, message: string, context?: Record<string, any>, error?: Error): LogEntry {
  return {
    timestamp: new Date().toISOString(),
    level,
    message,
    context,
    error,
  };
}

/**
 * Debug level logging
 */
export function debug(message: string, context?: Record<string, any>): void {
  const entry = createLogEntry(LogLevel.DEBUG, message, context);
  console.debug(formatLogEntry(entry));
}

/**
 * Info level logging
 */
export function info(message: string, context?: Record<string, any>): void {
  const entry = createLogEntry(LogLevel.INFO, message, context);
  console.info(formatLogEntry(entry));
}

/**
 * Warning level logging
 */
export function warn(message: string, context?: Record<string, any>): void {
  const entry = createLogEntry(LogLevel.WARN, message, context);
  console.warn(formatLogEntry(entry));
}

/**
 * Error level logging
 */
export function error(message: string, errorOrContext?: Error | Record<string, any>, context?: Record<string, any>): void {
  let err: Error | undefined;
  let ctx = context;

  if (errorOrContext instanceof Error) {
    err = errorOrContext;
  } else if (errorOrContext && typeof errorOrContext === 'object') {
    ctx = errorOrContext;
  }

  const entry = createLogEntry(LogLevel.ERROR, message, ctx, err);
  console.error(formatLogEntry(entry));
}

/**
 * Format a message with variable substitutions (similar to util.format)
 */
export function formatMessage(template: string, ...args: any[]): string {
  return format(template, ...args);
}