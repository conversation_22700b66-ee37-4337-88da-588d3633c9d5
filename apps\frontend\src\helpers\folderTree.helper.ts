import { HierarchicalFolderItem } from "~/types/global";

/**
 * Converts hierarchical folder data to flat list format
 */
export function processHierarchicalFolders(
  folders: HierarchicalFolderItem[]
): HierarchicalFolderItem[] {
  if (!Array.isArray(folders)) {
    console.warn('processHierarchicalFolders: folders is not an array', folders);
    return [];
  }

  const processedFolders: HierarchicalFolderItem[] = [];

  for (const folder of folders) {
    const processed = processSingleFolder(folder);
    processedFolders.push(processed);

    // Recursively process children
    if (folder.data && Array.isArray(folder.data) && folder.data.length > 0) {
      const childrenProcessed = processHierarchicalFolders(folder.data);
      processedFolders.push(...childrenProcessed);
    }
  }

  return processedFolders;
}

/**
 * Processes a single folder
 */
function processSingleFolder(folder: HierarchicalFolderItem): HierarchicalFolderItem {
  let folderName = "[Encrypted]"
  if (typeof folder.folder_name === 'string') {
    folderName = folder.folder_name;
  } 

  return {
    ...folder,
    folder_name: folderName,
  };
}

/**
 * Flattens hierarchical folder structure into a flat list
 */
export function flattenHierarchicalFolders(
  folders: HierarchicalFolderItem[],
  processedFolders: HierarchicalFolderItem[] = []
): HierarchicalFolderItem[] {
  for (let i = 0; i < folders.length; i++) {
    const folder = folders[i];
    const processed = processedFolders.find(p => p.folder_id === folder.folder_id);

    if (processed) {
      processedFolders.push(processed);
    }

    // Recursively process children
    if (folder.data && folder.data.length > 0) {
      flattenHierarchicalFolders(folder.data, processedFolders);
    }
  }

  return processedFolders;
}

/**
 * Filters out a specific folder and its children from the hierarchical structure
 * Used to exclude the current folder when selecting destination folders
 */
export function filterHierarchicalFolders(
  folders: HierarchicalFolderItem[],
  excludeFolderId: string
): HierarchicalFolderItem[] {
  if (!Array.isArray(folders)) {
    console.warn('filterHierarchicalFolders: folders is not an array', folders);
    return [];
  }

  return folders
    .filter(folder => folder.folder_id !== excludeFolderId)
    .map(folder => ({
      ...folder,
      data: folder.data ? filterHierarchicalFolders(folder.data, excludeFolderId) : []
    }));
}

/**
 * Searches for folders by name in the hierarchical structure
 */
export function searchHierarchicalFolders(
  folders: HierarchicalFolderItem[],
  searchTerm: string,
  processedFolders: HierarchicalFolderItem[]
): HierarchicalFolderItem[] {
  const results: HierarchicalFolderItem[] = [];

  for (const folder of folders) {
    const processed = processedFolders.find(p => p.folder_id === folder.folder_id);
    const folderName = processed?.folder_name as string;

    const matchesSearch = folderName.toLowerCase().includes(searchTerm.toLowerCase());
    const childResults = folder.data ? searchHierarchicalFolders(folder.data, searchTerm, processedFolders) : [];

    if (matchesSearch || childResults.length > 0) {
      results.push({
        ...folder,
        data: childResults
      });
    }
  }

  return results;
}
