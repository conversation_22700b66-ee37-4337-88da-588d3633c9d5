import logging
import httpx
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
import json

from core.constants import Fields, Roles, Endpoints, ReportTypes

from services.base_service import BaseService
from services.organization_service import OrganizationService
from services.organization_users_service import OrganizationUsersService
from services.user_service import UserStandaloneService
from exceptions.business_exceptions import (
    ApiKeyValidationError,
    ApiServiceUnavailableError,
    OrganizationNotFoundError,
    ApiKeyDatabaseOperationError,
    InvitePermissionDeniedError,
    UserNoActiveOrganizationError,
    UserNotFoundError
)

logger = logging.getLogger(__name__)

class ApikeyService(BaseService):

    def __init__(self, db: AsyncSession, user_id: str) -> None:
        logger.info("[ApikeyService][__init__] Initializing ApikeyService for user: %s", user_id)
        super().__init__(db)
        self.user_id=user_id
        self.api_key=None
        logger.debug("[ApikeyService][__init__] ApikeyService initialized successfully for user: %s", user_id)


        

    async def validade_api_key(self):
        logger.info("[ApikeyService][validade_api_key] Validating API key for user: %s", self.user_id)
        headers = {
                'Ocp-Apim-Subscription-Key': self.api_key,
                'Accept': 'application/json'
                }
        logger.info("[ApikeyService][validade_api_key] Request headers: %s", headers)
        logger.info("[ApikeyService][validade_api_key] Making request to endpoint: %s", Endpoints.credits_report_endpoint)
        max_retries = 3
        for attempt in range(1, max_retries + 1):
            async with httpx.AsyncClient() as client:
                try:
                    logger.info(f"[ApikeyService][validade_api_key] Attempt {attempt} of {max_retries}: Sending GET request...")
                    results_response = await client.get(
                        Endpoints.credits_report_endpoint,
                        headers=headers
                    )
                    logger.info("[ApikeyService][validade_api_key] Response status code: %s", results_response.status_code)

                    if results_response.status_code == 200:
                        logger.info("[ApikeyService][validade_api_key] API key validation successful for user: %s", self.user_id)
                        return  # Success, exit the function
                    elif results_response.status_code == 401:
                        logger.error("[ApikeyService][validade_api_key] API key validation failed with status code: %s", results_response.status_code)
                        raise ApiKeyValidationError("A chave inserida não é válida")
                    else:
                        logger.error("[ApikeyService][validade_api_key] API key validation failed with status code: %s", results_response.status_code)
                        try:
                            error_text = results_response.text
                            logger.error("[ApikeyService][validade_api_key] Error response from Api Key: %s", error_text)
                        except Exception as e:
                            logger.error("[ApikeyService][validade_api_key] Error reading response: %s", e)
                            if attempt == max_retries:
                                raise ApiKeyValidationError(str(e))
                        if attempt == max_retries:
                            raise ApiKeyValidationError(error_text)
                except httpx.RequestError as e:
                    logger.error("[ApikeyService][validade_api_key] Network error during API key validation: %s", e, exc_info=True)
                    if attempt == max_retries:
                        raise ApiServiceUnavailableError()
                except Exception as e:
                    logger.error("[ApikeyService][validade_api_key] Unexpected error during API key validation: %s", e, exc_info=True)
                    if attempt == max_retries:
                        raise ApiServiceUnavailableError()
                
                
    async def update_api_key_on_db(self, user_roles: list):
        logger.info("[ApikeyService][update_api_key_on_db] Starting API key update process for user: %s", self.user_id)

        await self.validade_api_key()

        
        if Roles.administrador in user_roles:
            logger.info("[ApikeyService][update_api_key_on_db] User is administrator, updating organization API key")
            data_base_to_update = "organizations"

            organization_service = OrganizationUsersService(db=self.db, user_id=self.user_id)
            logger.debug("[ApikeyService][update_api_key_on_db] Getting organization user data for user: %s", self.user_id)
            user_organization_data = await organization_service.get_organization_user()
            if not user_organization_data:
                logger.warning("[ApikeyService][update_api_key_on_db] No active organization found for user: %s", self.user_id)
                raise UserNoActiveOrganizationError(self.user_id)
            id_type = Fields.organization_id
            id_reference = user_organization_data.organization_id
            logger.debug("[ApikeyService][update_api_key_on_db] Organization ID for update: %s", id_reference)

        elif Roles.standAlone in user_roles:
            logger.info("[ApikeyService][update_api_key_on_db] User is standalone, updating user API key")
            data_base_to_update = "users"
            id_type = Fields.user_id
            id_reference = self.user_id
        else:
            logger.error("[ApikeyService][update_api_key_on_db] User: %s does not have permission to update the api_key", self.user_id)
            raise InvitePermissionDeniedError("alterar api_key")
                
        logger.info("[ApikeyService][update_api_key_on_db] Inserting API key into database table: %s", data_base_to_update)
        await self.insert_api_key_on_db(data_base_to_be_used=data_base_to_update,
                                        id_type=id_type, id_reference=id_reference)
        logger.info("[ApikeyService][update_api_key_on_db] API key update completed successfully for user: %s", self.user_id)


    async def insert_api_key_on_db(self, data_base_to_be_used, id_type: str, id_reference: str):
        logger.info("[ApikeyService][insert_api_key_on_db] Inserting API key into table: %s with ID: %s", data_base_to_be_used, id_reference)
        
        valid_tables = ["users", "organizations"]
        if data_base_to_be_used not in valid_tables:
            logger.error("[ApikeyService][insert_api_key_on_db] Invalid table name provided: %s", data_base_to_be_used)
            raise ValueError("Invalid table name provided.")

        set_clauses = ["api_key = :api_key"]
        params = {
            "id_reference": id_reference,
            "api_key": self.api_key
        }

        logger.info("[ApikeyService][insert_api_key_on_db] Initial set_clauses: %s", set_clauses)
        logger.info("[ApikeyService][insert_api_key_on_db] Initial params: %s", params)

        if data_base_to_be_used == "users":
            set_clauses.append("report_types = :report_types")
            params["report_types"] = [ReportTypes.cpf, ReportTypes.cnpj, ReportTypes.combinado,
                                    ReportTypes.email, ReportTypes.telefone, ReportTypes.relacao]
            params["report_types"] = json.dumps(params["report_types"])
            logger.info("[ApikeyService][insert_api_key_on_db] Updated set_clauses for users: %s", set_clauses)
            logger.info("[ApikeyService][insert_api_key_on_db] Updated params for users: %s", params)

        set_clause_str = ", ".join(set_clauses)
        logger.info("[ApikeyService][insert_api_key_on_db] set_clause_str: %s", set_clause_str)

        query = text(f"""
            UPDATE \"{data_base_to_be_used}\"
            SET {set_clause_str}
            WHERE {id_type} = :id_reference;
        """)
        logger.info("[ApikeyService][insert_api_key_on_db] Final query: %s", query)

        try:
            await self.db.execute(query, params)
            
            await self.db.commit()

            logger.info("[ApikeyService][insert_api_key_on_db] API key successfully inserted/updated in database for table: %s, id: %s", data_base_to_be_used, id_reference)
        except Exception as e:
            logger.error("[ApikeyService][insert_api_key_on_db] Database error while inserting API key: %s", e, exc_info=True)
            raise ApiKeyDatabaseOperationError()
            

    async def get_api_key(self):
        logger.info("[ApikeyService][get_api_key] Getting API key for user: %s", self.user_id)
        organization_user_service = OrganizationUsersService(db=self.db, user_id= self.user_id)
        logger.debug("[ApikeyService][get_api_key] Getting organization user data for user: %s", self.user_id)
        user_organization_data = await organization_user_service.get_organization_user()

        user_service = UserStandaloneService(db=self.db, user_id=self.user_id)
        logger.debug("[ApikeyService][get_api_key] Getting user data for user: %s", self.user_id)
        user_data = await user_service.get_user_data()
        if not user_organization_data:
            if user_data:
                logger.info("[ApikeyService][get_api_key] User is standalone, returning user API key for user: %s", self.user_id)
                return user_data.api_key
            else:
                logger.warning("[ApikeyService][get_api_key] No active user found for user_id: %s", self.user_id)
                raise UserNotFoundError(self.user_id)
        else:
            organization_id = user_organization_data.organization_id
            logger.debug("[ApikeyService][get_api_key] User belongs to organization: %s", organization_id)
            organization_service = OrganizationService(db=self.db , organization_id=organization_id)
            organization_data = await organization_service.get_organization_data()

            if organization_data:
                logger.info("[ApikeyService][get_api_key] Returning organization API key for organization_id: %s", organization_id)
                return organization_data.api_key
            else:
                logger.warning("[ApikeyService][get_api_key] No active organization found for organization_id: %s", organization_id)
                raise OrganizationNotFoundError(organization_id)