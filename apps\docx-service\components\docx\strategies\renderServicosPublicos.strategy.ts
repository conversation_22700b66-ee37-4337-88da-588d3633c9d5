import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintServicosPublicosProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      nome_completo?: ValueWithSource;
      remuneracao?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      detalhes?: Record<string, ValueWithSource>;
      [key: string]: any;
    }>;
  };
}

export const renderServicosPublicos = ({ section }: RenderPrintServicosPublicosProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((servicoPublico) => {
    if (servicoPublico.nome_completo && !servicoPublico.nome_completo.is_deleted) {
      children.push(new Paragraph({
        children: [
          new TextRun({ text: `${(servicoPublico.nome_completo.label || "Nome Completo").toUpperCase()}: `, bold: true, color: "FE473C" }),
          new TextRun(String(servicoPublico.nome_completo.value)),
        ],
        spacing: { after: 200 }
      }));
    }

    if (servicoPublico.detalhes) {
      const tableRows = Object.entries(servicoPublico.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => {
          return new TableRow({
            children: [
              new TableCell({ children: [new Paragraph(translatePropToLabel(field.label || key).toUpperCase())] }),
              new TableCell({ children: [new Paragraph(String(field.value))] }),
            ],
          });
        });
      if (tableRows.length > 0) {
        children.push(new Table({ rows: tableRows, width: { size: 100, type: WidthType.PERCENTAGE } }));
      }
    }

    if (servicoPublico.remuneracao && servicoPublico.remuneracao.length > 0) {
        children.push(new Paragraph({text: "REMUNERAÇÃO", style: "subtitle"}));
        servicoPublico.remuneracao.filter(r => !r.is_deleted).forEach((rem, index) => {
            children.push(new Paragraph({text: `${translatePropToLabel(rem.label || "REMUNERAÇÃO").toUpperCase()} ${index + 1}`}));
            const remRows = Object.entries(rem.value).filter(([_, f]) => !f.is_deleted).map(([key, field]) => new TableRow({children: [new TableCell({children: [new Paragraph(translatePropToLabel(field.label || key))]}), new TableCell({children: [new Paragraph(String(field.value))]})]}))
            if(remRows.length > 0) {
                children.push(new Table({rows: remRows, width: {size: 100, type: WidthType.PERCENTAGE}}));
            }
        });
    }

  });

  return { children };
};
