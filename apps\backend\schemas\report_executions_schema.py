from typing import Optional
from pydantic import BaseModel
from uuid import UUID
from datetime import datetime

class ReportExecutionsSchema(BaseModel):
    report_executions_id: UUID
    user_reports_id: UUID  # This now references user_report_ledger.user_reports_id
    user_id: UUID
    dt_inicio_tentativa: Optional[datetime] = None
    dt_aquisicao_snap: Optional[datetime] = None
    dt_chegada_minio_reports: Optional[datetime] = None
    dt_proc_spark: Optional[datetime] = None
    dt_chegada_minio_processed_reports: Optional[datetime] = None
    dt_envio_websocket: Optional[datetime] = None
    dt_salvo_pelo_frontend: Optional[datetime] = None
    dt_deleted: Optional[datetime] = None
    status_tentativa: str
    detalhe_erro: Optional[str] = None

    class Config:
        orm_mode = True 