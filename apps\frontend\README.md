# SNAP Reports Frontend Documentation

## Setup and Running Instructions

### Prerequisites
- Node.js v22.13.1 (as specified in Volta config)
- pnpm v9.3.0 (as specified in Volta config)

### Installation
```bash
# Install dependencies
pnpm install
```

## Running the Application

### Development Mode
```bash
# Run in development mode localy with Vite
pnpm run dev
```

### Docker Development Mode (with backend api)

```bash
./generate_deploy.sh --profile dev
```

### Backend Development Mode (with VM services) - back-dev profile
This mode runs only the backend and Redis locally while connecting to services (Postgres, Keycloak, Kafka, Minio) running on Staging VM ***************. It allows for local frontend development while connecting to the backend and database services running on the VM.

#### Prerequisites
1. **VM Services**: Ensure the following services are running on VM ***************:
   - PostgreSQL (port 5432)
   - Keycloak with ngrok tunnel
   - <PERSON><PERSON><PERSON> (ports 9092/9093)
   - Minio (ports 9000/9001)

2. **Environment Configuration**: Update the root `.env` file with the following variables:

```bash
# Database (running on VM)
DB_HOST=
DB_NAME=
DB_USER=
DB_PASS=

# Keycloak Authentication (running on VM with ngrok)
KEYCLOAK_URL=
KEYCLOAK_ISSUER_URL=
CLIENT_ID_KEYCLOAK=
REALM_NAME=

# Backend API Configuration
BASE_API_URL=
FRONTEND_REDIRECT_URL=
REDIRECT_URI_KEYCLOAK=
LOG_LEVEL=

# Minio (running on VM)
MINIO_ROOT_USER=
MINIO_ROOT_PASSWORD=
MINIO_CONTAINER_NAME=
MINIO_S3_INTERNAL_PORT=

# Kafka (running on VM)
KAFKA_CONTAINER_NAME=
KAFKA_EXTERNAL_URI=
KAFKA_EXTERNAL_PORT=
KAFKA_INTERNAL_PORT=
SHOULD_USE_KAFKA_CONTAINER=

# Redis (running locally with backend)
REDIS_HOST=
REDIS_PORT=
REDIS_DB=
REDIS_PASSWORD=
REDIS_SSL=
REDIS_MAX_CONNECTIONS=

# Backend Performance
DELETE_PDFS_AFTER_DOWNLOAD=
ENABLE_KAFKA_CONSUMER=

# Gunicorn Configuration
GUNICORN_WORKERS=
GUNICORN_TIMEOUT=
GUNICORN_MAX_REQUESTS=
GUNICORN_MAX_REQUESTS_JITTER=
GUNICORN_LOG_LEVEL=
GUNICORN_GRACEFUL_TIMEOUT=
GUNICORN_KEEPALIVE=
GUNICORN_ACCESS_LOG=

# Database Pool Configuration
DB_POOL_SIZE=
DB_MAX_OVERFLOW=
DB_POOL_TIMEOUT=

# Secrets (required by generate_deploy.sh)
SNAP_API_CLIENT_SECRET=
CAPTCHA_KEY=
KEYCLOAK_ADMIN_PASSWORD=
CLIENT_SECRET_KEYCLOAK=

# OAuth (Google and Microsoft credentials)
CLIENT_SECRET_GOOGLE=
CLIENT_ID_GOOGLE=
CLIENT_SECRET_MICROSOFT=
CLIENT_ID_MICROSOFT=
```

3. **Get Keycloak Client Secret**: 
   - Access Keycloak Admin Console: `<your-ngrok-tunnel-url>/admin/master/console/` 
   - Login with Keycloak admin credentials
   - Navigate to: **Realm: <realm-name>** > **Clients** > **<client-id>** > **Credentials** > **Client Secret**
   - Copy the client secret value to `CLIENT_SECRET_KEYCLOAK` in your `.env` file

4. **Keycloak Client Configuration**: Ensure the following redirect URIs are configured in Keycloak:
   - Valid Redirect URIs: `http://localhost:8000/*`, `http://localhost:3000/*`
   - Web Origins: `http://localhost:8000`, `http://localhost:3000`

#### Running Backend Development Mode
```bash
# Deploy backend services (backend + Redis locally, connects to VM services)
./generate_deploy.sh --profile back-dev

# Run frontend development server (separate terminal)
pnpm run dev
```

#### What runs where:
- **Local Docker containers**: Backend API (port 8000), Redis (port 6379)
- **VM (***************)**: PostgreSQL, Keycloak, Kafka, Minio
- **Local development**: Frontend (port 3000)

### Production Mode
```bash
# Build the application
pnpm run build

# Preview production build
pnpm run preview
```

## Testing

### Unit Tests
```bash
# Run tests in watch mode
pnpm test

# Run tests once
pnpm run test:run

# Run tests for staged files
pnpm run test:staged

# Run tests with coverage
pnpm run test:ci
```

### E2E Tests
```bash
# Run E2E tests with UI
pnpm run test:e2e

# Run E2E tests in CI mode
pnpm run test:e2e:ci

# Install E2E test browser (Firefox)
pnpm run test:e2e:install

```

## Available Scripts

| Command                     | Description                        |
| --------------------------- | ---------------------------------- |
| `pnpm run dev`              | Start development server with Vite |
| `pnpm run build`            | Build for production               |
| `pnpm run preview`          | Preview production build           |
| `pnpm run lint`             | Run ESLint with auto-fix           |
| `pnpm run validate`         | Run type checking and linting      |
| `pnpm test`                 | Run Vitest in watch mode           |
| `pnpm run test:run`         | Run Vitest tests once              |
| `pnpm run test:staged`      | Run tests for staged files         |
| `pnpm run test:ci`          | Run tests with coverage            |
| `pnpm run test:e2e`         | Run Playwright E2E tests with UI   |
| `pnpm run test:e2e:ci`      | Run E2E tests in CI mode           |
| `pnpm run test:e2e:install` | Install Playwright Firefox browser |
| `pnpm run precommit`        | Run lint-staged pre-commit checks  |
