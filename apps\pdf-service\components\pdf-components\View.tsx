import React from 'react';

export interface ViewProps {
  id?: string;
  className?: string;
  style?: React.CSSProperties | React.CSSProperties[];
  /**
   * Render component in all wrapped pages.
   */
  fixed?: boolean;
  /**
   * Force the wrapping algorithm to start a new page when rendering the
   * element.
   */
  break?: boolean;
  /**
   * Force a page break after the element.
   */
  breakAfter?: boolean;
  /**
   * Hint that no page wrapping should occur between all sibling elements following the element within n points
   */
  minPresenceAhead?: number;
  /**
   * Enable/disable page wrapping for element.
   */
  wrap?: boolean;
  /**
   * Enables debug mode on page bounding box.
   */
  debug?: boolean;
  render?: (props: {
    pageNumber: number;
    subPageNumber: number;
  }) => React.ReactNode;
  children?: React.ReactNode;
}

/**
 * The most fundamental component for building a UI and is designed to be
 * nested inside other views and can have 0 to many children.
 */
export const View: React.FC<ViewProps> = ({
  id,
  style,
  fixed,
  break: pageBreak,
  breakAfter,
  minPresenceAhead,
  wrap,
  debug,
  render,
  children,
}) => {
  // Convert style array to single style object if needed
  const combinedStyle = Array.isArray(style) 
    ? style.reduce((acc, s) => ({ ...acc, ...s }), {})
    : style;

  // Base view styles that mimic PDF view behavior
  const viewStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
    boxSizing: 'border-box',
    border: debug ? '1px solid blue' : undefined,
    ...combinedStyle,
  };

  // Handle fixed positioning
  if (fixed) {
    viewStyle.position = 'absolute';
  }

  // Mock page context for render function
  const pageContext = {
    pageNumber: 1, // In a real implementation, this would come from context
    subPageNumber: 1,
  };

  // Determine the appropriate class names based on props
  const classNames = ['pdf-view'];

  // Handle page breaks
  if (pageBreak) {
    classNames.push('page-break-before');
    viewStyle.breakBefore = 'page';
  }

  if (breakAfter) {
    classNames.push('page-break-after');
    viewStyle.breakAfter = 'page';
  }

  if (wrap === false) {
    classNames.push('no-break-inside');
  }

  return (
    <div 
      id={id}
      className={classNames.join(' ')} 
      style={viewStyle}
      data-wrap={wrap}
      data-debug={debug}
      data-fixed={fixed}
      data-min-presence-ahead={minPresenceAhead}
    >
      {render ? render(pageContext) : children}
    </div>
  );
};

export default View;
