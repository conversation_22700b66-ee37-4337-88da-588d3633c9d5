{"SNAP": [{"pessoa": [{"cpf": "56507119687", "first names": "JOAO", "surname": "KENNEDY LOPES NUNES", "full name": "JOAO KENNEDY LOPES NUNES", "sexo": "M", "pessoa": [{"full name": "MARIA LOPES NUNES", "label default key": "parente MAE"}], "data nascimento": "03/06/1964", "procon": "(NAO TEM)", "phonenumber": [{"phone number": "5531996264775", "operadora": "CLARO", "bookmark": "1", "whatsapp": "<PERSON>m"}], "location": [{"logradouro": "PRACA TIRADENTES", "label default key": "5531996264775", "numero": "0", "bairro": "CENTRO", "cep ou zipcode": "32600054", "city": "BETIM", "area": "MG"}], "bookmark": 4}]}], "PIPLPhone": [{"pessoa": [{"first names": "JOAO", "surname": "Lopes Nunes", "surname1": "KENNEDY LOPES NUNES", "full name": "JOAO KENNEDY LOPES NUNES", "idade": "61", "data nascimento": "03/06/1964", "sexo": "Male", "idioma": "pt", "cpf": "56507119687", "vinculo": [{"rotulo": "CPF"}], "pessoa": [{"first names": "<PERSON>", "surname": "Nunes", "full name": "MARIA LOPES NUNES", "vinculo": [{"rotulo": "parente MAE", "classificacao": "Familia"}]}], "endereco": [{"nome": "110-AP 303 A<PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Minas Gerais", "estado ou regiao": "MG", "logradouro": "AV SEBASTIAO DAYRELL DE LIMA", "numero": "110", "cidade": "<PERSON><PERSON>", "cep ou zipcode": "32600266", "pais": "BR"}, {"nome": "110-AP 303 A<PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Minas Gerais", "estado ou regiao": "MG", "logradouro": "AV SEBASTIAO DAYRREL DE LIMA", "numero": "110", "cidade": "<PERSON><PERSON>", "cep ou zipcode": "32510470", "pais": "BR"}], "phonenumber": [{"phone number": "5531985828586", "country code": 55, "tipo": "celular"}, {"phone number": "5531995588285", "country code": 55, "tipo": "celular"}, {"phone number": "5531996264775", "country code": 55, "tipo": "celular"}, {"phone number": "553135942245", "country code": 55}, {"phone number": "553135313584", "country code": 55}, {"phone number": "553133313584", "country code": 55}], "label default key": "<PERSON><PERSON><PERSON>"}, {"first names": "VICTOR", "surname": "KENNEDY", "full name": "VICTOR KENNEDY", "sexo": "Male", "idioma": "pt", "endereco": [{"nome": "Brazil", "logradouro": "Brazil", "pais": "BR"}], "phonenumber": [{"phone number": "5531996264775", "country code": 55, "tipo": "celular"}], "label default key": "<PERSON><PERSON><PERSON>"}, {"first names": "VICTOR", "surname": "<PERSON><PERSON>", "surname1": "KENNEDY KANEKO NUNES", "full name": "VICTOR KENNEDY KANEKO NUNES", "idade": "27", "data nascimento": "08/09/1997", "sexo": "Male", "idioma": "pt", "cpf": "01629906697", "vinculo": [{"rotulo": "CPF"}], "pessoa": [{"idade": "56", "data nascimento": "02/06/1969", "first names": "<PERSON>", "surname": "Nunes", "full name": "MIRIAM TAMAMI KANEKO NUNES", "phonenumber": [{"phone number": "5531991699454", "country code": 55, "tipo": "celular"}], "vinculo": [{"rotulo": "parente MAE", "classificacao": "Familia"}]}, {"idade": "30", "data nascimento": "08/07/1994", "first names": "<PERSON><PERSON>", "surname": "Nunes", "full name": "RAYANE TAMAMI NUNES", "phonenumber": [{"phone number": "5531997942870", "country code": 55, "tipo": "celular"}], "vinculo": [{"rotulo": "parente IRMAO", "classificacao": "Familia"}]}, {"idade": "61", "data nascimento": "08/04/1963", "first names": "<PERSON>", "surname": "<PERSON><PERSON>", "full name": "JONATHAN TADAHIRO KANEKO", "phonenumber": [{"phone number": "5522998689303", "country code": 55, "tipo": "celular"}], "vinculo": [{"rotulo": "parente TIA", "classificacao": "Familia"}]}, {"idade": "59", "data nascimento": "21/04/1966", "first names": "Mine<PERSON>", "surname": "<PERSON><PERSON>", "full name": "MINEO KANEKO", "phonenumber": [{"phone number": "5511991042688", "country code": 55, "tipo": "celular"}], "vinculo": [{"rotulo": "parente TIA", "classificacao": "Familia"}]}], "endereco": [{"nome": "157 R <PERSON>, Bel<PERSON> Horizon<PERSON>, Minas Gerais", "estado ou regiao": "MG", "logradouro": "R D OSCAR ROMERO", "numero": "157", "cidade": "Belo Horizonte", "cep ou zipcode": "30510080", "pais": "BR"}], "phonenumber": [{"phone number": "5531975913669", "country code": 55, "tipo": "celular"}, {"phone number": "5531996264775", "country code": 55, "tipo": "celular"}, {"phone number": "553135313584", "country code": 55}], "label default key": "<PERSON><PERSON><PERSON>"}]}], "Provedor de Aplicacação da Internet": [{"provedor de aplicacoes de internet": [{"nome": "AmazonPhone", "termo procurado": "31996264775", "existe": true}, {"nome": "FacebookPhone", "termo procurado": "31996264775", "existe": "Erro: A aplicacao bloqueou a requisicao."}]}]}