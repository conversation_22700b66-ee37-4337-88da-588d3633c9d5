import { Text } from "@snap/design-system";
import { Database } from "lucide-react";

interface EmptyDataProps {
  title?: string;
  icon?: React.ReactNode;
  className?: string;
}

const EmptyData = ({ 
  title = "Nenhum dado encontrado", 
  icon,
  className = ""
}: EmptyDataProps) => {
  return (
    <div className={`flex flex-col gap-4 items-center text-gray-300 py-8 ${className}`}>
      {icon || <Database size={64} />}
      <Text variant="label-lg" align="center">{title}</Text>
    </div>
  );
};

export default EmptyData;
