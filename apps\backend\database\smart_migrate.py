#!/usr/bin/env python3
"""
Smart Migration Generator

This script automatically detects model changes and generates migration files
when differences are found between the current models and database schema.

Usage:
    python database/smart_migrate.py [options]

Options:
    --force          Generate migration even if no changes detected
    --dry-run        Show what would be done without making changes
    --message TEXT   Custom migration message
    --verbose        Enable verbose logging

Environment Variables:
    AUTO_GENERATE_MIGRATIONS=true/false   Enable/disable auto-generation
"""

import os
import sys
import asyncio
import subprocess
import argparse
import logging
from pathlib import Path
from datetime import datetime
from typing import Tuple, Optional

# Add the backend directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

class SmartMigrationGenerator:
    """Intelligent migration generator with change detection"""

    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.backend_dir = Path(__file__).parent.parent

        if self.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
            logger.debug("Smart Migration Generator initialized")

    async def check_alembic_available(self) -> bool:
        """Check if Alembic is properly configured and available"""
        try:
            # Check if alembic.ini exists
            alembic_ini = self.backend_dir / "alembic.ini"
            if not alembic_ini.exists():
                logger.error("❌ alembic.ini not found in backend directory")
                return False

            # Check if alembic directory exists
            alembic_dir = self.backend_dir / "alembic"
            if not alembic_dir.exists():
                logger.error("❌ alembic directory not found")
                return False

            # Test alembic command
            result = await asyncio.create_subprocess_exec(
                'alembic', '--version',
                cwd=self.backend_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                logger.debug(f"✅ Alembic available: {stdout.decode().strip()}")
                return True
            else:
                logger.error(f"❌ Alembic command failed: {stderr.decode()}")
                return False

        except FileNotFoundError:
            logger.error("❌ Alembic command not found in PATH")
            return False
        except Exception as e:
            logger.error(f"❌ Error checking Alembic availability: {e}")
            return False

    async def detect_model_changes(self) -> Tuple[bool, str]:
        """
        Detect if there are differences between models and database schema.

        Returns:
            Tuple of (has_changes: bool, details: str)
        """
        logger.info("🔍 Detecting model changes...")

        try:
            # Use alembic check to detect differences
            result = await asyncio.create_subprocess_exec(
                'alembic', 'check',
                cwd=self.backend_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await result.communicate()
            stdout_text = stdout.decode().strip()
            stderr_text = stderr.decode().strip()

            if result.returncode == 0:
                # No differences found
                logger.info("✅ No model changes detected - database is up to date")
                return False, "Database schema matches current models"

            else:
                # Differences found
                logger.info("📋 Model changes detected!")
                details = f"Alembic check output:\nSTDOUT: {stdout_text}\nSTDERR: {stderr_text}"

                if self.verbose:
                    logger.debug(f"Change details: {details}")

                return True, details

        except FileNotFoundError:
            logger.error("❌ Alembic command not found")
            return False, "Alembic not available"
        except Exception as e:
            logger.error(f"❌ Error detecting changes: {e}")
            return False, f"Error during change detection: {str(e)}"

    async def get_next_revision_info(self) -> Tuple[Optional[str], int]:
        """Get information about the next revision to create"""
        try:
            # Get current head revision
            result = await asyncio.create_subprocess_exec(
                'alembic', 'heads',
                cwd=self.backend_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await result.communicate()

            if result.returncode != 0:
                logger.warning(f"Could not get current head: {stderr.decode()}")
                return None, 1

            head_output = stdout.decode().strip()

            # Extract revision ID from head output
            current_head = None
            if head_output:
                # Format is usually: "revision_id (head)"
                current_head = head_output.split()[0] if head_output.split() else None

            # Count existing migrations to determine next number
            versions_dir = self.backend_dir / "alembic" / "versions"
            migration_count = len([f for f in versions_dir.glob("*.py") if f.name != "__init__.py"])
            next_number = migration_count + 1

            logger.debug(f"Current head: {current_head}, Next migration number: {next_number}")
            return current_head, next_number

        except Exception as e:
            logger.warning(f"Error getting revision info: {e}")
            return None, 1

    async def generate_migration(self, message: Optional[str] = None, force: bool = False) -> Tuple[bool, str]:
        """
        Generate a new migration file.

        Args:
            message: Custom message for the migration
            force: Generate migration even if no changes detected

        Returns:
            Tuple of (success: bool, details: str)
        """
        # Check for changes unless forced
        if not force:
            has_changes, change_details = await self.detect_model_changes()
            if not has_changes:
                return False, f"No changes detected: {change_details}"

        # Generate migration message
        if not message:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            current_head, next_number = await self.get_next_revision_info()
            message = f"Auto-migration {timestamp} ({next_number:03d})"

        logger.info(f"📝 Generating migration: {message}")

        try:
            # Run alembic revision --autogenerate
            result = await asyncio.create_subprocess_exec(
                'alembic', 'revision', '--autogenerate', '-m', message,
                cwd=self.backend_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await result.communicate()
            stdout_text = stdout.decode().strip()
            stderr_text = stderr.decode().strip()

            if result.returncode == 0:
                logger.info("✅ Migration generated successfully!")

                # Extract migration file name from output
                migration_file = None
                for line in stdout_text.split('\n'):
                    if 'Generating' in line and '.py' in line:
                        # Extract file path from "Generating /path/to/file.py"
                        parts = line.split()
                        for part in parts:
                            if part.endswith('.py'):
                                migration_file = Path(part).name
                                break
                        break

                details = f"Migration file created: {migration_file or 'unknown'}"
                if self.verbose:
                    details += f"\nOutput: {stdout_text}"

                return True, details
            else:
                error_msg = f"Migration generation failed: {stderr_text}"
                logger.error(f"❌ {error_msg}")
                return False, error_msg

        except Exception as e:
            error_msg = f"Error generating migration: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return False, error_msg

    async def run_smart_migration(self, force: bool = False, dry_run: bool = False,
                                 message: Optional[str] = None) -> bool:
        """
        Main entry point for smart migration generation.

        Returns:
            True if migration was generated or not needed, False if error occurred
        """
        logger.info("🚀 Smart Migration Generator starting...")

        # Check if auto-generation is enabled
        auto_generate = os.getenv('AUTO_GENERATE_MIGRATIONS', 'false').lower()
        if auto_generate not in ('true', '1', 'yes', 'on') and not force:
            logger.info("ℹ️  Auto-generation disabled (AUTO_GENERATE_MIGRATIONS=false)")
            return True

        # Check Alembic availability
        if not await self.check_alembic_available():
            logger.error("❌ Alembic not properly configured")
            return False

        if dry_run:
            logger.info("🧪 DRY RUN MODE - No changes will be made")
            has_changes, details = await self.detect_model_changes()
            if has_changes:
                logger.info("📋 Changes detected - migration would be generated")
                logger.info(f"Details: {details}")
            else:
                logger.info("✅ No changes detected - no migration needed")
            return True

        # Generate migration if needed
        success, details = await self.generate_migration(message, force)

        if success:
            logger.info(f"✅ {details}")
        elif not force and "No changes detected" in details:
            # This is not an error - just no changes
            logger.info("✅ No migration needed - database is up to date")
            return True
        else:
            logger.error(f"❌ {details}")
            return False

        return True

def create_cli() -> argparse.ArgumentParser:
    """Create command-line interface"""
    parser = argparse.ArgumentParser(
        description="Smart Migration Generator - Automatically detect and generate database migrations",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python database/smart_migrate.py                           # Auto-detect and generate if needed
  python database/smart_migrate.py --force                   # Force generation even if no changes
  python database/smart_migrate.py --dry-run                 # Check what would be done
  python database/smart_migrate.py --message "Add user roles" # Custom message

Environment Variables:
  AUTO_GENERATE_MIGRATIONS=true    Enable automatic generation
  AUTO_GENERATE_MIGRATIONS=false   Disable automatic generation (default)
        """
    )

    parser.add_argument(
        '--force',
        action='store_true',
        help='Generate migration even if no changes detected'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be done without making changes'
    )

    parser.add_argument(
        '--message', '-m',
        type=str,
        help='Custom migration message'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )

    return parser

async def main():
    """Main CLI entry point"""
    parser = create_cli()
    args = parser.parse_args()

    # Initialize generator
    generator = SmartMigrationGenerator(verbose=args.verbose)

    try:
        # Run smart migration
        success = await generator.run_smart_migration(
            force=args.force,
            dry_run=args.dry_run,
            message=args.message
        )

        if success:
            logger.info("🎉 Smart migration completed successfully")
            sys.exit(0)
        else:
            logger.error("💥 Smart migration failed")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("\n👋 Smart migration cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}")
        if args.verbose:
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())