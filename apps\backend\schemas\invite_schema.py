from datetime import datetime
from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel, EmailStr, Field
from enum import Enum
import pytz

from core.constants import ReportTypes
from models.enums import invite_status, invite_type

class ReportType(str, Enum):
    cpf = ReportTypes.cpf
    cnpj = ReportTypes.cnpj
    email = ReportTypes.email
    telefone = ReportTypes.telefone
    combinado = ReportTypes.combinado
    relacao = ReportTypes.relacao


class InviteBase(BaseModel):
    user_sender_id: UUID
    organization_id: UUID
    status_invite: invite_status
    type_invite: invite_type
    email_invited: EmailStr
    sent_at: datetime = Field(default_factory=datetime.now(pytz.timezone("UTC")))
    credits_sent: int = 0
    report_types: Optional[List[ReportType]] = None
    

class InviteId(BaseModel):
    invite_id: UUID = Field(default_factory=UUID)


class InviteAnswer(InviteId):
    accept_invite: bool



class InviteCreate(BaseModel):
    email_invited: EmailStr
    credits_sent: int = 0
    report_types: Optional[List[ReportType]] = None
    type_invite: invite_type


class InviteIdOptional(BaseModel):
    invite_id: Optional[UUID] = None


class InviteUpdate(BaseModel):
    status_invite: Optional[invite_status] = None
    report_types: Optional[dict] = None



    class Config:
        from_attributes = True




