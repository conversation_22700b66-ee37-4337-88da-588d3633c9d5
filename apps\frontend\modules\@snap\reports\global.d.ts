import { REPORT_CONSTANTS } from "./config/constants";
import { Draft } from "immer";

export type ReportType = "cpf" | "cnpj" | "email" | "telefone" | null;

export interface ReportSection {
  title: string;
  subtitle: string;
  subsection: string;
  source: string[];
  data_count: number;
  is_deleted?: boolean;
  user_reports_id?: string;
  data: Array<Record<string, any>>;
}

export interface ReportDataCombined {
  combined_data: Array<ReportSection>;
  report_sources_data: Array<ReportSection>;
}

export type NormalizeOptions = {
  case?: "lower" | "upper" | "preserve";
  allowSpecialChars?: RegExp;
};

export interface Dictionary {
  [key: string]: string;
}

export interface ReportMetadata {
  [REPORT_CONSTANTS.new_report.report_id]: string;
  [REPORT_CONSTANTS.new_report.report_status]: string;
  [REPORT_CONSTANTS.new_report.report_type]: string;
  [REPORT_CONSTANTS.new_report.report_search_args]: object;
  [REPORT_CONSTANTS.new_report.report_name]: string;
  [REPORT_CONSTANTS.new_report.creation_at]: string;
  [REPORT_CONSTANTS.new_report.modified_at]: string;
  /* podem vir sem valor */
  [REPORT_CONSTANTS.new_report.subject_name]: string;
  [REPORT_CONSTANTS.new_report.subject_mother_name]: string;
  [REPORT_CONSTANTS.new_report.subject_age]: number | null;
  [REPORT_CONSTANTS.new_report.subject_sex]: string;
  /* combinado */
  [REPORT_CONSTANTS.new_report.report_sources_used]?: Array<CombinadoFilterOption>
}

export interface ReportDetailStore {
  sections: ReportSection[];
  metadata: ReportMetadata;
  reportType: string;
  isTrashEnabled: boolean,
  isPrintEnabled: boolean,
  isActionLoading: boolean;
  image?: string | undefined;
  organizationLogo?: string | null;
  shouldPrintSnapLogo?: boolean;
  selectedCombinedFilter?: string;
  combinedReportSources?: Array<CombinadoFilterOption>;
  actions: {
    setMetadata: (metadata: ReportMetadata) => void;
    setReportSections: (sections: ReportSection[]) => void;
    setReportType: (type: string) => void;
    setProfileImage?: (image: string) => void;
    setSelectedCombinedFilter?: (filterId: string) => void;
    setCombinedReportSources?: (sources: Array<CombinadoFilterOption>) => void;
    updateSectionEntries?: (
      sectionTitle: string,
      updaterFn: (entry: Draft<ReportSection["data"][0]>, index?: number) => void,
      testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
      testSectionDeletedFn: (section: ReportSection) => boolean,
      calculateDataCountFn?: (section: ReportSection) => number,
      includeSubsections?: boolean,
      crossSectionUpdate?: { matchingProp: string; updaterFn: (entry: any, index?: number) => void }
    ) => void;
    updateSubsectionWithMainSection?: (
      sectionTitle: string,
      subsectionName: string,
      matchingProp: string,
      updaterFn: (entry: Draft<ReportSection["data"][0]>, index?: number) => void,
      testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
      testSectionDeletedFn: (section: ReportSection) => boolean,
      calculateDataCountFn?: (section: ReportSection) => number
    ) => void;
  };
}

export interface CombinadoFilterOption {
  report_type: string;
  report_input_value: string;
  user_reports_id: string;
  report_name: string;
}
