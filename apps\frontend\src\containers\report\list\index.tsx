import { useEffect, useRef } from "react";
import { useParams } from "react-router";
import ReportsList from "~/containers/report/ReportsList";
import { useDialogActions } from "~/store/dialogStore";
import { CreateReportDialog } from "~/containers/report/CreateReportDialog";
import { Folder } from "lucide-react";
import { useReportListActions } from "~/store/reportListStore";
import { ReportListSkeleton } from "~/components/ReportListSkeleton";
import MissingKey from "~/components/MissingKey";
import { AUTH_STORE_INSTANCE } from "~/store/auth";
import { useIsPasswordExpired, useIsTemporaryKey } from "~/store/credentials";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { useSecretKeyDialog } from "~/hooks/useSecretKeyDialog";
import { useReportList } from "~/store/reportListStore";
import { toast } from "sonner";

const ReportListContainer = () => {
  const { folderId } = useParams<{ folderId?: string }>();
  const { openDialog } = useDialogActions();
  const { reportListQuery, invalidateCurrentFolder } = useReportCRUD(
    folderId || null
  );
  const { data: reportDataResponse, isLoading, isFetching, isRefetching } = reportListQuery;
  const reportList = useReportList();
  const { clearFolderPath } = useReportListActions();
  const hasShownToastKey = "reports_list_toast_shown";
  const firstLoadRef = useRef(
    sessionStorage.getItem(hasShownToastKey) !== "true"
  );
  const isPasswordExpired = useIsPasswordExpired();
  const prevFolderIdRef = useRef<string | undefined>(folderId);
  const {
    selectors: { useIsAuthorized },
  } = AUTH_STORE_INSTANCE;
  const isUserAuthorized = useIsAuthorized();
  const isUsingTemporaryKey = useIsTemporaryKey();
  const { openSecretKeyDialog } = useSecretKeyDialog();

  useEffect(() => {
    if (!folderId && prevFolderIdRef.current) {
      clearFolderPath();
    };

    if (prevFolderIdRef.current !== folderId) {
      if (!isUserAuthorized || isPasswordExpired) {
        openSecretKeyDialog();
      }
      invalidateCurrentFolder(folderId || null);
      prevFolderIdRef.current = folderId;
    }
  }, [folderId, invalidateCurrentFolder]);

  useEffect(() => {
    if (!isUserAuthorized || isPasswordExpired) {
      openSecretKeyDialog();
    }
  }, [isUserAuthorized, openSecretKeyDialog]);

  useEffect(() => {
    if (!reportDataResponse) return;

    const { isScheduledRefetch } = reportDataResponse;

    if (firstLoadRef.current || isScheduledRefetch) {
      toast.success("Lista atualizada com sucesso!");
      firstLoadRef.current = false;
      sessionStorage.setItem(hasShownToastKey, "true");
    }
  }, [reportDataResponse]);

  function handleOpenDialog() {
    // TODO - trocar pelo modal do DS quando corrigir bug que quebra altura do componente
    openDialog({
      title: "Criar Novo Relatório ou Pasta",
      icon: <Folder />,
      content: <CreateReportDialog.Content />,
      footer: <CreateReportDialog.Footer />,
      className: "max-w-2xl",
    });
  }

  const renderListContent = () => {
    console.log("[ ReportListContainer ] RENDERIZEI")
    if (isLoading || isFetching || isRefetching) {
      return <ReportListSkeleton />;
    }

    if (!isUserAuthorized || (isPasswordExpired && !isUsingTemporaryKey)) {
      return <MissingKey onInsertKey={openSecretKeyDialog} />;
    }

    if (reportList) {
      return (
        <ReportsList
          list={reportList}
          onNewReport={handleOpenDialog}
          isFetched={!isLoading}
        />
      );
    }

  };

  return renderListContent();
};

export default ReportListContainer;
