import json
import logging
import reports_processor.constants
from reports_processor.constants import MINIO_ENDPOINT, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, logger
from reports_processor.processor import handle_message, debug_processing

logging.basicConfig(level=logging.DEBUG, format='%(asctime)s %(levelname)s: %(message)s')
logging.getLogger("py4j").setLevel(logging.WARNING)
logging.getLogger("py4j.java_gateway").setLevel(logging.WARNING)


#TODO: mandar so cpf sem mascara

import json
import os
from confluent_kafka import Consumer, KafkaException


KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092")
KAFKA_GROUP_ID = os.getenv("KAFKA_GROUP_ID", "report-processing-consumer")
KAFKA_TOPIC = os.getenv("KAFKA_TOPIC", "reports")

def create_consumer():
    return Consumer({
        # "bootstrap.servers": "192.168.134.101:9093,kafka:9092",
        # "bootstrap.servers": "kafka:9092",
        "bootstrap.servers": KAFKA_BOOTSTRAP_SERVERS,
        "group.id": KAFKA_GROUP_ID,
        # "group.id": "testing-reports",
        "auto.offset.reset": "latest",
        "enable.auto.commit": False,
    })

def parse_kafka_message(msg: str) -> tuple[str, str]:
    """Extract bucket/key from MinIO notification"""
    try:
        raw = json.loads(msg)
        record = raw["Records"][0]
        bucket = record["s3"]["bucket"]["name"]
        key = record["s3"]["object"]["key"]
        return bucket, key
    except Exception as e:
        raise ValueError(f"Invalid message format: {e}")


def main():
    consumer = create_consumer()
    consumer.subscribe([KAFKA_TOPIC])

    try:
        while True:
            msg = consumer.poll(1.0)
            if msg is None:
                logger.debug(f'no message received by consumer in 1 second. Continuing...')
                continue
            if msg.error():
                raise KafkaException(msg.error())

            try:
                bucket, key = parse_kafka_message(msg.value())
                logger.info(f"Received message for bucket={bucket}, key={key}")
                handle_message(bucket, key)
                consumer.commit(message=msg)

            except Exception as e:
                print(f"Error processing message: {e}")
    except KeyboardInterrupt:
        print("Shutting down.")
    finally:
        consumer.close()


#
#
# def main():
#     """Main function to start the Spark processing job."""
#     # reports_processor.constants.spark = SparkSession.builder.appName("ReportProcessor").config("spark.executor.cores", "2") \
#     # .config("spark.executor.memory", "1g") \
#     # .config("spark.default.parallelism", "4") \
#     # .getOrCreate()
#
#     # 2 workers × 3 cores × 2 parallelism?
#     reports_processor.constants.spark = SparkSession.builder \
#         .appName("ReportProcessor") \
#         .config("spark.executor.cores", "2") \
#         .config("spark.executor.memory", "1500m") \
#         .config("spark.executor.memoryFraction", "0.8") \
#         .config("spark.sql.adaptive.enabled", "true") \
#         .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
#         .config("spark.default.parallelism", "12") \
#         .config("spark.sql.streaming.forceDeleteTempCheckpointLocation", "true") \
#         .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
#         .getOrCreate()
#
#     # Configure MinIO access
#     hadoop_conf = reports_processor.constants.spark._jsc.hadoopConfiguration()
#     hadoop_conf.set("fs.s3a.endpoint", MINIO_ENDPOINT)
#     hadoop_conf.set("fs.s3a.access.key", MINIO_ACCESS_KEY)
#     hadoop_conf.set("fs.s3a.secret.key", MINIO_SECRET_KEY)
#     hadoop_conf.set("fs.s3a.path.style.access", "true")
#     hadoop_conf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
#
#     # Read from Kafka
#     kafka_df = reports_processor.constants.spark.readStream \
#         .format("kafka") \
#         .option("kafka.bootstrap.servers", "kafka:9092") \
#         .option("subscribe", "reports") \
#         .option("startingOffsets", "earliest") \
#         .option("failOnDataLoss", "false") \
#         .load()
#
#     # Define schema for S3 event records
#     schema = StructType([
#         StructField("EventName", StringType()),
#         StructField("Key", StringType()),
#         StructField("Records", ArrayType(
#             StructType([
#                 StructField("s3", StructType([
#                     StructField("bucket", StructType([
#                         StructField("name", StringType())
#                     ])),
#                     StructField("object", StructType([
#                         StructField("key", StringType())
#                     ]))
#                 ]))
#             ])
#         ))
#     ])
#
#     # Parse records
#     raw_df = kafka_df.selectExpr("CAST(value AS STRING) as raw_json")
#     parsed_df = raw_df.select(from_json(col("raw_json"), schema).alias("data"))
#
#     # Extract bucket and key information
#     paths_df = parsed_df.select(
#         col("data.Records")[0]["s3"]["bucket"]["name"].alias("bucket"),
#         col("data.Records")[0]["s3"]["object"]["key"].alias("key")
#     )
#
#     # # Process each batch
#     # query = paths_df.writeStream \
#     #     .foreachBatch(handle_batch) \
#     #     .option("checkpointLocation", SPARK_CHECKPOINT_REPORTS) \
#     #     .outputMode("append") \
#     #     .start()
#
#     # Increase batch processing parallelism
#     # Process more records per batch
#     # Add trigger interval
#     query = paths_df.writeStream \
#         .foreachBatch(handle_batch) \
#         .option("checkpointLocation", SPARK_CHECKPOINT_REPORTS) \
#         .option("maxOffsetsPerTrigger", "1000") \
#         .trigger(processingTime='30 seconds') \
#         .outputMode("append") \
#         .start()
#
#     query.awaitTermination()


if __name__ == '__main__':
    main()
    # debug_processing()