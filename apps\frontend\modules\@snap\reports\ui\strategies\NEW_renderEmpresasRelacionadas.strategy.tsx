import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { formatFieldValue, translatePropToLabel } from "../../helpers";
import { getFieldLabel, getFieldValue, renderSourceTooltip } from "./helpers.strategy";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { CustomGridItem } from "../components/CustomGridItem";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { ItemSeparator } from "../components/ItemSeparator";

interface EmpresaRelacionada {
  detalhes: any;
}

export function useRenderEmpresasRelacionadas(sectionTitle: string): ArrayRenderStrategy<EmpresaRelacionada> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<EmpresaRelacionada, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  const shouldIncludeEntry = (entry: any) => {
    if (!entry.detalhes) return false;
    const vals = Object.values(entry.detalhes);
    return isTrash
      ? vals.some((v: any) => v.is_deleted === true)
      : vals.some((v: any) => v.is_deleted === false);
  };

  const onToggleField = (entryIdx: number, fieldKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (entry, i) => {
        if (i === entryIdx) {
          const field = (entry as any).detalhes?.[fieldKey];
          if (field) {
            field.is_deleted = !field.is_deleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleEntry = (entryIdx: number) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (entry, i) => {
        if (i === entryIdx && (entry as any).detalhes) {
          const targetDeletedState = isTrash ? false : true;

          Object.values((entry as any).detalhes).forEach((campo: any) => {
            if (campo) {
              campo.is_deleted = targetDeletedState;
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const formatByKey: Record<
    string,
    (empresa?: EmpresaRelacionada, renderIndex?: number) => React.ReactElement | null
  > = {
    detalhes: (empresa, renderIndex = 0) => {
      if (!empresa?.detalhes || !shouldIncludeEntry(empresa)) return null;
      const idx = idxMap.get(empresa)!;

      const fields = Object.entries(empresa.detalhes)
        .filter(([_, v]: any) =>
          isTrash ? v.is_deleted : !v.is_deleted
        );

      if (!fields.length) return null;

      return (
        <div key={`empresa-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12 pb-2"
            onToggleField={() => onToggleEntry(idx)}
          >
            <ReportsCustomLabel
              label={`EMPRESA ${!isTrash ? renderIndex + 1 : ""}`}
              colorClass="bg-accent"
            />
          </CustomGridItem>
          <div className="pl-5">
            <CustomGridContainer cols={2}>
              {fields.map(([fieldKey, fieldValue]: any, index) => (
                <CustomGridItem
                  key={`empresa-${idx}-${fieldKey}`}
                  cols={1}
                  className="py-1"
                  onToggleField={() => onToggleField(idx, fieldKey)}
                >
                  <CustomReadOnlyInputField
                    label={translatePropToLabel(
                      getFieldLabel(fieldKey, fieldValue)
                    ).toUpperCase()}
                    colorClass="bg-border"
                    value={formatFieldValue(fieldValue)}
                    isFirstLabelList={index === 0}
                    icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                    tooltip={renderSourceTooltip(fieldValue.source)}
                  />
                </CustomGridItem>
              ))}
            </CustomGridContainer>
          </div>
        </div>
      );
    },
  };

  const testEntryDeleted = (entry: any): boolean =>
    entry.detalhes
      ? Object.values(entry.detalhes).every((v: any) => v.is_deleted === true)
      : false;

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) &&
    section.data.every(testEntryDeleted);

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;

      const hasNonDeletedFields = Object.values(entry.detalhes).some(
        (field: any) => field.is_deleted !== true
      );

      return hasNonDeletedFields ? count + 1 : count;
    }, 0);
  };

  const validateKeys = (keys: Array<keyof EmpresaRelacionada>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (empresa: EmpresaRelacionada, renderIndex: number): React.ReactElement[] => {
    const keys = Object.keys(empresa) as Array<keyof EmpresaRelacionada>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Empresas Relacionadas] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(empresa, renderIndex))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: EmpresaRelacionada[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Empresas Relacionadas] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        return entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true);
      } else {
        const isDeleted = testEntryDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((empresa, renderIndex) => {
      const elements = renderSingleItem(empresa, renderIndex);

      if (elements.length === 0) {
        return;
      }

      if (filteredData.length > 1) {
        const isLastItem = renderIndex === filteredData.length - 1;
        allElements.push(
          <div
            key={`empresa-wrapper-${renderIndex}`}
            className={`relative mb-6 pb-1 ${isLastItem ? '!mb-0 !pb-0' : ''}`}
          >
            {elements}
            {!isLastItem && <ItemSeparator />}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        if (entry.detalhes) {
          Object.values(entry.detalhes).forEach((campo: any) => {
            if (campo) campo.is_deleted = targetDeletedState;
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}