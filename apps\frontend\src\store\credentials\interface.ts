export interface CredentialsState {
  crypto: {
    iv: string | null;
    salt: string | null;
    encrypted: string | null;
  };
  password: {
    validThrough: Date | null;
    isDirty: boolean;
    persistedValue: string | null;
  };
}

export interface CredentialsActions {
  updatePassword: (password: string) => void;
  persistPassword: (
    password: CredentialsState["password"]["validThrough"]
  ) => void;
  setSalt: (salt: string | null) => void;
  extendPasswordValidity: (duration: Date) => void;
  expirePassword: () => void;
  setPasswordSafety: (safety: boolean) => void;
  isPasswordSafe: () => boolean;
  clearCredentials: () => void;
  setCrypto: (crypto: Partial<CredentialsState["crypto"]>) => void;
  hasValidVerifiers: () => boolean;
}
