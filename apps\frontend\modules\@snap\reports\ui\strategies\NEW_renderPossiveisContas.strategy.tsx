import React, { useMemo, useCallback } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import {
  PossivelConta,
  PossiveisContasSection,
  TestFunctions,
  TestFunction,
  SectionTestFunction,
  CalculateFunction,
  UpdaterFunction
} from "../../model/PossiveisContas";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { formatFieldValue, parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { Icon } from "@snap/design-system";
import { useNestedRender } from "./NestedRenderContext";

export function useRenderPossiveisContas(
  sectionTitle: string
): ArrayRenderStrategy<PossivelConta> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const includeKey = useCallback((isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted, [isTrash]);

  const testFunctions = useMemo((): TestFunctions => ({
    site: (e: PossivelConta) => e.site?.is_deleted === true,
    detalhes: (e: PossivelConta) => e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted === true)
      : false,
  }), []);

  const testEntryDeleted: TestFunction = useCallback((entry: PossivelConta): boolean => {
    const isSiteDeleted = testFunctions.site(entry);
    const areDetalhesDeleted = testFunctions.detalhes(entry);
    return isSiteDeleted && areDetalhesDeleted;
  }, [testFunctions]);

  const testSectionDeleted: SectionTestFunction = useCallback((section: PossiveisContasSection): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted),
    [testEntryDeleted]);

  const calculateDataCount: CalculateFunction = useCallback((section: PossiveisContasSection): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: PossivelConta) => {
      // Count non-deleted entries (whole objects)
      // An entry is considered deleted only when ALL its parts are deleted
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  }, [testEntryDeleted]);

  const applyCascadingDeletion: (entry: PossivelConta, targetDeletedState: boolean) => void = useCallback((entry: PossivelConta, targetDeletedState: boolean) => {
    if (entry.detalhes) {
      Object.values(entry.detalhes).forEach((detalhe: any) => {
        if (detalhe) detalhe.is_deleted = targetDeletedState;
      });
    }
  }, []);

  const shouldShowEntry: TestFunction = useCallback((entry: PossivelConta): boolean => {
    if (isTrash) {
      // In trash mode, show entries that have ANY deleted field
      return testFunctions.site(entry) ||
        testFunctions.detalhes(entry) ||
        (entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true));
    } else {
      // In normal mode, show entries that are NOT completely deleted
      const isCompletelyDeleted = testEntryDeleted(entry);
      return !isCompletelyDeleted;
    }
  }, [isTrash, testFunctions, testEntryDeleted]);

  const updateEntries = useCallback((
    updater: UpdaterFunction,
    testDeleted: TestFunction = testEntryDeleted,
    testSectionDel: SectionTestFunction = testSectionDeleted,
    calcDataCount: CalculateFunction = calculateDataCount
  ) => {
    actions.updateSectionEntries?.(
      sectionTitle,
      updater,
      testDeleted as (entry: Record<string, any>) => boolean,
      testSectionDel as (section: Record<string, any>) => boolean,
      calcDataCount as (section: Record<string, any>) => number
    );
  }, [actions, sectionTitle, testEntryDeleted, testSectionDeleted, calculateDataCount]);

  const calculateNestedDataCount = useCallback((nestedSection: PossiveisContasSection): number => {
    if (!Array.isArray(nestedSection.data)) return 0;
    return nestedSection.data.reduce((count: number, entry: PossivelConta) => {
      const isEntryDeleted: boolean = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  }, [testEntryDeleted]);

  const SiteBlock = React.memo(({
    entry,
    index,
    sectionTitle
  }: {
    entry: PossivelConta;
    index: number;
    sectionTitle: string;
  }) => {
    const nested = useNestedRender();

    if (!entry?.site || !includeKey(entry.site.is_deleted || false)) return null;

    const existeValue = entry.detalhes?.existe?.value ?? entry.detalhes?.found?.value;

    let imageIcon: string | null = null;
    let imageColor: string | null = null;

    if (typeof existeValue === 'boolean') {
      if (existeValue === true) {
        imageIcon = "/icons/icone_check.svg";
        imageColor = "text-green-400";
      } else {
        imageIcon = "/icons/icone_error.svg";
        imageColor = "text-red-500";
      }
    }

    const handleToggleSite = useCallback(() => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: PossiveisContasSection) => {
          const targetEntry = nestedSection.data?.[index];
          if (targetEntry?.site) {
            targetEntry.site.is_deleted = !targetEntry.site.is_deleted;
            applyCascadingDeletion(targetEntry, targetEntry.site.is_deleted);
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: PossivelConta, i?: number) => {
        if (i === index && e.site) {
          e.site.is_deleted = !e.site.is_deleted;
          applyCascadingDeletion(e, e.site.is_deleted);
        }
      });
    }, [nested, index, sectionTitle, updateEntries, applyCascadingDeletion]);

    return (
      <CustomGridItem
        fullWidth
        onToggleField={handleToggleSite}
      >
        <CustomReadOnlyInputField
          label={entry.site.label.toUpperCase()}
          colorClass="bg-accent"
          labelTextClass="text-accent"
          value=""
          tooltip={renderSourceTooltip(entry.site.source)}
        />
        <div className="flex items-center gap-2 pt-2">
          {imageIcon && imageColor && (
            <Icon
              src={imageIcon}
              className={imageColor}
            />
          )}
          <CustomReadOnlyInputField
            value={parseValue(formatFieldValue(entry.site.value))}
            tooltip={renderSourceTooltip(entry.site.source)}
            className="w-full"
          />
        </div>
      </CustomGridItem>
    );
  });

  const DetalhesBlock = React.memo(({
    entry,
    index,
    sectionTitle
  }: {
    entry: PossivelConta;
    index: number;
    sectionTitle: string;
  }) => {
    const nested = useNestedRender();

    if (!entry?.detalhes) return null;
    const subs = Object.entries(entry.detalhes).filter(([, v]) =>
      includeKey((v as any).is_deleted)
    );
    if (!subs.length) return null;

    const handleToggleDetalhe = useCallback((fieldKey: string) => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: PossiveisContasSection) => {
          const targetEntry = nestedSection.data?.[index];
          const d = (targetEntry?.detalhes as any)?.[fieldKey];
          if (d) d.is_deleted = !d.is_deleted;
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: PossivelConta, i?: number) => {
        if (i === index) {
          const d = (e.detalhes as any)?.[fieldKey];
          if (d) d.is_deleted = !d.is_deleted;
        }
      });
    }, [nested, index, sectionTitle, updateEntries]);

    return (
      <CustomGridContainer
        cols={1}
        columnFirst
        key={`detalhes-${index}`}
        className="mb-6"
      >
        <div className="pl-5">
          {subs.map(([fieldKey, val]) => (
            <CustomGridItem
              key={fieldKey}
              cols={1}
              className="py-1"
              onToggleField={() => handleToggleDetalhe(fieldKey)}
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
                value={parseValue(formatFieldValue((val as any).value))}
                tooltip={renderSourceTooltip((val as any).source)}
              />
            </CustomGridItem>
          ))}
        </div>
      </CustomGridContainer>
    );
  });

  const formatByKey: Record<
    string,
    (entry?: PossivelConta, index?: number) => React.ReactElement | null
  > = {
    site: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <SiteBlock entry={entry} index={index} sectionTitle={sectionTitle} />;
    },

    detalhes: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <DetalhesBlock entry={entry} index={index} sectionTitle={sectionTitle} />;
    },
  };

  const validateKeys = (keys: Array<keyof PossivelConta>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: PossivelConta, index?: number): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof PossivelConta>;

    if (!validateKeys(keys)) {
      console.warn(`[Seção ${sectionTitle}] Chaves inválidas:`, keys);
    }

    const orderedKeys: Array<keyof PossivelConta> = [
      'site',
      'detalhes'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    const elements: React.ReactElement[] = [];

    filteredKeys.forEach((chave) => {
      const element = formatByKey[chave]?.(entry, index);
      if (element) elements.push(element);
    });

    return elements;
  };

  const render = (dataArray: PossivelConta[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn(`[Seção ${sectionTitle}] esperava array, mas recebeu:`, typeof dataArray);
      return [];
    }

    const filteredData = dataArray
      .map((entry, originalIndex) => ({ entry, originalIndex }))
      .filter(({ entry }) => shouldShowEntry(entry));

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    // Render in two-column grid layout like the original
    if (filteredData.length > 1) {
      allElements.push(
        <CustomGridContainer cols={2} key="possiveisContas-grid">
          {filteredData.map(({ entry, originalIndex }, filterIndex) => {
            const elements = renderSingleItem(entry, originalIndex);
            return (
              <div key={`conta-${originalIndex}`}>
                {elements}
              </div>
            );
          })}
        </CustomGridContainer>
      );
    } else {
      // Single item, render normally
      const { entry, originalIndex } = filteredData[0];
      const elements = renderSingleItem(entry, originalIndex);
      allElements.push(...elements);
    }

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      ((entry: PossivelConta) => {
        // Marca todos os campos como deletado/restaurado
        if (entry.detalhes) {
          Object.values(entry.detalhes).forEach((detalhe: any) => {
            if (detalhe) detalhe.is_deleted = targetDeletedState;
          });
        }
        if (entry.site) {
          entry.site.is_deleted = targetDeletedState;
        }
      }) as UpdaterFunction,
      testEntryDeleted as (entry: Record<string, any>) => boolean,
      testSectionDeleted as (section: Record<string, any>) => boolean,
      calculateDataCount as (section: Record<string, any>) => number
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
    calculateDataCount,
  } as ArrayRenderStrategy<PossivelConta> & { calculateDataCount: typeof calculateDataCount };
}
