import logging
from keycloak import KeycloakAdmin
from core.config import settings
from exceptions.business_exceptions import KeycloakAdminInitializationError, KeycloakAdminUnavailableError

logger = logging.getLogger(__name__)

keycloak_admin_client: KeycloakAdmin = None

async def initialize_keycloak_admin_client():
    """Initializes the KeycloakAdmin client and authenticates using client credentials."""
    global keycloak_admin_client
    if keycloak_admin_client is None:
        logger.info("[KeycloakClient] Initializing Keycloak Admin client...")
        logger.info(f"[KeycloakClient] Configuration:")
        logger.info(f"  - Server URL: http://keycloak:8080/authkc/")
        logger.info(f"  - Realm: {settings.REALM_NAME}")
        logger.info(f"  - Client ID: {settings.CLIENT_ID_KEYCLOAK}")
        logger.info(f"  - SSL Verification: {settings.KEYCLOAK_VERIFY_SSL}")

        if settings.KC_ENV != "production":
            server_url = settings.KEYCLOAK_URL
        else:
            server_url = "http://keycloak:8080/authkc/"

        try:
            admin_instance = KeycloakAdmin(
                server_url=server_url,
                realm_name=settings.REALM_NAME,
                client_id=settings.CLIENT_ID_KEYCLOAK,
                client_secret_key=settings.CLIENT_SECRET_KEYCLOAK,
                verify=False
            )

            # Test the connection by getting realm info
            try:
                realms = admin_instance.get_realms()
                logger.info(f"[KeycloakClient] Successfully connected! Found {len(realms)} realms")
                for realm in realms:
                    logger.info(f"  - Realm: {realm.get('realm', 'unknown')}")
            except Exception as test_error:
                logger.error(f"[KeycloakClient] Connection test failed: {test_error}")

            keycloak_admin_client = admin_instance
            logger.info("[KeycloakClient] Keycloak Admin client successfully initialized and authenticated using client credentials.")
        except Exception as e:
            logger.error(f"[KeycloakClient] Failed to initialize Keycloak Admin client: {e}")
            logger.error(f"[KeycloakClient] Will continue without Keycloak admin client")


async def shutdown_keycloak_admin_client():
    """Logs out the Keycloak Admin client."""
    global keycloak_admin_client
    if keycloak_admin_client:
        logger.info("[KeycloakClient] Shutting down Keycloak Admin client...")
        try:
            # For client credentials, explicit logout might not be as critical
            # as the token will expire. However, if the library supports it,
            # it's good practice. `logout_admin_client` is often for password grants.
            # For client_credentials, the token is obtained via `get_token_by_client_credentials`
            # and automatically refreshed. There's no session to "log out" in the same way.
            # You can simply set the client to None.
            pass # No explicit logout needed for client_credentials if the token expires

            logger.info("[KeycloakClient] Keycloak Admin client instance cleared.")
        except Exception as e:
            logger.warning(f"[KeycloakClient] Error during Keycloak Admin client shutdown: {e}")
        finally:
            keycloak_admin_client = None

def get_keycloak_admin() -> KeycloakAdmin:
    """FastAPI dependency to get the initialized KeycloakAdmin client."""
    if keycloak_admin_client is None:
        logger.error("[KeycloakClient] Keycloak Admin client requested before initialization or after shutdown.")
        raise KeycloakAdminUnavailableError()
    return keycloak_admin_client

def log_keycloak_operation(operation_name: str, *args, **kwargs):
    """Log Keycloak operations with detailed parameters."""
    logger.info(f"[KeycloakClient] Operation: {operation_name}")
    if args:
        logger.info(f"[KeycloakClient] Args: {args}")
    if kwargs:
        logger.info(f"[KeycloakClient] Kwargs: {kwargs}")

class LoggingKeycloakAdmin:
    """Wrapper around KeycloakAdmin to log all operations."""

    def __init__(self, admin_client: KeycloakAdmin):
        self._admin = admin_client

    def __getattr__(self, name):
        """Intercept all method calls to log them."""
        attr = getattr(self._admin, name)
        if callable(attr):
            def logged_method(*args, **kwargs):
                logger.info(f"[KeycloakClient] Calling method: {name}")
                logger.info(f"[KeycloakClient] Args: {args}")
                logger.info(f"[KeycloakClient] Kwargs: {kwargs}")
                try:
                    result = attr(*args, **kwargs)
                    logger.info(f"[KeycloakClient] Method {name} completed successfully")
                    logger.info(f"[KeycloakClient] Result type: {type(result).__name__}")
                    if isinstance(result, (list, dict)):
                        logger.info(f"[KeycloakClient] Result length/keys: {len(result) if isinstance(result, list) else list(result.keys())[:5]}")
                    return result
                except Exception as e:
                    logger.error(f"[KeycloakClient] Method {name} failed: {e}")
                    logger.error(f"[KeycloakClient] Exception type: {type(e).__name__}")
                    raise
            return logged_method
        return attr

def get_logged_keycloak_admin() -> LoggingKeycloakAdmin:
    """Get a logging wrapper around the Keycloak admin client."""
    admin = get_keycloak_admin()
    return LoggingKeycloakAdmin(admin)