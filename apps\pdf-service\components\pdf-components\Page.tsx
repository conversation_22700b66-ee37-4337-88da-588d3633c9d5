import React from 'react';

export interface PageProps {
  id?: string;
  style?: React.CSSProperties | React.CSSProperties[];
  /**
   * Render component in all wrapped pages.
   */
  fixed?: boolean;
  /**
   * Force the wrapping algorithm to start a new page when rendering the
   * element.
   */
  break?: boolean;
  /**
   * Hint that no page wrapping should occur between all sibling elements following the element within n points
   */
  minPresenceAhead?: number;
  /**
   * Enable page wrapping for this page.
   */
  wrap?: boolean;
  /**
   * Enables debug mode on page bounding box.
   */
  debug?: boolean;
  size?: string | [number, number];
  orientation?: 'portrait' | 'landscape';
  dpi?: number;
  bookmark?: any;
  children?: React.ReactNode;
}

/**
 * Represents single page inside the PDF document, or a subset of them if
 * using the wrapping feature. A <Document /> can contain as many pages as
 * you want, but ensure not rendering a page inside any component besides
 * Document.
 */
export const Page: React.FC<PageProps> = ({
  id,
  style,
  fixed,
  break: pageBreak,
  minPresenceAhead,
  wrap,
  debug,
  size = 'A4',
  orientation = 'portrait',
  dpi,
  bookmark,
  children,
}) => {
  // Convert style array to single style object if needed
  const combinedStyle = Array.isArray(style) 
    ? style.reduce((acc, s) => ({ ...acc, ...s }), {})
    : style;

  // Calculate page dimensions based on size and orientation
  const getPageDimensions = () => {
    let width: string, height: string;

    if (Array.isArray(size)) {
      width = `${size[0]}px`;
      height = `${size[1]}px`;
    } else {
      // Standard page sizes in pixels (at 96 DPI)
      const pageSizes: Record<string, [number, number]> = {
        'A4': [794, 1123], // 210mm x 297mm
        'A3': [1123, 1587], // 297mm x 420mm
        'A5': [559, 794], // 148mm x 210mm
        'Letter': [816, 1056], // 8.5" x 11"
        'Legal': [816, 1344], // 8.5" x 14"
      };

      const dimensions = pageSizes[size] || pageSizes['A4'];
      width = `${dimensions[0]}px`;
      height = `${dimensions[1]}px`;
    }

    // Swap dimensions for landscape
    if (orientation === 'landscape') {
      [width, height] = [height, width];
    }

    return { width, height };
  };

  const { width, height } = getPageDimensions();

  // Base page styles that mimic PDF page behavior
  const pageStyle: React.CSSProperties = {
    position: 'relative',
    width,
    height,
    backgroundColor: 'white',
    boxSizing: 'border-box',
    pageBreakAfter: 'always',
    breakAfter: 'page',
    pageBreakInside: 'avoid',
    breakInside: 'avoid',
    overflow: 'hidden',
    display: 'block',
    margin: '0 auto',
    boxShadow: debug ? '0 0 0 1px red' : undefined,
    ...combinedStyle,
  };

  // Add page break styles if needed
  if (pageBreak) {
    pageStyle.pageBreakBefore = 'always';
  }

  if (fixed) {
    pageStyle.position = 'fixed';
  }

  return (
    <div 
      id={id}
      className="pdf-page"
      style={pageStyle}
      data-size={size}
      data-orientation={orientation}
      data-wrap={wrap}
      data-debug={debug}
      data-dpi={dpi}
      data-min-presence-ahead={minPresenceAhead}
    >
      {children}
    </div>
  );
};

export default Page;
