import { ReportSection } from "~/types/global";
import { REPORT_CONSTANTS } from "./constants";

const combinedDataKey = REPORT_CONSTANTS.combined_data_keys.combined_data;
const reportSourcesKey = REPORT_CONSTANTS.combined_data_keys.report_sources_data;

export const extractCombinadoSectionsForTrash = (sections: ReportSection[]) => {
    const combinadoContainer = sections.find((section: any) =>
        section[combinedDataKey] || section[reportSourcesKey]
    );

    if (!combinadoContainer) return sections;

    if (Array.isArray((combinadoContainer as Repor)[combinedDataKey])) {
        return (combinadoContainer as any)[combinedDataKey];
    }

    return sections;
};