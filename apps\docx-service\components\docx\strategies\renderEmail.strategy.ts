import {
  ISectionOptions,
  Paragraph,
  TextRun,
  Table,
  TableRow,
  TableCell,
  WidthType,
} from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintEmailProps {
  section: Omit<ReportSection, "data"> & {
    data: Array<{
      detalhes?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
    }>;
  };
}

export const renderEmail = ({
  section,
}: RenderPrintEmailProps): ISectionOptions => {
  const allEmails =
    section.data?.flatMap(
      (entry) => entry.detalhes?.filter((detalhe) => !detalhe.is_deleted) || []
    ) || [];

  if (!allEmails.length) {
    return {
      children: [
        createSectionTitle(section.title),
        new Paragraph("Nenhuma informação encontrada."),
      ],
    };
  }

  const rows: TableRow[] = [];
  let cells: TableCell[] = [];

  allEmails.forEach((detalhe, index) => {
    const emailValue = Object.values(detalhe.value)[0]?.value;
    const sourceValue = Object.values(detalhe.value)[0]?.source;
    if (!emailValue) return;

    const cellContent = new Paragraph({
      children: [
        new TextRun({
          text: `EMAIL ${index + 1}`,
          bold: true,
          color: "889EA3",
        }),
        new TextRun({
          text: ` | ${sourceValue?.map(translateSource).join(", ")}`,
          size: 16,
          color: "FE473C",
        }),
        new TextRun({ text: `\n${emailValue}`, break: 1 }),
      ],
    });

    cells.push(new TableCell({ children: [cellContent] }));

    if (cells.length === 2) {
      rows.push(new TableRow({ children: cells }));
      cells = [];
    }
  });

  if (cells.length > 0) {
    rows.push(
      new TableRow({
        children: [...cells, new TableCell({ children: [new Paragraph("")] })],
      })
    );
  }

  const table = new Table({
    rows,
    width: { size: 100, type: WidthType.PERCENTAGE },
    columnWidths: [4500, 4500],
  });

  return {
    children: [createSectionTitle(section.title), table],
  };
};
