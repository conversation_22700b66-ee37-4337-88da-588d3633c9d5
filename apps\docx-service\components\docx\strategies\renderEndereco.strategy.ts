import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, WidthType } from "docx";import { ReportSection, ValueWithSource } from "../../../global";import { translatePropToLabel, translateSource, getFieldLabel, getFieldValue } from "../../../helpers";import { createSectionTitle } from "./utils";interface RenderPrintEnderecoProps {  section: Omit<ReportSection, 'data'> &  {    data: Array<{      detalhes?: Array<ValueWithSource<Record<string, ValueWithSource>>>;    }>  };}export const renderEndereco = ({ section }: RenderPrintEnderecoProps): ISectionOptions => {  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];  const allAddresses = section.data?.flatMap(entry =>    entry.detalhes?.filter(detalhe => !detalhe.is_deleted) || []  ) || [];  if (!allAddresses.length) {    return { children: [createSectionTitle(section.title), new Paragraph("Nenhuma informação encontrada.")] };  }  allAddresses.forEach((detalhe, index) => {    children.push(new Paragraph({         children: [            new TextRun({ text: `ENDEREÇO ${index + 1}`, bold: true, color: "889EA3"}),            new TextRun({ text: ` | ${detalhe.source?.map(translateSource).join(', ')}`, size: 16, color: "FE473C" })        ],        spacing: { before: 200 }    }));    const fields = Object.entries(detalhe.value).filter(([_, field]) => !field.is_deleted);    const rows: TableRow[] = [];    const cells: TableCell[] = [];    fields.forEach(([key, fieldValue]) => {        const cellContent = new Paragraph({            children: [                new TextRun({ text: `${translatePropToLabel(getFieldLabel(key, fieldValue)).toUpperCase()}`, bold: true, color: "889EA3" }),                new TextRun({ text: `\n${String(getFieldValue(fieldValue) || "")}`, break: 1 }),            ]        });        cells.push(new TableCell({ children: [cellContent] }));        if (cells.length === 2) {            rows.push(new TableRow({ children: cells.splice(0, 2) }));        }    });    if (cells.length > 0) {        rows.push(new TableRow({ children: [...cells, new TableCell({ children: [new Paragraph("")] })] }));    }    if (rows.length > 0) {        children.push(new Table({ rows, width: { size: 100, type: WidthType.PERCENTAGE }, columnWidths: [4500, 4500] }));    }  });  return { children };};