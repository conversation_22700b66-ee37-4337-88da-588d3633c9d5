#!/bin/bash
set -euo pipefail

# Database Initialization for Deployment Scripts
# This script is designed to be integrated into generate_deploy.sh

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_db_title() {
    echo ""
    echo "===================="
    echo "🗄️  $1"
    echo "===================="
}

print_db_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_db_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_db_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_db_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to wait for database to be ready
wait_for_database() {
    local max_attempts=30
    local attempt=1

    print_db_title "Waiting for Database Connection"

    while [ $attempt -le $max_attempts ]; do
        print_db_info "Attempt $attempt/$max_attempts - Checking database connectivity..."

        # Try to connect to PostgreSQL
        if docker exec $(docker ps -q -f "name=${STACK_NAME}_postgres") pg_isready -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
            print_db_success "Database is ready!"
            return 0
        fi

        print_db_warning "Database not ready yet, waiting 5 seconds..."
        sleep 5
        ((attempt++))
    done

    print_db_error "Database failed to become ready after $max_attempts attempts"
    return 1
}

# Function to check if Python environment is ready in backend container
check_python_environment() {
    local backend_container

    print_db_title "Checking Backend Container Environment"

    # Find backend container
    backend_container=$(docker ps -q -f "name=${STACK_NAME}_backend" | head -n 1)

    if [ -z "$backend_container" ]; then
        print_db_error "Backend container not found"
        return 1
    fi

    print_db_info "Backend container found: $backend_container"

    # Check if Python is available
    if docker exec "$backend_container" python3 --version >/dev/null 2>&1; then
        print_db_success "Python environment is ready"
        return 0
    else
        print_db_error "Python environment not ready in backend container"
        return 1
    fi
}

# Function to initialize database using the backend container
initialize_database() {
    local backend_container
    local init_result

    print_db_title "Initializing Database Schema"

    # Find backend container
    backend_container=$(docker ps -q -f "name=${STACK_NAME}_backend" | head -n 1)

    if [ -z "$backend_container" ]; then
        print_db_error "Backend container not found for database initialization"
        return 1
    fi

    print_db_info "Using backend container: $backend_container"

    # Run database setup script
    print_db_info "Running database initialization..."

    if docker exec "$backend_container" python3 database/setup_db.py --no-interactive --verbose; then
        print_db_success "Database initialization completed successfully!"
        return 0
    else
        print_db_warning "Database setup script failed, trying fallback method..."

        # Fallback: try direct Python initialization
        if docker exec "$backend_container" python3 -c "
import asyncio
import sys
sys.path.append('/app')
from database.auto_init import auto_initialize_database

async def main():
    result = await auto_initialize_database()
    if result['errors']:
        print('❌ Initialization failed:')
        for error in result['errors']:
            print(f'  - {error}')
        sys.exit(1)
    else:
        print('✅ Database initialized successfully')
        if result['is_first_run']:
            print('🚀 First run - schema created')
        sys.exit(0)

asyncio.run(main())
"; then
            print_db_success "Database initialized using fallback method!"
            return 0
        else
            print_db_error "Database initialization failed!"
            return 1
        fi
    fi
}

# Function to validate database initialization
validate_database() {
    local backend_container

    print_db_title "Validating Database Schema"

    backend_container=$(docker ps -q -f "name=${STACK_NAME}_backend" | head -n 1)

    if [ -z "$backend_container" ]; then
        print_db_error "Backend container not found for validation"
        return 1
    fi

    # Run database status check
    if docker exec "$backend_container" python3 database/setup_db.py --check-only --no-interactive; then
        print_db_success "Database validation passed!"
        return 0
    else
        print_db_error "Database validation failed!"
        return 1
    fi
}

# Main function to be called from generate_deploy.sh
setup_database_for_deployment() {
    local profile="${1:-dev}"

    print_db_title "Database Setup for Deployment (Profile: $profile)"

    # Set default stack name if not provided
    STACK_NAME=${STACK_NAME:-mystack}
    DB_USER=${DB_USER:-postgres}
    DB_NAME=${DB_NAME:-keycloak}  # Note: Using shared Keycloak database with reports schema

    print_db_info "Stack Name: $STACK_NAME"
    print_db_info "Database: $DB_NAME"
    print_db_info "User: $DB_USER"

    # Step 1: Wait for database to be ready
    if ! wait_for_database; then
        print_db_error "Database connection failed - cannot continue"
        return 1
    fi

    # Step 2: Wait a bit more for the backend container to be fully ready
    print_db_info "Waiting for backend container to be ready..."
    sleep 10

    # Step 3: Check Python environment
    local max_python_attempts=12
    local python_attempt=1

    while [ $python_attempt -le $max_python_attempts ]; do
        if check_python_environment; then
            break
        fi

        print_db_warning "Python environment not ready (attempt $python_attempt/$max_python_attempts), waiting..."
        sleep 5
        ((python_attempt++))
    done

    if [ $python_attempt -gt $max_python_attempts ]; then
        print_db_error "Python environment failed to become ready"
        return 1
    fi

    # Step 4: Initialize database
    if ! initialize_database; then
        print_db_error "Database initialization failed"
        return 1
    fi

    # Step 5: Validate database
    if ! validate_database; then
        print_db_warning "Database validation failed, but initialization may have succeeded"
        # Don't fail here - validation might fail for non-critical reasons
    fi

    print_db_success "Database setup completed successfully!"
    return 0
}

# If script is run directly (not sourced), run the setup
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    setup_database_for_deployment "${1:-dev}"
fi