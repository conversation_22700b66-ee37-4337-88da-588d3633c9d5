import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintRedesSociaisProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      detalhes: Array<{
        value: {
          [socialMedia: string]: Array<{
            [fieldKey: string]: ValueWithSource;
          }>;
        };
        label: string;
        source: string[];
        is_deleted: boolean;
      }>;
    }>;
  };
}

export const renderRedesSociais = ({ section }: RenderPrintRedesSociaisProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((dataItem) => {
    dataItem.detalhes.filter(d => !d.is_deleted).forEach(detalhe => {
        Object.entries(detalhe.value).forEach(([platform, profiles]) => {
            if (!Array.isArray(profiles) || profiles.length === 0) return;
            children.push(new Paragraph({text: platform.toUpperCase(), style: "subtitle"}));
            profiles.forEach((profile, index) => {
                children.push(new Paragraph({text: `${platform.toUpperCase()} ${index + 1}`}));
                const profileRows = Object.entries(profile).filter(([_, f]) => !f.is_deleted).map(([key, field]) => new TableRow({children: [new TableCell({children: [new Paragraph(translatePropToLabel(field.label || key))]}), new TableCell({children: [new Paragraph(String(field.value))]})]}))
                if(profileRows.length > 0) {
                    children.push(new Table({rows: profileRows, width: {size: 100, type: WidthType.PERCENTAGE}}));
                }
            });
        });
    });
  });

  return { children };
};
