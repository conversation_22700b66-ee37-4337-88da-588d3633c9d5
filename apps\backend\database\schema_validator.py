"""
Schema Validation Utilities for Database Operations

This module provides utilities to validate database schema configurations
and ensure consistency with established patterns.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Set
from sqlalchemy import inspect, MetaData, Table
from sqlalchemy.engine import Engine
from sqlalchemy.dialects.postgresql import UUID, JSONB
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Result of schema validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: List[str]

class SchemaValidator:
    """Validates database schema against established patterns"""

    # Required patterns for reports schema tables
    REQUIRED_PATTERNS = {
        'schema': 'reports',
        'primary_key_suffix': '_id',
        'timestamp_columns': ['created_at'],
        'uuid_primary_keys': True,
        'foreign_key_format': r'^reports\.[a-z_]+\.[a-z_]+_id$'
    }

    # Recommended naming conventions
    NAMING_CONVENTIONS = {
        'table_names': r'^[a-z_]+$',  # lowercase with underscores
        'column_names': r'^[a-z_]+$',  # lowercase with underscores
        'index_names': r'^(idx_|ix_)[a-z_]+_[a-z_]+(_(desc|asc))?$',  # idx_ or ix_ prefix
        'constraint_names': r'^[a-z_]+_(unique|fkey|pkey|check)$'
    }

    # Common anti-patterns to detect
    ANTI_PATTERNS = {
        'missing_created_at': 'Table should have created_at timestamp',
        'non_uuid_primary_key': 'Primary key should be UUID type',
        'missing_schema': 'Table should be in reports schema',
        'no_indexes': 'Table should have appropriate indexes',
        'missing_foreign_keys': 'References should use proper foreign keys'
    }

    def __init__(self, engine: Engine):
        self.engine = engine
        self.inspector = inspect(engine)

    def validate_table(self, table_name: str, schema: str = 'reports') -> ValidationResult:
        """
        Validate a specific table against established patterns.

        Args:
            table_name: Name of the table to validate
            schema: Schema name (default: reports)

        Returns:
            ValidationResult with validation details
        """
        errors = []
        warnings = []
        suggestions = []

        try:
            # Check if table exists
            if not self.inspector.has_table(table_name, schema=schema):
                errors.append(f"Table {schema}.{table_name} does not exist")
                return ValidationResult(False, errors, warnings, suggestions)

            # Get table information
            columns = self.inspector.get_columns(table_name, schema=schema)
            primary_keys = self.inspector.get_pk_constraint(table_name, schema=schema)
            foreign_keys = self.inspector.get_foreign_keys(table_name, schema=schema)
            indexes = self.inspector.get_indexes(table_name, schema=schema)
            unique_constraints = self.inspector.get_unique_constraints(table_name, schema=schema)

            # Validate naming conventions
            self._validate_naming(table_name, errors, warnings)

            # Validate primary key
            self._validate_primary_key(primary_keys, columns, errors, suggestions)

            # Validate required columns
            self._validate_required_columns(columns, table_name, warnings, suggestions)

            # Validate foreign keys
            self._validate_foreign_keys(foreign_keys, errors, suggestions)

            # Validate indexes
            self._validate_indexes(indexes, table_name, warnings, suggestions)

            # Check for common patterns
            self._check_common_patterns(columns, table_name, suggestions)

        except Exception as e:
            errors.append(f"Error validating table {table_name}: {str(e)}")

        is_valid = len(errors) == 0
        return ValidationResult(is_valid, errors, warnings, suggestions)

    def validate_all_tables(self, schema: str = 'reports') -> Dict[str, ValidationResult]:
        """
        Validate all tables in the specified schema.

        Args:
            schema: Schema to validate (default: reports)

        Returns:
            Dictionary mapping table names to their validation results
        """
        results = {}

        try:
            table_names = self.inspector.get_table_names(schema=schema)

            for table_name in table_names:
                results[table_name] = self.validate_table(table_name, schema)

        except Exception as e:
            logger.error(f"Error validating schema {schema}: {str(e)}")

        return results

    def _validate_naming(self, table_name: str, errors: List[str], warnings: List[str]) -> None:
        """Validate naming conventions"""
        if not re.match(self.NAMING_CONVENTIONS['table_names'], table_name):
            warnings.append(f"Table name '{table_name}' doesn't follow naming convention (lowercase with underscores)")

    def _validate_primary_key(self, primary_keys: Dict, columns: List[Dict], errors: List[str], suggestions: List[str]) -> None:
        """Validate primary key configuration"""
        if not primary_keys or not primary_keys.get('constrained_columns'):
            errors.append("Table must have a primary key")
            return

        pk_columns = primary_keys['constrained_columns']

        # For single-column primary keys, check if it follows naming convention
        if len(pk_columns) == 1:
            pk_column = pk_columns[0]

            # Find the column definition
            pk_col_def = next((col for col in columns if col['name'] == pk_column), None)

            if pk_col_def:
                # Check if UUID type
                col_type = str(pk_col_def['type'])
                if 'UUID' not in col_type:
                    suggestions.append(f"Consider using UUID type for primary key '{pk_column}'")

                # Check naming convention
                if not pk_column.endswith('_id'):
                    suggestions.append(f"Primary key '{pk_column}' should end with '_id'")

    def _validate_required_columns(self, columns: List[Dict], table_name: str, warnings: List[str], suggestions: List[str]) -> None:
        """Validate presence of commonly required columns"""
        column_names = [col['name'] for col in columns]

        # Check for timestamp columns
        if 'created_at' not in column_names:
            suggestions.append("Consider adding 'created_at' timestamp column")

        # For tables that might be updated, suggest modified_at
        if table_name not in ['user_columns_hmac', 'folder_hmac', 'report_executions']:
            if 'modified_at' not in column_names:
                suggestions.append("Consider adding 'modified_at' timestamp column for auditing")

    def _validate_foreign_keys(self, foreign_keys: List[Dict], errors: List[str], suggestions: List[str]) -> None:
        """Validate foreign key configuration"""
        for fk in foreign_keys:
            referred_schema = fk.get('referred_schema')
            referred_table = fk.get('referred_table')

            if referred_schema != 'reports':
                errors.append(f"Foreign key should reference reports schema, found: {referred_schema}")

            # Check if following naming conventions
            fk_name = fk.get('name', '')
            if fk_name and not fk_name.endswith('_fkey'):
                suggestions.append(f"Foreign key constraint '{fk_name}' should follow naming convention")

    def _validate_indexes(self, indexes: List[Dict], table_name: str, warnings: List[str], suggestions: List[str]) -> None:
        """Validate index configuration"""
        if not indexes:
            suggestions.append("Consider adding indexes for frequently queried columns")
            return

        for index in indexes:
            index_name = index['name']

            # Check naming convention
            if not (index_name.startswith('idx_') or index_name.startswith('ix_')):
                warnings.append(f"Index '{index_name}' doesn't follow naming convention")

    def _check_common_patterns(self, columns: List[Dict], table_name: str, suggestions: List[str]) -> None:
        """Check for common patterns and suggest improvements"""
        column_names = [col['name'] for col in columns]

        # Check for JSONB columns without GIN indexes
        jsonb_columns = [col['name'] for col in columns if 'JSONB' in str(col['type'])]
        if jsonb_columns:
            suggestions.append("Consider adding GIN indexes for JSONB columns for better query performance")

        # Suggest common columns for user-related tables
        if 'user_id' in column_names and table_name != 'users':
            suggestions.append("User-related tables should have appropriate user_id indexes")

    def generate_validation_report(self, schema: str = 'reports') -> str:
        """
        Generate a comprehensive validation report.

        Args:
            schema: Schema to validate

        Returns:
            Formatted validation report
        """
        results = self.validate_all_tables(schema)

        report = f"# Database Schema Validation Report\n"
        report += f"Schema: {schema}\n"
        report += f"Tables validated: {len(results)}\n\n"

        valid_tables = sum(1 for result in results.values() if result.is_valid)
        report += f"✅ Valid tables: {valid_tables}\n"
        report += f"⚠️  Tables with issues: {len(results) - valid_tables}\n\n"

        for table_name, result in results.items():
            status = "✅ VALID" if result.is_valid else "❌ INVALID"
            report += f"## {table_name} - {status}\n"

            if result.errors:
                report += "### Errors:\n"
                for error in result.errors:
                    report += f"- ❌ {error}\n"
                report += "\n"

            if result.warnings:
                report += "### Warnings:\n"
                for warning in result.warnings:
                    report += f"- ⚠️  {warning}\n"
                report += "\n"

            if result.suggestions:
                report += "### Suggestions:\n"
                for suggestion in result.suggestions:
                    report += f"- 💡 {suggestion}\n"
                report += "\n"

        return report

    def check_table_dependencies(self, table_name: str, schema: str = 'reports') -> Dict[str, List[str]]:
        """
        Check dependencies for a table (what it references and what references it).

        Args:
            table_name: Table to check
            schema: Schema name

        Returns:
            Dictionary with 'references' and 'referenced_by' lists
        """
        dependencies = {
            'references': [],  # Tables this table references
            'referenced_by': []  # Tables that reference this table
        }

        try:
            # Get what this table references
            foreign_keys = self.inspector.get_foreign_keys(table_name, schema=schema)
            for fk in foreign_keys:
                referred_table = fk.get('referred_table')
                if referred_table:
                    dependencies['references'].append(referred_table)

            # Get what references this table
            all_tables = self.inspector.get_table_names(schema=schema)
            for other_table in all_tables:
                if other_table == table_name:
                    continue

                other_fks = self.inspector.get_foreign_keys(other_table, schema=schema)
                for fk in other_fks:
                    if fk.get('referred_table') == table_name:
                        dependencies['referenced_by'].append(other_table)

        except Exception as e:
            logger.error(f"Error checking dependencies for {table_name}: {str(e)}")

        return dependencies

def validate_model_definition(model_class) -> ValidationResult:
    """
    Validate a SQLAlchemy model definition against established patterns.

    Args:
        model_class: SQLAlchemy model class to validate

    Returns:
        ValidationResult with validation details
    """
    errors = []
    warnings = []
    suggestions = []

    try:
        # Check table args
        table_args = getattr(model_class, '__table_args__', None)
        if not table_args:
            warnings.append("Model should define __table_args__ with schema")
        else:
            # Check for schema specification
            schema_found = False
            if isinstance(table_args, dict):
                schema_found = table_args.get('schema') == 'reports'
            elif isinstance(table_args, tuple):
                for arg in table_args:
                    if isinstance(arg, dict) and arg.get('schema') == 'reports':
                        schema_found = True
                        break

            if not schema_found:
                errors.append("Model must specify schema='reports' in __table_args__")

        # Check primary key
        if hasattr(model_class, '__table__'):
            table = model_class.__table__

            # Check schema
            if table.schema != 'reports':
                errors.append(f"Table schema should be 'reports', found: {table.schema}")

            # Check primary key columns
            pk_columns = list(table.primary_key.columns)
            if not pk_columns:
                errors.append("Model must have a primary key")

            # Check for created_at column
            if 'created_at' not in table.columns:
                suggestions.append("Consider adding created_at timestamp column")

    except Exception as e:
        errors.append(f"Error validating model: {str(e)}")

    is_valid = len(errors) == 0
    return ValidationResult(is_valid, errors, warnings, suggestions)