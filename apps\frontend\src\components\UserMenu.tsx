import {
  <PERSON><PERSON>,
  Cham<PERSON>B<PERSON>,
  List,
  Separator,
  Text,
  UserMenu as UserProfile,
  useModalControl
} from "@snap/design-system";
import { formatIsoDate, getInitials } from "~/helpers";
import { USER_CONSTANTS } from "~/helpers/constants";
import { useUserData } from "~/store/userStore";
import { Tooltip, TooltipTrigger, TooltipContent } from "./ui/tooltip";
import { Info } from "lucide-react";
import { ConsultasInfoDialog } from "./ConsultasInfoDialog";

interface UserMenuProps {
  userMenuContent: React.ReactNode;
}

export default function UserMenu({ userMenuContent }: UserMenuProps) {
  const { open } = useModalControl();
  const userData = useUserData();
  const userImage = userData?.[USER_CONSTANTS.user_data.image as keyof typeof userData] as string;
  const userName = userData?.[USER_CONSTANTS.user_data.name as keyof typeof userData] as string;
  const userCredits = userData?.[USER_CONSTANTS.user_data.credits_minimun as keyof typeof userData] as number;
  const initials = getInitials(userName);
  const userCreditsResetDate = userData?.[USER_CONSTANTS.user_data.next_reset_credits as keyof typeof userData] as string;
  const userProfile = userData?.[USER_CONSTANTS.user_data.role as keyof typeof userData] as string;
  const isUserStandalone = userProfile === USER_CONSTANTS.profile_types.standalone;

  const getValidImageUrl = (image: string | null | undefined) => {
    return image || undefined;
  };

  const renderConsultasInfo = () => {
    return (
      <Tooltip delayDuration={0}>
        <TooltipTrigger className="cursor-help" asChild>
          <Info size={14} className="text-accent" />
        </TooltipTrigger>
        <TooltipContent side="bottom" className="!p-0 !bg-transparent !shadow-none !border-none max-w-[288px]">
          <ChamferBox corner="topLeft" className="rounded-md px-0.5 pt-0.5 bg-neutral-800">
            <div className="relative z-10">
              <Text className="text-left text-netral-100">
                Você possui <span className="font-bold"><span className="text-accent">{`${!isUserStandalone ? "*" : ""}`}</span>{` ${userCredits} consultas `}</span>
              </Text>
              <Text className="text-left text-netral-100">
                válidas até a data <span className="font-semibold">{formatIsoDate(userCreditsResetDate, true)}</span>.
              </Text>
              {
                !isUserStandalone && (
                  <div>
                    <Separator className="my-2 border-border" />
                    <Text className="text-left text-neutral-200">
                      <span className="font-bold text-accent">*</span> {`Saiba mais sobre as consultas disponíveis `}
                      <span className="text-accent underline cursor-pointer" onClick={handleOpenConsultasInfo} title="Abrir mais informações de consultas">
                        clicando aqui.
                      </span>
                    </Text>

                  </div>
                )
              }
            </div>
          </ChamferBox>
        </TooltipContent>
      </Tooltip>
    );
  };

  const handleOpenConsultasInfo = () => {
    open({
      modal: () => ({
        title: "INFORMAÇÕES SOBRE CONSULTAS",
        content: <ConsultasInfoDialog.Content />,
        footer: <ConsultasInfoDialog.Footer />,
      }),
      config: {
        content: {
          className: "max-w-xl",
        },
      },
    });
  };

  const renderUserProfile = () => {
    const profileProps = {
      Profile: (
        <div className="flex items-center gap-3 w-full">
          <Avatar
            size="sm"
            className="size-9 hidden md:flex"
            src={getValidImageUrl(userImage)}
            fallback={initials || "NA"}
            textAlign="left"
          />
          <List className="space-y-0.5 items-start">
            <span className="text-sm leading-tight">
              {userName || "Sem nome"}
            </span>
            <Separator />
            <div className="flex items-end gap-2">
              <Text className="opacity-80">
                {`${userCredits || 0} Consultas`}
              </Text>
              {renderConsultasInfo()}
            </div>
          </List>
        </div>
      ),
      Menu: userMenuContent,
    };

    return <UserProfile {...profileProps} menuClassName="py-0 px-0" />;
  };

  return renderUserProfile();
}
