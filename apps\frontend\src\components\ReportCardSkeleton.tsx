import { Skeleton } from "~/components/ui/skeleton"
import { Card } from "~/components/ui/card"

export function AnimatedFilledButtonSkeleton() {
  return (
    <div className="opacity-70">
      <Card className="max-w-[282px] border-0 shadow-md p-6 min-h-[288px] flex flex-col justify-between">
      </Card>
    </div>
  )
}

export function ReportCardSkeleton() {
  return (
    <div className="opacity-70">
      <Card className="max-w-[282px] border-0 shadow-md">
        {/* Header */}
        <div className="bg-neutral-700 p-3">
          <div className="flex items-center justify-between gap-3">
            <Skeleton className="h-8 w-8 rounded" />
            <Skeleton className="h-4 flex-1" />
          </div>
        </div>
        
        {/* Avatar section */}
        <div className="p-3">
          <div className="flex items-center justify-between gap-2">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="flex-1">
              <Skeleton className="h-4 w-full mb-1" />
              <Skeleton className="h-3 w-3/4" />
            </div>
          </div>
        </div>
        
        {/* Content list */}
        <div className="px-3 pb-3 space-y-2">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex justify-between items-center py-1">
              <Skeleton className="h-3 w-16" />
              <Skeleton className="h-3 w-20" />
            </div>
          ))}
        </div>
        
        {/* Footer */}
        <div className="p-3 pt-0">
          <Skeleton className="h-9 w-full rounded-md" />
          <div className="flex justify-between mt-2">
            <Skeleton className="h-3 w-20" />
            <Skeleton className="h-3 w-20" />
          </div>
        </div>
      </Card>
    </div>
  )
}