import { create } from "zustand";

interface SecretKeyState {
  secretKey: string | null;
  isUsingTemporaryKey: boolean;
  actions: {
    setSecretKey: (key: string) => void;
    clearSecretKey: () => void;
    setIsUsingTemporaryKey: (isUsingTemporaryKey: boolean) => void;
  };
}

const useSecretKeyStore = create<SecretKeyState>((set) => ({
  secretKey: null,
  isUsingTemporaryKey: false,
  actions: {
    setSecretKey: (key) => set({ secretKey: key }),
    clearSecretKey: () => set({ secretKey: null }),
    setIsUsingTemporaryKey: (isUsingTemporaryKey) =>
      set({ isUsingTemporaryKey }),
  },
}));

export const useSecretKey = () => useSecretKeyStore((state) => state.secretKey);
export const useSecretKeyActions = () =>
  useSecretKeyStore((state) => state.actions);
export const useIsUsingTemporaryKey = () =>
  useSecretKeyStore((state) => state.isUsingTemporaryKey);
