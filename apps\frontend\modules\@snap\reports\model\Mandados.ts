import { ValueWithSource } from "./ValueWithSource";

interface _PessoaData {
  alcunha: ValueWithSource<string>;
  "data de nascimento": ValueWithSource<string>;
  filiacao: ValueWithSource<string>;
  naturalidade: ValueWithSource<string>;
  nome_completo: ValueWithSource<string>;
  pele: ValueWithSource<string>;
  rg: ValueWithSource<string>;
  rji: ValueWithSource<string>;
  sexo: ValueWithSource<string>;
}

interface _ProcessoData {
  "número do processo": ValueWithSource<string>;
}

interface _TelefoneData {
  numero: ValueWithSource<string>;
}

interface _DetalhesData {
  "data de validade": ValueWithSource<string>;
  "especie de prisao": ValueWithSource<string>;
  "número do processo": ValueWithSource<string>;
  "orgão expedidor": ValueWithSource<string>;
  "tipificacoes penais": ValueWithSource<string>;
}

export interface Mandado {
  numero: ValueWithSource<string>;
  detalhes: _DetalhesData;
  pessoa?: Array<ValueWithSource<_PessoaData>>;
  processos?: Array<ValueWithSource<_ProcessoData>>;
  telefones?: Array<ValueWithSource<_TelefoneData>>;
}