import { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, ChamferBox, Loading } from "@snap/design-system";
import { Card, CardContent, CardHeader } from "~/components/ui/card";
import { FaGoogle, FaMicrosoft } from "react-icons/fa";
import { redirectToAuthServer } from "~/services/gateways/auth.gateway";
import icon from "~/assets/reports_logo.svg";
import logo from "~/assets/logo.svg";
import { useQueryClient } from "@tanstack/react-query";
import { useUserActions } from "~/store/userStore";
import { useSecretKeyActions } from "~/store/secretKeyStore";

const LoginContainer = () => {
  const mounted = useRef(false);
  const queryClient = useQueryClient();
  const { clearUser } = useUserActions();
  const { clearSecretKey } = useSecretKeyActions();
  const [loadingProvider, setLoadingProvider] = useState<
    "microsoft" | "google" | null
  >(null);

  const clearStorages = () => {
    if (!document) return;
    try {
      clearSecretKey();
      queryClient.clear();
      clearUser();
      localStorage.clear();
      sessionStorage.clear();
    } catch (error) {
      console.error("Erro ao limpar os storages:", error);
    }
  };

  useEffect(() => {
    if (mounted.current) return;
    clearStorages();
    mounted.current = true;
  }, []);

  const handleProviderLogin = async (provider: "microsoft" | "google") => {
    setLoadingProvider(provider);
    try {
      await redirectToAuthServer({ provider });
    } catch (error) {
      console.error(
        "Erro ao redirecionar para o provedor de autenticação:",
        error
      );
      setLoadingProvider(null);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <ChamferBox
        corner="bottomRight"
        className="[&>div:first-child]:p-0 max-w-fit rounded-xl"
      >
        <Card className="w-full max-w-md bg-background border-none ">
          <CardHeader className="p-8">
            <div className="flex items-start gap-4">
              <img
                width={200}
                src={logo}
                alt="SNAP Reports logo"
                className="" //TODO - voltar com sm depois "hidden sm:block"
              />
              <img src={icon} alt="SNAP Reports icon" />
              <h1 className="text-xl leading-none font-mono">REPORTS</h1>
            </div>
          </CardHeader>
          <CardContent className="p-8 border-t-2 border-border border-dashed">
            <div className="flex flex-col gap-4">
              <Button
                variant="outline"
                className="uppercase hover:!bg-transparent hover:!text-foreground hover:!opacity-80 transition-opacity"
                onClick={() => handleProviderLogin("microsoft")}
                icon={
                  loadingProvider === "microsoft" ? (
                    <Loading size="sm" />
                  ) : (
                    <FaMicrosoft className="mr-2 h-4 w-4" />
                  )
                }
                iconPosition="right"
                data-testid="button-login-microsoft"
              >
                entrar com sua conta microsoft
              </Button>
              <Button
                variant="outline"
                className="uppercase hover:!bg-transparent hover:!text-foreground hover:!opacity-80 transition-opacity"
                onClick={() => handleProviderLogin("google")}
                icon={
                  loadingProvider === "google" ? (
                    <Loading size="sm" />
                  ) : (
                    <FaGoogle className="mr-2 h-4 w-4" />
                  )
                }
                iconPosition="right"
                data-testid="button-login-google"
              >
                entrar com sua conta google
              </Button>
            </div>
          </CardContent>
        </Card>
      </ChamferBox>
    </div>
  );
};

export default LoginContainer;
