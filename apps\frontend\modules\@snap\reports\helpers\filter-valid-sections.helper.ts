import { REPORT_SECTIONS } from '../config/constants';

export const filterValidSections = (sections: any[], strategyMap: Record<string, any>) => {
  return sections.filter((section) => {
    // Skip process subsections
    if (section.title === REPORT_SECTIONS.processos && !!section.subsection) {
      return false;
    }

    // Only include sections with strategies
    const strategy = strategyMap[section.title];
    if (!strategy) {
      console.warn(`Sem estratégia de renderização para seção "${section.title}"`);
      return false;
    }

    return true;
  });
};

export const createSectionKey = (sectionTitle: string): string => {
  return sectionTitle.replace(/\s+/g, '-').toLowerCase();
};
