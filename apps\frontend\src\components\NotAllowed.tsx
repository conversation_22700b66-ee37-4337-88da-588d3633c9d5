import { Text } from "@snap/design-system";
import { ShieldX } from "lucide-react";

const NotAllowed = () => {
  return (
    <div className="flex flex-col justify-center gap-8 items-center text-gray-300">
      <div className="p-4 rounded-full bg-destructive/10">
        <ShieldX size={64} className="text-destructive" />
      </div>
      <Text variant="label-lg" align="center">Você não possui permissão para acessar o conteúdo esta página.</Text>
    </div>
  );
};

export default NotAllowed;
