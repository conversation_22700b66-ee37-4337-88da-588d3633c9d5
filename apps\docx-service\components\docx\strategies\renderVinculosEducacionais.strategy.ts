import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintVinculosEducacionais {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      empresa_educadora?: {
        value: string;
        label: string;
        source: string[];
        is_deleted: boolean;
      };
      detalhes?: {
        [key: string]: {
          value: string;
          label: string;
          source: string[];
          is_deleted: boolean;
        }
      };
      [key: string]: any;
    }>;
  };
}

export const renderVinculosEducacionais = ({ section }: RenderPrintVinculosEducacionais): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((vinculo) => {
    if (vinculo.empresa_educadora && !vinculo.empresa_educadora.is_deleted) {
      children.push(new Paragraph({
        children: [
          new TextRun({ text: `${(vinculo.empresa_educadora.label || "Empresa Educadora").toUpperCase()}: `, bold: true, color: "FE473C" }),
          new TextRun(String(vinculo.empresa_educadora.value)),
        ],
        spacing: { after: 200 }
      }));
    }

    if (vinculo.detalhes) {
      const tableRows = Object.entries(vinculo.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => {
          return new TableRow({
            children: [
              new TableCell({ children: [new Paragraph(translatePropToLabel(field.label || key).toUpperCase())] }),
              new TableCell({ children: [new Paragraph(String(field.value))] }),
            ],
          });
        });
      if (tableRows.length > 0) {
        children.push(new Table({ rows: tableRows, width: { size: 100, type: WidthType.PERCENTAGE } }));
      }
    }
  });

  return { children };
};
