import { BaseValidator } from '../BaseValidator';
import { ValueWithSource } from '../../global.d';

export interface Socio {
  nome_completo?: ValueWithSource<string>;
  razao_social?: ValueWithSource<string>;
  detalhes?: Record<string, ValueWithSource>;
  empresa?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
  [key: string]: any;
}

/**
 * Validator for Socio data
 */
export class SocioValidator extends BaseValidator<Socio> {
  isValidValueWithSource(obj: any): boolean {
    return (
      obj && 
      typeof obj === 'object' && 
      'value' in obj && 
      'label' in obj && 
      'source' in obj &&
      Array.isArray(obj.source)
    );
  }

  /**
   * Validates if a socio entry has the expected structure
   */
  isValid(entry: any): entry is Socio {
    if (!entry || typeof entry !== 'object') return false;

    const hasValidMainField = 
      (entry.nome_completo && this.isValidValueWithSource(entry.nome_completo)) || 
      (entry.razao_social && this.isValidValueWithSource(entry.razao_social));

    const hasValidDetalhes = !entry.detalhes || (
      typeof entry.detalhes === 'object' && 
      Object.values(entry.detalhes).every(field => this.isValidValueWithSource(field))
    );

    const hasValidEmpresa = !entry.empresa || (
      Array.isArray(entry.empresa) && 
      entry.empresa.every((item: ValueWithSource<Record<string, ValueWithSource>>) => 
        this.isValidValueWithSource(item) && 
        typeof item.value === 'object' &&
        Object.values(item.value).every((field: any) => this.isValidValueWithSource(field))
      )
    );

    return hasValidMainField && hasValidDetalhes && hasValidEmpresa;
  }

  /**
   * Ensures all values in a socio entry are properly stringified
   * to prevent React rendering errors
   */
  sanitize(entry: Socio): Socio {
    const result = { ...entry };

    if (result.nome_completo?.value !== undefined) {
      result.nome_completo = {
        ...result.nome_completo,
        value: String(result.nome_completo.value || '')
      };
    }

    if (result.razao_social?.value !== undefined) {
      result.razao_social = {
        ...result.razao_social,
        value: String(result.razao_social.value || '')
      };
    }

    if (result.detalhes && typeof result.detalhes === 'object') {
      const sanitizedDetalhes: Record<string, ValueWithSource> = {};
      
      Object.entries(result.detalhes).forEach(([key, field]) => {
        sanitizedDetalhes[key] = {
          ...field,
          value: String(field.value || '')
        };
      });
      
      result.detalhes = sanitizedDetalhes;
    }

    if (result.empresa && Array.isArray(result.empresa)) {
      result.empresa = result.empresa.map(empresa => {
        if (empresa?.value && typeof empresa.value === 'object') {
          const sanitizedValue: Record<string, ValueWithSource> = {};
          
          Object.entries(empresa.value).forEach(([key, field]) => {
            sanitizedValue[key] = {
              ...field,
              value: String(field.value || '')
            };
          });
          
          return {
            ...empresa,
            value: sanitizedValue as unknown as Record<string, ValueWithSource>
          };
        }
        return empresa;
      }) as Array<ValueWithSource<Record<string, ValueWithSource>>>;
    }

    return result;
  }
}