import logging
logger = logging.getLogger(__name__)
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from core.config import settings
from models import Base
from exceptions.business_exceptions import DatabaseUnavailableError
import sqlalchemy as sa


MAX_RETRIES = 3
DATABASE_URL = settings.DATABASE_URL


def get_database_async_engine():
    _engine = create_async_engine(
        DATABASE_URL,
        echo=True,
        pool_pre_ping=True,
        pool_recycle=1800,
        pool_size=settings.DB_POOL_SIZE,
        max_overflow=settings.DB_MAX_OVERFLOW,
        pool_timeout=settings.DB_POOL_TIMEOUT,
        pool_reset_on_return='commit'
        # Schema is handled at model level - all models specify schema='reports'
        # This avoids asyncpg connection parameter conflicts
    )
    return _engine


engine = get_database_async_engine()
async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

async def get_db():
    logger.debug("[get_db] Opening DB session")
    try:
        async with async_session() as session:
            yield session
    except SQLAlchemyError as e: # Catch only database-related errors
        # ------------------------
            logger.error("[get_db] Failed to create a database session: %s", e)
            # You might want to raise a 503 here, as the DB is unavailable
            raise DatabaseUnavailableError()
    finally:
        logger.debug("[get_db] Closing DB session")


async def init_db():
    """
    Initialize database tables.
    In development, this creates all tables.
    In production, you should use Alembic migrations instead.
    """
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
            logger.info("[init_db] Database tables created successfully.")
    except Exception as e:
        logger.error("[init_db] Failed to initialize database: %s", e)
        raise


async def check_db_connection():
    """Check if database connection is working"""
    try:
        async with engine.begin() as conn:
            await conn.execute(sa.text("SELECT 1"))
            logger.info("[check_db_connection] Database connection successful")
            return True
    except Exception as e:
        logger.error("[check_db_connection] Database connection failed: %s", e)
        return False


async def get_pool_status():
    """Get current pool status for monitoring"""
    try:
        pool = engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
            "total_connections": pool.checkedin() + pool.checkedout() + pool.overflow()
        }
    except Exception as e:
        logger.error("[get_pool_status] Failed to get pool status: %s", e)
        return {"error": str(e)}

