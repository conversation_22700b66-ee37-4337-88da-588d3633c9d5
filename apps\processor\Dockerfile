FROM python:3.12-slim
LABEL authors="gbrabelo"

ARG DEV_MODE=true
ARG INSTALL_PATH=/opt/processor/reports_processor/
ENV WAIT_ON_KAFKA=${INSTALL_PATH}/scripts/wait_until_kafka_alive.sh

COPY ./apps/processor/ ${INSTALL_PATH}
COPY ./scripts/wait_until_kafka_alive.sh ${INSTALL_PATH}/scripts/
WORKDIR $INSTALL_PATH
RUN pip install -r requirements.txt

COPY ./constants/* ${INSTALL_PATH}/reports_processor/


CMD ["/bin/bash", "scripts/spark_init.sh"]
