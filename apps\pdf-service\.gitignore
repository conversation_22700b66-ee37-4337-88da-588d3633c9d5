# Auto-generated .gitignore file.
# See https://gowebly.org for more information.

# macOS files.
.DS_Store

# IDE files.
.idea/
.vscode/

package-lock.json

# Go workflow files.
go.work

# Environment files.
.env

# Generated folders.
bin/
dist-ssr/
dist/
pb_data/

# Node modules.
node_modules/

# Vendor folders.
vendor/

# Temp folders.
.parcel-cache/
tmp/

# Logs.
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Ignore by extensions.
*.exe
*.exe~
*.dll
*.so
*.dylib
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.local
*.lockb
*.test
*.tmp
*.out
*.lock