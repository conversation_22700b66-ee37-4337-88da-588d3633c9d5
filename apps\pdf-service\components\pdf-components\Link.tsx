import React from 'react';

export interface LinkProps {
  id?: string;
  style?: React.CSSProperties | React.CSSProperties[];
  /**
   * Render component in all wrapped pages.
   */
  fixed?: boolean;
  /**
   * Force the wrapping algorithm to start a new page when rendering the
   * element.
   */
  break?: boolean;
  /**
   * Hint that no page wrapping should occur between all sibling elements following the element within n points
   */
  minPresenceAhead?: number;
  /**
   * Enable/disable page wrapping for element.
   */
  wrap?: boolean;
  /**
   * Enables debug mode on page bounding box.
   */
  debug?: boolean;
  href?: string;
  src?: string;
  children?: React.ReactNode;
}

/**
 * A React component for displaying a hyperlink. Link's can be nested
 * inside a Text component, or being inside any other valid primitive.
 */
export const Link: React.FC<LinkProps> = ({
  id,
  style,
  fixed,
  break: pageBreak,
  minPresenceAhead,
  wrap,
  debug,
  href,
  src,
  children,
}) => {
  // Convert style array to single style object if needed
  const combinedStyle = Array.isArray(style) 
    ? style.reduce((acc, s) => ({ ...acc, ...s }), {})
    : style;

  // Base link styles that mimic PDF link behavior
  const linkStyle: React.CSSProperties = {
    color: '#0066cc',
    textDecoration: 'underline',
    cursor: 'pointer',
    border: debug ? '1px solid purple' : undefined,
    ...combinedStyle,
  };

  // Handle fixed positioning
  if (fixed) {
    linkStyle.position = 'absolute';
  }

  // Handle page break
  if (pageBreak) {
    linkStyle.pageBreakBefore = 'always';
  }

  // Use href or src as the link target
  const linkTarget = href || src;

  return (
    <a 
      id={id}
      className="pdf-link" 
      href={linkTarget}
      style={linkStyle}
      target="_blank"
      rel="noopener noreferrer"
      data-wrap={wrap}
      data-debug={debug}
      data-fixed={fixed}
      data-min-presence-ahead={minPresenceAhead}
    >
      {children}
    </a>
  );
};

export default Link;
