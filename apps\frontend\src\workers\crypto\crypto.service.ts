import { CryptoWorkerRequest, CryptoWorkerResponse } from "./interfaces";

export class CryptoWorkerService {
  private worker: Worker | null = null;
  private isTerminated = false;
  private static instance: CryptoWorkerService | null = null;
  private readonly TIMEOUT_DURATION = 30000;
  private requestCounter = 0;
  private pendingRequests: Map<
    number,
    { resolve: (value: any) => void; reject: (reason?: any) => void }
  > = new Map();
  private initialized = false;
  private initializingPromise: Promise<any> | null = null;

  private constructor() {
    if (typeof window === "undefined") {
      throw new Error(
        "Workers can only be instantiated in browser environment"
      );
    }
  }

  public static getInstance(): CryptoWorkerService {
    if (!CryptoWorkerService.instance) {
      CryptoWorkerService.instance = new CryptoWorkerService();
    }
    return CryptoWorkerService.instance;
  }

  private initWorker(): void {
    if (!this.worker && !this.isTerminated) {
      if (typeof Worker === "undefined") {
        throw new Error("Web Workers are not supported in this environment");
      }
      this.worker = new Worker(
        new URL("./crypto.worker.ts?worker", import.meta.url),
        { type: "module" }
      );
      this.worker.onmessage = (event: MessageEvent<CryptoWorkerResponse>) => {
        const { operacao, success, error, ...rest } = event.data;
        let id = (event.data as any).id;
        if (typeof id !== "number") {
          id = this.requestCounter - 1;
        }
        const pending = this.pendingRequests.get(id);
        if (!pending) return;
        this.pendingRequests.delete(id);
        if (!success) {
          pending.reject(new Error(error || "Crypto worker error"));
        } else {
          // If this was the INICIALIZAR operation, set initialized
          if (operacao === "INICIALIZAR") {
            this.initialized = true;
            this.initializingPromise = null;
          }
          pending.resolve({ operacao, ...rest });
        }
      };
      this.worker.onerror = (error) => {
        console.error("Crypto Worker error:", error);
        this.terminateWorker();
      };
      this.worker.onmessageerror = (error) => {
        console.error("Crypto Worker message error:", error);
        this.terminateWorker();
      };
    }
  }

  private async ensureInitialized(): Promise<void> {
    if (this.initialized) return;
    if (this.initializingPromise) return this.initializingPromise;
    this.initializingPromise = new Promise((resolve, reject) => {
      this.initWorker();
      if (!this.worker) {
        reject(new Error("Crypto worker failed to initialize"));
        return;
      }
      const requestId = this.requestCounter++;
      const timeoutId = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error("INICIALIZAR operation timed out"));
      }, this.TIMEOUT_DURATION);
      this.pendingRequests.set(requestId, {
        resolve: (value) => {
          clearTimeout(timeoutId);
          this.initialized = true;
          this.initializingPromise = null;
          resolve(value);
        },
        reject: (error) => {
          clearTimeout(timeoutId);
          this.initializingPromise = null;
          reject(error);
        },
      });
      this.worker.postMessage({ operacao: "INICIALIZAR", id: requestId });
    });
    return this.initializingPromise;
  }

  private async sendOperation(request: CryptoWorkerRequest): Promise<any> {
    try {
      await this.ensureInitialized();
      this.initWorker();
      if (!this.worker) {
        throw new Error("Crypto worker failed to initialize");
      }
      const requestId = this.requestCounter++;
      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          this.pendingRequests.delete(requestId);
          reject(new Error(`${request.operacao} operation timed out`));
        }, this.TIMEOUT_DURATION);
        this.pendingRequests.set(requestId, {
          resolve: (value) => {
            clearTimeout(timeoutId);
            resolve(value);
          },
          reject: (error) => {
            clearTimeout(timeoutId);
            reject(error);
          },
        });
        this.worker?.postMessage({ ...request, id: requestId });
      });
    } catch (error) {
      this.terminateWorker();
      throw error;
    }
  }

  public async initialize(): Promise<any> {
    return this.ensureInitialized();
  }

  public async deriveKey(password: Uint8Array, salt: Uint8Array): Promise<any> {
    return this.sendOperation({ operacao: "KEY_DERIVATION", password, salt });
  }

  // public async encrypt(key: Uint8Array, plaintext: Uint8Array): Promise<any> {
  //   // Debug: log key and plaintext
  //   console.log("[CryptoWorkerService] ENCRYPTION:", {
  //     key: Array.from(key),
  //     plaintext: Array.from(plaintext),
  //   });
  //   return this.sendOperation({ operacao: "ENCRYPTION", key, plaintext });
  // }

  public async decrypt(
    key: Uint8Array,
    iv: Uint8Array,
    ciphertext: Uint8Array
  ): Promise<any> {
    return this.sendOperation({ operacao: "DECRYPTION", key, iv, ciphertext });
  }

  public async hmac(key: Uint8Array, message: Uint8Array): Promise<any> {
    return this.sendOperation({ operacao: "HMAC", key, message });
  }

  public async performance(): Promise<any> {
    return this.sendOperation({ operacao: "PERFORMANCE" });
  }

  private terminateWorker(): void {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
      this.isTerminated = true;
    }
    this.initialized = false;
    this.initializingPromise = null;
  }

  public dispose(): void {
    this.terminateWorker();
    CryptoWorkerService.instance = null;
  }

  public isWorkerActive(): boolean {
    return !!this.worker && !this.isTerminated;
  }

  public resetWorker(): void {
    this.terminateWorker();
    this.isTerminated = false;
    this.initialized = false;
    this.initializingPromise = null;
    this.initWorker();
  }
}
