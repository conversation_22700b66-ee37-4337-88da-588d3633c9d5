import { Button, Text } from "@snap/design-system";
import { Info, X } from "lucide-react";
import { useDialogActions } from "~/store/dialogStore";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { Column, DataTable } from "~/components/Table";
import { useMemo, useCallback } from "react";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { toast } from "sonner";
import { formatIsoDate, maskDocumentNumber } from "~/helpers";
import { BiMerge } from "react-icons/bi";
import { usePasswordCheck } from "~/hooks/usePasswordCheck";

interface MergeReportDialogContentProps {
  reportId: string;
  reportName: string;
  currentFolderId?: string | null;
  onMergeStart?: () => void;
  onMergeEnd?: () => void;
}

interface MergeReportDialogFooterProps {
  currentFolderId?: string | null;
}

export function MergeReportDialogContent({
  currentFolderId,
  reportId,
  reportName,
  onMergeStart,
  onMergeEnd
}: MergeReportDialogContentProps) {
   const { withPasswordCheck } = usePasswordCheck();
  const { mergeReportsMutation, reportListQuery } = useReportCRUD(currentFolderId || null);
  const { data: reportList, isLoading, error, refetch } = reportListQuery;

  const columnProps = useMemo(() => ({
    report_name: REPORT_CONSTANTS.new_report.report_name,
    report_type: REPORT_CONSTANTS.new_report.report_type,
    report_search_args: REPORT_CONSTANTS.new_report.report_search_args,
    modified_at: REPORT_CONSTANTS.new_report.modified_at,
  }), []);

  const handleMergeReports = useCallback((selectedReportId: string) => {
    onMergeStart?.();

    if (!selectedReportId) {
      toast.error("Erro ao combinar relatórios", {
        description: "Selecione um relatório para combinar",
      });
      return;
    }

    withPasswordCheck(() => {
      mergeReportsMutation.mutate({
        reportsSelectedIdList: [reportId, selectedReportId],
        parent_folder_id: currentFolderId || null,
      }, {
        onSuccess: () => {
          onMergeEnd?.();
        },
        onError: () => {
          onMergeEnd?.();
        }
      });
    })
  }, [mergeReportsMutation, reportId, currentFolderId, onMergeStart, onMergeEnd]);

  const reportColumns: Column[] = useMemo(() => [
    {
      key: columnProps.report_name,
      header: "Nome Relatório",
      widthClass: "w-1/5 min-w-[200px]",
      className: "overflow-hidden",
      render: (_, row: any) => (
        <div className="flex items-center gap-4">
          <div className="truncate">
            <Text

              className="font-semibold uppercase">
              {row?.report_name as string || `${row?.report_type ? "Relatório " : "Pasta"} ${row?.report_type || ""}`}
            </Text>
          </div>
        </div>
      ),
    },
    {
      key: columnProps.report_search_args,
      header: "Entradas",
      widthClass: "w-1/5 min-w-[120px]",
      render: (_, row: any) => (
        <div className="flex flex-col items-start gap-4">
          {
            row?.report_search_args && Object.entries(row.report_search_args).map(([key, value]) => (
              <div key={key} className="flex items-center gap-1">
                <Text className="font-semibold uppercase">{key.replace(/_[12]$/, '') + ":"}</Text>
                <Text>{maskDocumentNumber(value as string, key)}</Text>
              </div>
            ))
          }
        </div>
      ),
    },
    {
      key: columnProps.modified_at,
      header: "Modificado Em",
      widthClass: "w-1/8 min-w-[120px]",
      render: (_, row: any) => (
        <div className="flex items-center gap-4">
          <Text className="font-semibold">{formatIsoDate(row?.modified_at as string)}</Text>
        </div>
      ),
    },
    {
      key: "actions",
      header: " ",
      widthClass: "w-1/9",
      render: (_, row: any) => {
        const selectedReportId = row?.[REPORT_CONSTANTS.new_report.report_id] as string;
        return (
          <div className="flex justify-center">
            <Button
              variant="ghost"
              className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:opacity-80"
              icon={mergeReportsMutation.isPending ? <AiOutlineLoading3Quarters size={16} className="animate-spin" /> : <BiMerge size={16} />}
              iconPosition="right"
              onClick={() => handleMergeReports(selectedReportId)}
              disabled={mergeReportsMutation.isPending}
              size="sm"
              title="Combinar relatório"
            >
              Combinar
            </Button>
          </div>
        );
      },
    }
  ], [columnProps, mergeReportsMutation.isPending, handleMergeReports]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <AiOutlineLoading3Quarters className="size-8 animate-spin" />
        <Text variant="body-md">Carregando relatórios...</Text>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <X className="size-8 text-primary" />
        <Text variant="body-md" className="text-center">
          Erro ao carregar relatórios.
        </Text>
        <Button
          size="sm"
          onClick={() => refetch()}
          className="uppercase"
        >
          Tentar novamente
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4 py-1">
        <div className="flex items-center gap-3 p-4 bg-blue-500/10 border border-blue-500 rounded-sm">
          <Info className="size-5 text-white flex-shrink-0" />
          <Text variant="body-md" className="font-semibold">
            O relatório atual
            <span className="font-bold text-accent uppercase">{` ${reportName} `}</span>
            será combinado com outro de sua escolha. Esta ação irá gerar um novo relatório combinado. Os relatórios originais serão mantidos.
          </Text>
        </div>
        <Text variant="body-lg" className="py-2">
          Selecione <span className="font-bold">o relatório para combinar:</span>
        </Text>
      </div>

      <div className="overflow-y-auto overflow-x-hidden min-h-0 max-h-[394px] [scrollbar-gutter:stable]">
        <DataTable<any>
          columns={reportColumns}
          data={reportList?.data || []}
          keyField={REPORT_CONSTANTS.new_report.report_id}
          actionsWidthClass="w-32"
          useFixedLayout={true}
          rowClassName="group transition-colors duration-200"
        />
      </div>
    </div>
  );
}

export function MergeReportDialogFooter({ currentFolderId }: MergeReportDialogFooterProps) {
  const { closeDialog } = useDialogActions();
  const { mergeReportsMutation } = useReportCRUD(currentFolderId || null);

  const handleCancel = () => {
    closeDialog();
  };

  return (
    <div className="flex gap-3">
      <Button
        className="uppercase !bg-transparent"
        onClick={handleCancel}
        disabled={mergeReportsMutation.isPending}
      >
        Cancelar
      </Button>
    </div>
  );
}

export const MergeReportDialog = {
  Content: MergeReportDialogContent,
  Footer: MergeReportDialogFooter,
};
