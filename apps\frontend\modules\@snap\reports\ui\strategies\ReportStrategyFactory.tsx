import { REPORT_SECTIONS } from "../../config/constants";
import { ReportType } from "../../global";
import { useLocation } from "react-router";
// Hooks para renderizar as seções
import { useRenderDadosPessoais } from "./NEW_renderDadosPessoais.strategy";
import { useRenderEmails } from "./NEW_renderEmails.strategy";
import { useRenderEnderecos } from "./NEW_renderEnderecos.strategy";
import { useRenderDiariosOficiais } from "./NEW_renderDiariosOficiais.strategy";
import { useRenderTelefonesArray } from "./NEW_renderTelefones.strategy";
import { useRenderPossiveisContas } from "./NEW_renderPossiveisContas.strategy";
import { useRenderSocios } from "./NEW_renderSocios.strategy"
import { useRenderParentes } from "./NEW_renderParentes.strategy";
import { useRenderMandados } from "./NEW_renderMandados.strategy";
import { useRenderRedesSociais } from "./NEW_renderRedesSociais.strategy";
import { useRenderImagens } from "./NEW_renderImagens.strategy";
import { useRenderNomeUsuarios } from "./NEW_renderNomeUsuarios.strategy";
import { useRenderOutrasUrls } from "./NEW_renderOutrasUrls.strategy";
import { useRenderVinculosEmpregaticios } from "./NEW_renderVinculosEmpregaticios.strategy";
import { useRenderVinculosEducacionais } from "./NEW_renderVinculosEducacionais.strategy";
import { useRenderSociedades } from "./NEW_renderSociedades.strategy";
import { useRenderServicosPublicos } from "./NEW_renderServicosPublicos.strategy";
import { useRenderDoacoesEnviadas } from "./NEW_renderDoacoesEnviadas.strategy";
import { useRenderDoacoesRecebidas } from "./NEW_renderDoacoesRecebidas.strategy";
import { useRenderFornecimentosRecebidos } from "./NEW_renderFornecimentosRecebidos.strategy";
import { useRenderFornecimentosEnviados } from "./NEW_renderFornecimentosEnviados.strategy";
import { useRenderFiliacaoPartidaria } from "./NEW_renderFiliacaoPartidaria.strategy";
import { useRenderPossiveisContatos } from "./NEW_renderPossiveisContatos.strategy";
import { useRenderJuntasComerciais } from "./NEW_renderJuntasComerciais.strategy";
import { useRenderRecursosPublicos } from "./NEW_renderRecursosPublicos.strategy";
import { useRenderContatosSalvos } from "./NEW_renderContatosSalvos.strategy";
import { useRenderPossiveisPessoasRelacionadas } from "./NEW_renderPossiveisPessoasRelacionadas.strategy";
import { useRenderProcessos } from "./NEW_renderProcessos.strategy";
import { useRenderMultiplosResultados } from "./renderMultiplosResultados.strategy";

export const useStrategyMap = () => {
  const location = useLocation();
  const reportType: ReportType | string = location.pathname.split("/")[2];

  // COMUM PARA TODOS OS TIPOS
  const common_base_map: Record<string, any> = {
    [REPORT_SECTIONS.telefones]: useRenderTelefonesArray(REPORT_SECTIONS.telefones),
    [REPORT_SECTIONS.emails]: useRenderEmails(REPORT_SECTIONS.emails),
    [REPORT_SECTIONS.enderecos]: useRenderEnderecos(REPORT_SECTIONS.enderecos),
  };

  // CPF, TELEFONE e EMAIL
  const cpf_telefone_email_map: Record<string, any> = {
    [REPORT_SECTIONS.dados_pessoais]: useRenderDadosPessoais(REPORT_SECTIONS.dados_pessoais),
    [REPORT_SECTIONS.parentes]: useRenderParentes(REPORT_SECTIONS.parentes),
    [REPORT_SECTIONS.imagens]: useRenderImagens(REPORT_SECTIONS.imagens),
    [REPORT_SECTIONS.nomes_usuario]: useRenderNomeUsuarios(REPORT_SECTIONS.nomes_usuario),
    [REPORT_SECTIONS.outras_urls]: useRenderOutrasUrls(REPORT_SECTIONS.outras_urls),
    [REPORT_SECTIONS.vinculos_educacionais]: useRenderVinculosEducacionais(REPORT_SECTIONS.vinculos_educacionais),
    [REPORT_SECTIONS.possiveis_contatos]: useRenderPossiveisContatos(REPORT_SECTIONS.possiveis_contatos),
    [REPORT_SECTIONS.possiveis_contas_em_sites]: useRenderPossiveisContas(REPORT_SECTIONS.possiveis_contas_em_sites),
    [REPORT_SECTIONS.perfis_redes_sociais]: useRenderRedesSociais(REPORT_SECTIONS.perfis_redes_sociais),
  };

  // CPF e CNPJ
  const cpf_cnpj_map: Record<string, any> = {
    [REPORT_SECTIONS.processos]: useRenderProcessos(REPORT_SECTIONS.processos),
    [REPORT_SECTIONS.socios]: useRenderSocios(REPORT_SECTIONS.socios),
    [REPORT_SECTIONS.recursos_publicos_recebidos]: useRenderRecursosPublicos(REPORT_SECTIONS.recursos_publicos_recebidos),
    [REPORT_SECTIONS.doacoes_enviadas_campanha]: useRenderDoacoesEnviadas(REPORT_SECTIONS.doacoes_enviadas_campanha),
    [REPORT_SECTIONS.doacoes_recebidas_campanha]: useRenderDoacoesRecebidas(REPORT_SECTIONS.doacoes_recebidas_campanha),
    [REPORT_SECTIONS.fornecimentos_enviados_campanha]: useRenderFornecimentosEnviados(REPORT_SECTIONS.fornecimentos_enviados_campanha),
    [REPORT_SECTIONS.fornecimentos_recebidos_campanha]: useRenderFornecimentosRecebidos(REPORT_SECTIONS.fornecimentos_recebidos_campanha),
    [REPORT_SECTIONS.diarios_oficiais_nome]: useRenderDiariosOficiais(REPORT_SECTIONS.diarios_oficiais_nome),
  };

  // TELEFONE e EMAIL
  const telefone_email_map: Record<string, any> = {
    [REPORT_SECTIONS.dados_empresa]: useRenderDadosPessoais(REPORT_SECTIONS.dados_empresa),
    [REPORT_SECTIONS.contatos_salvos]: useRenderContatosSalvos(REPORT_SECTIONS.contatos_salvos),
    [REPORT_SECTIONS.empresas_relacionadas]: useRenderSociedades(REPORT_SECTIONS.empresas_relacionadas),
    [REPORT_SECTIONS.possiveis_pessoas_relacionadas]: useRenderPossiveisPessoasRelacionadas(REPORT_SECTIONS.possiveis_pessoas_relacionadas),
  };

  // CPF
  const cpf_map: Record<string, any> = {
    [REPORT_SECTIONS.mandados_de_prisao]: useRenderMandados(REPORT_SECTIONS.mandados_de_prisao),
    [REPORT_SECTIONS.sociedades]: useRenderSociedades(REPORT_SECTIONS.sociedades),
    [REPORT_SECTIONS.vinculos_empregaticios]: useRenderVinculosEmpregaticios(REPORT_SECTIONS.vinculos_empregaticios),
    [REPORT_SECTIONS.diarios_oficiais_cpf]: useRenderDiariosOficiais(REPORT_SECTIONS.diarios_oficiais_cpf),
    [REPORT_SECTIONS.servico_publico]: useRenderServicosPublicos(REPORT_SECTIONS.servico_publico),
    [REPORT_SECTIONS.filiacao_partidaria]: useRenderFiliacaoPartidaria(REPORT_SECTIONS.filiacao_partidaria),
  };

  // CNPJ
  const cnpj_map: Record<string, any> = {
    [REPORT_SECTIONS.dados_empresa]: useRenderDadosPessoais(REPORT_SECTIONS.dados_empresa),
    [REPORT_SECTIONS.juntas_comerciais]: useRenderJuntasComerciais(REPORT_SECTIONS.juntas_comerciais),
    [REPORT_SECTIONS.diarios_oficiais_cnpj]: useRenderDiariosOficiais(REPORT_SECTIONS.diarios_oficiais_cnpj),
  };

  // Multiplos Resultados
  const multiplos_resultados_map: Record<string, any> = {
    [REPORT_SECTIONS.multiplos_registros_encontrados]: useRenderMultiplosResultados(REPORT_SECTIONS.multiplos_registros_encontrados),
  };

  switch (reportType) {
    case "cnpj":
      return {
        ...common_base_map,
        ...cpf_cnpj_map,
        ...cnpj_map,
      };
    case "telefone":
      return {
        ...common_base_map,
        ...cpf_telefone_email_map,
        ...telefone_email_map,
        ...multiplos_resultados_map
      };
    case "email":
      return {
        ...common_base_map,
        ...cpf_telefone_email_map,
        ...telefone_email_map,
        ...multiplos_resultados_map
      };
    case "cpf":
      return {
        ...common_base_map,
        ...cpf_telefone_email_map,
        ...cpf_cnpj_map,
        ...cpf_map
      };
    case "combinado":
      return {
        ...common_base_map,
        ...cpf_telefone_email_map,
        ...telefone_email_map,
        ...cpf_cnpj_map,
        ...cpf_map,
        ...cnpj_map,
        ...multiplos_resultados_map
      };
    default:
      return common_base_map;
  }
};