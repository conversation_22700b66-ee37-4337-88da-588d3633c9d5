
#!/bin/bash
set -e

# Check if KRaft storage is already formatted
if [ ! -f /var/lib/kafka/data/meta.properties ]; then
    echo "Formatting KRaft storage directory..."
    # Use the cluster ID from environment variable or generate one
    CLUSTER_ID=${KAFKA_KRAFT_CLUSTER_ID:-$(cat /proc/sys/kernel/random/uuid)}
    echo "Using cluster ID: $CLUSTER_ID"

    # Format the storage directory
    /opt/kafka/bin/kafka-storage.sh format -t $CLUSTER_ID -c /opt/kafka/config/server.properties
    echo "KRaft storage formatted successfully"
else
    echo "KRaft storage already formatted, skipping format step"
fi

# Start Kafka server in the background
# Apache Kafka image uses standard server properties
/opt/kafka/bin/kafka-server-start.sh /opt/kafka/config/server.properties &

until /opt/kafka/bin/kafka-topics.sh --bootstrap-server kafka:9092 --list &>/dev/null; do
  sleep 1
done

# Partitions configuration (can be overridden via environment variables)
# By default, requests topic partitions match MAX_REPLICAS to enable full parallelism
MAX_REPLICAS_DEFAULT=${MAX_REPLICAS:-12}
PDF_REQ_PARTITIONS=${PDF_REQ_PARTITIONS:-${MAX_REPLICAS_DEFAULT}}
PDF_RES_PARTITIONS=${PDF_RES_PARTITIONS:-3}
PDF_LOG_PARTITIONS=${PDF_LOG_PARTITIONS:-3}

# echo "creating topic"
# Create reports topic with more partitions for better parallelism
/opt/kafka/bin/kafka-topics.sh --create --if-not-exists --topic reports --bootstrap-server kafka:9092 --partitions 12 --replication-factor 1

# Create processed-reports topic
/opt/kafka/bin/kafka-topics.sh --create --if-not-exists --topic processed-reports --bootstrap-server kafka:9092 --partitions 1 --replication-factor 1

# PDF related topics
/opt/kafka/bin/kafka-topics.sh --create --if-not-exists --topic pdf-generation-requests --bootstrap-server kafka:9092 --partitions ${PDF_REQ_PARTITIONS} --replication-factor 1
/opt/kafka/bin/kafka-topics.sh --create --if-not-exists --topic pdf-generation-results --bootstrap-server kafka:9092 --partitions ${PDF_RES_PARTITIONS} --replication-factor 1
/opt/kafka/bin/kafka-topics.sh --create --if-not-exists --topic pdf-generation-logs --bootstrap-server kafka:9092 --partitions ${PDF_LOG_PARTITIONS} --replication-factor 1

# Ensure partitions are at least the desired values (safe if equal or larger)
/opt/kafka/bin/kafka-topics.sh --alter --topic reports --bootstrap-server kafka:9092 --partitions 12 || true
/opt/kafka/bin/kafka-topics.sh --alter --topic pdf-generation-requests --bootstrap-server kafka:9092 --partitions ${PDF_REQ_PARTITIONS} || true
/opt/kafka/bin/kafka-topics.sh --alter --topic pdf-generation-results --bootstrap-server kafka:9092 --partitions ${PDF_RES_PARTITIONS} || true
/opt/kafka/bin/kafka-topics.sh --alter --topic pdf-generation-logs --bootstrap-server kafka:9092 --partitions ${PDF_LOG_PARTITIONS} || true

echo "Kafka Started With topics: reports | processed-reports | pdf-generation-requests | pdf-generation-results | pdf-generation-logs"

wait