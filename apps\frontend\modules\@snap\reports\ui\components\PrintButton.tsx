import React from "react";
import { PiFilePdf } from "react-icons/pi";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { FileText } from "lucide-react";
import { FabButton } from "./base/fab-button";
import { TbFileTypeDocx } from "react-icons/tb";

export interface PrintButtonProps {
  type: string;
  onClick: () => void;
  loading?: boolean;
  position?: "bottomRight" | "bottomLeft" | "topRight" | "topLeft" | "middleRight" | "none";
  title: string;
  ariaLabel?: string;
  className?: string;
  variant?: "primary" | "secondary";
  size?: "sm" | "md" | "lg";
}

const PrintButton: React.FC<PrintButtonProps> = ({
  type,
  onClick,
  loading = false,
  position = "middleRight",
  title,
  ariaLabel,
  className,
  variant = "primary",
  size = "md",
}) => {
  const isPdf = type.toLowerCase() === "pdf";
  const isDocx = type.toLowerCase() === "docx";

  const getButtonConfig = () => {
    if (loading) {
      return {
        icon: <AiOutlineLoading3Quarters size={20} className="animate-spin" />,
        cursor: "not-allowed",
      };
    }

    if (isPdf) {
      return {
        icon: <PiFilePdf size={20} />,
        cursor: "cursor-pointer",
      };
    }

    if (isDocx) {
      return {
        icon: <TbFileTypeDocx size={20} />,
        cursor: "cursor-pointer",
      };
    }

    return {
      icon: <FileText size={20} />,
      cursor: "cursor-pointer",
    };
  };


  const computedTitle = title;
  const computedAria = ariaLabel ?? (loading ? "Gerando arquivo..." : computedTitle);
  const { cursor, icon } = getButtonConfig();

  return (
    <FabButton
      onClick={onClick}
      className={`${cursor} ${className ?? ""}`}
      icon={icon}
      ariaLabel={computedAria}
      title={computedTitle}
      position={position}
      disabled={loading}
      variant={variant}
      size={size}
    />
  );
};

export default PrintButton;