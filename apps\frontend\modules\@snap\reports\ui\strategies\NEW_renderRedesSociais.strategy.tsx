import React, { useCallback } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { formatFieldValue, parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { isBase64Image, isValidUrl, renderSourceTooltip } from "./helpers.strategy";
import { GridItem } from "@snap/design-system";
import { ValidatedImage } from "../components/ValidateImage";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { useNestedRender } from "./NestedRenderContext";
import {
  CalculateFunction,
  TestFunction,
  SectionTestFunction,
  UpdaterFunction,
  PerfilRedeSocial,
  RedesSociaisSection
} from "../../model/PerfisRedesSociais";

export function useRenderRedesSociais(
  sectionTitle: string
): ArrayRenderStrategy<PerfilRedeSocial> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  // Test functions for deletion logic
  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? e.detalhes.every((detalhe: any) => {
        if (!detalhe.value) return true;
        return Object.values(detalhe.value).every((platform: any) => {
          if (!Array.isArray(platform)) return true;
          return platform.every((profile: any) => {
            return Object.values(profile).every((field: any) => field.is_deleted === true);
          });
        });
      })
      : true;

  const testEntryDeleted = (entry: any): boolean => {
    return testDetalhesDeleted(entry);
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  // Data count calculation function for Redes Sociais section
  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;

      return entry.detalhes.reduce((entryCount: number, detalhe: any) => {
        if (!detalhe.value) return entryCount;

        return Object.values(detalhe.value).reduce((platformCount: number, profiles: any) => {
          if (!Array.isArray(profiles)) return platformCount;

          const activeProfiles = profiles.filter((profile: any) => {
            const allFieldsDeleted = Object.values(profile).every((field: any) => field.is_deleted === true);
            return !allFieldsDeleted;
          });

          return platformCount + activeProfiles.length;
        }, entryCount);
      }, count);
    }, 0);
  };

  const updateEntries = useCallback((
    updater: UpdaterFunction,
    testDeleted: TestFunction = testEntryDeleted,
    testSectionDel: SectionTestFunction = testSectionDeleted,
    calcDataCount: CalculateFunction = calculateDataCount
  ) => {
    actions.updateSectionEntries?.(
      sectionTitle,
      updater as (entry: Record<string, any>, index?: number) => void,
      testDeleted as (entry: Record<string, any>) => boolean,
      testSectionDel as (section: Record<string, any>) => boolean,
      calcDataCount as (section: Record<string, any>) => number
    );
  }, [actions, sectionTitle, testEntryDeleted, testSectionDeleted, calculateDataCount]);

  const shouldIncludePlatform = (platform: any[]) => {
    return platform.some((profile: any) => {
      const fields = Object.values(profile);
      return isTrash
        ? fields.some((field: any) => field.is_deleted === true)
        : fields.some((field: any) => field.is_deleted === false);
    });
  };

  const shouldIncludeProfile = (profile: any) => {
    const fields = Object.values(profile);
    return isTrash
      ? fields.some((field: any) => field.is_deleted === true)
      : fields.some((field: any) => field.is_deleted === false);
  };

  const PlatformBlock = React.memo(({
    entry,
    index,
    sectionTitle,
    platform,
    profiles,
    detalheIdx
  }: {
    entry: PerfilRedeSocial;
    index: number;
    sectionTitle: string;
    platform: string;
    profiles: any[];
    detalheIdx: number;
  }) => {
    const nested = useNestedRender();

    if (!shouldIncludePlatform(profiles)) return null;

    let platformName = platform.charAt(0).toUpperCase() + platform.slice(1);

    const filteredProfiles = profiles
      .map((profile, profileIdx) => ({ profile, originalIdx: profileIdx }))
      .filter(({ profile }) => shouldIncludeProfile(profile));

    if (filteredProfiles.length === 0) return null;

    const handleTogglePlatform = useCallback(() => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: RedesSociaisSection) => {
          const targetEntry = nestedSection.data[index];
          if (targetEntry?.detalhes?.[detalheIdx]?.value?.[platform]) {
            const platformProfiles = targetEntry.detalhes[detalheIdx].value[platform];
            if (Array.isArray(platformProfiles)) {
              const targetDeletedState = isTrash ? false : true;

              platformProfiles.forEach((profile: any) => {
                Object.values(profile).forEach((field: any) => {
                  if (field && typeof field === 'object' && 'is_deleted' in field) {
                    field.is_deleted = targetDeletedState;
                  }
                });
              });
            }
          }

          nestedSection.data_count = calculateDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: PerfilRedeSocial, i?: number) => {
        if (i === index) {
          const platformProfiles = e.detalhes?.[detalheIdx]?.value?.[platform];
          if (platformProfiles && Array.isArray(platformProfiles)) {
            const targetDeletedState = isTrash ? false : true;

            platformProfiles.forEach((profile: any) => {
              Object.values(profile).forEach((field: any) => {
                if (field && typeof field === 'object' && 'is_deleted' in field) {
                  field.is_deleted = targetDeletedState;
                }
              });
            });
          }
        }
      });
    }, [nested, index, updateEntries, detalheIdx, platform, isTrash]);

    return (
      <CustomGridContainer cols={1} key={`platform-${platform}-${detalheIdx}`} className="mb-6">
        <CustomGridItem
          fullWidth
          onToggleField={handleTogglePlatform}
        >
          <ReportsCustomLabel
            label={platformName.toUpperCase()}
            colorClass="bg-primary"
          />
        </CustomGridItem>

        <CustomGridContainer cols={2}>
          {filteredProfiles.map(({ profile, originalIdx }, renderIdx) => (
            <ProfileBlock
              key={`${platform}-profile-${originalIdx}`}
              entry={entry}
              index={index}
              sectionTitle={sectionTitle}
              platform={platform}
              platformName={platformName}
              profile={profile}
              originalIdx={originalIdx}
              renderIdx={renderIdx}
              detalheIdx={detalheIdx}
            />
          ))}
        </CustomGridContainer>
      </CustomGridContainer>
    );
  });

  const ProfileBlock = React.memo(({
    entry,
    index,
    sectionTitle,
    platform,
    platformName,
    profile,
    originalIdx,
    renderIdx,
    detalheIdx
  }: {
    entry: PerfilRedeSocial;
    index: number;
    sectionTitle: string;
    platform: string;
    platformName: string;
    profile: any;
    originalIdx: number;
    renderIdx: number;
    detalheIdx: number;
  }) => {
    const nested = useNestedRender();

    if (!shouldIncludeProfile(profile)) return null;

    const handleToggleProfile = useCallback(() => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: RedesSociaisSection) => {
          const targetEntry = nestedSection.data[index];
          const profileData = targetEntry?.detalhes?.[detalheIdx]?.value?.[platform]?.[originalIdx];
          if (profileData) {
            const targetDeletedState = isTrash ? false : true;

            Object.values(profileData).forEach((field: any) => {
              if (field && typeof field === 'object' && 'is_deleted' in field) {
                field.is_deleted = targetDeletedState;
              }
            });
          }

          nestedSection.data_count = calculateDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: PerfilRedeSocial, i?: number) => {
        if (i === index) {
          const profileData = e.detalhes?.[detalheIdx]?.value?.[platform]?.[originalIdx];
          if (profileData) {
            const targetDeletedState = isTrash ? false : true;

            Object.values(profileData).forEach((field: any) => {
              if (field && typeof field === 'object' && 'is_deleted' in field) {
                field.is_deleted = targetDeletedState;
              }
            });
          }
        }
      });
    }, [nested, index, updateEntries, detalheIdx, platform, originalIdx, isTrash]);

    const handleToggleField = useCallback((fieldKey: string) => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: RedesSociaisSection) => {
          const targetEntry = nestedSection.data[index];
          const field = targetEntry?.detalhes?.[detalheIdx]?.value?.[platform]?.[originalIdx]?.[fieldKey];
          if (field) {
            field.is_deleted = !field.is_deleted;
          }

          nestedSection.data_count = calculateDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: PerfilRedeSocial, i?: number) => {
        if (i === index) {
          const field = e.detalhes?.[detalheIdx]?.value?.[platform]?.[originalIdx]?.[fieldKey];
          if (field) {
            field.is_deleted = !field.is_deleted;
          }
        }
      });
    }, [nested, index, updateEntries, detalheIdx, platform, originalIdx]);

    return (
      <GridItem key={`${platform}-profile-${originalIdx}`} cols={1}>
        <CustomGridItem
          fullWidth
          className="py-2"
          onToggleField={handleToggleProfile}
        >
          <ReportsCustomLabel
            label={`${platformName.toUpperCase()} ${!isTrash ? renderIdx + 1 : ""}`}
            colorClass="bg-border"
            icon={<MdOutlineSubdirectoryArrowRight size={16} />}
          />
        </CustomGridItem>
        <div className="pl-5">
          {Object.entries(profile)
            .filter(([_, fieldValue]: any) =>
              isTrash ? fieldValue.is_deleted : !fieldValue.is_deleted
            )
            .map(([fieldKey, fieldValue]: any) => {
              const isImageValue = !Array.isArray(fieldValue.value) &&
                (isValidUrl(fieldValue.value) || isBase64Image(fieldValue.value));
              if (isImageValue) {
                return (
                  <CustomGridItem
                    key={`${platform}-${originalIdx}-${fieldKey}`}
                    cols={1}
                    className="py-1"
                    onToggleField={() => handleToggleField(fieldKey)}
                  >
                    <CustomReadOnlyInputField
                      label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                      value={parseValue(formatFieldValue(fieldValue.value))}
                      tooltip={renderSourceTooltip(fieldValue.source)}
                    />

                    <ValidatedImage
                      src={String(fieldValue.value)}
                      alt={`Imagem ${renderIdx + 1}`}
                      className="w-full max-w-full h-48 mx-auto m-2 bg-background/40 rounded-sm"
                    />

                  </CustomGridItem>
                );
              }

              // Default text field
              return (
                <CustomGridItem
                  key={`${platform}-${originalIdx}-${fieldKey}`}
                  cols={1}
                  className="py-1"
                  onToggleField={() => handleToggleField(fieldKey)}
                >
                  <CustomReadOnlyInputField
                    label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                    value={parseValue(formatFieldValue(fieldValue.value))}
                    tooltip={renderSourceTooltip(fieldValue.source)}
                  />
                </CustomGridItem>
              );
            })}
        </div>
      </GridItem>
    );
  });

  const formatByKey: Record<
    string,
    (entry?: PerfilRedeSocial, index?: number) => React.ReactElement | null
  > = {
    detalhes: (entry, index) => {
      if (!entry?.detalhes?.length || index === undefined) return null;

      const allElements: React.ReactElement[] = [];

      entry.detalhes.forEach((detalhe, detalheIdx) => {
        if (!detalhe.value) return;

        Object.entries(detalhe.value).forEach(([platform, profiles]) => {
          if (!Array.isArray(profiles) || !shouldIncludePlatform(profiles)) return;

          allElements.push(
            <PlatformBlock
              key={`platform-${platform}-${detalheIdx}`}
              entry={entry}
              index={index}
              sectionTitle={sectionTitle}
              platform={platform}
              profiles={profiles}
              detalheIdx={detalheIdx}
            />
          );
        });
      });

      return allElements.length > 0 ? <>{allElements}</> : null;
    },
  };

  const validateKeys = (keys: Array<keyof PerfilRedeSocial>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: PerfilRedeSocial, index: number): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof PerfilRedeSocial>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Redes Sociais] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(entry, index))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: PerfilRedeSocial[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Redes Sociais] Expected array but received:", typeof dataArray);
      return [];
    }

    const items = dataArray.map((entry, originalIndex) => ({ entry, originalIndex }));

    const filteredData = items.filter(({ entry }) => {
      if (isTrash) {
        // In trash mode, show entries that have ANY deleted field
        return entry.detalhes?.some((detalhe: any) => {
          if (!detalhe.value) return false;
          return Object.values(detalhe.value).some((platform: any) => {
            if (!Array.isArray(platform)) return false;
            return platform.some((profile: any) => {
              return Object.values(profile).some((field: any) => field.is_deleted === true);
            });
          });
        });
      } else {
        // In normal mode, show entries that are NOT completely deleted
        const isCompletelyDeleted = testEntryDeleted(entry);
        return !isCompletelyDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach(({ entry, originalIndex }, renderIndex) => {
      const elements = renderSingleItem(entry, originalIndex);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`redes-sociais-${originalIndex}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Marca todos os campos como deletado/restaurado
        if (entry.detalhes) {
          entry.detalhes.forEach((detalhe: any) => {
            if (detalhe.value) {
              Object.values(detalhe.value).forEach((platform: any) => {
                if (Array.isArray(platform)) {
                  platform.forEach((profile: any) => {
                    Object.values(profile).forEach((field: any) => {
                      if (field && typeof field === 'object' && 'is_deleted' in field) {
                        field.is_deleted = targetDeletedState;
                      }
                    });
                  });
                }
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
    calculateDataCount,
  } as ArrayRenderStrategy<PerfilRedeSocial> & { calculateDataCount: typeof calculateDataCount };
}