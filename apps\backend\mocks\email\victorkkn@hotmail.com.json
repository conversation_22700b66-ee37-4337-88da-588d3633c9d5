{"SNAP": [{"pessoa": [{"cpf": "01629906697", "first names": "VICTOR", "surname": "KENNEDY KANEKO NUNES", "full name": "VICTOR KENNEDY KANEKO NUNES", "sexo": "M", "pessoa": [{"full name": "MIRIAM TAMAMI KANEKO NUNES", "label default key": "parente MAE"}], "data nascimento": "09/08/1997"}]}], "PIPLEmail": [{"pessoa": [{"first names": "VICTOR", "surname": "<PERSON><PERSON>", "surname1": "KENNEDY KANEKO NUNES", "full name": "VICTOR KENNEDY KANEKO NUNES", "idade": "27", "data nascimento": "08/09/1997", "sexo": "Male", "idioma": "pt", "cpf": "01629906697", "vinculo": [{"rotulo": "CPF"}], "pessoa": [{"idade": "56", "data nascimento": "02/06/1969", "first names": "<PERSON>", "surname": "Nunes", "full name": "MIRIAM TAMAMI KANEKO NUNES", "phonenumber": [{"phone number": "5531991699454", "country code": 55, "tipo": "celular"}], "vinculo": [{"rotulo": "parente MAE", "classificacao": "Familia"}]}, {"idade": "30", "data nascimento": "08/07/1994", "first names": "<PERSON><PERSON>", "surname": "Nunes", "full name": "RAYANE TAMAMI NUNES", "phonenumber": [{"phone number": "5531997942870", "country code": 55, "tipo": "celular"}], "vinculo": [{"rotulo": "parente IRMAO", "classificacao": "Familia"}]}, {"idade": "61", "data nascimento": "08/04/1963", "first names": "<PERSON>", "surname": "<PERSON><PERSON>", "full name": "JONATHAN TADAHIRO KANEKO", "phonenumber": [{"phone number": "5522998689303", "country code": 55, "tipo": "celular"}], "vinculo": [{"rotulo": "parente TIA", "classificacao": "Familia"}]}, {"idade": "59", "data nascimento": "21/04/1966", "first names": "Mine<PERSON>", "surname": "<PERSON><PERSON>", "full name": "MINEO KANEKO", "phonenumber": [{"phone number": "5511991042688", "country code": 55, "tipo": "celular"}], "vinculo": [{"rotulo": "parente TIA", "classificacao": "Familia"}]}], "endereco": [{"nome": "157 R <PERSON>, Bel<PERSON> Horizon<PERSON>, Minas Gerais", "estado ou regiao": "MG", "logradouro": "R D OSCAR ROMERO", "numero": "157", "cidade": "Belo Horizonte", "cep ou zipcode": "30510080", "pais": "BR"}], "emailaddress": [{"email address": "<EMAIL>", "tipo de email": "Pessoal", "provedor de email": "<PERSON>m"}], "phonenumber": [{"phone number": "5531975913669", "country code": 55, "tipo": "celular"}, {"phone number": "5531996264775", "country code": 55, "tipo": "celular"}, {"phone number": "553135313584", "country code": 55}]}]}], "IRBIS": [{"pessoa": [{"first names": "VICTOR", "surname": "KENNEDY KANEKO-NUNES", "full name": "VICTOR KENNEDY KANEKO-NUNES", "full name1": "VICTOR KENNEDY NUNES", "idioma": "pt", "data nascimento": "08/09/1997", "sexo": "<PERSON><PERSON><PERSON><PERSON>", "pessoa": [{"full name": "MIRIAM TAMAMI KANEKO NUNES", "data nascimento": "02/06/1969", "label default key": "family Mother"}, {"full name": "RAYANE TAMAMI NUNES", "data nascimento": "08/07/1994", "label default key": "family Brother"}, {"full name": "JONATHAN TADAHIRO KANEKO", "data nascimento": "08/04/1963", "label default key": "family Aunt"}, {"full name": "MINEO KANEKO", "data nascimento": "21/04/1966", "label default key": "family Aunt"}], "cpf": "01629906697", "bookmark": 4, "endereco": [{"logradouro": "R D OSCAR ROMERO", "numero": "157", "cidade": "Belo Horizonte", "estado ou regiao": "MG", "cep ou zipcode": "30510080", "pais": "BR"}], "phonenumber": [{"country code": "55", "area code": "31", "phone number": "5531975913669"}, {"country code": "55", "area code": "31", "phone number": "5531996264775"}, {"country code": "55", "area code": "31", "phone number": "553135313584"}], "emailaddress": [{"email address": "<EMAIL>", "tipo": "personal", "email valido": "<PERSON>m"}]}]}], "Provedor de Aplicacação da Internet": [{"provedor de aplicacoes de internet": [{"nome": "Amazon", "termo procurado": "<EMAIL>", "existe": true}, {"nome": "Facebook", "termo procurado": "<EMAIL>", "existe": "Erro: A aplicacao bloqueou a requisicao."}, {"nome": "Instagram", "termo procurado": "<EMAIL>", "existe": true, "e-mail enviado": true}, {"nome": "<PERSON><PERSON>", "termo procurado": "<EMAIL>", "existe": false}, {"nome": "Milhas123", "termo procurado": "<EMAIL>", "existe": false}, {"nome": "Olx", "termo procurado": "<EMAIL>", "existe": "Erro: A aplicacao bloqueou a requisicao."}]}]}