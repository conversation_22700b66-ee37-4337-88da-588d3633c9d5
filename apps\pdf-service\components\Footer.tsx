import { Text, View, Image } from "./pdf-components";
import { PdfStyles } from "./styles";

export const ReportsPdfFooter = () =>
  <View className={"footer"} style={PdfStyles.footer} fixed>
    <View className={"footerContent"} style={PdfStyles.footerContent}>
      <Text style={PdfStyles.footerCopyright}>Uso restrito, conforme LGPD (Lei 13.709/2018). Compartilhamento ou cópia não autorizados é proibido.</Text>
      <View className={"footerPageNumberContent"} style={PdfStyles.footerPageNumberContent}>
        <Text style={PdfStyles.pageNumberText}> Página </Text>
        <div className={"pageNumber"} style={PdfStyles.pageNumber}></div>
        <Text style={PdfStyles.pageNumberText}> de </Text>
        <div className={"totalPages"} style={PdfStyles.pageNumber}></div>
      </View>
    </View>
  </View>

export const wrapFooterWithInlineCSS = (html: string): string => {
  return `
    <style>${Object.entries(PdfStyles)
      .map(([key, value]) => `.${key} { ${Object.entries(value).map(([k, v]) => `${k}: ${v};`)
        .join(' ')} }`).join('\n')}
    }</style>
    ${html}
  `;
}
