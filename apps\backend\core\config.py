import os
import logging
from pathlib import Path
from pydantic_settings import BaseSettings
from minio import Minio

logger = logging.getLogger(__name__)

def read_docker_secret(path: str) -> str:
    try:
        secret = Path(path).read_text().strip()
        logger.info(f"Successfully read secret from {path}")
        return secret
    except Exception as e:
        logger.error("Could not read secret from %s: %s", path, e)
        return ""

class Settings(BaseSettings):
    DB_HOST: str = os.getenv("DB_HOST")
    DB_NAME: str = os.getenv("DB_NAME")
    DB_USER: str = os.getenv("DB_USER")
    DB_PASS: str = os.getenv("DB_PASS")

    CLIENT_ID_KEYCLOAK: str = os.getenv("CLIENT_ID_KEYCLOAK")
    CLIENT_SECRET_KEYCLOAK: str = ""  # Will assign after init
    SNAP_API_CLIENT_SECRET: str = ""
    KEYCLOAK_VERIFY_SSL: bool = os.getenv("KEYCLOAK_VERIFY_SSL", False)
    CAPTCHA_KEY: str = ""   
    REDIRECT_URI_KEYCLOAK: str = os.getenv("REDIRECT_URI_KEYCLOAK")
    SERVER_URL: str = os.getenv("SERVER_URL", "http://localhost:8000")
    KC_ENV: str = os.getenv("KC_ENV", "development")  # 'development' or 'production' or 'staging'
    
    # Google OAuth configuration
    GOOGLE_CLIENT_ID: str = os.getenv("GOOGLE_CLIENT_ID", "409707573351-30funda9lsmaml5is48nsdh0f6vjht9d.apps.googleusercontent.com")
    GOOGLE_CLIENT_SECRET: str = ""  # Will assign after init

    KEYCLOAK_URL: str = os.getenv("KEYCLOAK_URL")
    KEYCLOAK_ISSUER_URL: str = os.getenv("KEYCLOAK_ISSUER_URL")
    REALM_NAME: str = os.getenv("REALM_NAME")
    FRONTEND_REDIRECT_URL: str = os.getenv("FRONTEND_REDIRECT_URL")
    BASE_API_URL: str = os.getenv("BASE_API_URL")

    MINIO_ROOT_USER: str = os.getenv("MINIO_ROOT_USER")
    MINIO_ROOT_PASSWORD: str = os.getenv("MINIO_ROOT_PASSWORD")
    MINIO_CONTAINER_NAME: str = os.getenv("MINIO_CONTAINER_NAME")
    MINIO_S3_INTERNAL_PORT: str = os.getenv("MINIO_S3_INTERNAL_PORT")
        
    KAFKA_CONTAINER_NAME: str = os.getenv("KAFKA_CONTAINER_NAME")
    KAFKA_EXTERNAL_URI: str = os.getenv("KAFKA_EXTERNAL_URI")
    KAFKA_INTERNAL_PORT: str = os.getenv("KAFKA_INTERNAL_PORT")
    KAFKA_EXTERNAL_PORT: str = os.getenv("KAFKA_EXTERNAL_PORT")

    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "DEBUG") 

    # Redis configuration
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    REDIS_PASSWORD: str = os.getenv("REDIS_PASSWORD", "")
    REDIS_SSL: bool = os.getenv("REDIS_SSL", "false").lower() == "true"
    REDIS_MAX_CONNECTIONS: int = int(os.getenv("REDIS_MAX_CONNECTIONS", "10"))

    # Database pool settings
    DB_POOL_SIZE: int = int(os.getenv("DB_POOL_SIZE", 20))
    DB_MAX_OVERFLOW: int = int(os.getenv("DB_MAX_OVERFLOW", 30))
    DB_POOL_TIMEOUT: int = int(os.getenv("DB_POOL_TIMEOUT", 30))

    @property
    def JWKS_URL(self):
        return f"{self.KEYCLOAK_URL}/realms/{self.REALM_NAME}/protocol/openid-connect/certs"

    @property
    def REFRESH_URL(self):
        return f"{self.KEYCLOAK_URL}/realms/{self.REALM_NAME}/protocol/openid-connect/token"

    @property
    def ISSUER_URL(self):
        # Use separate issuer URL for JWT validation, fallback to KEYCLOAK_URL for backward compatibility
        issuer_base = self.KEYCLOAK_ISSUER_URL or self.KEYCLOAK_URL
        return f"{issuer_base}/realms/{self.REALM_NAME}"

    @property
    def DATABASE_URL(self):
        logger.info("Connecting to database at {settings.DB_HOST}:5432/{settings.DB_NAME} with schema: reports")
        # Simplified URL without options parameter - schema will be handled at connection level
        return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASS}@{self.DB_HOST}:5432/{self.DB_NAME}"
    
    @property
    def REDIS_URL(self):
        """Generate Redis connection URL"""
        protocol = "rediss" if self.REDIS_SSL else "redis"
        if self.REDIS_PASSWORD:
            return f"{protocol}://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"{protocol}://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    model_config = {"env_file": ".env"}

# Instantiate settings
settings = Settings()

# Read secrets after instantiation
settings.CLIENT_SECRET_KEYCLOAK = read_docker_secret("/run/secrets/client_secret_keycloak")
settings.SNAP_API_CLIENT_SECRET = read_docker_secret("/run/secrets/snap_api_client_secret")
settings.CAPTCHA_KEY = read_docker_secret("/run/secrets/captcha_key")

# Initialize MinIO client safely
try:
    minio_client = Minio(
        "%s:%s" % (settings.MINIO_CONTAINER_NAME, settings.MINIO_S3_INTERNAL_PORT),
        access_key=settings.MINIO_ROOT_USER,
        secret_key=settings.MINIO_ROOT_PASSWORD,
        secure=False  # Set True if using HTTPS
    )
    logger.info("Successfully initialized MinIO client for %s:%s", settings.MINIO_CONTAINER_NAME, settings.MINIO_S3_INTERNAL_PORT)
except Exception as e:
    logger.error("Failed to initialize MinIO client: %s", e)
    raise

