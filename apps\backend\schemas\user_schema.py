from pydantic import BaseModel, <PERSON>
from typing import Optional, List, Generic, TypeVar
from uuid import UUID

T = TypeVar('T')

class UserCreate(BaseModel):
    email: str
    first_name: str
    last_name: str
    credits: Optional[int]
    report_types: List[str]
    role: str
    organization_id: UUID

class OtherUser(BaseModel):
    report_types: List[str]
    role: str
    credits_monthly: int


class InvestigatorUser(BaseModel):
    report_types: List[str]


class AdministratorUser(BaseModel):
    report_types: Optional[List[str]] = None

class PaginationMetadata(BaseModel):
    current_page: int
    total_pages: int
    total_items: int
    page_size: int
    has_next: bool
    has_previous: bool


class PaginatedResponse(BaseModel, Generic[T]):
    data: List[T]
    pagination: PaginationMetadata

class StandAloneUser(BaseModel):
    """
    Schema for standalone users.
    These users have api_key but no organization association, credits, role, or report_types.
    """
    api_key: str = Field(..., description="API key for the standalone user")

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "first_name": "<PERSON>",
                "last_name": "Doe",
                "api_key": "your-api-key-here"
            }
        }
