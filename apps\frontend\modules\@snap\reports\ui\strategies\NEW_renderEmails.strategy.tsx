import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { Email } from "../../model/Emails";
import { GridItem } from "@snap/design-system";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { useNestedRender } from "./NestedRenderContext";
import { formatFieldValue } from "../../helpers";

export function useRenderEmails(sectionTitle: string): ArrayRenderStrategy<Email> {
  const actions = useReportActions();
  const mode = useReportMode();

  const shouldInclude = (isDeleted: boolean) => {
    switch (mode) {
      case "trash":
        return isDeleted;
      case "print-pdf":
      case undefined:
      default:
        return !isDeleted;
    }
  };

  const testEntryDeleted = (entry: any): boolean =>
    entry.detalhes?.every((d: any) => d.is_deleted === true) ?? false;

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  // Data count calculation function for Emails section
  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;

      // Count non-deleted blocks in detalhes array
      const nonDeletedBlocks = entry.detalhes.filter(
        (bloco: any) => bloco.is_deleted !== true
      ).length;

      return count + nonDeletedBlocks;
    }, 0);
  };

  const calculateNestedDataCount = (nestedSection: any): number => {
    if (!Array.isArray(nestedSection.data)) return 0;
    return nestedSection.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;
      const nonDeletedBlocks = entry.detalhes.filter(
        (bloco: any) => bloco.is_deleted !== true
      ).length;
      return count + nonDeletedBlocks;
    }, 0);
  };

  const formatByKey: Record<
    string,
    (emails?: Email) => React.ReactElement | null
  > = {
    detalhes: (emails) => {
      if (!emails?.detalhes?.length) return null;
      const indexed = emails.detalhes.map((d, i) => ({ detalhe: d, origIndex: i }));
      const shown = indexed.filter(({ detalhe }) => shouldInclude(detalhe.is_deleted ?? false));

      return (
        <CustomGridContainer cols={1}>
          <GridItem fullWidth>
            <CustomGridContainer cols={2}>
              {shown.map(({ detalhe, origIndex }, renderIdx) => (
                <EmailItem
                  key={`email-detalhe-${origIndex}`}
                  sectionTitle={sectionTitle}
                  detalhe={detalhe}
                  origIndex={origIndex}
                  renderIdx={renderIdx}
                />
              ))}
            </CustomGridContainer>
          </GridItem>
        </CustomGridContainer>
      );
    },
  };

  function EmailItem({
    sectionTitle,
    detalhe,
    origIndex,
    renderIdx,
  }: {
    sectionTitle: string;
    detalhe: any;
    origIndex: number;
    renderIdx: number;
  }) {
    const actions = useReportActions();
    const mode = useReportMode();
    const isTrashMode = mode === "trash";
    const nested = useNestedRender();

    const handleToggle = () => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection) => {
          const entry = nestedSection.data?.[0];
          const alvo = entry?.detalhes?.[origIndex];
          if (alvo) {
            alvo.is_deleted = !alvo.is_deleted;
            Object.values(alvo.value ?? {}).forEach((campo: any) => { if (campo) campo.is_deleted = alvo.is_deleted; });
          }
          // Use dedicated nested data count calculation
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      actions.updateSectionEntries?.(
        sectionTitle,
        (entry) => {
          const alvo = (entry as any).detalhes?.[origIndex];
          if (alvo) {
            alvo.is_deleted = !alvo.is_deleted;
            Object.values(alvo.value ?? {}).forEach((campo: any) => { if (campo) campo.is_deleted = alvo.is_deleted; });
          }
        },
        testEntryDeleted,
        testSectionDeleted,
        calculateDataCount
      );
    };

    return (
      <CustomGridItem cols={1} onToggleField={handleToggle}>
        <CustomReadOnlyInputField
          label={`EMAIL${!isTrashMode ? ` ${renderIdx + 1}` : ""}`}
          value={formatFieldValue((Object.values(detalhe.value)[0] as { value?: string })?.value ?? "")}
          tooltip={renderSourceTooltip(
            typeof Object.values(detalhe.value)[0] === "object" && Object.values(detalhe.value)[0] !== null
              ? (Object.values(detalhe.value)[0] as { source?: any }).source
              : undefined
          )}
        />
      </CustomGridItem>
    );
  }

  const validateKeys = (keys: Array<keyof Email>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (emails: Email): React.ReactElement[] => {
    const keys = Object.keys(emails) as Array<keyof Email>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Emails] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(emails))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: Email[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Emails] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        return entry.detalhes && entry.detalhes.some((d: any) => d.is_deleted === true);
      } else {
        const isDeleted = testEntryDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((emails, index) => {
      const elements = renderSingleItem(emails);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`emails-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  // Função para deletar/restaurar toda a seção
  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Percorre todos os detalhes e marca como deletado/restaurado
        if (entry.detalhes) {
          entry.detalhes.forEach((detalhe: any) => {
            if (detalhe) {
              detalhe.is_deleted = targetDeletedState;

              // Marca também os campos individuais
              if (detalhe.value) {
                Object.values(detalhe.value).forEach((campo: any) => {
                  if (campo) campo.is_deleted = targetDeletedState;
                });
              }
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
    calculateDataCount,
  } as ArrayRenderStrategy<Email> & { calculateDataCount: typeof calculateDataCount };
}
