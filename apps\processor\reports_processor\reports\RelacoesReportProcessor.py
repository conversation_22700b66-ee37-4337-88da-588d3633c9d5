from typing import Dict

from .BaseReportProcessor import BaseReportProcessor, ReportTypeConfig
from ..FormatFrontFormat import VinculoSection
from ..EntityExtractor import VinculoConfig
from ..constants import OtherResultDataTypes


class RelacoesReportProcessor(BaseReportProcessor):
    """Processor for Phone reports"""

    def get_config(self) -> ReportTypeConfig:
        return ReportTypeConfig(
            do_filter_doc='Relacoes',
            do_filter_name='Relacoes',
            do_doc = {'title': 'Diários Oficiais - Relacoes'},
            dados_pessoais={'title': 'Dad<PERSON> Pessoais'},  # TODO: pode dar merda
            possiveis_pessoas_relacionadas={'title': 'Possíveis Pessoas Relacionadas'},
            possiveis_empresas_relacionadas={'title': 'Empresas Relacionadas'},
            enabled_sections=[
                VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS, VinculoSection.POSSIVEIS_EMPRESAS_RELACIONADAS,
                VinculoSection.PHONES, VinculoSection.EMAILS, VinculoSection.ENDERECOS,
                VinculoSection.PARENTES, VinculoSection.OUTROS_CONTATOS, VinculoSection.IMAGENS, VinculoSection.NOMES_USUARIO,
                VinculoSection.OUTRAS_URLS, VinculoSection.REDES_SOCIAIS,
                VinculoSection.RELACOES

            ]
        )

    def get_custom_vinculos(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[
        str, VinculoConfig]:
        # Phone reports might have simpler vinculo requirements
        return {}

    def adjust_metadata(self, processed_result, metadata):
        self._adjust_metadata_relacoes(processed_result, metadata)
        return


    def _get_basic_section_vinculo_config(self, config=None, has_main_data: bool = True):
        vinculos = {
            VinculoSection.RELACOES: self.make_vinculo_relacoes()
        }
        return vinculos



