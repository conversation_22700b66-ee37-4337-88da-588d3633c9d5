import { Tabs } from '@snap/design-system'
import { usePermissionCheck } from '~/components/router/PermissionGuard';
import { Permission } from '~/helpers/permissions.helper';
import NotAllowed from '~/components/NotAllowed';
import SupportTab from './SupportTab';

const TAB_CONFIG = [
  {
    value: 'suporte',
    label: 'Suporte',
    children: <SupportTab />,
    permission: Permission.ADD_API_KEY,
    order: 1
  },
];

const TabSupportContainer = () => {
  const { checkPermission } = usePermissionCheck();

  const renderTabList = () => {
    return TAB_CONFIG
      .filter(tab => checkPermission(tab.permission))
      .sort((a, b) => a.order - b.order)
      .map(({ value, label, children }) => ({
        value,
        label,
        children
      }));
  }

  const tabList = renderTabList();

  if (tabList.length === 0) {
    return (
      <div className="flex align-center justify-center w-full shadow-md bg-foreground/5 rounded-md ">
        <NotAllowed />
      </div>
    );
  }

  return (
    <Tabs items={tabList} className='[&_[role=tab]]:cursor-pointer' />
  );
};

export default TabSupportContainer;
