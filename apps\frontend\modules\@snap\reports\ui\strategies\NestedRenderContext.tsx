import React, { createContext, useContext } from "react";

export type UpdateNestedSectionFn = (
  nestedSectionTitle: string,
  updater: (nestedSection: any) => void
) => void;

export type NestedRenderScope = {
  isNested: boolean;
  updateNestedSection: UpdateNestedSectionFn;
};

const NestedRenderContext = createContext<NestedRenderScope | null>(null);

export const useNestedRender = () => useContext(NestedRenderContext);

export const NestedRenderProvider: React.FC<{
  value: NestedRenderScope;
  children: React.ReactNode;
}> = ({ value, children }) => {
  return (
    <NestedRenderContext.Provider value={value}>
      {children}
    </NestedRenderContext.Provider>
  );
};

