FROM quay.io/keycloak/keycloak:25.0.6

USER root



# Copy scripts and configuration
COPY assign-roles.sh /opt/keycloak/assign-roles.sh
COPY entrypoint.sh /opt/keycloak/entrypoint.sh
COPY update-docker-secret.sh /opt/keycloak/update-docker-secret.sh
COPY ./cache/cache-ispn-jdbc-ping-local.xml /opt/keycloak/conf/cache-ispn-jdbc-ping-local.xml
COPY ./cache/cache-ispn-jdbc-ping-production.xml /opt/keycloak/conf/cache-ispn-jdbc-ping-production.xml
COPY ./cache/cache-ispn-jdbc-ping-staging.xml /opt/keycloak/conf/cache-ispn-jdbc-ping-staging.xml
COPY deployment_type.txt /opt/keycloak/deployment_type.txt

# Set permissions
RUN chmod +x /opt/keycloak/*.sh && \
    chmod 600 /opt/keycloak/conf/cache-ispn-jdbc-ping-local.xml && \
    chmod 600 /opt/keycloak/conf/cache-ispn-jdbc-ping-production.xml && \
    chmod 600 /opt/keycloak/conf/cache-ispn-jdbc-ping-staging.xml && \
    chown -R 1000:1000 /opt/keycloak/

# Security: Default environment variables
ARG KC_HTTP_RELATIVE_PATH="/"

RUN /opt/keycloak/bin/kc.sh build \
    --db=postgres \
    --features=admin-fine-grained-authz,token-exchange,admin2 \
    --http-relative-path=${KC_HTTP_RELATIVE_PATH}

# Switch back to non-root user for security
USER 1000

# Build Keycloak for production optimization with relative path
RUN /opt/keycloak/bin/kc.sh build --db=postgres --features=admin-fine-grained-authz,token-exchange,admin2 --http-relative-path=/authkc

ENTRYPOINT ["/opt/keycloak/entrypoint.sh"]

CMD []