import { useAuthStore } from "./store";

// Hook-based selectors for reactivity in components
export const useUser = () => useAuthStore((state) => state.authentication.user);
export const useIsAuthenticated = () =>
  useAuthStore((state) => state.authentication.isAuthenticated);
export const useSession = () =>
  useAuthStore((state) => state.authentication.session);
export const usePermissions = () =>
  useAuthStore((state) => state.authorization.permissions);
export const useIsAuthorized = () =>
  useAuthStore((state) => !!state.authorization.isAuthorized);
