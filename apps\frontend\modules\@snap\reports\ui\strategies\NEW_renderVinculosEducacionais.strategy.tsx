import React, { useMemo, useCallback } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { formatFieldValue, parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { ValueWithSource } from "../../model/ValueWithSource";
import { ItemSeparator } from "../components/ItemSeparator";
import { useNestedRender } from "./NestedRenderContext";
import {
  VinculoEducacional,
  VinculosEducacionaisSection,
  TestFunctions, TestFunction,
  SectionTestFunction,
  CalculateFunction,
  UpdaterFunction
} from "../../model/VinculosEducacionais";

export function useRenderVinculosEducacionais(
  sectionTitle: string
): ArrayRenderStrategy<VinculoEducacional> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const includeKey = useCallback((isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted, [isTrash]);

  const testFunctions = useMemo((): TestFunctions => ({
    empresaEducadora: (e: VinculoEducacional) => e.empresa_educadora?.is_deleted === true,
    detalhes: (e: VinculoEducacional) => e.detalhes
      ? Object.values(e.detalhes).every((v: ValueWithSource<string>) => v.is_deleted === true)
      : false,
  }), []);

  const testEntryDeleted: TestFunction = useCallback((entry: VinculoEducacional): boolean => {
    const isEmpresaEducadoraDeleted = testFunctions.empresaEducadora(entry);
    const areDetalhesDeleted = testFunctions.detalhes(entry);

    return isEmpresaEducadoraDeleted && areDetalhesDeleted;
  }, [testFunctions]);

  const testSectionDeleted: SectionTestFunction = useCallback((section: VinculosEducacionaisSection): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted),
    [testEntryDeleted]);

  const calculateDataCount: CalculateFunction = useCallback((section: VinculosEducacionaisSection): number => {
    if (!Array.isArray(section.data)) return 0;
    return section.data.reduce((count: number, entry: VinculoEducacional) => {
      return testEntryDeleted(entry) ? count : count + 1;
    }, 0);
  }, [testEntryDeleted]);

  const applyCascadingDeletion: (entry: VinculoEducacional, targetDeletedState: boolean) => void = useCallback((entry: VinculoEducacional, targetDeletedState: boolean) => {
    if (entry.detalhes) {
      Object.values(entry.detalhes).forEach((detalhe: ValueWithSource<string>) => {
        if (detalhe) detalhe.is_deleted = targetDeletedState;
      });
    }
  }, []);

  const shouldShowEntry: TestFunction = useCallback((entry: VinculoEducacional): boolean => {
    const hasDeletedContent = (field: ValueWithSource<string> | Record<string, ValueWithSource<string>> | undefined): boolean => {
      if (!field) return false;
      if (typeof field === 'object') {
        return 'is_deleted' in field ? field.is_deleted === true :
          Object.values(field).some((v: ValueWithSource<string>) => v.is_deleted === true);
      }
      return false;
    };

    const hasNonDeletedContent = (field: ValueWithSource<string> | Record<string, ValueWithSource<string>> | undefined): boolean => {
      if (!field) return false;
      if (typeof field === 'object') {
        return 'is_deleted' in field ? field.is_deleted !== true :
          Object.values(field).some((v: ValueWithSource<string>) => v.is_deleted !== true);
      }
      return false;
    };

    const checkFunction = isTrash ? hasDeletedContent : hasNonDeletedContent;

    return checkFunction(entry.empresa_educadora) ||
      checkFunction(entry.detalhes);
  }, [isTrash]);

  const updateEntries = useCallback((
    updater: UpdaterFunction,
    testDeleted: TestFunction = testEntryDeleted,
    testSectionDel: SectionTestFunction = testSectionDeleted,
    calcDataCount: CalculateFunction = calculateDataCount
  ) => {
    actions.updateSectionEntries?.(
      sectionTitle,
      updater,
      testDeleted as (entry: Record<string, any>) => boolean,
      testSectionDel as (section: Record<string, any>) => boolean,
      calcDataCount as (section: Record<string, any>) => number
    );
  }, [actions, sectionTitle, testEntryDeleted, testSectionDeleted, calculateDataCount]);

  const calculateNestedDataCount = useCallback((nestedSection: VinculosEducacionaisSection): number => {
    if (!Array.isArray(nestedSection.data)) return 0;
    return nestedSection.data.reduce((count: number, entry: VinculoEducacional) => {
      const isEntryDeleted: boolean = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  }, [testEntryDeleted]);

  const EmpresaEducadoraBlock = React.memo(({
    entry,
    index,
    sectionTitle
  }: {
    entry: VinculoEducacional;
    index: number;
    sectionTitle: string;
  }) => {
    const nested = useNestedRender();

    if (!entry?.empresa_educadora || !includeKey(entry.empresa_educadora.is_deleted || false)) return null;

    const handleToggleEmpresaEducadora = useCallback(() => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: VinculosEducacionaisSection) => {
          const targetEntry = nestedSection.data?.[index];
          if (targetEntry?.empresa_educadora) {
            targetEntry.empresa_educadora.is_deleted = !targetEntry.empresa_educadora.is_deleted;
            applyCascadingDeletion(targetEntry, targetEntry.empresa_educadora.is_deleted);
          }
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: VinculoEducacional, i?: number) => {
        if (i === index && e.empresa_educadora) {
          e.empresa_educadora.is_deleted = !e.empresa_educadora.is_deleted;
          applyCascadingDeletion(e, e.empresa_educadora.is_deleted);
        }
      });
    }, [nested, index, updateEntries, applyCascadingDeletion]);

    return (
      <CustomGridContainer cols={1} key={`empresa_educadora-${index}`}>
        <CustomGridItem
          fullWidth
          className="mb-4"
          onToggleField={handleToggleEmpresaEducadora}
        >
          <CustomReadOnlyInputField
            label={entry.empresa_educadora.label.toUpperCase()}
            colorClass="bg-primary"
            labelTextClass="text-accent"
            value={parseValue(formatFieldValue(entry.empresa_educadora.value))}
            tooltip={renderSourceTooltip(entry.empresa_educadora.source)}
          />
        </CustomGridItem>
      </CustomGridContainer>
    );
  });

  const DetalhesBlock = React.memo(({
    entry,
    index,
    sectionTitle
  }: {
    entry: VinculoEducacional;
    index: number;
    sectionTitle: string;
  }) => {
    const nested = useNestedRender();

    if (!entry?.detalhes) return null;
    const subs = Object.entries(entry.detalhes).filter(([, v]) =>
      includeKey((v as ValueWithSource<string>).is_deleted || false)
    );
    if (!subs.length) return null;

    const handleToggleDetalhe = useCallback((fieldKey: string) => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection: VinculosEducacionaisSection) => {
          const targetEntry = nestedSection.data?.[index];
          const d = targetEntry?.detalhes?.[fieldKey];
          if (d) d.is_deleted = !d.is_deleted;
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      updateEntries((e: VinculoEducacional, i?: number) => {
        if (i === index) {
          const d = e.detalhes?.[fieldKey];
          if (d) d.is_deleted = !d.is_deleted;
        }
      });
    }, [nested, index, updateEntries]);

    return (
      <CustomGridContainer
        cols={2}
        columnFirst
        key={`detalhes-${index}`}
        className="mb-6"
      >
        {subs.map(([fieldKey, val]) => (
          <CustomGridItem
            key={fieldKey}
            cols={1}
            onToggleField={() => handleToggleDetalhe(fieldKey)}
          >
            <CustomReadOnlyInputField
              label={translatePropToLabel((val as ValueWithSource<string>).label || fieldKey).toUpperCase()}
              value={parseValue(formatFieldValue((val as ValueWithSource<string>).value))}
              tooltip={renderSourceTooltip((val as ValueWithSource<string>).source)}
            />
          </CustomGridItem>
        ))}
      </CustomGridContainer>
    );
  });

  const formatByKey: Record<
    string,
    (entry?: VinculoEducacional, index?: number) => React.ReactElement | null
  > = {
    empresa_educadora: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <EmpresaEducadoraBlock entry={entry} index={index} sectionTitle={sectionTitle} />;
    },

    detalhes: (entry, index) => {
      if (!entry || index === undefined) return null;
      return <DetalhesBlock entry={entry} index={index} sectionTitle={sectionTitle} />;
    },
  };

  const validateKeys = (keys: Array<keyof VinculoEducacional>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: VinculoEducacional, index?: number): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof VinculoEducacional>;

    if (!validateKeys(keys)) {
      console.warn(`[Seção ${sectionTitle}] Chaves inválidas:`, keys);
    }

    const orderedKeys: Array<keyof VinculoEducacional> = [
      'empresa_educadora',
      'detalhes'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    const elements: React.ReactElement[] = [];

    filteredKeys.forEach((chave) => {
      const element = formatByKey[chave]?.(entry, index);
      if (element) elements.push(element);
    });

    return elements;
  };

  const render = (dataArray: VinculoEducacional[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn(`[Seção ${sectionTitle}] esperava array, mas recebeu:`, typeof dataArray);
      return [];
    }

    const filteredData = dataArray
      .map((entry, originalIndex) => ({ entry, originalIndex }))
      .filter(({ entry }) => shouldShowEntry(entry));

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach(({ entry, originalIndex }, filterIndex) => {
      const elements = renderSingleItem(entry, originalIndex);

      if (filteredData.length > 1) {
        const isLastItem = filterIndex === filteredData.length - 1;
        allElements.push(
          <div
            key={`vinculo-educacional-${originalIndex}`}
            className={`relative mb-8 pb-1 ${isLastItem ? '!mb-0 !pb-0' : ''}`}
          >
            {elements}
            {!isLastItem && <ItemSeparator />}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      ((entry: VinculoEducacional) => {
        if (entry.empresa_educadora) {
          entry.empresa_educadora.is_deleted = targetDeletedState;
        }
        applyCascadingDeletion(entry, targetDeletedState);
      }) as UpdaterFunction,
      testEntryDeleted as (entry: Record<string, any>) => boolean,
      testSectionDeleted as (section: Record<string, any>) => boolean,
      calculateDataCount as (section: Record<string, any>) => number
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
