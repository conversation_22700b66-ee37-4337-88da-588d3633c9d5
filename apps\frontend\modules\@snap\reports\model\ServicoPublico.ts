import { ValueWithSource } from "./ValueWithSource";

interface RemuneracaoValue {
    "bonificacao de ferias"?: ValueWithSource<string>;
    "remuneracao liquida"?: ValueWithSource<string>;
    "valor bruto"?: ValueWithSource<string>;
    [key: string]: ValueWithSource<string> | undefined;
}

interface RemuneracaoItem {
    value: RemuneracaoValue;
    label: string;
    source: string[];
    is_deleted: boolean;
}

export interface ServicoPublico {
    nome_completo: ValueWithSource<string>;
    remuneracao?: RemuneracaoItem[];
    detalhes?: Record<string, ValueWithSource<string>>;
}
