import { ItemType } from "root/domain/entities/base.model";
import { FolderModel } from "root/domain/entities/folder.model";
import { ReportModel } from "root/domain/entities/report.model";

export function isReportModel(item: any): item is ReportModel {
  return item && item.type === ItemType.REPORT;
}

export function isFolderModel(item: any): item is FolderModel {
  return item && item.type === ItemType.FOLDER;
}