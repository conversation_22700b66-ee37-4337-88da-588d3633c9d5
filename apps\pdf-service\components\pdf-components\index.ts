/**
 * Pure JSX PDF Components
 * 
 * This module provides pure JSX equivalents of @react-pdf/renderer components
 * that can be used for HTML rendering while maintaining the same API and behavior.
 * 
 * These components are designed to:
 * 1. Replicate the behavior and styles from @react-pdf/renderer
 * 2. Work with standard HTML/CSS rendering
 * 3. Maintain compatibility with existing PDF component usage
 * 4. Provide a seamless migration path from PDF to HTML rendering
 */

// Core document components
export { Document } from './Document';
export type { DocumentProps } from './Document';

export { Page } from './Page';
export type { PageProps } from './Page';

export { View } from './View';
export type { ViewProps } from './View';

export { Text } from './Text';
export type { TextProps } from './Text';

export { Image } from './Image';
export type { ImageProps, BaseImageProps, ImageWithSrcProp, ImageWithSourceProp } from './Image';

// SVG components
export { Svg, Rect, Path } from './Svg';
export type { SVGProps, RectProps, PathProps, SVGPresentationAttributes } from './Svg';

// Link component
export { Link } from './Link';
export type { LinkProps } from './Link';

// Utility
export { StyleSheet } from './StyleSheet';
export type { Styles } from './StyleSheet';

// Print styles
export { PrintStyles } from './PrintStyles';

// Re-export everything as default for convenience
import { Document } from './Document';
import { Page } from './Page';
import { View } from './View';
import { Text } from './Text';
import { Image } from './Image';
import { Svg, Rect, Path } from './Svg';
import { Link } from './Link';
import { StyleSheet } from './StyleSheet';
import { PrintStyles } from './PrintStyles';

export default {
  Document,
  Page,
  View,
  Text,
  Image,
  Svg,
  Rect,
  Path,
  Link,
  StyleSheet,
  PrintStyles,
};
