import { FolderModel } from "root/domain/entities/folder.model";
import { getInitials } from "~/helpers";
import {
  SubjectCard,
  Separator,
  Avatar,
  Button,
  Text,
  Loading,
} from "@snap/design-system";
import { ArrowR<PERSON>, Pencil, Trash, X } from "lucide-react"
import { MdFolderCopy } from "react-icons/md";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { MdOutlineDriveFileMove } from "react-icons/md";
import { BiMerge } from "react-icons/bi";
import { AiOutlineLoading3Quarters } from "react-icons/ai";

export default function FolderCard({
  folder,
  onOpen,
  onMove,
  onMerge,
  onRename,
  onDelete,
  isDeletingFolder = false,
  isRenamingFolder = false,
  isMovingFolder = false,
  isMergingFolders = false,
}: {
  folder: FolderModel;
  onOpen?: (folder: FolderModel) => void;
  onMove?: (folder: FolderModel) => void;
  onMerge?: (folder: FolderModel) => void;
  onRename?: (folder: FolderModel) => void;
  onDelete?: (folder: FolderModel) => void;
  isDeletingFolder?: boolean;
  isRenamingFolder?: boolean;
  isMovingFolder?: boolean;
  isMergingFolders?: boolean;
}) {
  const childFolders = folder.childFolders;
  const childReports = folder.childReports;

  const header = (
    <>
      <div
        className="flex items-center bg-background justify-between gap-3 p-3 w-full cursor-pointer hover:bg-muted/50 transition-colors duration-200 group"
        onClick={() => onOpen?.(folder)}
      >
        <MdFolderCopy className="size-8 text-secondary" />
        <span className="uppercase line-clamp-2 text-ellipsis max-w-4/5 group-hover:underline">
          {folder.name}
        </span>
      </div>
      <Separator />
    </>
  );

  const content = (
    <div className="flex flex-wrap gap-3.5 p-3 justify-start" id="this is crazy">
      {childFolders.map((childFolder) => (
        <div key={childFolder.folder_id} className="border-border border-4 rounded-full p-3" title={childFolder.folder_name} >
          <MdFolderCopy className="size-5 text-secondary" />
        </div>
      ))}

      {childReports.map((report) => {
        const status = typeof report.report_status === 'object' ? report.report_status.status_report : report.report_status;
        const isPending = status === REPORT_CONSTANTS.status.pending;
        const isError = status === REPORT_CONSTANTS.status.error;

        return (
          <div key={report.user_reports_id} title={report.subject_name || status}>
            {isPending ? (
              <div className="border-border border-4 rounded-full p-3" title={report.subject_name}>
                <Loading size="lg" color="var(--accent)" />
              </div>
            ) : isError ? (
              <div className="border-border border-4 rounded-full p-3" title={report.subject_name}>
                <X className="size-5 text-destructive" />
              </div>
            ) : (
              <Avatar
                fallback={getInitials(report.subject_name) || "N/A"}
                size="md"
                textAlign="right"
                textClassName="text-sm uppercase"
              />
            )}
          </div>
        );
      })}

      {(childFolders.length === 0 && childReports.length === 0) && (
        <Text variant="body-md" align="center" className="py-2">
          Pasta vazia
        </Text>
      )}
      {
        (folder.numberOfItems && folder.numberOfItems > 0) && (
          <div className="flex items-center justify-center rounded-full p-2 bg-border h-[52px] w-[52px] overflow-hidden" title={`+${folder.numberOfItems} itens`}>
            <Text variant="body-md" align="center" className="font-bold truncate w-full text-center">
              +{folder.numberOfItems}
            </Text>
          </div>
        )
      }
    </div>
  );

  const actions = (
    <Button
      size="sm"
      variant="outline"
      iconPosition="right"
      icon={<ArrowRight className="size-4" />}
      className="w-full"
      onClick={() => onOpen?.(folder)}
    >
      Abrir
    </Button>
  );

  const menu = [
    {
      label: "mover para pasta",
      icon: isMovingFolder ? (
        <AiOutlineLoading3Quarters size={16} className="animate-spin" />
      ) : (
        <MdOutlineDriveFileMove className="size-4" />
      ),
      onClick: () => onMove?.(folder),
      disabled: isMovingFolder,
    },
    {
      label: "Combinar pastas",
      icon: isMergingFolders ? (
        <AiOutlineLoading3Quarters size={16} className="animate-spin" />
      ) : (
        <BiMerge className="size-4" />
      ),
      onClick: () => onMerge?.(folder),
      disabled: isMergingFolders,
    },
    {
      label: "renomear pasta",
      icon: isRenamingFolder ? (
        <AiOutlineLoading3Quarters size={16} className="animate-spin" />
      ) : (
        <Pencil className="size-4" />
      ),
      onClick: () => onRename?.(folder),
      disabled: isRenamingFolder,
    },
    {
      label: "Excluir pasta",
      icon: isDeletingFolder ? (
        <AiOutlineLoading3Quarters size={16} className="animate-spin" />
      ) : (
        <Trash className="size-4" />
      ),
      onClick: () => onDelete?.(folder),
      disabled: isDeletingFolder,
    },
  ];

  return (
    <SubjectCard
      headerClassName="p-0"
      header={header}
      content={content}
      actions={actions}
      menu={menu}
      menuWrapperClassName="top-0 right-0"
      markShadowClassName="!bg-transparent"
      contentClassName="!bg-background [&>div>div]:!pb-0 [&>div>div]:!space-y-0 !mt-0 !px-0"
      menuClassName="border border-card"
      cardClassName="max-w-[282px]"
      footerClassName="[&_p]:text-sm !bg-background"
      menuTriggerType="click"
      border={true}
      footer={{
        createdAt: folder.createdAt,
        updatedAt: folder.modifiedAt,
      }}
    />
  );
}