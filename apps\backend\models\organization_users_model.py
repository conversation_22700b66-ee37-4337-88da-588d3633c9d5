from sqlalchemy import Column, ForeignKey, DateTime, text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, ENUM as PGEnum

from models.base import Base
from models.enums import user_status

class OrganizationUsers(Base):
    __tablename__ = 'organization_users'
    __table_args__ = {"schema": "reports"}

    organization_users_id = Column(PostgresUUID, primary_key=True, server_default=func.gen_random_uuid())
    organization_id = Column(PostgresUUID, ForeignKey("reports.organizations.organization_id"), nullable=False, index=True)
    user_id = Column(PostgresUUID, ForeignKey("reports.users.user_id"), nullable=False, index=True)
    status = Column(
        PGEnum(user_status, name="user_status", schema="reports", create_type=True),
        nullable=False,
        index=True,
        server_default='ativo'  # database default
    )
    joined_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=text("timezone('UTC', now())"),
        index=True
    )
    exited_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    organization = relationship("Organizations", back_populates="users")
    user = relationship("Users", back_populates="organizations")

    def __repr__(self):
        return f"<OrganizationUsers(organization_users_id={self.organization_users_id}, organization_id={self.organization_id}, user_id={self.user_id})>"
