import React from 'react'
import { Input, Textarea } from '@snap/design-system'
import { ReportsCustomLabel, TooltipProps } from './ReportsCustomLabel'

interface InputFieldProps {
  label?: string
  value: string | number
  icon?: React.ReactNode
  element?: 'input' | 'textarea'
  tooltip?: TooltipProps
  colorClass?: string
  className?: string
  labelTextClass?: string
  isFirstLabelList?: boolean
}

export const CustomReadOnlyInputField: React.FC<InputFieldProps> = ({
  label,
  isFirstLabelList = false,
  value,
  icon = undefined,
  element = 'input',
  tooltip = undefined,
  colorClass = 'bg-secondary',
  className = '',
  labelTextClass = '',
}) => {
  return (
    <div className="space-y-1 w-full">
      {label &&
        <div className={isFirstLabelList ? '-ml-5' : 'ml-0'}>
          <ReportsCustomLabel label={label} icon={icon} tooltip={tooltip} colorClass={colorClass} labelTextClass={labelTextClass} />
        </div>}
      {
        value !== '' && (element === 'textarea' ? (
          <Textarea
            onFocus={(e) => e.target.select()}
            value={String(value)}
            readOnly
            className="rounded-none border-0 w-full border-b border-dashed border-neutral-400"
          />
        ) : (
          <Input
            onFocus={(e) => e.target.select()}
            value={String(value)}
            readOnly
            className={`rounded-none border-0 w-full border-b-1 border-neutral-400 border-dashed pl-0  uppercase ${className} focus-within:border-b-1`}
          />
        ))
      }
    </div>
  )
}
