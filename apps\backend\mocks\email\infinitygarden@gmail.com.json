{"SNAP": [{"pessoa": [{"cpf": "38244335831", "first names": "NEYMAR", "surname": "DA SILVA SANTOS JUNIOR", "full name": "NEYMAR DA SILVA SANTOS JUNIOR", "sexo": "M", "pessoa": [{"full name": "NADINE GONCALVES DA SILVA SANTOS", "label default key": "parente MAE"}], "data nascimento": "05/02/1992", "phonenumber": [{"phone number": "5511976141493"}, {"phone number": "551123713572"}, {"phone number": "5535987013285"}, {"phone number": "5511946265535"}, {"phone number": "5511992699562"}, {"phone number": "5511940485117"}, {"phone number": "5511988786905"}, {"phone number": "5562993532493"}, {"phone number": "5511991831225"}, {"phone number": "5531982951265"}, {"phone number": "5573999769326"}, {"phone number": "5527996039664"}, {"phone number": "5511934438135"}, {"phone number": "5571999693589"}, {"phone number": "5566999440724"}, {"phone number": "5521968178512"}, {"phone number": "5521977528324"}, {"phone number": "5531989699297"}, {"phone number": "5511915199260"}, {"phone number": "5551993863681"}, {"phone number": "5511966340675"}, {"phone number": "5511976112940"}, {"phone number": "5511994345044"}, {"phone number": "5521992548818"}, {"phone number": "5511992745753"}, {"phone number": "5511966421354"}, {"phone number": "5513991996002"}, {"phone number": "5596991127143"}, {"phone number": "5538999547958"}, {"phone number": "5513988129961"}, {"phone number": "5513988060897"}, {"phone number": "5524993096563"}, {"phone number": "5513991133894"}, {"phone number": "5524988257633"}, {"phone number": "5512920007110"}, {"phone number": "5521990397112"}, {"phone number": "552126893144"}, {"phone number": "5573988850553"}, {"phone number": "552126893051"}, {"phone number": "5567992152053"}, {"phone number": "5592999647099"}, {"phone number": "552126893413"}, {"phone number": "552126893420"}, {"phone number": "5531985951647"}, {"phone number": "5571981407489"}, {"phone number": "5511992763702"}, {"phone number": "5551985100788"}, {"phone number": "5548984945470"}, {"phone number": "5538998915852"}, {"phone number": "5577981376871"}, {"phone number": "5577981329752"}, {"phone number": "5577981314328"}, {"phone number": "5511934255214"}, {"phone number": "5593991168482"}, {"phone number": "5511943565789"}, {"phone number": "5569999550796"}, {"phone number": "5511910308008"}, {"phone number": "5551995907230"}, {"phone number": "5593991367179"}, {"phone number": "5582981836529"}, {"phone number": "5511975320107"}, {"phone number": "5527996442830"}, {"phone number": "5515998594958"}, {"phone number": "5522997491007"}, {"phone number": "5567998140391"}, {"phone number": "5585988174224"}, {"phone number": "5545991196969"}, {"phone number": "5585986311402"}], "location": [{"logradouro": "RUA SENADOR CESAR LACERDA VERGUEIRO", "label default key": "5511988786905", "numero": "511", "bairro": "SUMAREZINHO", "cep ou zipcode": "05435060", "city": "SAO PAULO", "area": "SP", "complemento": "APT 21"}, {"logradouro": "RUA SENADOR CESAR LACERDA VERGUEIRO", "label default key": "5511940485117", "numero": "0", "complemento": "APT 21", "bairro": "SUMAREZINHO", "cep ou zipcode": "05435060", "city": "SAO PAULO", "area": "SP"}, {"logradouro": "AVENIDA ALMIRANTE COCHRANE", "label default key": "5513991996002", "numero": "83", "complemento": "2", "bairro": "EMBARE", "cep ou zipcode": "11040001", "city": "SANTOS", "area": "SP"}, {"logradouro": "AVENIDA ALMIRANTE COCHRANE", "label default key": "5511934438135", "complemento": "AP 202", "bairro": "EMBARE", "cep ou zipcode": "11040001", "city": "SANTOS", "area": "SP", "numero": "83"}, {"logradouro": "AVENIDA ALMIRANTE COCHRANE", "label default key": "5571999693589", "numero": "83", "complemento": "SEM COMPLEMENTO", "bairro": "EMBARE", "cep ou zipcode": "11040001", "city": "SANTOS", "area": "SP"}, {"logradouro": "RUA AV ANA COSTA", "label default key": "5596991127143", "numero": "56", "bairro": "VL MATHIAS", "cep ou zipcode": "11060000", "city": "SANTOS", "area": "SP"}, {"logradouro": "AV ANA COSTA", "label default key": "5538999547958", "bairro": "VL MATHIAS", "cep ou zipcode": "11060000", "city": "SANTOS", "area": "SP"}, {"logradouro": "ANA COSTA", "label default key": "5513988129961", "numero": "0", "bairro": "VILA MATIAS", "cep ou zipcode": "11060000", "city": "SANTOS", "area": "SP"}, {"logradouro": "ANA COSTA", "label default key": "5513988060897", "numero": "56", "bairro": "VILA MATIAS", "cep ou zipcode": "11060000", "city": "SANTOS", "area": "SP"}, {"logradouro": "RUA GENI APARECIDA DE MOURA", "label default key": "5524988257633", "bairro": "GLORIA", "cep ou zipcode": "11724165", "city": "PRAIA GRANDE", "area": "SP", "numero": "374"}, {"logradouro": "RUA PROFESSORA CARMEN MARINA DOTTA", "label default key": "5512920007110", "bairro": "JARDIM SANTA TEREZA", "cep ou zipcode": "12045150", "city": "TAUBATE", "area": "SP"}, {"logradouro": "RUA YUCATAN", "label default key": "5521990397112", "numero": "247", "bairro": "INHAUMA", "cep ou zipcode": "21061570", "city": "RIO DE JANEIRO", "area": "RJ"}, {"logradouro": "R DAS PALMEIRAS GLEBA K", "label default key": "5551985100788", "complemento": "KM 6 KM 6 LE B 4 4", "bairro": "COND PORTO BELO", "cep ou zipcode": "23860000", "city": "PORTO BELO", "area": "RJ"}, {"logradouro": "ROD RIO SANTOS", "numero": "0", "bairro": "MANGARATIBA", "cep ou zipcode": "23860000", "city": "MANGARATIBA", "area": "RJ", "label default key": "552126893051"}, {"logradouro": "ROD RIO SANTOS", "label default key": "5548984945470", "complemento": "KM 438 S N", "bairro": "COND PORT", "cep ou zipcode": "23860000", "city": "MANGARATIBA", "area": "RJ"}, {"logradouro": "R DAS PALMEIRAS GLEBA K", "label default key": "552126893420", "complemento": "KM 6 KM 6 LE B", "bairro": "COND PORTO BELO", "cep ou zipcode": "23860000", "city": "PORTO BELO", "area": "RJ"}, {"logradouro": "RUA DO FLAMENGO", "label default key": "5538998915852", "numero": "351", "bairro": "MARACANA", "cep ou zipcode": "39403069", "city": "MONTES CLAROS", "area": "MG"}, {"logradouro": "RUA GUILHERME AGUIAR", "label default key": "5577981314328", "bairro": "CENTRO", "cep ou zipcode": "45000815", "city": "VITORIA DA CONQUISTA", "area": "BA"}, {"logradouro": "RUA CAPITAO JUSTINIANO LOPES DA CUNHA", "label default key": "5522997491007", "numero": "1101", "bairro": "JARDIM BANDEIRANTES", "cep ou zipcode": "61934210", "city": "MARACANAU", "area": "CE"}, {"logradouro": "RUA PEDRO CLAUDINO DA SILVA", "label default key": "5567998140391", "complemento": "CASA 2", "bairro": "JARDIM FLAMBOYANT", "cep ou zipcode": "79630370", "city": "TRES LAGOAS", "area": "MS"}], "full name1": "NEYMAR DA SILVA SANTOS JINIOR", "full name2": "LEANDRO CAETANO SOARES"}]}], "PIPLEmail": [{"pessoa": [{"first names": "NEYMAR", "surname": "<PERSON>", "surname1": "DA SILVA SANTOS JUNIOR", "full name": "NEYMAR DA SILVA SANTOS JUNIOR", "idade": "33", "data nascimento": "02/05/1992", "sexo": "Male", "idioma": "pt, fr_FR", "cpf": "38244335831", "vinculo": [{"rotulo": "CPF"}], "pessoa": [{"first names": "Nadine", "surname": "<PERSON>", "full name": "NADINE GONCALVES DA SILVA SANTOS", "vinculo": [{"rotulo": "parente MAE", "classificacao": "Familia"}]}], "alias": [{"alias": "<PERSON><PERSON><PERSON>"}, {"alias": "<PERSON><PERSON>"}, {"alias": "neymar.das<PERSON>.39"}], "imagem": [{"url": "https://sun6-23.userapi.com/impf/c847122/v847122650/1d8265/YY1yqLcjq-E.jpg?size=200x0&quality=96&crop=0,169,686,686&sign=d20ed22b80afb2b854a19fb1e26ee204&ava=1"}], "endereco": [{"nome": "Rod <PERSON>, Mangaratiba, Rio de Janeiro", "estado ou regiao": "RJ", "logradouro": "Rod <PERSON>, Mangaratiba, Rio de Janeiro", "cidade": "Mangaratiba", "cep ou zipcode": "23860000", "pais": "BR"}, {"nome": "83-2 <PERSON>v <PERSON><PERSON>, <PERSON>, Sao Paulo", "estado ou regiao": "SP", "logradouro": "AV ALM COCHRANE", "numero": "83", "cidade": "<PERSON>", "cep ou zipcode": "11040001", "pais": "BR"}, {"nome": "Paris, France", "logradouro": "Paris, France", "cidade": "Paris", "pais": "FR"}, {"nome": "Lyon, France", "logradouro": "Lyon, France", "cidade": "Lyon", "pais": "FR"}], "emailaddress": [{"email address": "<EMAIL>", "tipo de email": "Pessoal", "provedor de email": "<PERSON>m"}, {"email address": "<EMAIL>", "tipo de email": "Pessoal", "provedor de email": "<PERSON>m"}, {"email address": "<EMAIL>", "tipo de email": "Pessoal", "provedor de email": "<PERSON>m"}, {"email address": "<EMAIL>", "tipo de email": "Pessoal", "provedor de email": "<PERSON>m"}, {"email address": "david<PERSON><PERSON><EMAIL>", "tipo de email": "Pessoal", "provedor de email": "<PERSON>m"}], "empresa": [{"razao social": "PARIS-SEN-GERMEIN", "vinculo": [{"descricao": "Paris-Sen-Germein", "cargo atual": "Paris-Sen-Germein", "rotulo": "vinculo empregaticio"}]}], "phonenumber": [{"phone number": "5511934438135", "country code": 55, "tipo": "celular"}, {"phone number": "5538999547958", "country code": 55, "tipo": "celular"}, {"phone number": "5513991133894", "country code": 55, "tipo": "celular"}, {"phone number": "5566999440724", "country code": 55, "tipo": "celular"}, {"phone number": "5532999250748", "country code": 55, "tipo": "celular"}, {"phone number": "5512920007110", "country code": 55, "tipo": "celular"}, {"phone number": "5524988257633", "country code": 55, "tipo": "celular"}, {"phone number": "5515988157656", "country code": 55, "tipo": "celular"}, {"phone number": "5531989697742", "country code": 55, "tipo": "celular"}, {"phone number": "5531987560261", "country code": 55, "tipo": "celular"}, {"phone number": "5589988183352", "country code": 55, "tipo": "celular"}, {"phone number": "5511976741100", "country code": 55, "tipo": "celular"}, {"phone number": "33621550465", "country code": 33, "tipo": "celular"}, {"phone number": "551146098286", "country code": 55}, {"phone number": "551159785934", "country code": 55}, {"phone number": "552126893051", "country code": 55}, {"phone number": "552126893413", "country code": 55}, {"phone number": "551335643333", "country code": 55}, {"phone number": "552126893122", "country code": 55}], "perfil vk": [{"url": "https://vk.com/id433534162/", "vinculo": [{"rotulo": "Perfil pessoal"}]}, {"url": "https://vk.com/id402173701/", "vinculo": [{"rotulo": "Perfil pessoal"}]}, {"id perfil": "433534162", "vinculo": [{"rotulo": "vk"}]}, {"id perfil": "402173701", "vinculo": [{"rotulo": "vk"}]}], "perfil facebook": [{"url do perfil": "http://www.facebook.com/people/_/1187492792", "id perfil": "1187492792", "vinculo": [{"rotulo": "Perfil pessoal"}, {"rotulo": "facebook"}]}, {"url do perfil": "http://www.facebook.com/neymar.dasilva.39", "alias": "neymar.das<PERSON>.39", "vinculo": [{"rotulo": "Perfil pessoal"}]}], "url": [{"url": "http://badoo.com/0263416533", "short title": "http://badoo.com/0263416533", "dominio": "badoo.com", "vinculo": [{"rotulo": "Perfil pessoal"}]}], "perfil de rede social": [{"id perfil": "0263416533", "vinculo": [{"rotulo": "badoo"}]}]}]}], "IRBIS": [{"pessoa": [{"first names": "NEYMAR", "surname": "DA SILVA", "full name": "NEYMAR DA SILVA", "full name1": "RAMZI BARHOUMI", "full name2": "NEYMAR DA-SILVA-SANTOS JUNIOR", "empresa": [{"vinculo": [{"rotulo": "<PERSON><PERSON><PERSON>"}], "razao social": "PARIS-SEN-GERMEIN"}], "idioma": "pt", "idioma1": "fr_FR", "data nascimento": "02/05/1992", "sexo": "<PERSON><PERSON><PERSON><PERSON>", "pessoa": [{"full name": "NADINE GONCALVES DA SILVA SANTOS", "label default key": "family Mother"}], "cpf": "38244335831", "bookmark": 4, "endereco": [{"logradouro": "ROD RIO SANTOS", "cidade": "Mangaratiba", "estado ou regiao": "RJ", "cep ou zipcode": "23860000", "pais": "BR"}, {"logradouro": "AV ALM COCHRANE", "numero": "83", "complemento": "2", "cidade": "<PERSON>", "estado ou regiao": "SP", "cep ou zipcode": "11040001", "pais": "BR"}, {"cidade": "Paris", "pais": "FR"}, {"cidade": "Lyon", "pais": "FR"}], "phonenumber": [{"country code": "55", "area code": "11", "phone number": "5511934438135"}, {"country code": "55", "area code": "38", "phone number": "5538999547958"}, {"country code": "55", "area code": "13", "phone number": "5513991133894"}, {"country code": "55", "area code": "66", "phone number": "5566999440724"}, {"country code": "55", "area code": "32", "phone number": "5532999250748"}, {"country code": "55", "area code": "12", "phone number": "5512920007110"}, {"country code": "55", "area code": "24", "phone number": "5524988257633"}, {"country code": "55", "area code": "15", "phone number": "5515988157656"}, {"country code": "55", "area code": "31", "phone number": "5531989697742"}, {"country code": "55", "area code": "31", "phone number": "5531987560261"}, {"country code": "55", "area code": "89", "phone number": "5589988183352"}, {"country code": "55", "area code": "11", "phone number": "5511976741100"}, {"country code": "33", "phone number": "33621550465"}, {"country code": "55", "area code": "11", "phone number": "551146098286"}, {"country code": "55", "area code": "11", "phone number": "551159785934"}, {"country code": "55", "area code": "21", "phone number": "552126893051"}, {"country code": "55", "area code": "21", "phone number": "552126893413"}, {"country code": "55", "area code": "13", "phone number": "551335643333"}, {"country code": "55", "area code": "21", "phone number": "552126893122"}], "emailaddress": [{"email address": "<EMAIL>", "tipo": "personal", "email valido": "<PERSON>m"}, {"email address": "<EMAIL>", "tipo": "personal", "email valido": "<PERSON>m"}, {"email address": "<EMAIL>", "tipo": "personal", "email valido": "<PERSON>m"}, {"email address": "<EMAIL>", "tipo": "personal", "email valido": "<PERSON>m"}, {"email address": "david<PERSON><PERSON><EMAIL>", "tipo": "personal", "email valido": "<PERSON>m"}], "imagem": [{"url": "https://sun6-23.userapi.com/impf/c849136/v849136950/23862/kfeyyw9u0d4.jpg?size=200x0&quality=96&crop=0,0,477,591&sign=edb0eaa53a3e0dc8cdbd6c12947f4c0d&ava=1"}, {"url": "https://sun6-23.userapi.com/impf/c847122/v847122650/1d8265/YY1yqLcjq-E.jpg?size=200x0&quality=96&crop=0,169,686,686&sign=d20ed22b80afb2b854a19fb1e26ee204&ava=1"}], "perfil": [{"id": "433534162", "link": "https://vk.com/id433534162/"}, {"id": "402173701", "link": "https://vk.com/id402173701/"}, {"id": "0263416533", "link": "http://badoo.com/0263416533"}], "facebook": [{"id": "1187492792", "link": "http://www.facebook.com/people/_/1187492792"}, {"link": "http://www.facebook.com/neymar.dasilva.39", "nome de usuario": "neymar.das<PERSON>.39"}]}]}], "Provedor de Aplicacação da Internet": [{"provedor de aplicacoes de internet": [{"nome": "Amazon", "termo procurado": "<EMAIL>", "existe": "Erro ao realizar a busca - AmazonPAI"}, {"nome": "Facebook", "termo procurado": "<EMAIL>", "existe": "Erro: A aplicacao bloqueou a requisicao."}, {"nome": "Instagram", "termo procurado": "<EMAIL>", "existe": "Erro: A aplicacao bloqueou a requisicao.", "e-mail enviado": false}, {"nome": "<PERSON><PERSON>", "termo procurado": "<EMAIL>", "existe": false}, {"nome": "Milhas123", "termo procurado": "<EMAIL>", "existe": false}, {"nome": "Olx", "termo procurado": "<EMAIL>", "existe": "Erro: A aplicacao bloqueou a requisicao."}]}]}