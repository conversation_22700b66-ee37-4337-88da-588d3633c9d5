import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { AuthMutations, AuthState } from "./interface";
import { AUTH_INITIAL_STATE } from "./state";

export const useAuthStore = create<AuthState & { actions: AuthMutations }>()(
  devtools(
    persist(
      (set) => ({
        ...AUTH_INITIAL_STATE,
        actions: {
          setIsAuthenticated: (isAuthenticated) =>
            set((state) => ({
              authentication: { ...state.authentication, isAuthenticated },
            })),
          setHasAcceptedTerms: (hasAcceptedTerms) =>
            set((state) => ({
              authentication: { ...state.authentication, hasAcceptedTerms },
            })),
          setSession: (session) =>
            set((state) => ({
              authentication: { ...state.authentication, session },
            })),
          setUser: (user) =>
            set((state) => ({
              authentication: { ...state.authentication, user },
            })),
          clearAuthentication: () =>
            set(() => ({
              authentication: AUTH_INITIAL_STATE.authentication,
            })),
          setIsAuthorized: (isAuthorized) =>
            set((state) => ({
              authorization: { ...state.authorization, isAuthorized },
            })),
          setPermissions: (permissions) =>
            set((state) => ({
              authorization: { ...state.authorization, permissions },
            })),
          clearAuthorization: () =>
            set(() => ({
              authorization: AUTH_INITIAL_STATE.authorization,
            })),
        },
      }),
      {
        name: "auth-storage",
        partialize: (state) => ({
          authentication: state.authentication,
          authorization: state.authorization,
        }),
      }
    )
  )
);
