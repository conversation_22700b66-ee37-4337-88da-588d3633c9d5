/**
 * Formata valores de campo para exibição no CustomReadOnlyInputField
 * @param value - Valor a ser formatado (string, array de objetos, objeto, ou qualquer outro tipo)
 * @returns String formatada ou string vazia se inválido
 */
export function formatFieldValue(value: any): string {
  if (value === null || value === undefined || value === '') {
    return '';
  }

  if (typeof value === 'string') {
    return value.trim();
  }

  if (Array.isArray(value)) {
    if (value.length === 0) {
      return '';
    }

    // Para arrays de objetos, extrai todos os valores e concatena
    const extractedValues = value.map(item => {
      if (typeof item === 'string') {
        return item;
      }

      if (typeof item === 'object' && item !== null) {
        return Object.values(item)
          .filter(val => val !== null && val !== undefined && val !== '')
          .join(', ');
      }

      return String(item);
    }).filter(val => val !== '');

    return extractedValues.join(', ');
  }

  if (typeof value === 'object' && value !== null) {
    const objectValues = Object.values(value)
      .filter(val => val !== null && val !== undefined && val !== '')
      .map(val => String(val));

    return objectValues.join(', ');
  }

  if (typeof value === 'number' || typeof value === 'boolean') {
    return String(value);
  }

  return '';
}