export interface CryptoWorkerRequest {
  operacao: string;
  [key: string]: any;
}

export interface CryptoWorkerResponse {
  operacao: string;
  success: boolean;
  [key: string]: any;
  error?: string;
}

export interface CryptoResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface EncryptedPayload {
  encrypted: string;
  iv: string;
}

export interface EncryptedData {
  encrypted: string;
  iv: string;
}

export type EncryptedValue = {
  encrypted: string;
  iv: string;
};
