import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '../pdf-components';
import { ReportSection, ValueWithSource } from '../../global';
import { translatePropToLabel, translateSource, getFieldLabel, getFieldValue, formatFieldValue } from '../../helpers';

interface RenderPrintEnderecoProps {
  section: Omit<ReportSection, 'data'> &  {
    data: Array<{
      detalhes?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
    }>
  };
}

export const RenderPrintEndereco: React.FC<RenderPrintEnderecoProps> = ({ section }) => {
  const validEntries = section.data?.filter(entry =>
    entry.detalhes?.some(detalhe =>
      Object.values(detalhe.value).some(field => !field.is_deleted)
    )
  ) || [];

  if (!validEntries.length) return null;

  return (
    <View style={styles.container} key={section.title} wrap={true}>
      <View style={styles.sectionTitleContainer} wrap={false}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {validEntries.map((entry, entryIndex) => {

        return (
          <View 
            key={`entry-${entryIndex}`} 
            style={styles.entryContainer} 
            wrap={true}
          >
            {entry.detalhes?.map((detalhe, detalheIndex) => {
              const validFields = Object.entries(detalhe.value)
                .filter(([_, field]) => !field.is_deleted);

              if (!validFields.length) return null;

              return (
                <View 
                  key={`detalhe-${detalheIndex}`} 
                  style={styles.addressBlock} 
                  wrap={false}
                >
                  <View style={styles.subtitleContainer} wrap={false}>
                    <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                      <Rect width="4" height="4" fill='#889EA3' />
                    </Svg>
                    <Text style={styles.addressTitle}>ENDEREÇO {detalheIndex + 1}</Text>
                    <Text style={styles.sourceText}>
                      {detalhe.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                    </Text>
                  </View>
                  <View style={styles.grid} wrap={false}>
                    {validFields.map(([fieldKey, fieldValue], fieldIndex) => (
                      <View key={`field-${fieldIndex}`} style={styles.cell} wrap={false}>
                        <View style={styles.infoContainer} wrap={false}>
                          <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                            <Rect width="8" height="8" fill='#CCCCCC' />
                          </Svg>
                          <Text style={styles.label}>{translatePropToLabel(getFieldLabel(fieldKey, fieldValue)).toUpperCase()}</Text>
                          <Text style={styles.sourceText}>
                            {fieldValue?.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                          </Text>
                        </View>
                        <Text style={styles.value}>
                          {formatFieldValue(getFieldValue(fieldValue))}
                        </Text>
                      </View>
                    ))}
                  </View>
                </View>
              );
            })}
          </View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: { width: 4, height: 4, marginRight: 4, marginTop: 1 },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    flexWrap: 'wrap',
  },
  entryContainer: {
    marginBottom: 8,
    paddingTop: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  addressBlock: {
    paddingLeft: 8,
    marginBottom: 12,
    borderBottom: '1pt solid #eee',
    paddingBottom: 8,
  },
  addressTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  grid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  cell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
});
