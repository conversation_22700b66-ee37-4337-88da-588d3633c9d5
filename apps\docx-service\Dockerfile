FROM node:24
WORKDIR /app

RUN npm install -g pnpm@10.5.1

COPY ./apps/docx-service/package.json ./
RUN pnpm install

COPY ./apps/docx-service/ .

# Ensure assets directory exists and has proper permissions
RUN mkdir -p assets && ls -la assets/

RUN pnpm build

# Verify assets were copied correctly
RUN ls -la dist/ && ls -la dist/assets/ || echo "dist/assets not found"

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nodejs

ENV HOME=/home/<USER>

RUN mkdir -p /home/<USER>/home/<USER>

# Ensure nodejs user can access assets
RUN chown -R nodejs:nodejs /app/assets /app/dist/assets || echo "Assets directories not found"

USER nodejs

EXPOSE 3008

CMD ["node", "./dist/main.js"]

