"""
Table Creation Best Practices and Utilities

This module provides utilities and guidelines for creating new tables
in the reports schema following established patterns.
"""

import uuid
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class TableCreationGuide:
    """
    Comprehensive guide and utilities for creating database tables
    following the established patterns in the reports schema.
    """

    # Standard patterns used across the application
    STANDARD_PATTERNS = {
        'primary_key': {
            'type': 'UUID',
            'default': 'gen_random_uuid()',
            'naming': '{table_name}_id'
        },
        'timestamps': {
            'created_at': 'DateTime(timezone=True), server_default=timezone("UTC", now())',
            'modified_at': 'DateTime(timezone=True), server_default=timezone("UTC", now())'
        },
        'schema': 'reports',
        'foreign_key_format': 'reports.{table}.{column}'
    }

    # Common column types and their SQLAlchemy definitions
    COLUMN_TYPES = {
        'uuid_pk': "Column('{name}', UUID, primary_key=True, server_default=func.gen_random_uuid())",
        'uuid_fk': "Column('{name}', UUID, ForeignKey('{fk_ref}'), nullable={nullable})",
        'text': "Column('{name}', Text, nullable={nullable})",
        'string': "Column('{name}', String({length}), nullable={nullable})",
        'integer': "Column('{name}', Integer, nullable={nullable})",
        'bigint': "Column('{name}', BigInteger, nullable={nullable})",
        'boolean': "Column('{name}', Boolean, nullable={nullable}, server_default='{default}')",
        'datetime': "Column('{name}', DateTime(timezone=True), server_default=func.timezone('UTC', func.now()), nullable={nullable})",
        'date': "Column('{name}', Date, nullable={nullable})",
        'jsonb': "Column('{name}', JSONB, nullable={nullable})",
        'enum': "Column('{name}', PGEnum({enum_type}, name='{enum_name}', create_type=True), nullable={nullable}, server_default='{default}')"
    }

    # Index patterns for common scenarios
    INDEX_PATTERNS = {
        'single_btree': "Index('idx_{table}_{column}', '{column}')",
        'composite_btree': "Index('idx_{table}_{columns}', {column_list})",
        'gin_jsonb': "Index('idx_{table}_{column}', '{column}', postgresql_using='gin')",
        'unique': "Index('idx_{table}_{column}_unique', '{column}', unique=True)"
    }

    def __init__(self):
        self.migration_dir = Path(__file__).parent.parent / 'alembic' / 'versions'

    def create_model_template(self, table_name: str, columns: List[Dict], **options) -> str:
        """
        Generate SQLAlchemy model template for a new table.

        Args:
            table_name: Name of the table
            columns: List of column definitions
            **options: Additional options (relationships, etc.)

        Returns:
            Complete SQLAlchemy model code
        """
        class_name = self._to_pascal_case(table_name)

        model_template = f'''from sqlalchemy import Column, DateTime, Text, String, Integer, BigInteger, Boolean, Index, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB, ENUM as PGEnum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from models.base import Base

class {class_name}(Base):
    __tablename__ = '{table_name}'
    __table_args__ = (
        # Add indexes here
{self._generate_indexes(table_name, columns, options.get('indexes', []))}
        {{"schema": "reports"}}
    )

    # Columns
{self._generate_columns(table_name, columns)}

{self._generate_relationships(options.get('relationships', []))}

    def __repr__(self):
        return f"<{class_name}({self._get_primary_key_name(table_name)}={{self.{self._get_primary_key_name(table_name)}}})>"
'''
        return model_template

    def create_migration_file(self, table_name: str, columns: List[Dict], **options) -> str:
        """
        Generate Alembic migration file for creating a new table.

        Args:
            table_name: Name of the table
            columns: List of column definitions
            **options: Additional options

        Returns:
            Migration file path
        """
        from .migration_templates import generate_migration_file

        # Generate revision ID and get previous revision
        revision_id = str(uuid.uuid4())[:8]
        previous_revision = self._get_latest_revision()

        migration_content = generate_migration_file(
            table_name=table_name,
            template_type=options.get('template_type', 'standard'),
            revision_id=revision_id,
            previous_revision=previous_revision,
            **options
        )

        # Customize the migration with specific columns and indexes
        migration_content = self._customize_migration(migration_content, table_name, columns, options)

        # Write to file
        filename = f"{revision_id}_{table_name}.py"
        file_path = self.migration_dir / filename

        with open(file_path, 'w') as f:
            f.write(migration_content)

        logger.info(f"Created migration file: {file_path}")
        return str(file_path)

    def validate_table_design(self, table_name: str, columns: List[Dict]) -> Dict[str, Any]:
        """
        Validate table design against best practices.

        Args:
            table_name: Name of the table
            columns: List of column definitions

        Returns:
            Validation results with suggestions
        """
        issues = []
        suggestions = []
        warnings = []

        # Check naming convention
        if not table_name.islower() or not table_name.replace('_', '').isalnum():
            issues.append("Table name should be lowercase with underscores only")

        # Check for required columns
        column_names = [col['name'] for col in columns]

        # Primary key check
        primary_keys = [col for col in columns if col.get('primary_key')]
        if not primary_keys:
            issues.append("Table must have a primary key")
        elif len(primary_keys) == 1:
            pk = primary_keys[0]
            if not pk['name'].endswith('_id'):
                suggestions.append("Primary key should end with '_id'")
            if pk.get('type') != 'UUID':
                suggestions.append("Consider using UUID for primary key")

        # Timestamp columns
        if 'created_at' not in column_names:
            suggestions.append("Consider adding 'created_at' timestamp")

        # Foreign key validation
        foreign_keys = [col for col in columns if col.get('foreign_key')]
        for fk in foreign_keys:
            if not fk.get('foreign_key', '').startswith('reports.'):
                warnings.append(f"Foreign key {fk['name']} should reference reports schema")

        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'suggestions': suggestions,
            'warnings': warnings
        }

    def _to_pascal_case(self, snake_str: str) -> str:
        """Convert snake_case to PascalCase"""
        return ''.join(word.capitalize() for word in snake_str.split('_'))

    def _get_primary_key_name(self, table_name: str) -> str:
        """Get standard primary key name for table"""
        return f"{table_name}_id"

    def _generate_columns(self, table_name: str, columns: List[Dict]) -> str:
        """Generate column definitions"""
        column_lines = []

        for col in columns:
            col_def = self._create_column_definition(col)
            column_lines.append(f"    {col_def}")

        return '\n'.join(column_lines)

    def _create_column_definition(self, col: Dict) -> str:
        """Create SQLAlchemy column definition"""
        name = col['name']
        col_type = col['type']
        nullable = col.get('nullable', True)
        default = col.get('default')
        foreign_key = col.get('foreign_key')
        primary_key = col.get('primary_key', False)

        # Build column definition
        parts = [f"'{name}'"]

        # Type
        if col_type == 'UUID':
            if primary_key:
                parts.append('UUID')
                parts.append("server_default=func.gen_random_uuid()")
            else:
                parts.append('UUID')
        elif col_type == 'Text':
            parts.append('Text')
        elif col_type == 'String':
            length = col.get('length', 255)
            parts.append(f'String({length})')
        elif col_type == 'DateTime':
            parts.append('DateTime(timezone=True)')
            if name in ['created_at', 'modified_at']:
                parts.append("server_default=func.timezone('UTC', func.now())")
        elif col_type == 'JSONB':
            parts.append('JSONB')
        elif col_type.startswith('ENUM'):
            # Handle enums
            parts.append(col_type)
        else:
            parts.append(col_type)

        # Foreign key
        if foreign_key:
            parts.append(f"ForeignKey('{foreign_key}')")

        # Primary key
        if primary_key:
            parts.append('primary_key=True')

        # Nullable
        if not nullable:
            parts.append('nullable=False')

        # Default
        if default and name not in ['created_at', 'modified_at']:
            if isinstance(default, str):
                parts.append(f"server_default='{default}'")
            else:
                parts.append(f"server_default={default}")

        return f"Column({', '.join(parts)})"

    def _generate_indexes(self, table_name: str, columns: List[Dict], custom_indexes: List[Dict]) -> str:
        """Generate index definitions"""
        index_lines = []

        # Auto-generate indexes for common patterns
        for col in columns:
            col_name = col['name']
            col_type = col['type']

            # Index foreign keys
            if col.get('foreign_key'):
                index_lines.append(f'        Index("idx_{table_name}_{col_name}", "{col_name}"),')

            # Index JSONB columns with GIN
            if col_type == 'JSONB':
                index_lines.append(f'        Index("idx_{table_name}_{col_name}", "{col_name}", postgresql_using="gin"),')

            # Index commonly queried columns
            if col_name in ['email', 'api_key', 'status', 'type']:
                index_lines.append(f'        Index("idx_{table_name}_{col_name}", "{col_name}"),')

        # Add custom indexes
        for idx in custom_indexes:
            if idx['type'] == 'composite':
                columns_str = ', '.join(f'"{col}"' for col in idx['columns'])
                index_lines.append(f'        Index("idx_{table_name}_{idx["name"]}", {columns_str}),')
            elif idx['type'] == 'unique':
                index_lines.append(f'        Index("idx_{table_name}_{idx["column"]}_unique", "{idx["column"]}", unique=True),')

        return '\n'.join(index_lines)

    def _generate_relationships(self, relationships: List[Dict]) -> str:
        """Generate relationship definitions"""
        if not relationships:
            return ""

        rel_lines = ["    # Relationships"]
        for rel in relationships:
            rel_line = f'    {rel["name"]} = relationship("{rel["target"]}", back_populates="{rel["back_populates"]}"'
            if rel.get('cascade'):
                rel_line += f', cascade="{rel["cascade"]}"'
            rel_line += ')'
            rel_lines.append(rel_line)

        return '\n'.join(rel_lines)

    def _customize_migration(self, migration_content: str, table_name: str, columns: List[Dict], options: Dict) -> str:
        """Customize migration template with specific columns and constraints"""
        # This would involve more complex string manipulation
        # For now, return the basic template
        return migration_content

    def _get_latest_revision(self) -> str:
        """Get the latest revision ID from migration files"""
        if not self.migration_dir.exists():
            return None

        migration_files = list(self.migration_dir.glob('*.py'))
        if not migration_files:
            return None

        # Find the latest migration file
        latest_file = max(migration_files, key=lambda p: p.stat().st_mtime)

        # Extract revision ID from file content
        try:
            with open(latest_file) as f:
                content = f.read()
                for line in content.split('\n'):
                    if line.startswith('revision = '):
                        return line.split("'")[1]
        except Exception:
            pass

        return None


# Example usage patterns and utilities
COMMON_TABLE_PATTERNS = {
    'user_data_table': {
        'description': 'Table for storing user-related data',
        'required_columns': [
            {'name': 'user_id', 'type': 'UUID', 'foreign_key': 'reports.users.user_id', 'nullable': False}
        ],
        'suggested_indexes': ['user_id']
    },

    'organization_table': {
        'description': 'Table for organization-related data',
        'required_columns': [
            {'name': 'organization_id', 'type': 'UUID', 'foreign_key': 'reports.organizations.organization_id', 'nullable': True}
        ],
        'suggested_indexes': ['organization_id']
    },

    'lookup_table': {
        'description': 'Static lookup/reference table',
        'required_columns': [
            {'name': 'id', 'type': 'Integer', 'primary_key': True},
            {'name': 'code', 'type': 'String', 'length': 50, 'nullable': False},
            {'name': 'name', 'type': 'Text', 'nullable': False},
            {'name': 'is_active', 'type': 'Boolean', 'default': True, 'nullable': False}
        ],
        'suggested_indexes': ['code', 'is_active']
    },

    'audit_table': {
        'description': 'Table for auditing/logging changes',
        'required_columns': [
            {'name': 'user_id', 'type': 'UUID', 'foreign_key': 'reports.users.user_id', 'nullable': True},
            {'name': 'action', 'type': 'Text', 'nullable': False},
            {'name': 'entity_type', 'type': 'Text', 'nullable': False},
            {'name': 'entity_id', 'type': 'Text', 'nullable': False},
            {'name': 'old_values', 'type': 'JSONB', 'nullable': True},
            {'name': 'new_values', 'type': 'JSONB', 'nullable': True}
        ],
        'suggested_indexes': ['user_id', 'created_at', 'action', 'entity_type']
    }
}

def get_table_creation_checklist() -> Dict[str, List[str]]:
    """
    Get a comprehensive checklist for creating new tables.

    Returns:
        Checklist organized by categories
    """
    return {
        'planning': [
            'Define the purpose and scope of the table',
            'Identify relationships with existing tables',
            'Plan the data types and constraints',
            'Consider indexing strategy',
            'Plan for future scalability'
        ],
        'design': [
            'Use lowercase names with underscores',
            'Include UUID primary key with {table_name}_id format',
            'Add created_at timestamp column',
            'Add modified_at for tables that will be updated',
            'Use proper foreign key constraints',
            'Assign to reports schema',
            'Add appropriate indexes'
        ],
        'implementation': [
            'Create SQLAlchemy model class',
            'Generate Alembic migration',
            'Test migration up and down',
            'Validate schema with schema_validator',
            'Update models/__init__.py imports',
            'Create corresponding Pydantic schemas if needed'
        ],
        'testing': [
            'Test table creation and deletion',
            'Test foreign key constraints',
            'Test indexes performance',
            'Validate with sample data',
            'Run schema validation'
        ],
        'documentation': [
            'Document table purpose and usage',
            'Document relationships',
            'Add to ER diagram if applicable',
            'Update API documentation if exposing data'
        ]
    }

# Utility functions for quick operations
def quick_create_standard_table(table_name: str, additional_columns: List[Dict] = None) -> str:
    """
    Quickly create a standard table with common patterns.

    Args:
        table_name: Name of the table
        additional_columns: Additional columns beyond the standard ones

    Returns:
        Generated model code
    """
    guide = TableCreationGuide()

    # Standard columns
    columns = [
        {'name': f'{table_name}_id', 'type': 'UUID', 'primary_key': True},
        {'name': 'created_at', 'type': 'DateTime'},
        {'name': 'modified_at', 'type': 'DateTime'}
    ]

    # Add additional columns
    if additional_columns:
        columns.extend(additional_columns)

    return guide.create_model_template(table_name, columns)