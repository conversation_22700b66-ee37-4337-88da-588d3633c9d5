import React from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { Telefone } from "../../model/Telefones";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { GridItem } from "@snap/design-system";
import { formatFieldValue, translatePropToLabel } from "../../helpers";
import { getFieldLabel, getFieldValue, renderSourceTooltip } from "./helpers.strategy";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomGridItem } from "../components/CustomGridItem";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { useNestedRender } from "./NestedRenderContext";
import { parseValue } from "~/helpers";

export function useRenderTelefonesArray(sectionTitle: string): ArrayRenderStrategy<Telefone> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const shouldIncludeBlock = (detalhe: any) => {
    const vals = Object.values(detalhe.value || {});
    return isTrash
      ? vals.some((v: any) => v.is_deleted === true)
      : vals.some((v: any) => v.is_deleted === false);
  };



  const testEntryDeleted = (entry: any): boolean =>
    entry.detalhes?.every((d: any) =>
      Object.values(d.value).every((v: any) => v.is_deleted === true)
    ) ?? false;

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) &&
    section.data.every(testEntryDeleted);

  // Data count calculation function for Telefones section
  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;

      // Count non-deleted blocks in detalhes array
      const nonDeletedBlocks = entry.detalhes.filter(
        (bloco: any) => bloco.is_deleted !== true
      ).length;

      return count + nonDeletedBlocks;
    }, 0);
  };

  const calculateNestedDataCount = (nestedSection: any): number => {
    if (!Array.isArray(nestedSection.data)) return 0;
    return nestedSection.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;
      const nonDeletedBlocks = entry.detalhes.filter(
        (bloco: any) => bloco.is_deleted !== true
      ).length;
      return count + nonDeletedBlocks;
    }, 0);
  };

  const formatByKey: Record<
    string,
    (telefone?: Telefone) => React.ReactElement | null
  > = {
    detalhes: (telefone) => {
      if (!telefone?.detalhes?.length) return null;

      const blocks = telefone.detalhes
        .map((d, idx) => ({ bloco: d, idx }))
        .filter(({ bloco }) => shouldIncludeBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={2}>
          {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
            <TelefoneBlock
              key={`tel-${origIdx}`}
              sectionTitle={sectionTitle}
              origIdx={origIdx}
              bloco={bloco}
              blockRenderIdx={blockRenderIdx}
            />
          ))}
        </CustomGridContainer>
      );
    },
  };

  function TelefoneBlock({
    sectionTitle,
    origIdx,
    bloco,
    blockRenderIdx,
  }: {
    sectionTitle: string;
    origIdx: number;
    bloco: any;
    blockRenderIdx: number;
  }) {
    const actions = useReportActions();
    const mode = useReportMode();
    const isTrash = mode === "trash";
    const nested = useNestedRender();

    const handleToggleBlock = () => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection) => {
          const entry = nestedSection.data?.[0];
          const detalhe = entry?.detalhes?.[origIdx];
          if (detalhe?.value) {
            const target = isTrash ? false : true;
            detalhe.is_deleted = target;
            Object.values(detalhe.value).forEach((campo: any) => { if (campo) campo.is_deleted = target; });
          }
          // Use dedicated nested data count calculation
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      actions.updateSectionEntries?.(
        sectionTitle,
        (entry) => {
          const detalhe = (entry as any).detalhes?.[origIdx];
          if (detalhe?.value) {
            const target = isTrash ? false : true;
            detalhe.is_deleted = target;
            Object.values(detalhe.value).forEach((campo: any) => { if (campo) campo.is_deleted = target; });
          }
        },
        (entry) => entry.detalhes?.every((d: any) => Object.values(d.value).every((v: any) => v.is_deleted === true)) ?? false,
        (section) => Array.isArray(section.data) && section.data.every((e) => e.detalhes?.every((d: any) => Object.values(d.value).every((v: any) => v.is_deleted === true))),
        (section) => calculateDataCount(section)
      );
    };

    const handleToggleField = (fieldKey: string) => {
      if (nested?.isNested) {
        nested.updateNestedSection(sectionTitle, (nestedSection) => {
          const entry = nestedSection.data?.[0];
          const detalhe = entry?.detalhes?.[origIdx];
          if (detalhe?.value?.[fieldKey]) {
            detalhe.value[fieldKey].is_deleted = !detalhe.value[fieldKey].is_deleted;
            detalhe.is_deleted = Object.values(detalhe.value).every((c: any) => c.is_deleted === true);
          }
          // Use dedicated nested data count calculation
          nestedSection.data_count = calculateNestedDataCount(nestedSection);
        });
        return;
      }

      actions.updateSectionEntries?.(
        sectionTitle,
        (entry) => {
          const detalhe = (entry as any).detalhes?.[origIdx];
          if (detalhe?.value?.[fieldKey]) {
            detalhe.value[fieldKey].is_deleted = !detalhe.value[fieldKey].is_deleted;
            detalhe.is_deleted = Object.values(detalhe.value).every((c: any) => c.is_deleted === true);
          }
        },
        (entry) => entry.detalhes?.every((d: any) => Object.values(d.value).every((v: any) => v.is_deleted === true)) ?? false,
        (section) => Array.isArray(section.data) && section.data.every((e) => e.detalhes?.every((d: any) => Object.values(d.value).every((v: any) => v.is_deleted === true))),
        (section) => calculateDataCount(section)
      );
    };

    return (
      <GridItem cols={1}>
        <CustomGridItem cols={1} className="py-1" onToggleField={handleToggleBlock}>
          <ReportsCustomLabel label={`TELEFONE ${isTrash ? "" : blockRenderIdx + 1}`} colorClass="bg-primary" />
        </CustomGridItem>
        <div className="pl-5">
          {Object.entries(bloco.value)
            .filter(([_, v]: any) => (isTrash ? v.is_deleted : !v.is_deleted))
            .map(([fieldKey, fieldValue]: any, index) => (
              <CustomGridItem key={`tel-${origIdx}-${fieldKey}`} cols={1} className="py-1" onToggleField={() => handleToggleField(fieldKey)}>
                <CustomReadOnlyInputField
                  label={translatePropToLabel(getFieldLabel(fieldKey, fieldValue)).toUpperCase()}
                  colorClass="bg-border"
                  isFirstLabelList={index === 0}
                  icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                  value={parseValue(formatFieldValue(fieldValue.value))}
                  tooltip={renderSourceTooltip(fieldValue.source)}
                />
              </CustomGridItem>
            ))}
        </div>
      </GridItem>
    );
  }

  const validateKeys = (keys: Array<keyof Telefone>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (telefone: Telefone): React.ReactElement[] => {
    const keys = Object.keys(telefone) as Array<keyof Telefone>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Telefones] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(telefone))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: Telefone[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Telefones] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        return entry.detalhes && entry.detalhes.some((d: any) =>
          Object.values(d.value).some((v: any) => v.is_deleted === true)
        );
      } else {
        const isDeleted = testEntryDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((telefone, index) => {
      const elements = renderSingleItem(telefone);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`telefone-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  // Função para deletar/restaurar toda a seção
  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Percorre todos os detalhes e marca como deletado/restaurado
        if (entry.detalhes) {
          entry.detalhes.forEach((detalhe: any) => {
            // Define o is_deleted do bloco principal
            detalhe.is_deleted = targetDeletedState;

            if (detalhe.value) {
              Object.values(detalhe.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
    calculateDataCount, // Export for nested operations
  } as ArrayRenderStrategy<Telefone> & { calculateDataCount: typeof calculateDataCount };
}
