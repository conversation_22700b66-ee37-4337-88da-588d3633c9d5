{"full name": "Nome <PERSON>to", "nome_da_mae": "<PERSON><PERSON>", "idade": "<PERSON><PERSON>", "sexo": "Sexo", "nacionalidade": "Nacionalidade", "pais do passaporte": "País do Passaporte", "data nascimento": "Data de Nascimento", "cpf": "CPF", "identidade": "Identidade", "data de admissao": "Data de Admissão", "escolaridade": "Escolaridade", "info restricao": "Informação de Restrição", "pis/pasep": "PIS/PASEP", "procon": "Procon", "renda estimada": "Renda Estimada", "status receita": "Status na Receita", "titulo de eleitor": "<PERSON><PERSON><PERSON><PERSON>", "signo": "Sígno", "instancia": "Instância", "movimentacoes": "Movimentações", "numero do processo": "Número do Processo", "orgao": "<PERSON><PERSON><PERSON>", "data da remessa": "<PERSON> da Remessa", "data da instauracao": "Data da Instauração", "advogado": "Advogado", "pessoa": "<PERSON><PERSON><PERSON>", "empresa": "Empresa", "razao social": "Razão Social", "city": "Cidade", "bairro": "Bairro", "cep ou zipcode": "CEP", "logradouro": "Logradouro", "complemento": "Complemento", "numero": "Número", "uf": "UF", "pais": "<PERSON><PERSON>", "estado ou regiao": "Estado ou Região", "email": "Email", "telefone": "Telefone", "area code": "Código <PERSON>", "country code": "Código do País", "phone number": "Telefone", "phonenumber": "Telefone", "provedor de aplicacoes de internet": "Provedor de Aplicações de Internet", "candidato": "Candi<PERSON><PERSON>", "alcunha": "Alcunha", "naturalidade": "Naturalidade", "nome mae": "<PERSON><PERSON>", "nome pai": "Nome do Pai", "pena imposta": "<PERSON><PERSON>", "recaptura": "Recaptura", "source": "Fonte", "area": "Á<PERSON>", "doacoes": "Doações", "entradasociedade": "Data de Entrada na Sociedade", "data de inicio em sociedade": "Data de Início em Sociedade", "identificador socio": "Identificador do Sócio", "qualificação societária": "Cargo em Sociedade", "faixa etaria": "Faixa <PERSON>", "cargo em sociedade": "Participação Sociedade", "cnpj": "CNPJ", "data de fundacao": "Data de Fundação", "data_abertura": "Data de Abertura", "sequencial": "Sequencial", "porte": "Porte", "tipo de imposto": "Tipo de Imposto", "total de funcionarios": "Total de Funcionários", "quantidade de funcionarios acima de 5 salarios": "Funcionários acima de 5 salários", "quantidade de funcionarios abaixo de 5 salarios": "Funcionários abaixo de 5 salários", "email address": "Email", "telefone relacionado": "Telefone Relacionado", "local": "Local", "dados adicionais": "<PERSON><PERSON>", "link": "Link", "descricao": "Descrição", "texto correspondente": "Descrição", "edicao extra?": "Edição Extra?", "ocorrencia": "Texto", "ano": "<PERSON><PERSON>", "cargo eleitoral": "<PERSON><PERSON>", "unidade eleitoral": "Unidade Eleitoral", "número do candidato": "Número", "partido eleitoral": "Partido", "cnae": "CNAE", "site": "Site", "entrada_cpf": "Entrada CPF", "found": "Conta encontrada?", "alerta": "Alvo alertado?", "tipo_alerta": "Tipo de Alerta", "mae": "<PERSON><PERSON><PERSON>", "pai": "<PERSON><PERSON>", "parentesco": "Parentesco", "nome1": "Nome", "carreira": "Função", "instituicao": "Instituição", "quadro funcional": "Quadro Funcional", "municipio": "Cidade / PR", "servidor": "Nome do Servidor", "empresa_pagadora": "Empres<PERSON>", "situacao": "Situação", "cargo": "Cargo", "data demissao": "Data de Demissão", "tipo de mandado": "Tipo de Mandado", "tipificacoes penais": "Tipificaçõ<PERSON>", "data de expedicao": "Data de Expedição", "data de validade": "Data de Validade", "especie de prisao": "Espécie de Prisão", "orgao expedidor": "Órgão Expedidor", "magistrado": "<PERSON><PERSON><PERSON><PERSON>", "nome do orgao": "Nome do Orgão", "codigo do orgao": "Código do Orgão", "pagamento execicio anterior": "Pagamento Execício <PERSON>", "numero de ordem bancaria": "Nº Ordem Bancária", "numero da nota de lancamento": "Nº Nota de Lançamento", "numero do empenho": "Nº Empenho", "fonte do recurso": "Fonte do Recurso", "classificacao": "Classificação", "credor": "C<PERSON>r", "unidade gestora": "Unidade Gestora", "status pagamento": "Status Pagamento", "pagamento": "Pagamento", "historico": "Hist<PERSON><PERSON><PERSON>", "favorecido": "Favorecido", "acrescimo": "Acréscimo (R$)", "anulado": "Anulado (R$)", "pago": "<PERSON><PERSON> (R$)", "liquidado": "<PERSON>or <PERSON>ado (R$)", "valor pago": "<PERSON><PERSON> (R$)", "valor empenhado": "<PERSON><PERSON> (R$)", "valor liquidado": "<PERSON>or <PERSON>ado (R$)", "tipo de despesa": "Tipo de Despesa", "id": "ID", "esfera": "Esfera", "programa de trabalho": "Programa de Trabalho", "funcao": "Função", "subfuncao": "Sub-Função", "programa": "Programa", "categoria economica": "Categoria Econômica", "grupo de natureza da despesa": "Grupo de Despesas", "modalidade de aplicacao": "Modalidade de Aplicação", "elemento": "Elemento", "subelemento": "Sub-Elemento", "empenhado": "<PERSON><PERSON> (R$)", "pago ex": "Pago Ex (R$)", "pago rpp": "Pago RPP (R$)", "pago rpnp": "Pago RPNP (R$)", "pago ret": "Pago RET (R$)", "nire": "NIRE", "data": "Data", "documento": "Documento", "recibo": "Recibo", "especie": "Espécie", "natureza": "Natureza", "natureza estimavel": "<PERSON><PERSON>", "origem": "Origem", "fonte": "Fonte", "tipo": "Tipo", "renda presumida": "<PERSON><PERSON> Pre<PERSON>mi<PERSON>", "ctps": "CTPS", "grauinstrucao": "Grau de Instrução", "descricao cbo": "Descrição CBO", "cbo": "CBO", "estadocivil": "Estado Civil", "rotulo": "<PERSON><PERSON><PERSON><PERSON>", "formacao educacional": "Formação Educacional", "data inicio": "Data de Início", "data termino": "Data de Término", "alias": "Nome de Usuário", "nome_completo": "Nome <PERSON>to", "razao_social": "Razão Social", "nome_fantasia": "Nome Fantasia", "nome": "Nome", "data admissao": "Data de Admissão", "carga horaria": "Carga Horária", "remuneracao": "Remuneração", "nome do servidor": "Nome do Servidor", "periodo": "<PERSON><PERSON><PERSON>", "valor": "Valor", "bonificacao de ferias": "Bonificação de Férias (R$)", "remuneracao liquida": "Remuneração Líquida (R$)", "valor bruto": "<PERSON><PERSON> (R$)", "matricula": "<PERSON><PERSON><PERSON><PERSON>", "reu": "<PERSON><PERSON><PERSON>", "correu": "<PERSON><PERSON><PERSON><PERSON>", "juiz": "Juíz", "origin": "Origem", "relacoes": "Relações"}