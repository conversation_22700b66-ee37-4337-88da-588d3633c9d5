import reportTypes from "~constants/report_types.json";
import reportLabels from "~constants/labels.json";
import reportSections from "~constants/ui_group_titles.json";
import translatedLabels from "~constants/translate_prop_to_label.json"
import parsedValues from "~constants/parsed_values.json";
import user from "~constants/user.json";
import relacoesVinculos from "~constants/relacoes_vinculos.json";

export const RELACOES_VINCULOS = relacoesVinculos;
export const REPORT_CONSTANTS = reportTypes;
export const REPORT_LABELS = reportLabels;
export const REPORT_SECTIONS = reportSections;
export const USER_CONSTANTS = user;
export const USER_INVITE_CONSTANTS = user.user_invite;
export const TRANSLATED_LABELS = translatedLabels;
export const PARSED_VALUES = parsedValues;
export const REPORT_TYPES = Object.entries(reportTypes.types);
export const NON_ENCRYPTED_KEYS = [
  REPORT_CONSTANTS.new_report.report_type,
  REPORT_CONSTANTS.new_report.organization_id,
  REPORT_CONSTANTS.new_report.user_id,
  REPORT_CONSTANTS.new_report.report_id,
  REPORT_CONSTANTS.new_report.subject_age,
  REPORT_CONSTANTS.new_report.creation_at,
  REPORT_CONSTANTS.new_report.modified_at,
];

export const verifierPhrase = "youshallnotpass";

export const tanstackQueryConfig = {
  refetchOnMount: false,
  refetchOnWindowFocus: false,
  refetchOnReconnect: false,
};

export const SK_STORAGE_DURATION = [
  { value: "none", label: "Não armazenar" },
  // { value: "1m", label: "1 minuto (teste)" },
  { value: "15m", label: "15 minutos" },
  { value: "30m", label: "30 minutos" },
  { value: "1h", label: "1 hora" },
  { value: "session", label: "Armazenar senha enquanto estiver logado(a)" } // "Armazenar a senha enquanto a sessão estiver ativa"
]

export const COLUMNS_BREAKPOINTS = {
  346: 1, // 282 + 64 (padding)
  628: 2, // (282 * 2) + 20 + 64
  930: 3, // (282 * 3) + (20 * 2) + 64
  1232: 4, // (282 * 4) + (20 * 3) + 64
  1534: 5, // (282 * 5) + (20 * 4) + 64
  1836: 6, // (282 * 6) + (20 * 5) + 64
  2138: 7, // (282 * 7) + (20 * 6) + 64
  2440: 8, // (282 * 8) + (20 * 7) + 64
  2742: 9, // (282 * 9) + (20 * 8) + 64
};

export const MAX_PAGE_HEIGHT = "max-h-[calc(100vh-212px)]"