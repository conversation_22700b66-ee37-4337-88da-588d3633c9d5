from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel
from models.enums import user_status

class OrganizationUserBase(BaseModel):
    organization_id: UUID
    user_id: UUID
    status: user_status
    joined_at: datetime
    exited_at: Optional[datetime] = None

class OrganizationUserCreate(BaseModel):
    organization_id: UUID


class OrganizationUserUpdate(BaseModel):
    status: Optional[user_status] = None
    exited_at: Optional[datetime] = None

class OrganizationUserInDB(OrganizationUserBase):
    organization_users_id: UUID

    class Config:
        from_attributes = True

class OrganizationUserResponse(OrganizationUserInDB):
    pass
