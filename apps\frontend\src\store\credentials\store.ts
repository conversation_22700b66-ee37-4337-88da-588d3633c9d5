import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { CredentialsActions, CredentialsState } from "./interface";
import { CREDENTIALS_INITIAL_STATE } from "./state";

export const useCredentialsStore = create<
  CredentialsState & { actions: CredentialsActions }
>()(
  devtools(
    persist(
      (set, get) => ({
        ...CREDENTIALS_INITIAL_STATE,
        actions: {
          hasValidVerifiers() {
            return !!(
              get().crypto.iv &&
              get().crypto.salt &&
              get().crypto.encrypted
            );
          },
          updatePassword: (password) =>
            set((state) => ({
              password: { ...state.password, persistedValue: password },
            })),
          persistPassword: (newExpiration) =>
            set((state) => ({
              password: { ...state.password, validThrough: newExpiration },
            })),
          setSalt: (salt) =>
            set((state) => ({
              crypto: {
                ...state.crypto,
                salt,
              },
            })),
          extendPasswordValidity: (duration) =>
            set((state) => ({
              password: {
                ...state.password,
                validThrough: duration,
              },
            })),

          expirePassword: () =>
            set(() => ({
              password: CREDENTIALS_INITIAL_STATE.password,
            })),
          setPasswordSafety: (safety) =>
            set((state) => ({
              password: {
                ...state.password,
                isDirty: !safety,
              },
            })),
          isPasswordSafe: () => {
            return !get().password.isDirty;
          },
          clearCredentials: () => set({ ...CREDENTIALS_INITIAL_STATE }),
          setCrypto: (crypto) =>
            set((state) => ({
              crypto: {
                ...state.crypto,
                ...crypto,
              },
            })),
        },
      }),
      {
        name: "credentials-storage",
        partialize: (state) => ({
          password: state.password,
          crypto: state.crypto,
        }),
      }
    )
  )
);
