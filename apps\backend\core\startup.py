import logging
from services.redis_service import redis_service

logger = logging.getLogger(__name__)

async def initialize_redis():
    """Initialize Redis connection on application startup."""
    try:
        logger.info("[startup] Initializing Redis connection...")
        await redis_service.initialize()
        logger.info("[startup] Redis connection initialized successfully")
    except Exception as e:
        logger.error("[startup] Failed to initialize Redis connection: %s", str(e))
        # Don't raise the exception - allow the application to start without Redis
        # The auth service will handle Redis connection failures gracefully

async def shutdown_redis():
    """Close Redis connection on application shutdown."""
    try:
        logger.info("[startup] Closing Redis connection...")
        await redis_service.close()
        logger.info("[startup] Redis connection closed successfully")
    except Exception as e:
        logger.error("[startup] Error closing Redis connection: %s", str(e))
