import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { UserData } from "~/types/global";

interface UserActions {
  setUser: (user: UserData) => void;
  setUserSalt: (salt: string | null) => void;
  setIsVerified: (isVerified: boolean) => void;
  setHasAcceptedTerms: (hasAcceptedTerms: boolean) => void;
  clearUser: () => void;
}

interface UserState {
  userData: UserData | null;
  userSalt: string | null;
  isVerified: boolean;
  hasAcceptedTerms: boolean;
  actions: UserActions;
}

const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set) => ({
        userData: null,
        userSalt: null,
        isVerified: false,
        hasAcceptedTerms: false,
        actions: {
          setUser: (user: UserData) => set({ userData: user }),
          setUserSalt: (salt: string | null) => set({ userSalt: salt }),
          setIsVerified: (isVerified: boolean) => set({ isVerified }),
          setHasAcceptedTerms: (hasAcceptedTerms: boolean) => set({ hasAcceptedTerms }),
          clearUser: () => set({ userData: null, userSalt: null, isVerified: false, hasAcceptedTerms: false }),
        },
      }),
      {
        name: "report-user",
        partialize: (state) => ({
          userData: state.userData,
          userSalt: state.userSalt,
        }),
      }
    )
  )
);

export const useUserData = () => useUserStore((state) => state.userData)
export const useUserSalt = () => useUserStore((state) => state.userSalt)
export const useUserIsVerified = () => useUserStore((state) => state.isVerified)
export const useUserHasAcceptedTerms = () => useUserStore((state) => state.hasAcceptedTerms);
export const useUserActions = () => useUserStore((state) => state.actions);
