import { useCredentialsStore } from "./store";

// Hook-based selectors for reactivity in components
export const usePassword = () =>
  useCredentialsStore((state) => state.password.persistedValue);

export const useVerifiers = () => useCredentialsStore((state) => state.crypto);

export const useIsPasswordSafe = () =>
  useCredentialsStore((state) => state.password.isDirty);

export const useCredentialsStoreActions = () =>
  useCredentialsStore((state) => state.actions);

export const useIsTemporaryKey = () =>
  useCredentialsStore((state) => {
    const validThrough = state.password.validThrough;
    const isPasswordExpired = !validThrough || new Date() > new Date(validThrough);
    return isPasswordExpired && state.password.persistedValue !== "";
  });

export const useIsPasswordExpired = () =>
  useCredentialsStore((state) => {
    const validThrough = state.password.validThrough;
    return !validThrough || new Date() > new Date(validThrough);
  });
