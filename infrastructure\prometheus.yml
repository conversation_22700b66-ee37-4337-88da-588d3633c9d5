global:
  scrape_interval: 15s

scrape_configs:
  # Scrape Prometheus itself
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  # Scrape the frontend NGINX service through the metrics exporter
  - job_name: "frontend"
    static_configs:
      - targets: ["host.docker.internal:9113"] # Observe the frontend NGINX service

  # Scrape <PERSON><PERSON> Alloy (listens on port 12345)
  - job_name: "grafana-alloy"
    static_configs:
      - targets: ["host.docker.internal:12345"]

  # Scrape Loki
  - job_name: "loki"
    static_configs:
      - targets: ["host.docker.internal:3100"]

  # Scrape Kafka metrics
  - job_name: "kafka"
    static_configs:
      - targets: ["kafka-exporter:9308"]
    metrics_path: /metrics

  # Docker container metrics are collected directly by Prometheus
  # No need for cAdvisor - Prometheus can scrape container metrics directly
  # Your autoscaler uses Prometheus queries for container monitoring
  
  # Scrape Docker container metrics directly
  - job_name: "docker-containers"
    static_configs:
      - targets: ["host.docker.internal:9323"] # Docker daemon metrics endpoint
    metrics_path: /metrics
    scrape_interval: 15s
    honor_labels: true
