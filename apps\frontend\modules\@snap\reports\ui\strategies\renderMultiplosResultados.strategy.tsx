import React from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { MultiplosResultados } from "../../model/MultiplosResultados";
import { Text } from "@snap/design-system";
import { parseValue, translatePropToLabel } from "../../helpers";
import { Accordion } from "../components/CustomAccordion";
import { Badge } from "../components/base/badge";
import { ActionButton } from "../components/ActionButton";
import { filterValidSections, getAgeFromBirthDate } from "../../helpers";
import { useStrategyMapMultipleResults } from "./ReportStrategyFactoryMultiple";
import { useReportActions, useReportMode, useIsTrashEnabled } from "../../context/ReportContext";
import { NestedRenderProvider } from "./NestedRenderContext";
import { MULTIPLOS_REGISTROS_DETALHES, REPORT_SECTIONS, FIELD_KEYS } from "../../config/constants";

export function useRenderMultiplosResultados(sectionTitle: string): ArrayRenderStrategy<MultiplosResultados> {
  const strategiesMap = useStrategyMapMultipleResults();
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";
  const isTrashEnabled = useIsTrashEnabled();

  const validateKeys = (keys: Array<keyof MultiplosResultados>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const deepSetIsDeleted = (obj: any, target: boolean) => {
    if (Array.isArray(obj)) {
      obj.forEach((item) => deepSetIsDeleted(item, target));
      return;
    }
    if (obj && typeof obj === "object") {
      if (Object.prototype.hasOwnProperty.call(obj, "is_deleted")) {
        (obj as any).is_deleted = target;
      }
      Object.values(obj).forEach((v) => deepSetIsDeleted(v, target));
    }
  };

  const deepDeletedStats = (obj: any): { hasFlag: boolean; allDeleted: boolean } => {
    if (Array.isArray(obj)) {
      if (obj.length === 0) return { hasFlag: false, allDeleted: false };
      return obj.reduce(
        (acc, item) => {
          const child = deepDeletedStats(item);
          return { hasFlag: acc.hasFlag || child.hasFlag, allDeleted: acc.allDeleted && child.allDeleted };
        },
        { hasFlag: false, allDeleted: true }
      );
    }
    if (obj && typeof obj === "object") {
      let hasFlag = false;
      let allDeleted = true;
      if (Object.prototype.hasOwnProperty.call(obj, "is_deleted")) {
        hasFlag = true;
        if ((obj as any).is_deleted !== true) return { hasFlag: true, allDeleted: false };
      }
      for (const v of Object.values(obj)) {
        const child = deepDeletedStats(v);
        hasFlag = hasFlag || child.hasFlag;
        allDeleted = allDeleted && child.allDeleted;
      }
      return { hasFlag, allDeleted };
    }
    return { hasFlag: false, allDeleted: true };
  };

  const deepAllDeleted = (obj: any): boolean => {
    const stats = deepDeletedStats(obj);
    return stats.hasFlag && stats.allDeleted;
  };

  // const testDetalhesDeleted = (entry: MultiplosResultados): boolean => {
  //   const detalhes = entry?.detalhes || {};
  //   const values = Object.values(detalhes);
  //   if (values.length === 0) return false;
  //   return values.every((v: any) => v?.is_deleted === true);
  // };

  const testNestedSectionDeleted = (sec: any): boolean => {
    if (!sec) return true;
    if (sec.is_deleted === true) return true;
    if (Array.isArray(sec.data)) {
      if (sec.data.length === 0) return false;
      return sec.data.every((item: any) => deepAllDeleted(item));
    }
    return false;
  };

  const testEntryDeleted = (entry: MultiplosResultados): boolean => {
    // const detalhesDeleted = testDetalhesDeleted(entry);
    const sections = Array.isArray(entry.sections) ? entry.sections : [];
    const nestedDeleted = sections.length === 0 ? true : sections.every(testNestedSectionDeleted);
    return nestedDeleted;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;
    return section.data.reduce((count: number, entry: any) => {
      return testEntryDeleted(entry) ? count : count + 1;
    }, 0);
  };

  const recalcNestedSectionDataCount = (nestedSec: any): number => {
    if (!Array.isArray(nestedSec?.data)) return 0;

    const strategy = strategiesMap[nestedSec.title];

    if (strategy && typeof (strategy as any).calculateDataCount === 'function') {
      return (strategy as any).calculateDataCount(nestedSec);
    }

    if (strategy && strategy.testEntryDeleted) {
      return nestedSec.data.reduce((count: number, entry: any) => {
        return strategy.testEntryDeleted(entry) ? count : count + 1;
      }, 0);
    }

    return nestedSec.data.reduce((acc: number, ent: any) => {
      const detalhes = (ent as any)?.detalhes;
      if (Array.isArray(detalhes)) {
        const nonDeletedBlocks = detalhes.filter((b: any) => b?.is_deleted !== true).length;
        return acc + nonDeletedBlocks;
      }
      if (detalhes && typeof detalhes === 'object') {
        const nonDeletedFields = Object.values(detalhes).filter((v: any) => v?.is_deleted !== true).length;
        return acc + nonDeletedFields;
      }
      return acc;
    }, 0);
  };

  const deepAnyDeleted = (obj: any): boolean => {
    if (Array.isArray(obj)) return obj.some((item) => deepAnyDeleted(item));
    if (obj && typeof obj === "object") {
      if (Object.prototype.hasOwnProperty.call(obj, "is_deleted") && (obj as any).is_deleted === true) return true;
      return Object.values(obj).some((v) => deepAnyDeleted(v));
    }
    return false;
  };

  const entryHasAnyDeleted = (entry: MultiplosResultados): boolean => {
    const anyDetalhe = entry?.detalhes ? Object.values(entry.detalhes).some((v: any) => v?.is_deleted === true) : false;
    const sections = Array.isArray(entry.sections) ? entry.sections : [];
    const anySection = sections.some((sec: any) => sec?.is_deleted === true || (sec?.data && deepAnyDeleted(sec.data)));
    return anyDetalhe || anySection;
  };

  const onToggleNestedSection = (entryIdx: number, nestedTitle: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;
    const targetDeletedState = isTrash ? false : true;

    updater(
      sectionTitle,
      (e: any, i?: number) => {
        if (i !== entryIdx) return;
        if (!Array.isArray(e.sections)) return;
        const target = e.sections.find((s: any) => s?.title === nestedTitle);
        if (!target) return;
        target.is_deleted = targetDeletedState;
        if (target.data) deepSetIsDeleted(target.data, targetDeletedState);
        // Recalcular data_count da subseção após toggle de seção inteira
        target.data_count = recalcNestedSectionDataCount(target);

        // Atualizar detalhes conforme estado final das subseções
        const allNestedDeleted = e.sections.every((sec: any) => testNestedSectionDeleted(sec));
        if (e.detalhes && typeof e.detalhes === 'object') {
          if (allNestedDeleted) {
            Object.values(e.detalhes).forEach((d: any) => { if (d) d.is_deleted = targetDeletedState; });
          } else {
            // Se ainda há subseções não deletadas, manter/forçar detalhes não deletados
            Object.values(e.detalhes).forEach((d: any) => { if (d) d.is_deleted = false; });
          }
        }
      },
      testEntryDeleted as unknown as (entry: Record<string, any>) => boolean,
      testSectionDeleted as unknown as (section: Record<string, any>) => boolean,
      calculateDataCount
    );
  };

  const onToggleEntry = (entryIdx: number) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;
    const targetDeletedState = isTrash ? false : true;

    updater(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          if (e.detalhes) {
            Object.values(e.detalhes).forEach((d: any) => {
              if (d) d.is_deleted = targetDeletedState;
            });
          }
          if (Array.isArray(e.sections)) {
            e.sections.forEach((sec: any) => {
              sec.is_deleted = targetDeletedState;
              if (sec.data) deepSetIsDeleted(sec.data, targetDeletedState);
              sec.data_count = recalcNestedSectionDataCount(sec);
            });
          }
        }
      },
      testEntryDeleted as unknown as (entry: Record<string, any>) => boolean,
      testSectionDeleted as unknown as (section: Record<string, any>) => boolean,
      calculateDataCount
    );
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      (entry: any) => {
        if (Array.isArray(entry.sections)) {
          entry.sections.forEach((sec: any) => {
            sec.is_deleted = targetDeletedState;
            if (sec.data) deepSetIsDeleted(sec.data, targetDeletedState);
            sec.data_count = recalcNestedSectionDataCount(sec);
          });
        }
      },
      testEntryDeleted as unknown as (entry: Record<string, any>) => boolean,
      testSectionDeleted as unknown as (section: Record<string, any>) => boolean,
      calculateDataCount
    );
  };

  const renderDetalhes = (entry: MultiplosResultados) => {
    const dadosPessoaisSection = entry?.sections?.find((sec: any) => sec?.title === REPORT_SECTIONS.dados_pessoais);
    if (!dadosPessoaisSection?.data?.[0]?.detalhes) return null;

    // Se a seção está deletada e não estamos no modo trash, não renderizar
    if (dadosPessoaisSection.is_deleted === true && !isTrash) return null;

    const detalhes = dadosPessoaisSection.data[0].detalhes;
    const validKeys = Object.keys(MULTIPLOS_REGISTROS_DETALHES);
    const entries = Object.entries(detalhes);
    
    const filteredEntries = entries.filter(([key, field]) => {
      if (!validKeys.includes(key) || field == null) return false;

      if (isTrash) {
        return (field as any)?.is_deleted === true;
      }

      return (field as any)?.is_deleted !== true;
    });

    const hasBirthDate = filteredEntries.some(([key]) => key === FIELD_KEYS.DATA_NASCIMENTO);

    // Se tem data de nascimento e idade, ignorar idade
    const entriesWithoutDuplicateAge = filteredEntries.filter(([key]) => {
      return !(hasBirthDate && key === FIELD_KEYS.IDADE);
    });

    // Converter data de nascimento para idade se necessário
    const processedEntries = entriesWithoutDuplicateAge.map(([key, field]) => {
      if (key !== FIELD_KEYS.DATA_NASCIMENTO) {
        return [key, field];
      }

      try {
        const age = getAgeFromBirthDate((field as any).value);
        return [FIELD_KEYS.IDADE, { ...(field as any), value: age, label: 'Idade' }];
      } catch (error) {
        console.warn('Error calculating age:', error);
        return [key, field];
      }
    });

    const keyOrder = Object.values(FIELD_KEYS);
    const sortedEntries = processedEntries.sort(([a], [b]) => {
      const indexA = keyOrder.indexOf(a as any);
      const indexB = keyOrder.indexOf(b as any);
      // ambos estão na ordem definida
      if (indexA !== -1 && indexB !== -1) return indexA - indexB;
      // só um está na ordem definida
      if (indexA !== -1) return -1;
      if (indexB !== -1) return 1;

      return 0;
    });

    if (sortedEntries.length === 0) return null; return (
      <div className={`px-4 py-3 flex items-center gap-4 flex-wrap bg-accordion-content/80 border-l border-dotted border-neutral-100 ${isTrash ? 'flex-col items-start' : ''}`}>
        {sortedEntries.map(([key, field]) => (
          <div key={key} className="flex items-center gap-1">
            <Text variant="label-sm" className="uppercase font-bold text-foreground/70">
              {`${translatePropToLabel((field as any).label || key)}:`}
            </Text>
            <Text variant="label-sm" className="uppercase text-foreground/70">
              {parseValue((field as any).value)}
            </Text>
          </div>
        ))}
      </div>
    );
  };

  const renderNestedSections = (entry: MultiplosResultados, entryIndex?: number) => {
    const sections = Array.isArray(entry.sections) ? entry.sections : [];
    if (!sections.length) return null;

    const validSections = filterValidSections(sections, strategiesMap);
    if (!validSections.length) return null;

    const isSectionVisible = (sec: any): boolean => {
      const strategy = strategiesMap[sec.title];
      const hasValidData = Array.isArray(sec.data) && sec.data.length > 0;

      if (!hasValidData) return false;

      const hasRealData = sec.data.some((entry: any) => {
        if (entry?.detalhes && Array.isArray(entry.detalhes)) {
          return entry.detalhes.length > 0;
        }

        return entry?.detalhes !== undefined || Object.keys(entry || {}).length > 1;
      });

      if (!hasRealData) return false;

      if (isTrash) {
        if (sec.is_deleted === true) return true;

        return sec.data.some((entry: any) => {
          if (strategy?.testEntryDeleted) {
            return strategy.testEntryDeleted(entry) || deepAnyDeleted(entry);
          }
          return deepAnyDeleted(entry); // Fallback
        });
      }

      if (sec.is_deleted === true) return false;

      const allEntriesDeleted = sec.data.every((entry: any) => {
        if (strategy?.testEntryDeleted) {
          return strategy.testEntryDeleted(entry);
        }
        return deepAllDeleted(entry);
      });
      return !allEntriesDeleted;
    };

    const visibleSections = validSections.filter(isSectionVisible);

    if (!visibleSections.length) return null;

    return (
      <Accordion type="multiple" className="">
        {visibleSections.map((sec, i) => {
          const strategy = strategiesMap[sec.title];
          const dataCount = sec.data_count ?? (Array.isArray(sec.data) ? sec.data.length : 0);
          const itemValue = `${sec.title}-${i}`;
          const canToggle = isTrashEnabled && typeof entryIndex === 'number';
          return (
            <Accordion.Item key={itemValue} value={itemValue} className="border-b-0 my-4">
              <div className="group">
                <Accordion.Trigger className="px-0 py-0 mb-0 no-underlinecursor-pointer bg-accordion-header">
                  <div className="flex items-center gap-4 w-full justify-between pr-8">
                    <div className="flex items-center gap-4">
                      <Text className="uppercase font-bold">{sec.title}</Text>
                      {!isTrash && (
                        <Badge className="rounded-2xl px-4 py-0.5 bg-accordion-badge hover:bg-accordion-badge border-0">
                          <Text className="text-foreground">{dataCount}</Text>
                        </Badge>
                      )}
                    </div>
                    {canToggle && (
                      <ActionButton
                        onClick={(e) => { e.stopPropagation(); onToggleNestedSection(entryIndex!, sec.title); }}
                        title={isTrash ? "Restaurar seção" : "Deletar seção"}
                        isTrashMode={isTrash}
                        size="lg"
                      />
                    )}
                  </div>
                </Accordion.Trigger>
              </div>
              <Accordion.Content className="px-5">
                <div className="pt-5">
                  <NestedRenderProvider value={{
                    isNested: true,
                    updateNestedSection: (nestedSectionTitle, updater) => {
                      actions.updateSectionEntries?.(
                        sectionTitle,
                        (parentEntry: any, idx?: number) => {
                          if (idx !== entryIndex) return;
                          const target = parentEntry.sections?.find((s: any) => s?.title === nestedSectionTitle);
                          if (target) updater(target);
                        },
                        testEntryDeleted as unknown as (entry: Record<string, any>) => boolean,
                        testSectionDeleted as unknown as (section: Record<string, any>) => boolean,
                        calculateDataCount
                      );
                    }
                  }}>
                    {strategy?.render(sec.data).map((el: React.ReactNode, j: number) => (
                      <div key={j}>{el}</div>
                    ))}
                  </NestedRenderProvider>
                </div>
              </Accordion.Content>
            </Accordion.Item>
          );
        })}
      </Accordion>
    );
  };

  const renderSingleItem = (entry: MultiplosResultados, index: number): React.ReactElement[] => {
    if (!entry) return [];

    const dadosPessoaisSection = entry?.sections?.find((sec: any) => sec?.title === REPORT_SECTIONS.dados_pessoais);
    const hasDadosPessoais = dadosPessoaisSection && dadosPessoaisSection.data && dadosPessoaisSection.data.length > 0;
    const title = hasDadosPessoais ? dadosPessoaisSection.data[0].detalhes.nome_completo.value : "Registro Encontrado";

    const detalhesEl = renderDetalhes(entry);
    const sectionsEl = renderNestedSections(entry, index);

    // Se não há conteúdo para mostrar (detalhes e seções), não renderizar o item
    if (!detalhesEl && !sectionsEl) {
      return [];
    }

    return [
      (
        <div key={`multiplos-${index}`} className="mb-4 bg-transparent">
          <Accordion type="multiple">
            <Accordion.Item value={`entry-${index}`} className="border-b-0">
              <div className="group">
                <Accordion.Trigger
                  className={`px-0 py-0 mb-0 data-[state=open]:mb-0 no-underline [&_div:first-child]:border-neutral-100 cursor-pointer data-[state=closed]:bg-accordion-content`}>
                  <div className="flex flex-col w-full">
                    <div className="flex items-center gap-4 w-full justify-between pr-8">
                      <div className="flex items-center gap-4">
                        <Text variant="" className="uppercase font-bold">{title}</Text>
                      </div>
                      {isTrashEnabled && (
                        <ActionButton
                          onClick={(e) => { e.stopPropagation(); onToggleEntry(index); }}
                          title={isTrash ? "Restaurar registro" : "Deletar registro"}
                          isTrashMode={isTrash}
                          size="lg"
                        />
                      )}
                    </div>
                  </div>
                </Accordion.Trigger>
                <div className="pl-12">
                  {detalhesEl}
                </div>
              </div>
              <Accordion.Content className="!pb-0 px-0 bg-transparent">
                <div className="pt-0 ">
                  <div className="pl-12">
                    {sectionsEl}
                  </div>
                </div>
              </Accordion.Content>
            </Accordion.Item>
          </Accordion>
        </div>
      )
    ];
  };

  const formatByKey: Record<string, (entry?: MultiplosResultados) => React.ReactElement | null> = {
    sections: (entry) => renderNestedSections(entry!),
  };

  const render = (dataArray: MultiplosResultados[]) => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Múltiplos Resultados] Expected array but received:", typeof dataArray);
      return [];
    }

    // Preservar os índices originais antes de filtrar
    const items = dataArray.map((entry, idx) => ({ entry, idx }));

    const filtered = items.filter(({ entry }) => {
      const fullyDeleted = testEntryDeleted(entry);
      if (isTrash) {
        // Mostrar na lixeira quando totalmente deletado ou quando QUALQUER conteúdo estiver deletado
        const anyDeleted = entryHasAnyDeleted(entry);
        return fullyDeleted || anyDeleted;
      }
      // No modo default, ocultar quando totalmente deletado
      return !fullyDeleted;
    });

    if (filtered.length === 0) return [];

    const allElements: React.ReactElement[] = [];
    filtered.forEach(({ entry, idx }) => {
      const elements = renderSingleItem(entry, idx);
      allElements.push(...elements);
    });
    return allElements;
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}