import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, getSingular, parseValue, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintParentesProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      parentesco?: ValueWithSource;
      pessoa?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      detalhes?: Record<string, ValueWithSource>;
      telefones?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      enderecos?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      [key: string]: any;
    }>;
  };
}

export const renderParentes = ({ section }: RenderPrintParentesProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((parente) => {
    if (parente.parentesco && !parente.parentesco.is_deleted) {
      children.push(new Paragraph({
        children: [
          new TextRun({ text: `${(parente.parentesco.label || "Parentesco").toUpperCase()}: `, bold: true, color: "FE473C" }),
          new TextRun(String(parente.parentesco.value)),
          new TextRun({ text: ` | ${parente.parentesco.source?.map(translateSource).join(', ')}`, size: 16, color: "FE473C" }),
        ],
        spacing: { after: 200 }
      }));
    }

    if (parente.detalhes) {
      const tableRows = Object.entries(parente.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => {
          return new TableRow({
            children: [
              new TableCell({
                children: [new Paragraph({ text: `${translatePropToLabel(field.label || key).toUpperCase()}`})],
                shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
              }),
              new TableCell({
                children: [new Paragraph(String(field.value))],
                shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
              }),
            ],
          });
        });

      if (tableRows.length > 0) {
        children.push(new Table({
          rows: tableRows,
          width: { size: 100, type: WidthType.PERCENTAGE },
          columnWidths: [3500, 5500],
        }));
      }
    }

    if (parente.pessoa && parente.pessoa.length > 0) {
        children.push(new Paragraph({ text: "PESSOA RELACIONADA", style: "subtitle" }));
        parente.pessoa.forEach((p, i) => {
            if(p.is_deleted) return;
            children.push(new Paragraph({text: `${getSingular(p.label || "Pessoa").toUpperCase()} ${i+1}`}));
            const pRows = Object.entries(p.value).map(([key, val]) => new TableRow({children: [new TableCell({children: [new Paragraph(translatePropToLabel(val.label || key))]}), new TableCell({children: [new Paragraph(String(val.value))]})]}))
            children.push(new Table({rows: pRows, width: {size: 100, type: WidthType.PERCENTAGE}}));
        });
    }

    // Similar logic for telefones and enderecos

  });

  return { children };
};
