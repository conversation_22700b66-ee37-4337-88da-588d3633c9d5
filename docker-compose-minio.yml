
services:
  minio:
    image: my-minio-image
    build:
      context: .
      dockerfile: apps/minio/Dockerfile
    container_name: ${MINIO_CONTAINER_NAME:-minio}
    volumes:
      - minio_data:/data
    networks:
      - mystack-net

    ports:
      - "${MINIO_S3_EXTERNAL_PORT:-9000}:9000"
      - "${MINIO_UI_EXTERNAL_PORT:-9001}:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-admin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-password}
      KAFKA_CONTAINER_NAME: ${KAFKA_CONTAINER_NAME}
      KAFKA_EXTERNAL_NAME: ${KAFKA_EXTERNAL_NAME}
      KAFKA_INTERNAL_PORT: ${KAFKA_INTERNAL_PORT}
      KAFKA_EXTERNAL_PORT: ${KAFKA_EXTERNAL_PORT}
      SHOULD_USE_KAFKA_CONTAINER: ${SHOULD_USE_KAFKA_CONTAINER:-true}

    healthcheck:
      test: ["CMD", "curl", "-f", "http://127.0.0.1:9000/minio/health/live"]
      interval: 10s
      # start_interval: 1s
      start_period: 10s

    deploy:
      resources:
        limits:
          cpus: "0.4"
          memory: "756M"
        reservations:
          cpus: "0.1"
          memory: "400M"
    restart: unless-stopped

  minio-test-setup:
    image: minio/mc
    container_name: ${MINIO_TEST_SETUP_CONTAINER_NAME:-minio-test-setup}
    depends_on:
      - minio
    entrypoint: /bin/sh -c "sleep infinity"
    networks:
      - mystack-net

    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: "256M"
        reservations:
          cpus: "0.1"
          memory: "128M"


volumes:
  minio_data:
#
## mc alias set minio http://minio:9000 admin password
## mc admin config set minio/reports notify_kafka:REPORTS brokers="kafka:9092" topic="reports" queue_limit="100000"
## mc event add minio/reports arn:minio:sqs::REPORTS:kafka --event put
##echo "teste" > testfile.txt
## mc cp /home/<USER>/reports/
#
#
## docker exec -it kafka kafka-console-consumer.sh --bootstrap-server kafka:9092 --topic reports --from-beginning
