import { create } from "zustand";
import { UserInviteResponse } from "~/types/global";

interface EditUserActions {
  setSelectedUser: (user: UserInviteResponse) => void;
  setName: (name: string) => void;
  setTypeInvite: (type: string) => void;
  setReportTypes: (types: { [key: string]: boolean }) => void;
  setCreditsSent: (credits: number) => void;
  clearEditUserValues: () => void;
}

interface EditUserState {
  selectedUser: UserInviteResponse | null;
  name: string;
  email_invited: string;
  type_invite: string;
  report_types: { [key: string]: boolean };
  credits_sent: number;
  actions: EditUserActions;
}

const initialReportTypes = {
  cpf: false,
  cnpj: false,
  email: false,
  telefone: false,
  relacoes: false,
};

const useEditUserStore = create<EditUserState>((set) => ({
  selectedUser: null,
  name: "",
  email_invited: "",
  type_invite: "investigador",
  report_types: initialReportTypes,
  credits_sent: 100,
  actions: {
    setSelectedUser: (user: UserInviteResponse) => {
      const userReportTypes = { ...initialReportTypes };
      if (user?.report_types && Array.isArray(user.report_types)) {
        user.report_types.forEach(type => {
          if (type in userReportTypes) {
            userReportTypes[type as keyof typeof userReportTypes] = true;
          }
        });
      }

      set({
        selectedUser: user,
        name: user?.name || "",
        email_invited: user?.email_invited || "",
        type_invite: user?.type_invite || "investigador",
        report_types: userReportTypes,
        credits_sent: typeof user?.credits_sent === 'string' ? parseInt(user.credits_sent) || 100 : user?.credits_sent || 100,
      });
    },
    setName: (name: string) => set({ name }),
    setTypeInvite: (type: string) => set({ type_invite: type }),
    setReportTypes: (types: { [key: string]: boolean }) => set({ report_types: types }),
    setCreditsSent: (credits: number) => set({ credits_sent: credits }),
    clearEditUserValues: () =>
      set({
        selectedUser: null,
        name: "",
        email_invited: "",
        type_invite: "investigador",
        report_types: initialReportTypes,
        credits_sent: 100,
      }),
  },
}));

export const useEditUserSelectedUser = () =>
  useEditUserStore((state) => state.selectedUser);
export const useEditUserName = () =>
  useEditUserStore((state) => state.name);
export const useEditUserEmailInvited = () =>
  useEditUserStore((state) => state.email_invited);
export const useEditUserTypeInvite = () =>
  useEditUserStore((state) => state.type_invite);
export const useEditUserReportTypes = () =>
  useEditUserStore((state) => state.report_types);
export const useEditUserCreditsSent = () =>
  useEditUserStore((state) => state.credits_sent);
export const useEditUserActions = () =>
  useEditUserStore((state) => state.actions);
