import { getImageAsDataUrlAsync, isValidUrl, getFieldValue } from './index';

/**
 * Simple utility to convert external image URLs to base64 data URLs
 * This should be used in section components that render images
 */
export async function convertImageUrlToBase64(imageUrl: string): Promise<string> {
  if (!imageUrl || !isValidUrl(imageUrl)) {
    return imageUrl;
  }

  try {
    const base64Image = await getImageAsDataUrlAsync(imageUrl);
    return base64Image || imageUrl;
  } catch (error) {
    console.error(`Failed to convert image URL to base64: ${imageUrl}`, error);
    return imageUrl;
  }
}

/**
 * Utility specifically for renderPrintImagens sections
 * Processes the section data and returns it with converted image URLs
 */
export async function processImagesSectionData(sectionData: any[]): Promise<any[]> {
  if (!Array.isArray(sectionData)) return sectionData;

  return Promise.all(
    sectionData.map(async (entry) => {
      if (!entry.detalhes || !Array.isArray(entry.detalhes)) {
        return entry;
      }

      const processedDetalhes = await Promise.all(
        entry.detalhes.map(async (detalhe: any) => {
          if (!detalhe.value?.url || detalhe.value.url.is_deleted) {
            return detalhe;
          }

          const imageUrl = getFieldValue(detalhe.value.url);
          if (typeof imageUrl === 'string' && isValidUrl(imageUrl)) {
            const convertedUrl = await convertImageUrlToBase64(imageUrl);

            return {
              ...detalhe,
              value: {
                ...detalhe.value,
                url: {
                  ...detalhe.value.url,
                  value: imageUrl
                },
                processedImage: {
                  value: convertedUrl,
                  label: 'Processed Image',
                  source: detalhe.value.url.source,
                  is_deleted: false
                }
              }
            };
          }

          return detalhe;
        })
      );

      return {
        ...entry,
        detalhes: processedDetalhes
      };
    })
  );
}

/**
 * Utility specifically for renderPrintPossiveisPessoasRelacionadas sections
 * Processes the section data and returns it with converted image URLs
 */
export async function processPessoasRelacionadasSectionData(sectionData: any[]): Promise<any[]> {
  if (!Array.isArray(sectionData)) return sectionData;

  return Promise.all(
    sectionData.map(async (person) => {
      if (!person.imagens || !Array.isArray(person.imagens)) {
        return person;
      }

      const processedImagens = await Promise.all(
        person.imagens.map(async (imagem: any) => {
          if (!imagem.value?.url || imagem.value.url.is_deleted) {
            return imagem;
          }

          const imageUrl = getFieldValue(imagem.value.url);
          if (typeof imageUrl === 'string' && isValidUrl(imageUrl)) {
            const convertedUrl = await convertImageUrlToBase64(imageUrl);

            return {
              ...imagem,
              value: {
                ...imagem.value,
                url: {
                  ...imagem.value.url,
                  value: convertedUrl
                }
              }
            };
          }

          return imagem;
        })
      );

      return {
        ...person,
        imagens: processedImagens
      };
    })
  );
}

/**
 * Utility specifically for renderPrintRedesSociais sections
 * Processes the section data and returns it with converted image URLs
 */
export async function processRedesSociaisSectionData(sectionData: any[]): Promise<any[]> {
  if (!Array.isArray(sectionData)) return sectionData;

  return Promise.all(
    sectionData.map(async (entry) => {
      if (!entry.detalhes || !Array.isArray(entry.detalhes)) {
        return entry;
      }

      const processedDetalhes = await Promise.all(
        entry.detalhes.map(async (detalhe: any) => {
          if (!detalhe.value || typeof detalhe.value !== 'object') {
            return detalhe;
          }

          const processedValue = { ...detalhe.value };

          // Process each social media platform
          for (const [platform, profiles] of Object.entries(detalhe.value)) {
            if (!Array.isArray(profiles)) continue;

            processedValue[platform] = await Promise.all(
              profiles.map(async (profile: any) => {
                const processedProfile = { ...profile };

                // Process each field in the profile, looking for image URLs
                for (const [fieldKey, fieldValue] of Object.entries(profile)) {
                  if (fieldValue && typeof fieldValue === 'object' && !(fieldValue as any).is_deleted) {
                    const value = getFieldValue(fieldValue as any);

                    // Check if this field contains an image URL
                    if (typeof value === 'string' && isValidUrl(value)) {
                      const lowerValue = value.toLowerCase();
                      const isImageUrl = lowerValue.includes('jpg') || lowerValue.includes('jpeg') ||
                        lowerValue.includes('png') || lowerValue.includes('gif') ||
                        lowerValue.includes('webp') || lowerValue.includes('image');

                      if (isImageUrl) {
                        const convertedUrl = await convertImageUrlToBase64(value);
                        processedProfile[fieldKey] = {
                          ...(fieldValue as any),
                          value: convertedUrl
                        };
                      }
                    }
                  }
                }

                return processedProfile;
              })
            );
          }

          return {
            ...detalhe,
            value: processedValue
          };
        })
      );

      return {
        ...entry,
        detalhes: processedDetalhes
      };
    })
  );
}