import { Button, Input, Text } from "@snap/design-system";
import { Check } from "lucide-react";
import { useState } from "react";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { useFolderCRUD } from "~/hooks/useFolderCRUD";
import { REPORT_CONSTANTS } from "~/helpers/constants";

export function RenameFolderDialog({
  folderId,
  folderName,
  onCancel,
  onConfirm,
}: {
  folderId: string;
  folderName: string;
  onCancel: () => void;
  onConfirm?: (newName: string) => void;
  onMoveStart?: () => void;
  onMoveEnd?: () => void;
}) {
  const [name, setName] = useState(folderName);
  const { renameFolderMutation } = useFolderCRUD();

  const handleRename = () => {
    const newName = name.trim();
    if (newName && newName !== folderName && !renameFolderMutation.isPending) {
      renameFolderMutation.mutate({
        [REPORT_CONSTANTS.new_folder.folder_id]: folderId,
        [REPORT_CONSTANTS.new_folder.folder_name]: newName,
      });
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleRename();
    }
  };

  return (
    <>
      <Text className="block mb-1">Digite o novo nome da pasta:</Text>
      <Input
        type="text"
        variant="outlined"
        value={name}
        onChange={e => setName(e.target.value)}
        onKeyDown={handleKeyDown}
        className="rounded-none border-0 w-full border-b dashed pl-0"
        maxLength={100}
        autoFocus
      />
      <div className="flex gap-3 mt-8">
        <Button onClick={onCancel} className="uppercase !bg-transparent" disabled={renameFolderMutation.isPending}>
          Cancelar
        </Button>
        <Button
          onClick={handleRename}
          className="uppercase !bg-foreground !text-background !font-bold disabled:opacity-50"
          icon={renameFolderMutation.isPending ? <AiOutlineLoading3Quarters size={16} className="animate-spin" /> : <Check size={16} />}
          iconPosition="right"
          disabled={name.trim() === folderName || renameFolderMutation.isPending}
        >
          CONFIRMAR
        </Button>
      </div>
    </>
  );
}
