from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from uuid import UUID
from typing import List


class FolderId(BaseModel):
    folder_id: UUID


class FolderBase(FolderId):
    folder_name: Optional[dict] = None
    parent_folder_id: Optional[UUID] = None


class FolderCreate(BaseModel):
    folder_name: Optional[dict] = None
    parent_folder_id: Optional[UUID] = None
    user_reports_id_list: Optional[List[UUID]] = None
    hmac_folder_name: Optional[list[str]] = None

class FolderRename(FolderBase):
    hmac_folder_name: Optional[list[str]] = None

class FolderGet(BaseModel):
    folder_name: Optional[dict] = None
    parent_folder_id: Optional[UUID] = None
    hmac_folder_name: Optional[list[str]] = None


class FolderOut(FolderBase):
    created_at: datetime
    modified_at: Optional[datetime] = None
    folder_path: str
    depth_level: int

    class Config:
        from_attributes = True


class FolderMove(BaseModel):
    src_folder_id: Optional[UUID] = None
    dest_folder_id: Optional[UUID] = None
    user_reports_id_list: Optional[List[UUID]] = None
    folder_id_list: Optional[List[UUID]] = None

class MergeFoldersInput(FolderId):
    folder_id_to_merge: UUID