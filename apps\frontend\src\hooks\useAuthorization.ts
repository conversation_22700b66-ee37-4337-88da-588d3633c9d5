import { verifierPhrase } from "~/helpers/constants";
import { useEncryption } from "./useEncryption";
import { useUserCRUD } from "./useUserCRUD";
import { toast } from "sonner";
import { usePassword, useVerifiers } from "~/store/credentials";

export const useAuthorization = () => {
  const verifiers = useVerifiers();
  const secretKey = usePassword();

  const { addNewVerifierMutation } = useUserCRUD();
  const { decryptData, _encryptData, deriveKey } = useEncryption();

  const handleVerifier = async (password?: string): Promise<boolean> => {
    try {

      if (!verifiers.encrypted || !verifiers.iv) {
        const rawKey = await deriveKey(password);
        const { data } = await _encryptData(verifierPhrase, rawKey.buffer as ArrayBuffer );
        let ok;

        if (!data) {
          console.error("Erro ao criptografar dados.");
          return false;
        }

        try {
          ok = await addNewVerifierMutation.mutateAsync(data);
        } catch (e) {
          console.error("Erro ao adicionar novo verifier:", e);
          return false;
        }
        return ok;
      }

      const keyToUse = password || secretKey;
      if (
        !verifiers.salt ||
        !keyToUse ||
        !verifiers.iv ||
        !verifiers.encrypted
      ) {
        console.error("Não é possível verificar a senha.");
        return false;
      }

      const iv = verifiers.iv;
      const encrypted = verifiers.encrypted;

      const { data: decrypted } = await decryptData({ encrypted, iv }, password);

      if (decrypted === verifierPhrase) {
        return true;
      }

      toast.error("Senha incorreta", {
        description:
          "A senha fornecida não corresponde à senha associada à sua conta.",
      });
      return false;
    } catch (error) {
      console.error("Erro ao descriptografar dados:", error);
      return false;
    }
  };

  return {
    handleVerifier,
  };
};
