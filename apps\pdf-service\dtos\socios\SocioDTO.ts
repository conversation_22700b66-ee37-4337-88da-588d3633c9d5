import { Socio, SocioValidator } from './SocioValidator';

/**
 * Data Transfer Object for Socio model
 * Provides static methods that delegate to SocioValidator
 */
export class SocioDTO {
  // Create a singleton instance of the validator
  private static validator = new SocioValidator();

  /**
   * Validates if a socio entry has the expected structure
   */
  static isValidSocioEntry(entry: any): entry is Socio {
    return SocioDTO.validator.isValid(entry);
  }

  /**
   * Validates an array of socio entries
   */
  static validateSocioArray(data: any[]): boolean {
    return SocioDTO.validator.validateArray(data);
  }

  /**
   * Filters an array to only include valid socio entries
   */
  static filterValidSocios(data: any[]): Socio[] {
    return SocioDTO.validator.filterValid(data);
  }

  /**
   * Ensures all values in a socio entry are properly stringified
   */
  static sanitizeValues(entry: Socio): Socio {
    return SocioDTO.validator.sanitize(entry);
  }

  /**
   * Filters and sanitizes an array of socio entries
   */
  static processData(data: any[]): Socio[] {
    return SocioDTO.validator.processData(data);
  }
}