FROM python:3.12-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# Create working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends curl netcat-openbsd bash && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install Node.js (LTS)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs

# Copy requirements early for caching
COPY ./apps/backend/requirements.txt .

# Install Python dependencies including debugpy
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install debugpy

# Copy application source code
COPY ./apps/backend /app

# Copy scripts and make them executable
COPY ./scripts/wait_until_kafka_alive.sh /app/scripts/wait_until_kafka_alive.sh
COPY ./scripts/wait_until_postgres.sh /app/scripts/wait_until_postgres.sh
COPY ./apps/backend/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/scripts/wait_until_kafka_alive.sh && \
    chmod +x /app/scripts/wait_until_postgres.sh && \
    chmod +x /app/entrypoint.sh

# Expose both app and debug ports
EXPOSE 8000 5678

# Set the entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]

# Default command is to start the application
CMD ["start"]