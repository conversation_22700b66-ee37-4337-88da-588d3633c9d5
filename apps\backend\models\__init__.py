import logging

from models.base import Base

# Level 1: Base tables with no dependencies
from models.user_model import Users
from models.organization_model import Organizations
from models.folder_model import Folder
from models.eula_model import Eula

# Level 2: Tables that depend on Level 1 tables
from models.organization_users_model import OrganizationUsers  # depends on users, organizations
from models.user_report_ledger_model import UserReportLedger   # depends on users, organizations
from models.invite_model import Invite                         # depends on users

# Level 3: Tables that depend on Level 2 tables
from models.report_model import UserReports                   # depends on user_report_ledger, users, organizations, folders

# Level 4: Tables that depend on Level 3 tables
from models.user_columns_hmac import UserColumnsHmac          # depends on user_reports
from models.report_executions_model import ReportExecutions   # depends on user_report_ledger, users
from models.folder_hmac import FolderHmac                     # depends on folders


# Setup logger
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s]: %(message)s")
    
# List all models to be imported easily (ordered by dependency level)
__all__ = [
    # Level 1: Base tables
    "Users", "Organizations", "Folder", "Eula",
    # Level 2: First level dependencies
    "OrganizationUsers", "UserReportLedger", "Invite",
    # Level 3: Second level dependencies
    "UserReports",
    # Level 4: Third level dependencies
    "UserColumnsHmac", "ReportExecutions", "FolderHmac"
]

def get_application_table_names():
    """Dynamically extract table names from all models"""
    try:
        # Get all table names from the metadata
        table_names = list(Base.metadata.tables.keys())
        
        # Filter out any system tables
        application_tables = []
        for table_name in table_names:
            # Skip system tables
            if not table_name.startswith('pg_') and not table_name.startswith('sql_'):
                application_tables.append(table_name)
        
        return application_tables
    except Exception as e:
        logger.error(f"Error extracting table names: {e}")
        return []

# Get table names for logging
table_names = get_application_table_names()
logger.info(f"[models] Models initialized: {', '.join(__all__)}")
logger.info(f"[models] Application tables: {', '.join(table_names)}")
