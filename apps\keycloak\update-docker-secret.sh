#!/bin/bash
set -e

echo "🔄 Updating Docker secret with Keycloak client secret..."

TARGET_REALM="SnapReportsRealm"
CLIENT_ID="myclient"
SECRET_NAME="client_secret_keycloak"

# Get the client UUID
CLIENT_UUID=$(/opt/keycloak/bin/kcadm.sh get clients -r ${TARGET_REALM} -q clientId=$CLIENT_ID --fields id | grep '"id"' | cut -d'"' -f4)

if [ -z "$CLIENT_UUID" ]; then
  echo "❌ Client $CLIENT_ID not found in realm $TARGET_REALM"
  exit 1
fi

# Get the current secret from Keycloak
KEYCLOAK_SECRET=$(/opt/keycloak/bin/kcadm.sh get clients/$CLIENT_UUID -r ${TARGET_REALM} --fields secret 2>/dev/null | grep '"secret"' | cut -d'"' -f4)

if [ -z "$KEYCLOAK_SECRET" ]; then
  echo "❌ No secret found for client $CLIENT_ID"
  exit 1
fi

echo "🔍 Found secret in Keycloak: ${KEYCLOAK_SECRET:0:8}...${KEYCLOAK_SECRET: -8}"

# Check if we're running in Docker Swarm
if docker node ls >/dev/null 2>&1; then
  echo "🐳 Running in Docker Swarm mode"

  # Remove existing secret
  if docker secret ls --filter name="$SECRET_NAME" -q | grep -q .; then
    echo "🗑️ Removing existing Docker secret: $SECRET_NAME"
    docker secret rm "$SECRET_NAME"
  fi

  # Create new secret with Keycloak value
  echo "✨ Creating new Docker secret with Keycloak value"
  echo -n "$KEYCLOAK_SECRET" | docker secret create "$SECRET_NAME" -

  echo "✅ Docker secret $SECRET_NAME updated successfully!"
  echo "🔄 Backend services will get the new secret on next restart"
else
  echo "ℹ️ Not running in Docker Swarm mode, skipping secret update"
fi