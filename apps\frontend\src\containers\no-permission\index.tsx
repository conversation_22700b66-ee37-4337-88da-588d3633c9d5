import { Text, Button } from "@snap/design-system";
import { ShieldX, Home, ArrowRight } from "lucide-react";
import { useNavigate } from "react-router";
import { useAuth } from "~/hooks/useAuth";
import { usePermissionManager } from "~/services/permissionManager";

const NoPermissionPage = () => {
  const navigate = useNavigate();
  const { canViewReportList } = usePermissionManager();
  const {
    logoutMutation: { mutateAsync: logout },
  } = useAuth();

  const handleGoHome = () => {
    // Redireciona para a página inicial se o usuário tem permissão, senão para configurações
    if (canViewReportList()) {
      navigate("/");
    } else {
      navigate("/conta/configuracoes");
    }
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="flex flex-col items-center justify-center px-8 py-12">
      <div className="flex flex-col items-center gap-6 max-w-md text-center">
        <div className="p-4 rounded-full bg-destructive/10">
          <ShieldX size={64} className="text-destructive" />
        </div>

        <div className="space-y-2">
          <Text variant="title-lg" className="text-foreground">
            Acesso Negado
          </Text>
          <Text variant="body-md" className="text-secondary">
            Você não possui permissão para acessar esta página. Entre em contato com um administrador para solicitar acesso.
          </Text>
        </div>

        <div className="flex gap-3 w-full">
          <Button
            variant="outline"
            onClick={handleGoHome}
            className="flex-1"
            icon={<Home size={16} />}
            iconPosition="right"
          >

            Voltar ao Início
          </Button>
          <Button
            variant="outline"
            onClick={handleLogout}
            className="flex-1"
            icon={<ArrowRight size={16} />}
            iconPosition="right"
          >
            Sair
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NoPermissionPage;
