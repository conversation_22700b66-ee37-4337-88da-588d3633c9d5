from sqlalchemy import <PERSON>umn, ForeignKey, DateTime, UUID, Text, Integer, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB

from models.base import Base

class Folder(Base):
    __tablename__ = 'folders'
    __table_args__ = (
        Index("idx_folders_folder_name", "folder_name", postgresql_using='gin'),
        # Composite indexes optimized for common filters and sorts
        Index('ix_folders_user_parent_created', "user_folder_id", "parent_folder_id", "created_at", postgresql_using="btree"),
        Index('ix_folders_user_parent_modified', "user_folder_id", "parent_folder_id", "modified_at", postgresql_using="btree"),
        Index('ix_folders_user_org_created', "user_folder_id", "organization_id", "created_at", postgresql_using="btree"),
        Index('ix_folders_user_org_modified', "user_folder_id", "organization_id", "modified_at", postgresql_using="btree"),
        Index('ix_folders_user_parent_org_modified', "user_folder_id", "parent_folder_id", "organization_id", "modified_at", postgresql_using="btree"),
        Index('ix_folders_user_parent_org_created', "user_folder_id", "parent_folder_id", "organization_id", "created_at", postgresql_using="btree"),
        Index('ix_folders_user_parent_org_created_desc', "user_folder_id", "parent_folder_id", "organization_id", "created_at", postgresql_using="btree"),
        Index('ix_folders_user_parent_org_modified_desc', "user_folder_id", "parent_folder_id", "organization_id", "modified_at", postgresql_using="btree"),
        Index('ix_folders_user_parent_org_created_desc_stable', "user_folder_id", "parent_folder_id", "organization_id", "created_at", "folder_id", postgresql_using="btree"),
        Index('ix_folders_user_parent_org_modified_desc_stable', "user_folder_id", "parent_folder_id", "organization_id", "modified_at", "folder_id", postgresql_using="btree"),
        {"schema": "reports"}
    )

    folder_id = Column(PostgresUUID, primary_key=True, server_default=func.gen_random_uuid())
    folder_name = Column(JSONB, nullable=False)
    parent_folder_id = Column(PostgresUUID, ForeignKey("reports.folders.folder_id"), nullable=True, index=True)
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now(), index=True)
    modified_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now(), index=True)
    user_folder_id = Column(PostgresUUID, ForeignKey("reports.users.user_id"), nullable=False, index=True)
    organization_id = Column(PostgresUUID, ForeignKey("reports.organizations.organization_id"), nullable=True, index=True)
    folder_path = Column(Text, nullable=False, index=True)
    depth_level = Column(Integer, nullable=False, server_default='1', index=True)

    # Relationships
    parent_folder = relationship("Folder", remote_side=[folder_id], back_populates="subfolders")
    subfolders = relationship("Folder", back_populates="parent_folder")
    reports = relationship("UserReports", back_populates="folder")
    organization = relationship("Organizations", back_populates="folders")

    def __repr__(self):
        return f"<Folder(folder_id={self.folder_id}, folder_name={self.folder_name}, folder_path={self.folder_path})>"
