
export const hasRenderableContents = (sections: any[]): boolean => {
  try {
    if (!Array.isArray(sections) || sections.length === 0) {
      return false;
    }

    const filteredSections = sections.filter(section => {
      if (!section || typeof section !== 'object') return false;
      if (section.subsection) return false;
      if (section.is_deleted) return false;
      if (section.data_count > 0) return true;

      return Array.isArray(section.data) &&
        section.data.some((item: any) => {
          return item &&
            typeof item === 'object' &&
            Array.isArray(item.detalhes) &&
            item.detalhes.length > 0;
        });
    });

    const hasValidSectionData = filteredSections.length > 0;
    return hasValidSectionData;
  } catch (error) {
    console.warn('Error checking renderable sections:', error);
    return false;
  }
}