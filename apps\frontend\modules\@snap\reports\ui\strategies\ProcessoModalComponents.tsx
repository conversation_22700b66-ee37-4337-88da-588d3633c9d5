import React from "react";
import {
  useReportActions,
  ReportProvider,
  RenderMode
} from "../../context/ReportContext";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { translatePropToLabel, parseValue, formatFieldValue } from "../../helpers";
import { REPORT_DETAIL_STORE_INSTANCE } from "../../../../../src/store/details";
import { renderSourceTooltip } from "./helpers.strategy";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { Processo } from "../../model/Processo";

// Types for component props
export interface ReactiveModalProviderProps {
  numeroValue: string;
  sectionTitle: string;
  isTrashEnabled: boolean;
  isPrintEnabled: boolean;
  autoSaveActions: any;
  mode: RenderMode;
  testEntryDeleted: (entry: any) => boolean;
  testSectionDeleted: (section: any) => boolean;
  calculateDataCount: (section: any) => number;
}

export interface ModalContentWithDataProps {
  numeroValue: string;
  mainSections: any[];
  mainMode: RenderMode;
  sectionTitle: string;
  testEntryDeleted: (entry: any) => boolean;
  testSectionDeleted: (section: any) => boolean;
  calculateDataCount: (section: any) => number;
}

// Provider reativo necessário para usar no modal
export const ReactiveModalProvider: React.FC<ReactiveModalProviderProps> = ({
  numeroValue,
  sectionTitle,
  isTrashEnabled,
  isPrintEnabled,
  autoSaveActions,
  mode,
  testEntryDeleted,
  testSectionDeleted,
  calculateDataCount
}) => {
  const {
    selectors: {
      useReportSections: useStoreSections,
      useReportMetadata: useStoreMetadata,
      useReportType: useStoreReportType,
      useReportProfileImage: useStoreProfileImage,
      useIsActionLoading: useStoreIsActionLoading
    }
  } = REPORT_DETAIL_STORE_INSTANCE;

  const freshSections = useStoreSections();
  const freshMetadata = useStoreMetadata();
  const freshReportType = useStoreReportType();
  const freshProfileImage = useStoreProfileImage();
  const freshIsActionLoading = useStoreIsActionLoading();

  return (
    <ReportProvider
      sections={freshSections}
      metadata={freshMetadata!}
      reportType={freshReportType}
      isTrashEnabled={isTrashEnabled}
      isPrintEnabled={isPrintEnabled}
      isActionLoading={freshIsActionLoading}
      image={freshProfileImage || undefined}
      actions={autoSaveActions}
      renderMode={mode}
    >
      <ModalContentWithData
        numeroValue={numeroValue}
        mainSections={freshSections}
        mainMode={mode}
        sectionTitle={sectionTitle}
        testEntryDeleted={testEntryDeleted}
        testSectionDeleted={testSectionDeleted}
        calculateDataCount={calculateDataCount}
      />
    </ReportProvider>
  );
};

export const ModalContentWithData: React.FC<ModalContentWithDataProps> = ({
  numeroValue,
  mainSections,
  mainMode,
  sectionTitle,
  testEntryDeleted,
  testSectionDeleted,
  calculateDataCount
}) => {
  // Use the actions from React context instead of prop drilling
  const modalActions = useReportActions();

  const currentMainSection = mainSections.find((s: any) => s.title === sectionTitle && !s.subsection);
  const currentProcessoData = currentMainSection?.data.find((r: any) => r.numero.value === numeroValue);

  if (!currentProcessoData) {
    return <div>Dados não encontrados</div>;
  }

  const renderFreshProcessoModal = (processo: Processo) => {
    const elements: React.ReactElement[] = [];
    const processoIdx = currentMainSection?.data.findIndex((p: any) => p.numero.value === processo.numero.value) ?? -1;
    const currentProcesso = currentMainSection?.data[processoIdx] || processo;
    const modalIncludeKey = (isDeleted: boolean) => mainMode === "trash" ? isDeleted : !isDeleted;

    if (currentProcesso.numero && modalIncludeKey(currentProcesso.numero.is_deleted)) {
      elements.push(
        <CustomGridContainer cols={1} key="numero" respectColsInTrash={true}>
          <CustomGridItem
            fullWidth
            className="mb-4"
            onToggleField={() => {
              const updater = modalActions?.updateSectionEntries;
              if (!updater) return;

              updater(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === processoIdx && e.numero) {
                    e.numero.is_deleted = !e.numero.is_deleted;
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              );
            }}
          >
            <CustomReadOnlyInputField
              label={translatePropToLabel(currentProcesso.numero.label || "numero").toUpperCase()}
              value={parseValue(formatFieldValue(currentProcesso.numero.value))}
              tooltip={renderSourceTooltip(currentProcesso.numero.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    }

    if (currentProcesso.detalhes && Object.keys(currentProcesso.detalhes).length > 0) {
      const detalhesEntries = Object.entries(currentProcesso.detalhes).filter(([_, val]) =>
        modalIncludeKey((val as any).is_deleted)
      );

      if (detalhesEntries.length > 0) {
        elements.push(
          <CustomGridContainer cols={2} key="detalhes" className="mb-4" respectColsInTrash={true}>
            {detalhesEntries.map(([fieldKey, val]) => (
              <CustomGridItem
                key={fieldKey}
                cols={1}
                onToggleField={() => {
                  const updater = modalActions?.updateSectionEntries;
                  if (!updater) return;

                  updater(
                    sectionTitle,
                    (e: any, i?: number) => {
                      if (i === processoIdx) {
                        const d = e.detalhes?.[fieldKey];
                        if (d) {
                          d.is_deleted = !d.is_deleted;
                        }
                      }
                    },
                    testEntryDeleted,
                    testSectionDeleted,
                    calculateDataCount
                  );
                }}
              >
                <CustomReadOnlyInputField
                  label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
                  value={parseValue(formatFieldValue((val as any).value))}
                  tooltip={renderSourceTooltip((val as any).source)}
                />
              </CustomGridItem>
            ))}
          </CustomGridContainer>
        );
      }
    }

    if (currentProcesso.movimentações && modalIncludeKey(currentProcesso.movimentações.is_deleted)) {
      elements.push(
        <CustomGridContainer cols={1} key="movimentacoes" className="mb-6" respectColsInTrash={true}>
          <CustomGridItem
            fullWidth
            onToggleField={() => {
              const updater = modalActions?.updateSectionEntries;
              if (!updater) return;

              updater(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === processoIdx && e.movimentações) {
                    e.movimentações.is_deleted = !e.movimentações.is_deleted;
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              );
            }}
          >
            <CustomReadOnlyInputField
              label={translatePropToLabel(currentProcesso.movimentações.label || "Movimentações").toUpperCase()}
              value={parseValue(formatFieldValue(currentProcesso.movimentações.value))}
              element="textarea"
              tooltip={renderSourceTooltip(currentProcesso.movimentações.source)}
              labelTextClass="py-2"
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    }

    const participantKeys = Object.keys(currentProcesso).filter(key =>
      key !== 'numero' && key !== 'detalhes' && key !== 'movimentações' && Array.isArray(currentProcesso[key])
    );

    participantKeys.forEach(key => {
      const participants = currentProcesso[key] as any[];

      // Lógica de visibilidade: lixeira mostra itens deletados ou com campos deletados
      const visibleParticipants = participants.filter(p => {
        if (mainMode === "trash") {
          const hasDeletedFields = p.value && Object.values(p.value).some((field: any) => field?.is_deleted === true);
          return p.is_deleted === true || hasDeletedFields;
        } else {
          const hasNonDeletedFields = p.value && Object.values(p.value).some((field: any) => field?.is_deleted === false);
          return p.is_deleted === false && hasNonDeletedFields;
        }
      });

      if (visibleParticipants.length > 0) {
        elements.push(
          <div key={key} className="mb-6">
            <CustomGridItem
              fullWidth
              containerClassName="pb-2"
              onToggleField={() => {
                const updater = modalActions?.updateSectionEntries;
                if (!updater) return;

                updater(
                  sectionTitle,
                  (e: any, i?: number) => {
                    if (i === processoIdx && e[key]) {
                      const targetDeletedState = mainMode === "trash" ? false : true;
                      e[key].forEach((participant: any) => {
                        participant.is_deleted = targetDeletedState;
                        if (participant.value) {
                          Object.values(participant.value).forEach((campo: any) => {
                            if (campo) {
                              campo.is_deleted = targetDeletedState;
                            }
                          });
                        }
                      });
                    }
                  },
                  testEntryDeleted,
                  testSectionDeleted,
                  calculateDataCount
                );
              }}
            >
              <ReportsCustomLabel
                label={translatePropToLabel(key).toUpperCase()}
                colorClass="bg-primary"
              />
            </CustomGridItem>
            <CustomGridContainer cols={2} respectColsInTrash={true}>
              {visibleParticipants.map((participant, participantIdx) => {
                const originalIdx = participants.indexOf(participant);
                if (participant.value && typeof participant.value === 'object') {
                  return (
                    <div key={`${key}-${participantIdx}`} className="col-span-1">
                      <CustomGridItem
                        fullWidth
                        containerClassName="py-3"
                        onToggleField={() => {
                          const updater = modalActions?.updateSectionEntries;
                          if (!updater) return;

                          updater(
                            sectionTitle,
                            (e: any, i?: number) => {
                              if (i === processoIdx && e[key]?.[originalIdx]) {
                                const targetDeletedState = mainMode === "trash" ? false : true;
                                e[key][originalIdx].is_deleted = targetDeletedState;
                                if (e[key][originalIdx].value) {
                                  Object.values(e[key][originalIdx].value).forEach((campo: any) => {
                                    if (campo) {
                                      campo.is_deleted = targetDeletedState;
                                    }
                                  });
                                }
                              }
                            },
                            testEntryDeleted,
                            testSectionDeleted,
                            calculateDataCount
                          );
                        }}
                      >
                        <ReportsCustomLabel
                          label={(() => {
                            const baseLabel = translatePropToLabel(key).toUpperCase();
                            if (mainMode === "trash") {
                              const isParticipantDeleted = participant.is_deleted === true;
                              return isParticipantDeleted ? `${baseLabel} ${participantIdx + 1}` : baseLabel;
                            } else {
                              return `${baseLabel} ${participantIdx + 1}`;
                            }
                          })()}
                          colorClass="bg-border"
                          icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                        />
                      </CustomGridItem>
                      <div className="pl-5">
                        {Object.entries(participant.value)
                          .filter(([_, field]) => modalIncludeKey((field as any)?.is_deleted))
                          .map(([fieldKey, field]) => (
                            <CustomGridItem
                              key={`${key}-${participantIdx}-${fieldKey}`}
                              cols={1}
                              className="py-1"
                              onToggleField={() => {
                                const updater = modalActions?.updateSectionEntries;
                                if (!updater) return;

                                updater(
                                  sectionTitle,
                                  (e: any, i?: number) => {
                                    if (i === processoIdx && e[key]?.[originalIdx]?.value?.[fieldKey]) {
                                      e[key][originalIdx].value[fieldKey].is_deleted = !e[key][originalIdx].value[fieldKey].is_deleted;

                                      const allFieldsDeleted = Object.values(e[key][originalIdx].value).every((f: any) => f.is_deleted === true);
                                      e[key][originalIdx].is_deleted = allFieldsDeleted;
                                    }
                                  },
                                  testEntryDeleted,
                                  testSectionDeleted,
                                  calculateDataCount
                                );
                              }}
                            >
                              <CustomReadOnlyInputField
                                label={translatePropToLabel((field as any).label || fieldKey).toUpperCase()}
                                colorClass="bg-border"
                                value={parseValue(formatFieldValue((field as any).value))}
                                tooltip={renderSourceTooltip((field as any).source)}
                              />
                            </CustomGridItem>
                          ))}
                      </div>
                    </div>
                  );
                }
                return null;
              })}
            </CustomGridContainer>
          </div>
        );
      }
    });

    return elements;
  };

  return (
    <div key={`modal-content-${numeroValue}-${mainMode}`}>
      {renderFreshProcessoModal(currentProcessoData as Processo).map((el, idx) => (
        <div key={idx} className="mb-4">
          {el}
        </div>
      ))}
    </div>
  );
};