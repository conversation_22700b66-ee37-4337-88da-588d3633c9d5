import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { Mandado } from "../../model/Mandados";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { formatFieldValue, parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";

export function useRenderMandados(
  sectionTitle: string
): ArrayRenderStrategy<Mandado> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  // pull our section once, then memoize an entry→index map:
  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<Mandado, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  // include‐flag helper
  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  const testNumeroDeleted = (e: any) => e.numero?.is_deleted === true;
  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted === true)
      : false;
  const testPessoaDeleted = (e: any) =>
    e.pessoa && Array.isArray(e.pessoa)
      ? e.pessoa.every((p: any) =>
        Object.values(p.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testProcessosDeleted = (e: any) =>
    e.processos && Array.isArray(e.processos)
      ? e.processos.every((p: any) =>
        Object.values(p.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testTelefonesDeleted = (e: any) =>
    e.telefones && Array.isArray(e.telefones)
      ? e.telefones.every((t: any) =>
        Object.values(t.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;

  const testEntryDeleted = (entry: any): boolean => {
    const isNumeroDeleted = testNumeroDeleted(entry);
    const areDetalhesDeleted = testDetalhesDeleted(entry);
    const arePessoaDeleted = testPessoaDeleted(entry);
    const areProcessosDeleted = testProcessosDeleted(entry);
    const areTelefonesDeleted = testTelefonesDeleted(entry);

    return isNumeroDeleted && areDetalhesDeleted && arePessoaDeleted && areProcessosDeleted && areTelefonesDeleted;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  // Data count calculation function for Mandados section
  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      // Count non-deleted entries (whole objects)
      // An entry is considered deleted only when ALL its parts are deleted
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  };

  // Helper functions for nested arrays
  const shouldIncludeNestedBlock = (item: any) => {
    const vals = Object.values(item.value || {});
    return isTrash
      ? vals.some((v: any) => v.is_deleted === true)
      : vals.some((v: any) => v.is_deleted === false);
  };

  const shouldIncludeList = (arrayItems: any[]) => {
    if (!Array.isArray(arrayItems)) return false;
    return arrayItems.some((item: any) => shouldIncludeNestedBlock(item));
  };

  const onToggleNestedField = (entryIdx: number, arrayKey: string, blockIdx: number, fieldKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const item = e[arrayKey]?.[blockIdx];
          if (item?.value?.[fieldKey]) {
            item.value[fieldKey].is_deleted = !item.value[fieldKey].is_deleted;

            // Verifica se todos os campos do bloco estão deletados
            const allFieldsDeleted = Object.values(item.value).every((campo: any) =>
              campo.is_deleted === true
            );

            // Se todos os campos estão deletados, marca o bloco como deletado
            item.is_deleted = allFieldsDeleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleNestedBlock = (entryIdx: number, arrayKey: string, blockIdx: number) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const item = e[arrayKey]?.[blockIdx];
          if (item?.value) {
            // Determina o novo estado baseado no modo atual
            const targetDeletedState = isTrash ? false : true;

            // Define o is_deleted do bloco principal
            item.is_deleted = targetDeletedState;

            // Define o is_deleted de todos os campos dentro do bloco
            Object.values(item.value).forEach((campo: any) => {
              if (campo) {
                campo.is_deleted = targetDeletedState;
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleListTitle = (entryIdx: number, arrayKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const arrayItems = e[arrayKey];
          if (arrayItems && Array.isArray(arrayItems)) {
            // Determina o novo estado baseado no modo atual
            const targetDeletedState = isTrash ? false : true;

            // Aplica a todos os itens e seus campos no array
            arrayItems.forEach((item: any) => {
              item.is_deleted = targetDeletedState;
              if (item.value) {
                Object.values(item.value).forEach((campo: any) => {
                  if (campo) {
                    campo.is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const formatByKey: Record<
    string,
    (entry?: Mandado) => React.ReactElement | null
  > = {
    numero: (entry) => {
      if (!entry?.numero || !includeKey(entry.numero.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer cols={1} key={`numero-${idx}`}>
          <CustomGridItem
            fullWidth
            className="mb-6"
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                // now only mutate the single entry at `idx`
                (e: any, i?: number) => {
                  if (i === idx && e.numero) {
                    e.numero.is_deleted = !e.numero.is_deleted;

                    // Block deletion logic: when numero is deleted, delete all other fields
                    // When numero is restored, restore all other fields
                    const targetDeletedState = e.numero.is_deleted;

                    // Apply to all detalhes
                    if (e.detalhes) {
                      Object.values(e.detalhes).forEach((detalhe: any) => {
                        if (detalhe) detalhe.is_deleted = targetDeletedState;
                      });
                    }

                    // Apply to pessoa array
                    if (e.pessoa && Array.isArray(e.pessoa)) {
                      e.pessoa.forEach((p: any) => {
                        p.is_deleted = targetDeletedState;
                        if (p.value) {
                          Object.values(p.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    // Apply to processos array
                    if (e.processos && Array.isArray(e.processos)) {
                      e.processos.forEach((p: any) => {
                        p.is_deleted = targetDeletedState;
                        if (p.value) {
                          Object.values(p.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    // Apply to telefones array
                    if (e.telefones && Array.isArray(e.telefones)) {
                      e.telefones.forEach((t: any) => {
                        t.is_deleted = targetDeletedState;
                        if (t.value) {
                          Object.values(t.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={entry.numero.label.toUpperCase()}
              colorClass="bg-primary"
              labelTextClass="text-accent"
              value={parseValue(formatFieldValue(entry.numero.value))}
              tooltip={renderSourceTooltip(entry.numero.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    detalhes: (entry) => {
      if (!entry?.detalhes) return null;
      const subs = Object.entries(entry.detalhes).filter(([, v]) =>
        includeKey((v as any).is_deleted)
      );
      if (!subs.length) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer
          cols={2}
          columnFirst
          key={`detalhes-${idx}`}
          className="mb-6"
        >
          {subs.map(([fieldKey, val]) => (
            <CustomGridItem
              key={fieldKey}
              cols={1}
              onToggleField={() =>
                actions.updateSectionEntries!(
                  sectionTitle,
                  (e: any, i?: number) => {
                    if (i === idx) {
                      const d = e.detalhes?.[fieldKey];
                      if (d) d.is_deleted = !d.is_deleted;
                    }
                  },
                  testDetalhesDeleted,
                  testSectionDeleted,
                  calculateDataCount
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
                value={parseValue(formatFieldValue((val as any).value))}
                tooltip={renderSourceTooltip((val as any).source)}
              />
            </CustomGridItem>
          ))}
        </CustomGridContainer>
      );
    },

    pessoa: (entry) => {
      if (!entry?.pessoa?.length || !shouldIncludeList(entry.pessoa)) return null;
      const idx = idxMap.get(entry)!;

      const blocks = entry.pessoa
        .map((p, i) => ({ bloco: p, idx: i }))
        .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} key={`pessoa-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => onToggleListTitle(idx, 'pessoa')}
          >
            <ReportsCustomLabel
              label="PESSOAS"
              colorClass="bg-primary"
            />
          </CustomGridItem>
          {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
            <div key={`pes-${idx}-${origIdx}`} className="mb-4">
              <CustomGridItem
                cols={1}
                containerClassName="w-fit pr-12 pb-2"
                onToggleField={() => onToggleNestedBlock(idx, 'pessoa', origIdx)}
              >
                <ReportsCustomLabel
                  label={`PESSOA ${!isTrash ? blockRenderIdx + 1 : ""}`}
                  colorClass="bg-border"
                />
              </CustomGridItem>
              <div className="pl-5">
                <CustomGridContainer cols={2}>
                  {Object.entries(bloco.value)
                    .filter(([_, v]: any) =>
                      isTrash ? v.is_deleted : !v.is_deleted
                    )
                    .map(([fieldKey, fieldValue]: any, index) => (
                      <CustomGridItem
                        key={`pes-${idx}-${origIdx}-${fieldKey}`}
                        cols={1}
                        className="py-1"
                        onToggleField={() => onToggleNestedField(idx, 'pessoa', origIdx, fieldKey)}
                      >
                        <CustomReadOnlyInputField
                          label={translatePropToLabel(
                            fieldValue.label || fieldKey
                          ).toUpperCase()}
                          colorClass="bg-border"
                          value={parseValue(formatFieldValue(fieldValue.value || ""))}
                          isFirstLabelList={index === 0}
                          icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                          tooltip={renderSourceTooltip(fieldValue.source)}
                        />
                      </CustomGridItem>
                    ))}
                </CustomGridContainer>
              </div>
            </div>
          ))}
        </CustomGridContainer>
      );
    },

    processos: (entry) => {
      if (!entry?.processos?.length || !shouldIncludeList(entry.processos)) return null;
      const idx = idxMap.get(entry)!;

      const blocks = entry.processos
        .map((p, i) => ({ bloco: p, idx: i }))
        .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} key={`processos-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}

            containerClassName="w-fit pr-12"
            onToggleField={() => onToggleListTitle(idx, 'processos')}
          >
            <ReportsCustomLabel
              label="PROCESSOS"
              colorClass="bg-primary"
            />
          </CustomGridItem>

          {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
            <div key={`proc-${idx}-${origIdx}`} className="mb-4">
              <CustomGridItem
                cols={1}
                containerClassName="w-fit pr-12 pb-2"
                onToggleField={() => onToggleNestedBlock(idx, 'processos', origIdx)}
              >
                <ReportsCustomLabel
                  label={`PROCESSO ${!isTrash ? blockRenderIdx + 1 : ""}`}
                  colorClass="bg-border"
                />
              </CustomGridItem>
              <div className="pl-5">
                <CustomGridContainer cols={2}>
                  {Object.entries(bloco.value)
                    .filter(([_, v]: any) =>
                      isTrash ? v.is_deleted : !v.is_deleted
                    )
                    .map(([fieldKey, fieldValue]: any, index) => (
                      <CustomGridItem
                        key={`proc-${idx}-${origIdx}-${fieldKey}`}
                        cols={1}
                        className="py-1"
                        onToggleField={() => onToggleNestedField(idx, 'processos', origIdx, fieldKey)}
                      >
                        <CustomReadOnlyInputField
                          label={translatePropToLabel(
                            fieldValue.label || fieldKey
                          ).toUpperCase()}
                          colorClass="bg-border"
                          value={parseValue(formatFieldValue(fieldValue.value))}
                          isFirstLabelList={index === 0}
                          icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                          tooltip={renderSourceTooltip(fieldValue.source)}
                        />
                      </CustomGridItem>
                    ))}
                </CustomGridContainer>
              </div>
            </div>
          ))}
        </CustomGridContainer>
      );
    },

    telefones: (entry) => {
      if (!entry?.telefones?.length || !shouldIncludeList(entry.telefones)) return null;
      const idx = idxMap.get(entry)!;

      const blocks = entry.telefones
        .map((t, i) => ({ bloco: t, idx: i }))
        .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} key={`telefones-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => onToggleListTitle(idx, 'telefones')}
          >
            <ReportsCustomLabel
              label="TELEFONES"
              colorClass="bg-primary"
            />
          </CustomGridItem>
          <CustomGridContainer cols={2}>
            {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
              <div key={`tel-${idx}-${origIdx}`} className="mb-4">
                <CustomGridItem
                  cols={1}
                  containerClassName="w-fit pr-12 pb-2"
                  onToggleField={() => onToggleNestedBlock(idx, 'telefones', origIdx)}
                >
                  <ReportsCustomLabel
                    label={`TELEFONE ${!isTrash ? blockRenderIdx + 1 : ""}`}
                    colorClass="bg-border"
                  />
                </CustomGridItem>
                <div className="pl-5">
                  <CustomGridContainer cols={1}>
                    {Object.entries(bloco.value)
                      .filter(([_, v]: any) =>
                        isTrash ? v.is_deleted : !v.is_deleted
                      )
                      .map(([fieldKey, fieldValue]: any, index) => (
                        <CustomGridItem
                          key={`tel-${idx}-${origIdx}-${fieldKey}`}
                          cols={1}
                          className="py-1"
                          onToggleField={() => onToggleNestedField(idx, 'telefones', origIdx, fieldKey)}
                        >
                          <CustomReadOnlyInputField
                            label={translatePropToLabel(
                              fieldValue.label || fieldKey
                            ).toUpperCase()}
                            colorClass="bg-border"
                            value={parseValue(formatFieldValue(fieldValue.value))}
                            isFirstLabelList={index === 0}
                            icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                            tooltip={renderSourceTooltip(fieldValue.source)}
                          />
                        </CustomGridItem>
                      ))}
                  </CustomGridContainer>
                </div>
              </div>
            ))}
          </CustomGridContainer>
        </CustomGridContainer>
      );
    },
  };



  const validateKeys = (keys: Array<keyof Mandado>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: Mandado): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof Mandado>;

    if (!validateKeys(keys)) {
      console.warn(`[Seção ${sectionTitle}] Chaves inválidas:`, keys);
    }

    const orderedKeys: Array<string> = [
      'numero',
      'detalhes',
      'pessoa',
      'processos',
      'telefones'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key as keyof Mandado));

    return filteredKeys
      .map((chave) => formatByKey[chave]?.(entry))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: Mandado[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn(`[Seção ${sectionTitle}] esperava array, mas recebeu:`, typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (isTrash) {
        // In trash mode, show entries that have ANY deleted field
        return testNumeroDeleted(entry) ||
          testDetalhesDeleted(entry) ||
          testPessoaDeleted(entry) ||
          testProcessosDeleted(entry) ||
          testTelefonesDeleted(entry) ||
          (entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true)) ||
          ((entry as any).pessoa && Array.isArray((entry as any).pessoa) && (entry as any).pessoa.some((p: any) =>
            Object.values(p.value || {}).some((v: any) => v.is_deleted === true)
          )) ||
          ((entry as any).processos && Array.isArray((entry as any).processos) && (entry as any).processos.some((p: any) =>
            Object.values(p.value || {}).some((v: any) => v.is_deleted === true)
          )) ||
          ((entry as any).telefones && Array.isArray((entry as any).telefones) && (entry as any).telefones.some((t: any) =>
            Object.values(t.value || {}).some((v: any) => v.is_deleted === true)
          ));
      } else {
        // In normal mode, show entries that are NOT completely deleted
        const isCompletelyDeleted = testEntryDeleted(entry);
        return !isCompletelyDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (filteredData.length > 1) {
        const isLastItem = index === filteredData.length - 1;
        allElements.push(
          <div
            key={`mandado-${index}`}
            className={`relative mb-8 pb-1 ${isLastItem ? '!mb-0 !pb-0' : ''}`}
          >
            {elements}
            {!isLastItem && (
              <div
                className="absolute bottom-0 border-b-2 border-dashed border-neutral-100"
                style={{ left: '-20px', right: '-20px' }}
              />
            )}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Marca todos os campos como deletado/restaurado
        if (entry.numero) {
          entry.numero.is_deleted = targetDeletedState;
        }
        if (entry.detalhes) {
          Object.values(entry.detalhes).forEach((detalhe: any) => {
            if (detalhe) detalhe.is_deleted = targetDeletedState;
          });
        }
        if ((entry as any).pessoa && Array.isArray((entry as any).pessoa)) {
          (entry as any).pessoa.forEach((p: any) => {
            p.is_deleted = targetDeletedState;
            if (p.value) {
              Object.values(p.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }
        if ((entry as any).processos && Array.isArray((entry as any).processos)) {
          (entry as any).processos.forEach((p: any) => {
            p.is_deleted = targetDeletedState;
            if (p.value) {
              Object.values(p.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }
        if ((entry as any).telefones && Array.isArray((entry as any).telefones)) {
          (entry as any).telefones.forEach((t: any) => {
            t.is_deleted = targetDeletedState;
            if (t.value) {
              Object.values(t.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
