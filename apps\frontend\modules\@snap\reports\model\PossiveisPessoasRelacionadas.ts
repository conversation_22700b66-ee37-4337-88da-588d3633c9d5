import { _Imagem } from "./Imagens";
import { ValueWithSource } from "./ValueWithSource";

export interface PossivelPessoaRelacionada {
  nome_completo: ValueWithSource;
  detalhes?: Array<Record<string, ValueWithSource>>;
  telefones?: Array<Record<string, ValueWithSource>>;
  imagens?: Array<Record<string, ValueWithSource>>;
  enderecos?: Array<Record<string, ValueWithSource>>;
  empresa?: Array<Record<string, ValueWithSource>>;
  emails?: Array<Record<string, ValueWithSource>>;
  pessoa?: Array<Record<string, ValueWithSource>>;
  redes_sociais?: Array<{
    detalhes: Array<{
      value: {
        [platform: string]: Array<{
          [field: string]: {
            value: any;
            label: string;
            source: string[];
            is_deleted: boolean;
          };
        }>;
      };
      label: string;
      source: string[];
      is_deleted: boolean;
    }>;
  }>;
  [key: string]: any; // listas genericas que não sei o que contém
}