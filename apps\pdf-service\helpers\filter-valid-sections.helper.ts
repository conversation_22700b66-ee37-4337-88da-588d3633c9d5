export const filterValidSections = (sections: any[]): any[] => {
  try {
    if (!Array.isArray(sections) || sections.length === 0) {
      return [];
    }

    return sections.filter(section => {
      // Verificações de segurança
      if (!section || typeof section !== 'object') return false;
      if (section.subsection) return false;
      if (section.is_deleted) return false;
      if (section.data_count > 0) return true;

      // Algumas seções vazias vem com a chave "detalhes" vazia dentro de "data", por isso a verificação abaixo
      return Array.isArray(section.data) &&
        section.data.some((item: any) => {
          return item && 
                 typeof item === 'object' && 
                 Array.isArray(item.detalhes) &&
                 item.detalhes.length > 0;
        });
    });
  } catch (error) {
    console.warn('Erro ao filtrar seções válidas:', error);
    return [];
  }
};