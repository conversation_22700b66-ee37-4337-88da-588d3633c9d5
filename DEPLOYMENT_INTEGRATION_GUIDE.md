# Deployment Integration Guide

## Overview

This guide shows how to integrate automatic database initialization into your `generate_deploy.sh` script so that database setup happens automatically during deployment.

## 🔄 How It Works

### **Automatic Detection:**
1. **First Run**: No tables exist → Creates initial schema using migration `001_initial_tables.py`
2. **Subsequent Runs**: Tables exist → Applies any pending migrations
3. **Already Current**: Everything up to date → No action needed

### **Integration Points:**
- **FastAPI Startup**: Automatic initialization when app starts
- **Deployment Script**: Pre-deployment database setup
- **Manual Setup**: Standalone database management

## 🚀 Integration with generate_deploy.sh

### **Option 1: Add Database Setup Step (Recommended)**

Add this section to your `generate_deploy.sh` after `deploy_stack()` and before the final success messages:

```bash
# Add this function after the existing functions in generate_deploy.sh
setup_database_after_deployment() {
  print_title "Setting up Database Schema"

  # Source the database initialization script
  source "apps/backend/database/deploy_init.sh"

  # Run database setup for the current profile
  if setup_database_for_deployment "$PROFILE"; then
    print_success "Database schema setup completed!"
  else
    print_error_and_exit "Database schema setup failed!"
  fi
}

# Then add this call after deploy_stack() in the main execution flow:
# Around line 686-690, after deploy_stack but before configure_keycloak_mappers:

deploy_stack

# Add this new line:
setup_database_after_deployment

configure_keycloak_mappers
```

### **Option 2: Environment Variable Control**

Add database setup as an optional step controlled by environment variable:

```bash
# Add this in the environment setup section (around line 100):
export AUTO_SETUP_DATABASE=${AUTO_SETUP_DATABASE:-true}

# Add this function:
conditional_database_setup() {
  if [[ "$AUTO_SETUP_DATABASE" == "true" ]]; then
    print_title "Automatic Database Setup Enabled"
    source "apps/backend/database/deploy_init.sh"
    setup_database_for_deployment "$PROFILE"
  else
    print_info "Automatic database setup disabled (AUTO_SETUP_DATABASE=false)"
  fi
}

# Call it after deploy_stack
```

### **Option 3: Minimal Integration (Just Logging)**

If you prefer to keep database initialization in the FastAPI startup only, just add logging:

```bash
# Add this after deploy_stack():
print_title "Database Initialization"
print_info "Database will be automatically initialized when backend starts"
print_info "First run: Creates tables using migration 001_initial_tables.py"
print_info "Subsequent runs: Applies pending migrations if any"
print_info "Monitor backend logs for initialization status"
```

## 🔧 Manual Database Management

### **Check Database Status:**
```bash
cd apps/backend
python3 database/setup_db.py --check-only
```

### **Initialize Database Manually:**
```bash
cd apps/backend
python3 database/setup_db.py --verbose
```

### **Force Migration:**
```bash
cd apps/backend
python3 database/setup_db.py --force-migrate --verbose
```

### **Health Check for CI/CD:**
```bash
cd apps/backend
python3 database/setup_db.py --check-only --no-interactive
```

## 📋 Environment Variables

Control the behavior with these environment variables:

```bash
# Enable/disable auto-initialization (default: true)
export AUTO_INIT_DB=true

# Enable/disable deployment database setup (default: true)
export AUTO_SETUP_DATABASE=true

# Database connection (already configured in your setup)
export DB_HOST=postgres
export DB_NAME=keycloak  # or your database name
export DB_USER=postgres
export DB_PASS=your_password
```

## 🎯 Recommended Integration

Here's the **complete integration** I recommend adding to your `generate_deploy.sh`:

### **1. Add after line 183 (in load_env function):**
```bash
# Database setup configuration
export AUTO_SETUP_DATABASE=${AUTO_SETUP_DATABASE:-true}
print_info "Auto database setup: $AUTO_SETUP_DATABASE"
```

### **2. Add this function after line 671 (after configure_keycloak_mappers function):**
```bash
setup_database_after_deployment() {
  if [[ "$AUTO_SETUP_DATABASE" != "true" ]]; then
    print_info "🗄️  Database auto-setup disabled"
    return 0
  fi

  print_title "🗄️  Setting up Database Schema"

  # Check if backend database setup script exists
  if [[ ! -f "apps/backend/database/deploy_init.sh" ]]; then
    print_warning "Database setup script not found, skipping..."
    return 0
  fi

  # Source and run the database initialization
  source "apps/backend/database/deploy_init.sh"

  print_info "Profile: $PROFILE"
  print_info "Stack: $STACK_NAME"

  if setup_database_for_deployment "$PROFILE"; then
    print_success "Database schema setup completed successfully!"
  else
    print_warning "Database schema setup encountered issues"
    print_info "Application will attempt auto-initialization on startup"
  fi
}
```

### **3. Add this call after line 686 (after deploy_stack):**
```bash
deploy_stack

# Add this line:
setup_database_after_deployment

# Configure Keycloak mappers
configure_keycloak_mappers
```

## 🔍 What Happens During Deployment

### **First Deployment (Fresh Database):**
```
🗄️  Setting up Database Schema
==================
🔌 Checking database connectivity...
✅ Database connectivity confirmed
🚀 First run detected - creating initial schema...
✅ Applied initial migration (001_initial_tables)
✅ Created 9 database tables
✅ Database initialization completed successfully
```

### **Subsequent Deployments:**
```
🗄️  Setting up Database Schema
==================
🔌 Checking database connectivity...
✅ Database connectivity confirmed
🔄 Checking for pending migrations...
✅ Database schema is current - no action needed
```

### **When New Columns Are Added:**
```
🗄️  Setting up Database Schema
==================
🔌 Checking database connectivity...
✅ Database connectivity confirmed
🔄 Migration needed - applying pending migrations...
✅ Applied migration: 002_add_status_column
✅ Database schema updated successfully
```

## 🚨 Troubleshooting

### **If Database Setup Fails:**
1. **Check logs** in the deployment output
2. **Verify database connectivity**:
   ```bash
   docker exec mystack_postgres pg_isready -U postgres
   ```
3. **Check backend container**:
   ```bash
   docker exec mystack_backend python3 database/setup_db.py --check-only
   ```
4. **Manual recovery**:
   ```bash
   docker exec mystack_backend python3 database/setup_db.py --verbose
   ```

### **Disable Auto-Setup Temporarily:**
```bash
# In your deployment command or .env file:
export AUTO_SETUP_DATABASE=false
./generate_deploy.sh --profile dev
```

### **Reset Database (Development Only):**
```bash
# Remove the stack, database volumes, and redeploy
docker stack rm mystack
docker volume prune -f
./generate_deploy.sh --profile dev
```

## ✅ Benefits of This Integration

### **For First-Time Setup:**
- ✅ **Automatic schema creation** using proper migrations
- ✅ **No manual database setup** required
- ✅ **Consistent across environments**

### **For Updates:**
- ✅ **Automatic migration application**
- ✅ **Safe column additions** with proper defaults
- ✅ **Rollback capability** through Alembic

### **For Operations:**
- ✅ **Deployment automation** - one command does everything
- ✅ **Error detection** - fails early if database issues
- ✅ **Logging** - clear visibility into what's happening

### **For Development:**
- ✅ **Environment parity** - same process everywhere
- ✅ **No forgotten steps** - automatic initialization
- ✅ **Easy testing** - spin up clean environments quickly

This integration ensures your database is always properly initialized and up-to-date, whether it's the first deployment or the hundredth!