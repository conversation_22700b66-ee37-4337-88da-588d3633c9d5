import express, { Request, Response, NextFunction } from "express";
import cors from "cors";
import { <PERSON><PERSON><PERSON> } from "puppeteer";
import { errorHandler } from './middlewares/error.middleware';
import { ReportsPDFController } from './controllers/ReportsPdf.controller';
import * as logger from './utils/logger';
import { ServerConfig } from './global';

/**
 * Initialize and start the Express server
 * @param browser - Puppeteer browser instance to be used for PDF generation
 * @param config - Server configuration
 * @returns The Express application instance
 */
export function initializeServer(browser: Browser, config: ServerConfig) {
  logger.info('Initializing Express server');

  const app = express();

  app.use(errorHandler);
  app.use(cors({
    origin: "*",
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Connect-Protocol-Version']
  }));
  app.use(express.json({ limit: '50mb' }));
  app.use(express.urlencoded({ limit: '50mb', extended: true }));

  app.get('/health', (req: Request, res: Response) => {
    logger.debug('Health check requested');
    res.status(200).send('OK');
  });

  app.post('/pdf/reports', (req: Request, res: Response, next: NextFunction) => {
    logger.info('PDF generation requested', { 
      contentType: req.headers['content-type'],
      bodySize: JSON.stringify(req.body).length
    });

    if (!browser) {
      const error = new Error('Browser not initialized');
      logger.error('PDF generation failed', error);
      res.status(500).send('Browser not initialized');
      return;
    }

    req.body.browserRef = browser;

    return ReportsPDFController(req, res, next);
  });

  app.listen(config.port, config.host, () => {
    logger.info(`Server running on ${config.host}:${config.port}`);
  });

  return app;
}
