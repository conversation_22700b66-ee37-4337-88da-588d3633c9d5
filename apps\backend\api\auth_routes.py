import asyncio
import hashlib
import logging
import time
from urllib.parse import quote

from fastapi import Request, APIRouter
from fastapi.responses import RedirectResponse

from services.user_service import sync_user
from services.auth_service import exchange_code_for_tokens

from core.config import settings

from exceptions.business_exceptions import MissingAuthCallbackParameterError

logger = logging.getLogger(__name__)

router = APIRouter()



# Retry configuration for cookie setting
MAX_COOKIE_RETRIES = 3
COOKIE_RETRY_DELAY = 0.1  # 100ms initial delay
COOKIE_RETRY_BACKOFF_MULTIPLIER = 2  # Exponential backoff


async def retry_set_cookie(response, cookie_name: str, cookie_value: str, **cookie_kwargs) -> bool:
    """
    Retry setting a cookie with exponential backoff.
    
    Args:
        response: The response object to set the cookie on
        cookie_name: Name of the cookie
        cookie_value: Value of the cookie
        **cookie_kwargs: Additional cookie parameters (httponly, secure, etc.)
    
    Returns:
        bool: True if cookie was set successfully, False otherwise
    """
    delay = COOKIE_RETRY_DELAY
    
    for attempt in range(MAX_COOKIE_RETRIES):
        try:
            response.set_cookie(cookie_name, cookie_value, **cookie_kwargs)
            logger.info(f"[retry_set_cookie] Successfully set {cookie_name} cookie on attempt {attempt + 1}")
            return True
            
        except Exception as e:
            logger.warning(f"[retry_set_cookie] Failed to set {cookie_name} cookie on attempt {attempt + 1}: {str(e)}")
            logger.warning(f"[retry_set_cookie] Cookie details - name: {cookie_name}, value_length: {len(cookie_value) if cookie_value else 0}, kwargs: {cookie_kwargs}")
            
            if attempt < MAX_COOKIE_RETRIES - 1:
                logger.info(f"[retry_set_cookie] Retrying in {delay:.3f} seconds...")
                await asyncio.sleep(delay)
                delay *= COOKIE_RETRY_BACKOFF_MULTIPLIER
            else:
                logger.error(f"[retry_set_cookie] Failed to set {cookie_name} cookie after {MAX_COOKIE_RETRIES} attempts")
                logger.error(f"[retry_set_cookie] Final error - error_type: {type(e).__name__}, error_message: {str(e)}")
                return False
    
    return False


def generate_session_state(request: Request, provider: str) -> str:
    """
    Generate a unique session state based on browser fingerprint and provider
    to prevent cross-browser session interference.
    """
    # Create a fingerprint based on browser characteristics
    user_agent = request.headers.get("user-agent", "")
    accept_language = request.headers.get("accept-language", "")
    client_ip = request.client.host if request.client else ""
    
    # Detect anonymous/incognito mode indicators
    is_anonymous = False
    anonymous_indicators = [
        "incognito", "private", "anonymous", "ephemeral"
    ]
    
    # Check user agent for anonymous indicators
    user_agent_lower = user_agent.lower()
    for indicator in anonymous_indicators:
        if indicator in user_agent_lower:
            is_anonymous = True
            break
    
    # Check for anonymous mode headers (some browsers set these)
    if request.headers.get("sec-fetch-dest") == "empty" or request.headers.get("sec-fetch-mode") == "cors":
        is_anonymous = True
    
    # Create a unique session identifier with anonymous mode consideration
    session_data = f"{provider}:{user_agent}:{accept_language}:{client_ip}:{int(time.time())}:{is_anonymous}"
    session_hash = hashlib.sha256(session_data.encode()).hexdigest()[:16]
    
    logger.info("[generate_session_state] Generated session state: %s for provider: %s (anonymous: %s)", 
                session_hash, provider, is_anonymous)
    return session_hash


def get_provider_specific_params(provider: str) -> dict:
    """
    Get provider-specific parameters to ensure proper session isolation.
    """
    provider_params = {
        "google": {
            "prompt": "select_account",  # Force account selection
            "access_type": "offline",    # Get refresh token
            "include_granted_scopes": "true"
        },
        "microsoft": {
            "prompt": "select_account",  # Force account selection
            "response_mode": "query"     # Ensure proper response handling
        }
    }
    
    return provider_params.get(provider, {"prompt": "select_account"})


def create_provider_isolation_url(request: Request, provider: str, redirect_uri: str) -> str:
    """
    Create a URL that forces provider selection and prevents cross-browser interference.
    """
    # Generate unique session state for browser isolation
    session_state = generate_session_state(request, provider)
    
    # Get provider-specific parameters
    provider_params = get_provider_specific_params(provider)
    
    # Build query parameters for session isolation
    query_params = [
        f"client_id={settings.CLIENT_ID_KEYCLOAK}",
        f"response_type=code",
        f"scope=openid%20profile%20email",
        f"redirect_uri={quote(redirect_uri)}",
        f"state={session_state}",
        f"max_age=0",  # Don't use existing sessions
        f"prompt=select_account",  # Force account selection screen
        f"kc_idp_hint={provider}"  # Suggest provider but don't force it
    ]
    
    # Add provider-specific parameters
    for key, value in provider_params.items():
        if key != "prompt":  # Don't override the main prompt
            query_params.append(f"{key}={value}")
    
    auth_url = (
        f"{settings.KEYCLOAK_URL}/realms/{settings.REALM_NAME}"
        f"/protocol/openid-connect/auth?"
        f"{'&'.join(query_params)}"
    )
    
    logger.info("[create_provider_isolation_url] Generated isolation URL for provider: %s with state: %s", provider, session_state)
    return auth_url


@router.get("/callback")
async def auth_callback(request: Request):
    logger.info("[auth_callback] Handling OAuth callback...")
    logger.info("[auth_callback] Received request: %s", request.query_params)
    body = await request.body()
    logger.info("[auth_callback] Received body: %s", body.decode("utf-8"))
    code = request.query_params.get("code")
    state = request.query_params.get("state")
    logger.info("[auth_callback] Received code: %s", code)
    logger.info("[auth_callback] Received state: %s", state)

    # Handle Keycloak OAuth callbacks
    frontend_redirect = request.query_params.get("frontend")  # From Keycloak callback
    session_id = request.query_params.get("session_id")
    timestamp = request.query_params.get("timestamp")
    
    logger.info("[auth_callback] Redirecting back to: %s", frontend_redirect)
    logger.info("[auth_callback] Frontend session_id: %s, timestamp: %s", session_id, timestamp)

    if not code:
        logger.error("[auth_callback] Missing authorization code in query params.")
        raise MissingAuthCallbackParameterError("code")
    
    if not frontend_redirect:
        logger.error("[auth_callback] Missing frontend redirect URL.")
        raise MissingAuthCallbackParameterError("frontend")

    # All OAuth flows go through Keycloak broker
    logger.info("[auth_callback] Exchanging Keycloak code for tokens...")
    tokens = await exchange_code_for_tokens(code, frontend_redirect)

    access_token = tokens.get("access_token")
    refresh_token = tokens.get("refresh_token")
    request.state.refreshed_tokens = {
        "access_token": access_token,
        "refresh_token": refresh_token,
    }
    if not access_token:
        logger.error("[auth_callback] Failed to obtain access_token from Keycloak.")
        raise MissingAuthCallbackParameterError("access_token")
    if not refresh_token:
        logger.error("[auth_callback] Failed to obtain refresh_token from Keycloak.")
        raise MissingAuthCallbackParameterError("refresh_token")

    logger.info("[auth_callback] Tokens received successfully.")

    logger.info("[auth_callback] Syncing user with access token...")
    await sync_user(access_token=access_token)

    # Store initial tokens in Redis for distributed caching
    logger.info("[auth_callback] Storing initial tokens in Redis...")
    try:
        from services.redis_service import redis_service
        from services.auth_service import _extract_user_id_from_token
        
        # Extract user ID from the access token
        user_id = _extract_user_id_from_token(access_token)
        logger.info("[auth_callback] Extracted user_id: %s for Redis storage", user_id)
        
        # Store tokens in Redis (TTL will be calculated from JWT expiration)
        redis_success = await redis_service.set_cached_tokens(
            user_id=user_id,
            access_token=access_token,
            refresh_token=refresh_token
        )
        
        if redis_success:
            logger.info("[auth_callback] Successfully stored initial tokens in Redis for user: %s", user_id)
        else:
            logger.error("[auth_callback] Failed to store initial tokens in Redis for user: %s", user_id)
            # Don't fail the login, just log the error
            
    except Exception as redis_error:
        logger.error("[auth_callback] Error storing tokens in Redis: %s", str(redis_error))
        # Don't fail the login, just log the error
        # The user can still authenticate, but won't benefit from distributed caching initially

    logger.info("[auth_callback] Preparing redirect to frontend: %s", frontend_redirect)
    redirect_response = RedirectResponse(url=frontend_redirect)

    logger.info("[auth_callback] Setting cookies.")
    # Force cross-site compatible cookies during development
    domain = None
    if "localhost" in settings.FRONTEND_REDIRECT_URL:
        is_secure = True
        same_site = "none"
    elif "189" in settings.FRONTEND_REDIRECT_URL or "reportsbeta" in settings.FRONTEND_REDIRECT_URL:
        is_secure = True
        same_site = "lax"
        domain=".reportsbeta.snapforensics.com"
    else:
        is_secure = False
        same_site = "lax"


    logger.info(f"[auth_callback] Cookie attrs: secure={is_secure} samesite={same_site} domain={domain}")

    # Set access_token cookie with retry
    access_token_success = await retry_set_cookie(
        redirect_response,
        "access_token",
        access_token,
        httponly=True,
        secure=is_secure,
        samesite=same_site,
        path="/",
        domain=domain
    )
    if access_token_success:
        logger.info("[auth_callback] access_token cookie set on redirect response.")
    else:
        logger.error("[auth_callback] Failed to set access_token cookie after all retry attempts")
        logger.error("[auth_callback] Cookie setting failed - access_token_length: %s, secure: %s, same_site: %s", 
                   len(access_token) if access_token else 0, is_secure, same_site)
    
    # Set refresh_token cookie with retry
    refresh_token_success = await retry_set_cookie(
        redirect_response,
        "refresh_token",
        refresh_token,
        httponly=True,
        secure=is_secure,
        samesite=same_site,
        path="/",
        domain=domain
    )
    if refresh_token_success:
        logger.info("[auth_callback] refresh_token cookie set on redirect response.")
    else:
        logger.error("[auth_callback] Failed to set refresh_token cookie after all retry attempts")
        logger.error("[auth_callback] Cookie setting failed - refresh_token_length: %s, secure: %s, same_site: %s", 
                   len(refresh_token) if refresh_token else 0, is_secure, same_site)
    
    # Generate browser-specific session identifier for cookie isolation
    user_agent = request.headers.get("user-agent", "")
    browser_session_id = hashlib.sha256(user_agent.encode()).hexdigest()[:8]
    
    # Detect if this is an anonymous session
    is_anonymous = False
    anonymous_indicators = ["incognito", "private", "anonymous", "ephemeral"]
    user_agent_lower = user_agent.lower()
    for indicator in anonymous_indicators:
        if indicator in user_agent_lower:
            is_anonymous = True
            break
    
    # Set browser session identifier cookie with anonymous flag
    browser_session_success = await retry_set_cookie(
        redirect_response,
        "browser_session_id",
        browser_session_id,
        httponly=False,  # Allow JavaScript access for debugging
        secure=is_secure,
        samesite=same_site,
        path="/",
        max_age=3600,  # 1 hour
        domain=domain
    )
    if browser_session_success:
        logger.info("[auth_callback] browser_session_id cookie set: %s (anonymous: %s)", browser_session_id, is_anonymous)
    else:
        logger.error("[auth_callback] Failed to set browser_session_id cookie after all retry attempts")
        logger.error("[auth_callback] Cookie setting failed - browser_session_id: %s, secure: %s, same_site: %s", 
                   browser_session_id, is_secure, same_site)
    
    # Set anonymous session flag
    if is_anonymous:
        session_type_success = await retry_set_cookie(
            redirect_response,
            "session_type",
            "anonymous",
            httponly=False,
            secure=is_secure,
            samesite=same_site,
            path="/",
            max_age=3600,  # 1 hour
            domain=domain
        )
        if session_type_success:
            logger.info("[auth_callback] Anonymous session detected and flagged")
        else:
            logger.error("[auth_callback] Failed to set session_type cookie after all retry attempts")
            logger.error("[auth_callback] Cookie setting failed - session_type: anonymous, secure: %s, same_site: %s", 
                       is_secure, same_site)
    
    try:
        set_cookie_headers = [
            h for (k, h) in ((k.decode().lower(), v.decode()) for k, v in getattr(redirect_response, "raw_headers", []))
            if k == "set-cookie"
        ]
        logger.info("[auth_callback] Set-Cookie headers on redirect: %s", set_cookie_headers)
    except Exception as e:
        logger.warning("[auth_callback] Could not read redirect raw_headers: %s", e)

    logger.info("[auth_callback] Callback completed successfully.")
    return redirect_response




@router.get("/{provider}")
async def auth_redirect(request: Request, provider: str):
    """
    Initiate OAuth flow by making server-side request to Keycloak to get the actual OAuth provider URL
    """
    logger.info("[auth_redirect] Initiating server-side OAuth initiation for provider: %s", provider)

    logger.info("[auth_redirect] Full request URL: %s", request.url)
    logger.debug("[auth_redirect] Request headers: %s", dict(request.headers))
    logger.info("[auth_redirect] HTTP Method: %s", request.method)
    client_ip = request.client.host
    logger.info("[auth_redirect] Client IP address: %s", client_ip)
    logger.info("[auth_redirect] Query parameters: %s", request.query_params)
    logger.info("[auth_redirect] Request scheme: %s", request.url.scheme)
    logger.info("[auth_redirect] Request hostname: %s", request.url.hostname)
    custom_header = request.headers.get("X-Forwarded-For")
    logger.info("[auth_redirect] X-Forwarded-For: %s", custom_header)
    logger.info("[auth_redirect] Request port: %s", request.url.port)
    logger.info("[auth_redirect] Request path: %s", request.url.path)

    origin = (request.query_params.get("redirect_url") or
              str(request.headers.get("origin")
                  or f"{request.url.scheme}://{request.url.hostname}"))

    # Get frontend session parameters for isolation
    session_id = request.query_params.get("session_id")
    timestamp = request.query_params.get("timestamp")
    
    logger.info("[auth_redirect] Received origin header: %s", origin)
    logger.info("[auth_redirect] Frontend session_id: %s, timestamp: %s", session_id, timestamp)

    # Include frontend session parameters in redirect URI
    frontend_params = f"frontend={quote(origin)}"
    if session_id:
        frontend_params += f"&session_id={session_id}"
    if timestamp:
        frontend_params += f"&timestamp={timestamp}"
    
    redirect_uri = f"{settings.REDIRECT_URI_KEYCLOAK}?{frontend_params}"
    logger.info("[auth_redirect] Received redirect_uri: %s", redirect_uri)

    # Use Keycloak broker for all providers including Google
    # Force HTTPS for external URLs (nginx handles SSL termination)
    backend_base = f"https://{request.url.netloc}"
    if "***************" in backend_base or "localhost" in backend_base:
        redirect_uri = f"{settings.REDIRECT_URI_KEYCLOAK}?{frontend_params}"
        auth_url = (
            f"{settings.KEYCLOAK_URL}/realms/{settings.REALM_NAME}"
            f"/protocol/openid-connect/auth?"
            f"client_id={settings.CLIENT_ID_KEYCLOAK}&"
            f"response_type=code&"
            f"scope=openid%20profile%20email&"
            f"redirect_uri={quote(redirect_uri)}&"
            f"kc_idp_hint={provider}"
        )
    else:
        auth_url = (
            f"{backend_base}/authkc/realms/{settings.REALM_NAME}"
            f"/protocol/openid-connect/auth?"
            f"client_id={settings.CLIENT_ID_KEYCLOAK}&"
            f"response_type=code&"
            f"scope=openid%20profile%20email&"
            f"redirect_uri={quote(redirect_uri)}&"
            f"kc_idp_hint={provider}"
        )

    logger.info("[auth_redirect] Generated proxied auth URL: %s", auth_url)
    return RedirectResponse(url=auth_url)
