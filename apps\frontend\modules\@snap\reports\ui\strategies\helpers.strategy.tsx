import { Separator, Text } from "@snap/design-system";
import { translatePropToLabel, translateSource } from "../../helpers";
import { Info } from "lucide-react";

/**
 * Gets the appropriate label for a field based on its structure
 * @param key The field key
 * @param value The field value (which might be an object with a label property)
 * @returns The appropriate label for the field
 */
export const getFieldLabel = (key: string, value: any): string => {
  // if the value has its own .label, translate _that_; otherwise translate the raw key
  const raw = typeof value === 'object' && value?.label
    ? value.label
    : key;

  return translatePropToLabel(raw);
};

/**
 * Gets the actual value from a field that might be a nested object
 * @param value The field value (which might be an object with a value property)
 * @returns The actual value to display
 */
export const getFieldValue = (value: any): any => {
  return typeof value === 'object' && value?.value !== undefined
    ? value.value
    : value;
};

export const getIconImage = (name: string) => {
  if (!name) return null;

  try {
    return `/assets/logo-${name}.png`;
  } catch (error) {
    return null;
  }
};

export const isValidUrl = (url: string | undefined | null) => {
  if (!url) return false;
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

export const isBase64Image = (str: string) => {
  if (!str) return false;
  try {
    if (str.startsWith('data:image')) {
      return true;
    }

    // Check for raw base64 image data by looking for common image format signatures
    // JPEG: starts with /9j/ or /9k/
    // PNG: starts with iVBORw0KGgo
    // GIF: starts with R0lGODlh or R0lGODdh
    // WebP: starts with UklGR
    if (str.startsWith('/9j/') || str.startsWith('/9k/') ||
      str.startsWith('iVBORw0KGgo') ||
      str.startsWith('R0lGODlh') || str.startsWith('R0lGODdh') ||
      str.startsWith('UklGR')) {
      return true;
    }

    // Fallback: try to validate as base64 (but only for shorter strings to avoid performance issues)
    if (str.length < 10000) {
      return btoa(atob(str)) == str;
    }

    return false;
  } catch (err) {
    return false;
  }
};

export const formatImageSrc = (value: string): string => {
  if (!value) return value;

  // If it's already a valid URL or data URL, return as is
  if (isValidUrl(value) || value.startsWith('data:')) {
    return value;
  }

  // If it's raw base64 data, add the appropriate data URL prefix
  if (isBase64Image(value)) {
    // Detect image type based on base64 signature
    if (value.startsWith('/9j/') || value.startsWith('/9k/')) {
      return `data:image/jpeg;base64,${value}`;
    } else if (value.startsWith('iVBORw0KGgo')) {
      return `data:image/png;base64,${value}`;
    } else if (value.startsWith('R0lGODlh') || value.startsWith('R0lGODdh')) {
      return `data:image/gif;base64,${value}`;
    } else if (value.startsWith('UklGR')) {
      return `data:image/webp;base64,${value}`;
    } else {
      // Default to JPEG if we can't determine the type
      return `data:image/jpeg;base64,${value}`;
    }
  }

  return value;
};

export const renderSourceTooltip = (source: string[]) => {
  if (!source?.length) return undefined;

  const tooltipContent = {
    title: "Origem do dado",
    icon: <Info size={16} className="text-accent" />,
    children: <div>
      <Text variant="label-sm" className="opacity-80">
        ORIGEM DO DADO
      </Text>
      <Separator className="my-2 border-border" />
      {source?.map((src, i) => (
        <Text key={i} className="text-accent">
          {translateSource(src)}
        </Text>
      ))}
    </div>
  }

  return tooltipContent
};
