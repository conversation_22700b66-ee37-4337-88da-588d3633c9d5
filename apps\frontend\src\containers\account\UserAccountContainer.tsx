import AccountUserProfile from "./configuration/AccountUserProfile";
import TabContainerAccount from "./configuration";
import TabSupportContainer from "./support";
import { useLocation } from "react-router";

export interface UserConfigDialogContentProps {
  onEntryTypeChange?: (value: string) => void;
  onInputChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFolderSelect?: () => void;
  description?: string;
  defaultEntryType?: string;
  defaultInputValue?: string;
}

const UserAccountContainer = ({ }: UserConfigDialogContentProps) => {
  const location = useLocation();
  const isSupport = location.pathname === '/conta/suporte';

  return (
    <div className="flex flex-col lg:flex-row gap-8 px-8">
      {isSupport ? <TabSupportContainer /> : <TabContainerAccount />}

      <div className="flex flex-col max-w-sm md:flex-2/3">
        <AccountUserProfile />
      </div>
    </div>
  );
}

export default UserAccountContainer;