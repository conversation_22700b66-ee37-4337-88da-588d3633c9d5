import { ReportDetailState } from "./interface";

export const REPORT_DETAIL_INITIAL_STATE: ReportDetailState = {
  data: {
    sections: [],
    deletedSections: [],
    metadata: null,
    reportType: "",
    profileImage: null,
    selectedCombinedFilter: "combined_data",
    combinedReportSources: [],
  },
  ui: {
    sectionListRecords: [],
    relacoesSectionList: [],
    totalRecords: 0,
    reloadTrigger: 0,
    isActionLoading: false,
  },
  autoSave: {
    timeoutId: null,
    isPending: false,
    hasPendingChanges: false,
    mutation: null,
  },
};