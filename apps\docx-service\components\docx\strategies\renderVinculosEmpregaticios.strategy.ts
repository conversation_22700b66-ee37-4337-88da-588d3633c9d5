import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintVinculosEmpregaticios {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      empresa_pagadora?: {
        value: string;
        label: string;
        source: string[];
        is_deleted: boolean;
      };
      detalhes?: {
        [key: string]: {
          value: string;
          label: string;
          source: string[];
          is_deleted: boolean;
        }
      };
      [key: string]: any;
    }>
  };
}

export const renderVinculosEmpregaticios = ({ section }: RenderPrintVinculosEmpregaticios): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((vinculo) => {
    if (vinculo.empresa_pagadora && !vinculo.empresa_pagadora.is_deleted) {
      children.push(new Paragraph({
        children: [
          new TextRun({ text: `${(vinculo.empresa_pagadora.label || "Empresa Pagadora").toUpperCase()}: `, bold: true, color: "FE473C" }),
          new TextRun(String(vinculo.empresa_pagadora.value)),
          new TextRun({ text: ` | ${vinculo.empresa_pagadora.source?.map(translateSource).join(', ')}`, size: 16, color: "FE473C" }),
        ],
        spacing: { after: 200 }
      }));
    }

    if (vinculo.detalhes) {
      const tableRows = Object.entries(vinculo.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => {
          return new TableRow({
            children: [
              new TableCell({
                children: [new Paragraph(translatePropToLabel(field.label || key).toUpperCase())],
                shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
              }),
              new TableCell({
                children: [new Paragraph(String(field.value))],
                shading: { fill: "F9F9FA", type: ShadingType.CLEAR },
              }),
            ],
          });
        });

      if (tableRows.length > 0) {
        children.push(new Table({
          rows: tableRows,
          width: { size: 100, type: WidthType.PERCENTAGE },
          columnWidths: [3500, 5500],
        }));
      }
    }
  });

  return { children };
};
