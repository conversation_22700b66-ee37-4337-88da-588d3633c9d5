import psycopg2
import logging

from reports_processor.constants import DB_USER, DB_PASS, DB_NAME, DB_HOST, DB_PORT

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def insert_report_execution(spark, bucket, key):
    """
    Inserts a record with bucket and key information into the report_executions table in PostgreSQL using Spark.
    Args:
        spark: The active SparkSession.
        bucket (str): The bucket name.
        key (str): The object key.
    """
    return
    logger.info(f"Preparing to insert report execution: bucket={bucket}, key={key}")
    # Prepare data as DataFrame
    data = [Row(bucket=bucket, key=key)]
    df = spark.createDataFrame(data)
    logger.info(f"DataFrame created with data: {data}")

    # JDBC connection properties (replace with your actual values or use environment variables)
    url = f"jdbc:postgresql://{DB_HOST}:{DB_PORT}/{DB_NAME}"
    properties = {
        "user": DB_USER,
        "password": DB_PASS,
        "driver": "org.postgresql.Driver"
    }
    logger.info(f"JDBC URL: {url}")
    logger.info(f"JDBC properties: user={DB_USER}, driver=org.postgresql.Driver")

    try:
        # Write to PostgreSQL
        df.write.jdbc(url=url, table="report_executions", mode="append", properties=properties)
        logger.info(f"Successfully inserted report execution for bucket={bucket}, key={key}")
    except Exception as e:
        logger.error(f"Failed to insert report execution: {e}")
        raise

def update_report_execution(user_reports_id, updates: dict):
    return
    logger.info(f"Attempting to update report_executions for user_reports_id={user_reports_id} with updates={updates}")
    try:
        conn = psycopg2.connect(
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASS,
            host=DB_HOST,
            port=DB_PORT
        )
        logger.info(f"Connected to PostgreSQL database {DB_NAME} at {DB_HOST}:{DB_PORT} as user {DB_USER}")
        cur = conn.cursor()
        set_clause = ', '.join([f"{k} = %s" for k in updates.keys()])
        values = list(updates.values())
        values.append(user_reports_id)
        sql = f"UPDATE report_executions SET {set_clause} WHERE user_reports_id = %s AND status_tentativa = 'pending'"
        logger.info(f"Executing SQL: {sql} with values {values}")
        cur.execute(sql, values)
        conn.commit()
        logger.info(f"Successfully updated report_executions for user_reports_id={user_reports_id}")
    except Exception as e:
        logger.error(f"Failed to update report_executions: {e}")
        raise
    finally:
        try:
            cur.close()
            conn.close()
            logger.info("Database connection closed.")
        except Exception:
            pass
