import { useState, useEffect } from 'react'
import { isValidBase64Image, formatImageSrc } from '../../helpers'

interface ValidatedImageProps {
  src?: string | null
  alt?: string
  className?: string
  /**
   * While we’re pinging the URL, do you want:
   *  • nothing (default)?
   *  • the broken placeholder?
   *
   * If `false`, we skip placeholder until after we’ve checked.
   */
  showPlaceholderImmediately?: boolean
}

/**
 * Renders an image only if it's valid. Consolidates image validation and rendering.
 * - Returns null if the image is not valid
 * - Shows loading state only if the image is potentially valid
 * - Renders the image with proper fallback handling
 */
export function ValidatedImage({
  src,
  alt = 'Imagem',
  className = '',
  showPlaceholderImmediately = false,
}: ValidatedImageProps) {
  // Format the source to handle base64 without data URI prefix
  const formattedSrc = src ? formatImageSrc(src) : src;

  const [isImage, setIsImage] = useState<boolean | null>(
    Boolean(formattedSrc && isValidBase64Image(formattedSrc))
  )

  useEffect(() => {
    if (isImage === true || !formattedSrc || isValidBase64Image(formattedSrc)) {
      return
    }

    let cancelled = false
    const img = new Image()
    img.onload = () => {
      if (!cancelled) setIsImage(true)
    }
    img.onerror = () => {
      if (!cancelled) setIsImage(false)
    }
    img.src = formattedSrc

    return () => {
      cancelled = true
    }
  }, [formattedSrc])

  // Don't render anything if no source
  if (!formattedSrc) return null

  // Show loading only if we're still validating and showPlaceholderImmediately is true
  if (isImage === null) {
    return showPlaceholderImmediately ? (
      <div className={`${className} flex items-center justify-center bg-neutral-100`}>
        <div className="animate-pulse bg-neutral-300 rounded-full w-12 h-12" />
      </div>
    ) : null
  }

  // Don't render anything if the image is invalid
  if (!isImage) {
    return null
  }

  // Render the valid image (consolidated ImageWithFallback functionality)
  return (
    <div
      className={`
        ${className}
        overflow-hidden
      `}
    >
      <img
        src={formattedSrc}
        alt={alt}
        className="
          w-full h-full
          object-contain
          block
        "
      />
    </div>
  )
}
