{"vinculos": {"socio": "É Sócio na empresa", "parente IRMA(O)": "É irmão(a) de", "vinculo empregaticio": "Possui v<PERSON><PERSON>lo empregat<PERSON>", "outros contatos": "É um possível contato de"}, "vinculos_from_empresa": {"socio": "possui como Sócio", "vinculo empregaticio": "possui vínculo empregatício com", "outros contatos": "tem como possível contato"}, "vinculos_from_pessoa": {"socio": "é Sócio na empresa", "parente IRMA(O)": "é irmão(a) de", "vinculo empregaticio": "possui vínculo empregatício na empresa", "outros contatos": "é um possível contato de"}, "prefixo_semantico": {"1": "Que", "2": "Que por sua vez", "3": "Que", "4": "Que", "5": "Que", "6": "Que", "7": "Que", "8": "Que", "9": "Que", "10": "Que"}}