"""
Migration Validation Utilities

This module provides utilities to validate migrations before applying them
to ensure they follow best practices and won't cause issues.
"""

import re
import os
import ast
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class MigrationValidationResult:
    """Result of migration validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: List[str]
    risk_level: str  # 'low', 'medium', 'high'

class MigrationValidator:
    """Validates Alembic migration files for safety and best practices"""

    # Dangerous operations that need careful review
    HIGH_RISK_OPERATIONS = {
        'drop_table': 'Dropping tables can cause data loss',
        'drop_column': 'Dropping columns can cause data loss',
        'alter_column': 'Altering columns can break existing code',
        'drop_constraint': 'Dropping constraints can affect data integrity',
        'drop_index': 'Dropping indexes can affect performance'
    }

    # Medium risk operations
    MEDIUM_RISK_OPERATIONS = {
        'create_table': 'Creating tables affects schema',
        'add_column': 'Adding columns can affect existing queries',
        'create_foreign_key': 'Foreign keys can affect data insertion',
        'create_unique_constraint': 'Unique constraints can cause insertion failures'
    }

    # Required patterns for reports schema
    REQUIRED_PATTERNS = {
        'schema_specification': r"schema=['\"]reports['\"]",
        'proper_imports': ['from alembic import op', 'import sqlalchemy as sa'],
        'revision_format': r"revision = ['\"][a-f0-9]{8}['\"]"
    }

    def __init__(self):
        self.alembic_versions_dir = Path("alembic/versions")

    def validate_migration_file(self, migration_file: Path) -> MigrationValidationResult:
        """
        Validate a specific migration file.

        Args:
            migration_file: Path to the migration file

        Returns:
            MigrationValidationResult with validation details
        """
        errors = []
        warnings = []
        suggestions = []
        risk_level = 'low'

        try:
            with open(migration_file, 'r') as f:
                content = f.read()

            # Parse the Python file to analyze operations
            try:
                tree = ast.parse(content)
            except SyntaxError as e:
                errors.append(f"Syntax error in migration file: {e}")
                return MigrationValidationResult(False, errors, warnings, suggestions, 'high')

            # Basic validation
            self._validate_basic_structure(content, errors, warnings)

            # Validate operations
            operations = self._extract_operations(content)
            risk_level = self._validate_operations(operations, errors, warnings, suggestions)

            # Validate schema usage
            self._validate_schema_usage(content, errors, warnings)

            # Validate naming conventions
            self._validate_naming_conventions(content, migration_file.name, warnings, suggestions)

            # Check for data migration patterns
            self._check_data_migration_patterns(content, warnings, suggestions)

        except Exception as e:
            errors.append(f"Error validating migration file: {str(e)}")
            risk_level = 'high'

        is_valid = len(errors) == 0
        return MigrationValidationResult(is_valid, errors, warnings, suggestions, risk_level)

    def validate_pending_migrations(self) -> Dict[str, MigrationValidationResult]:
        """
        Validate all migration files in the versions directory.

        Returns:
            Dictionary mapping migration filenames to their validation results
        """
        results = {}

        if not self.alembic_versions_dir.exists():
            logger.error("Alembic versions directory not found")
            return results

        for migration_file in self.alembic_versions_dir.glob("*.py"):
            if migration_file.name == "__init__.py":
                continue

            results[migration_file.name] = self.validate_migration_file(migration_file)

        return results

    def _validate_basic_structure(self, content: str, errors: List[str], warnings: List[str]) -> None:
        """Validate basic migration file structure"""

        # Check for required functions
        if 'def upgrade() -> None:' not in content:
            errors.append("Migration must have an 'upgrade() -> None' function")

        if 'def downgrade() -> None:' not in content:
            warnings.append("Migration should have a 'downgrade() -> None' function for rollback")

        # Check for required imports
        for required_import in self.REQUIRED_PATTERNS['required_imports']:
            if required_import not in content:
                errors.append(f"Missing required import: {required_import}")

        # Check revision format
        if not re.search(self.REQUIRED_PATTERNS['revision_format'], content):
            warnings.append("Revision ID should be 8-character hex string")

    def _extract_operations(self, content: str) -> List[str]:
        """Extract Alembic operations from migration content"""
        operations = []

        # Find all op.* calls
        op_patterns = [
            r'op\.create_table',
            r'op\.drop_table',
            r'op\.add_column',
            r'op\.drop_column',
            r'op\.alter_column',
            r'op\.create_index',
            r'op\.drop_index',
            r'op\.create_foreign_key',
            r'op\.drop_constraint',
            r'op\.create_unique_constraint'
        ]

        for pattern in op_patterns:
            matches = re.findall(pattern, content)
            operations.extend([match.replace('op.', '') for match in matches])

        return operations

    def _validate_operations(self, operations: List[str], errors: List[str],
                           warnings: List[str], suggestions: List[str]) -> str:
        """Validate the safety of operations"""
        risk_level = 'low'

        for operation in operations:
            if operation in self.HIGH_RISK_OPERATIONS:
                warnings.append(f"High-risk operation detected: {operation} - {self.HIGH_RISK_OPERATIONS[operation]}")
                risk_level = 'high'

            elif operation in self.MEDIUM_RISK_OPERATIONS:
                suggestions.append(f"Medium-risk operation: {operation} - {self.MEDIUM_RISK_OPERATIONS[operation]}")
                if risk_level == 'low':
                    risk_level = 'medium'

        return risk_level

    def _validate_schema_usage(self, content: str, errors: List[str], warnings: List[str]) -> None:
        """Validate proper schema usage"""

        # Check for schema='reports' in operations
        if 'op.' in content and not re.search(self.REQUIRED_PATTERNS['schema_specification'], content):
            errors.append("All operations must specify schema='reports'")

        # Check for hardcoded schema references
        if 'reports.' in content and 'schema=' not in content:
            warnings.append("Consider using schema parameter instead of hardcoded 'reports.' prefix")

    def _validate_naming_conventions(self, content: str, filename: str,
                                   warnings: List[str], suggestions: List[str]) -> None:
        """Validate naming conventions"""

        # Check filename format (should be revision_description.py)
        if not re.match(r'^[0-9a-f]{3,8}_[a-z_]+\.py$', filename):
            warnings.append(f"Filename '{filename}' doesn't follow convention (revision_description.py)")

        # Check index naming
        index_matches = re.findall(r"create_index\(['\"]([^'\"]+)['\"]", content)
        for index_name in index_matches:
            if not (index_name.startswith('idx_') or index_name.startswith('ix_')):
                warnings.append(f"Index '{index_name}' doesn't follow naming convention (should start with 'idx_' or 'ix_')")

        # Check constraint naming
        fk_matches = re.findall(r"create_foreign_key\(['\"]([^'\"]+)['\"]", content)
        for fk_name in fk_matches:
            if not fk_name.startswith('fk_'):
                suggestions.append(f"Foreign key '{fk_name}' should start with 'fk_' prefix")

    def _check_data_migration_patterns(self, content: str, warnings: List[str], suggestions: List[str]) -> None:
        """Check for data migration patterns and best practices"""

        # Check for raw SQL execution
        if 'op.execute(' in content:
            warnings.append("Raw SQL execution detected - ensure it's database-agnostic and safe")

        # Check for data modifications
        if any(keyword in content.upper() for keyword in ['UPDATE', 'DELETE', 'INSERT']):
            warnings.append("Data modification detected - ensure this is intentional and safe")

        # Check for non-nullable column additions without defaults
        if 'add_column' in content and 'nullable=False' in content and 'server_default=' not in content:
            warnings.append("Adding non-nullable column without default - may fail on tables with existing data")

    def generate_validation_report(self, results: Dict[str, MigrationValidationResult]) -> str:
        """
        Generate a comprehensive validation report.

        Args:
            results: Migration validation results

        Returns:
            Formatted validation report
        """
        report = "# Migration Validation Report\n\n"

        if not results:
            report += "❌ No migration files found to validate.\n"
            return report

        # Summary statistics
        valid_count = sum(1 for result in results.values() if result.is_valid)
        high_risk_count = sum(1 for result in results.values() if result.risk_level == 'high')
        medium_risk_count = sum(1 for result in results.values() if result.risk_level == 'medium')

        report += f"**Migration Summary:**\n"
        report += f"- Total migrations: {len(results)}\n"
        report += f"- ✅ Valid migrations: {valid_count}\n"
        report += f"- ❌ Invalid migrations: {len(results) - valid_count}\n"
        report += f"- 🔴 High risk: {high_risk_count}\n"
        report += f"- 🟡 Medium risk: {medium_risk_count}\n"
        report += f"- 🟢 Low risk: {len(results) - high_risk_count - medium_risk_count}\n\n"

        # Individual migration details
        for filename, result in results.items():
            risk_emoji = {'low': '🟢', 'medium': '🟡', 'high': '🔴'}[result.risk_level]
            status_emoji = "✅" if result.is_valid else "❌"

            report += f"## {filename} {status_emoji} {risk_emoji}\n\n"

            if result.errors:
                report += "### ❌ Errors:\n"
                for error in result.errors:
                    report += f"- {error}\n"
                report += "\n"

            if result.warnings:
                report += "### ⚠️ Warnings:\n"
                for warning in result.warnings:
                    report += f"- {warning}\n"
                report += "\n"

            if result.suggestions:
                report += "### 💡 Suggestions:\n"
                for suggestion in result.suggestions:
                    report += f"- {suggestion}\n"
                report += "\n"

        return report

    def check_migration_safety(self, migration_file: Path) -> Tuple[bool, List[str]]:
        """
        Quick safety check for a migration before applying.

        Args:
            migration_file: Path to migration file

        Returns:
            Tuple of (is_safe, safety_warnings)
        """
        result = self.validate_migration_file(migration_file)

        is_safe = result.is_valid and result.risk_level != 'high'
        safety_warnings = result.errors + result.warnings

        return is_safe, safety_warnings

def validate_migration_before_apply(migration_path: str) -> bool:
    """
    Utility function to validate a migration before applying it.

    Args:
        migration_path: Path to migration file

    Returns:
        True if safe to apply, False otherwise
    """
    validator = MigrationValidator()
    migration_file = Path(migration_path)

    if not migration_file.exists():
        logger.error(f"Migration file not found: {migration_path}")
        return False

    is_safe, warnings = validator.check_migration_safety(migration_file)

    if not is_safe:
        logger.error(f"Migration {migration_file.name} failed safety check:")
        for warning in warnings:
            logger.error(f"  - {warning}")

    return is_safe

def get_migration_status() -> Dict[str, Any]:
    """
    Get current migration status and pending validations.

    Returns:
        Dictionary with migration status information
    """
    validator = MigrationValidator()

    # Check if alembic is set up
    if not Path("alembic.ini").exists():
        return {"error": "Alembic not configured"}

    # Validate all migrations
    results = validator.validate_pending_migrations()

    # Get current revision (would need alembic command)
    # This is a placeholder - in practice you'd run: alembic current
    current_revision = "unknown"

    return {
        "current_revision": current_revision,
        "validation_results": results,
        "total_migrations": len(results),
        "valid_migrations": sum(1 for r in results.values() if r.is_valid),
        "high_risk_migrations": sum(1 for r in results.values() if r.risk_level == 'high')
    }

# CLI interface for migration validation
def main():
    """CLI interface for migration validation"""
    import argparse

    parser = argparse.ArgumentParser(description="Validate Alembic migrations")
    parser.add_argument("--file", help="Validate specific migration file")
    parser.add_argument("--all", action="store_true", help="Validate all migrations")
    parser.add_argument("--report", action="store_true", help="Generate detailed report")

    args = parser.parse_args()

    validator = MigrationValidator()

    if args.file:
        # Validate specific file
        result = validator.validate_migration_file(Path(args.file))
        if result.is_valid:
            print(f"✅ {args.file} is valid (Risk: {result.risk_level})")
        else:
            print(f"❌ {args.file} has errors:")
            for error in result.errors:
                print(f"  - {error}")
    else:
        # Validate all migrations
        results = validator.validate_pending_migrations()

        if args.report:
            print(validator.generate_validation_report(results))
        else:
            for filename, result in results.items():
                status = "✅" if result.is_valid else "❌"
                print(f"{status} {filename} (Risk: {result.risk_level})")

if __name__ == "__main__":
    main()