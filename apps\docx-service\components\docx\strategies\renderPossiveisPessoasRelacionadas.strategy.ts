import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, getSingular, translateSource, getPlural } from "../../../helpers";
import { createSectionTitle } from "./utils";

// Simplified interface, you might need to adjust based on the actual data structure
interface RenderPrintPossiveisPessoasRelacionadasProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      nome_completo?: ValueWithSource;
      [key: string]: any;
    }>;
  };
}

export const renderPossiveisPessoasRelacionadas = ({ section }: RenderPrintPossiveisPessoasRelacionadasProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((pessoa) => {
    if (pessoa.nome_completo && !pessoa.nome_completo.is_deleted) {
      children.push(new Paragraph({ children: [new TextRun({ text: `${(pessoa.nome_completo.label || "Nome Completo").toUpperCase()}: ${pessoa.nome_completo.value}`, bold: true, color: "FE473C" })] }));
    }

    Object.entries(pessoa).forEach(([key, value]) => {
        if (key === 'nome_completo' || key === 'is_deleted') return;

        if (Array.isArray(value) && value.length > 0) {
            children.push(new Paragraph({text: getPlural(translatePropToLabel(key)).toUpperCase(), style: "subtitle"}));
            value.filter(item => !item.is_deleted).forEach((item, index) => {
                children.push(new Paragraph({text: `${getSingular(translatePropToLabel(item.label || key)).toUpperCase()} ${index + 1}`}));
                const itemRows = Object.entries(item.value as Record<string, ValueWithSource>)
                    .filter(([_, f]) => !f.is_deleted)
                    .map(([fieldKey, field]) => new TableRow({children: [new TableCell({children: [new Paragraph(translatePropToLabel(field.label || fieldKey))]}), new TableCell({children: [new Paragraph(String(field.value))]})]}))
                if(itemRows.length > 0) {
                    children.push(new Table({rows: itemRows, width: {size: 100, type: WidthType.PERCENTAGE}}));
                }
            });
        } else if (typeof value === 'object' && value !== null) {
            // Handle details, redes_sociais etc.
        }
    });

  });

  return { children };
};
