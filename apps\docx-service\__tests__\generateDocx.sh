
#!/bin/bash

# Refactored DOCX generation test using input-paylod.json

echo "Testing DOCX Generation Endpoint..."
echo "=================================="

# Set the endpoint URL
ENDPOINT="http://127.0.0.1:3008/docx/reports"
OUTPUT="report.docx"
PAYLOAD_PATH="input-paylod.json"
CONTENT_TYPE="application/json"

# Check if payload file exists
if [ ! -f "$PAYLOAD_PATH" ]; then
    echo "Error: Test payload file not found: $PAYLOAD_PATH"
    exit 1
fi

# Start timing for parsing input
PARSE_START=$(date +%s.%N)
PAYLOAD=$(cat "$PAYLOAD_PATH")
PARSE_END=$(date +%s.%N)
PARSE_TIME=$(echo "$PARSE_END - $PARSE_START" | bc)
echo "Parse input time: ${PARSE_TIME}s"

# Start timing for overall process
TOTAL_START=$(date +%s.%N)

# Search and delete the output file if exists
if [ -f "$OUTPUT" ]; then
    rm "$OUTPUT"
fi

# Make the request and save the response (includes DOCX creation time)
DOCX_CREATE_START=$(date +%s.%N)
RESPONSE=$(curl -X POST \
    -H "Content-Type: $CONTENT_TYPE" \
    -d @"$PAYLOAD_PATH" \
    "$ENDPOINT" \
    --output "$OUTPUT" \
    --write-out "HTTP Status: %{http_code}\nSize: %{size_download} bytes\n")
DOCX_CREATE_END=$(date +%s.%N)
DOCX_CREATE_TIME=$(echo "$DOCX_CREATE_END - $DOCX_CREATE_START" | bc)

# Stop timing for overall process
TOTAL_END=$(date +%s.%N)
TOTAL_TIME=$(echo "$TOTAL_END - $TOTAL_START" | bc)

echo "$RESPONSE"
echo "Parse input time: ${PARSE_TIME}s"
echo "Create DOCX time: ${DOCX_CREATE_TIME}s"
echo "Total time: ${TOTAL_TIME}s"

# Check if DOCX was created
if [ -f "$OUTPUT" ]; then
        echo ""
        echo "Success! DOCX generated and saved as $OUTPUT"
        echo "File size: $(ls -lh "$OUTPUT" | awk '{print $5}')"
else
        echo ""
        echo "Error: DOCX file was not created"
        exit 1
fi

echo ""
echo "Test completed!"