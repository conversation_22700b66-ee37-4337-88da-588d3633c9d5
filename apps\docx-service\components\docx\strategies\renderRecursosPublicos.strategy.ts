import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintRecursosPublicosProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      orgao?: ValueWithSource;
      detalhes?: Record<string, ValueWithSource>;
      [key: string]: any;
    }>
  };
}

export const renderRecursosPublicos = ({ section }: RenderPrintRecursosPublicosProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((recurso) => {
    if (recurso.orgao && !recurso.orgao.is_deleted) {
      children.push(new Paragraph({
        children: [
          new TextRun({ text: `${(recurso.orgao.label || "Nome do Órgão").toUpperCase()}: `, bold: true, color: "FE473C" }),
          new TextRun(String(recurso.orgao.value)),
          new TextRun({ text: ` | ${recurso.orgao.source?.map(translateSource).join(', ')}`, size: 16, color: "FE473C" }),
        ],
        spacing: { after: 200 }
      }));
    }

    if (recurso.detalhes) {
      const tableRows = Object.entries(recurso.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => {
          return new TableRow({
            children: [
              new TableCell({ children: [new Paragraph(translatePropToLabel(field.label || key).toUpperCase())] }),
              new TableCell({ children: [new Paragraph(String(field.value))] }),
            ],
          });
        });
      if (tableRows.length > 0) {
        children.push(new Table({ rows: tableRows, width: { size: 100, type: WidthType.PERCENTAGE } }));
      }
    }
  });

  return { children };
};
