import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection, ValueWithSource } from "../../../global";
import { translatePropToLabel, translateSource } from "../../../helpers";
import { createSectionTitle } from "./utils";

interface RenderPrintJuntasComerciaisProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      razao_social?: ValueWithSource;
      detalhes?: Record<string, ValueWithSource>;
      [key: string]: any;
    }>;
  };
}

export const renderJuntasComerciais = ({ section }: RenderPrintJuntasComerciaisProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];

  if (!section.data?.length) {
    return { children };
  }

  section.data.forEach((juntaComercial) => {
    if (juntaComercial.razao_social && !juntaComercial.razao_social.is_deleted) {
      children.push(new Paragraph({
        children: [
          new TextRun({ text: `${(juntaComercial.razao_social.label || "Razão Social").toUpperCase()}: `, bold: true, color: "FE473C" }),
          new TextRun(String(juntaComercial.razao_social.value)),
        ],
        spacing: { after: 200 }
      }));
    }

    if (juntaComercial.detalhes) {
      const tableRows = Object.entries(juntaComercial.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => {
          return new TableRow({
            children: [
              new TableCell({ children: [new Paragraph(translatePropToLabel(field.label || key).toUpperCase())] }),
              new TableCell({ children: [new Paragraph(String(field.value))] }),
            ],
          });
        });
      if (tableRows.length > 0) {
        children.push(new Table({ rows: tableRows, width: { size: 100, type: WidthType.PERCENTAGE } }));
      }
    }
  });

  return { children };
};
