import React, { useMemo, useEffect, useRef } from 'react';
import { useStrategyMap } from '../strategies/ReportStrategyFactory';
import { useVisibleReportSections, useReportMode, useIsSaving, useIsTrashEnabled, useReportType, useCombinadoFilter, useReportActions } from '../../context/ReportContext';
import { useSectionScrollToTop } from '../../hooks/useSectionScrollToTop';
import { useAccordionState } from '../../hooks/useAccordionState';
import { filterValidSections } from '../../helpers';
import ReportAccordionItem from './ReportAccordionItem';
import CombinedReportFilter from './CombinedReportFilter';
import { Text } from '@snap/design-system';
import { REPORT_CONSTANTS } from '../../config/constants';

const NEW_ReportDetailComponent: React.FC = () => {
  const mode = useReportMode();
  const isTrashEnabled = useIsTrashEnabled();
  const isTrashMode = mode === 'trash';
  const sections = useVisibleReportSections();
  const newMap = useStrategyMap();
  const isSaving = useIsSaving();
  const reportType = useReportType();
  const isCombinado = reportType === REPORT_CONSTANTS.types.combinado;
  const { selectedFilterId, reportSources } = useCombinadoFilter();
  const actions = useReportActions();
  const { visibleButtons, sectionRefs, handleScrollToTop } = useSectionScrollToTop(
    isTrashMode ? 'report-content-trash' : 'report-content'
  );
  const { toggleSection, isSectionOpen } = useAccordionState();
  const triggerClassName = "bg-accordion-header px-0 py-0 mb-4 data-[state=open]:mb-0 no-underline [&_div:first-child]:border-neutral-100 cursor-pointer";

  const validSections = useMemo(() => {
    return filterValidSections(sections, newMap);
  }, [sections, newMap]);

  const openedFirstRef = useRef(false);
  useEffect(() => {
    if (openedFirstRef.current || isTrashMode) return;
    if (validSections.length > 0) {
      const firstId = 'section-0'; // iniciar com primeiro accordion aberto
      if (!isSectionOpen(firstId)) {
        toggleSection(firstId);
      }
      openedFirstRef.current = true;
    }
  }, [validSections, isSectionOpen, toggleSection]);

  const renderAccordionItem = useMemo(() => {
    return (section: typeof validSections[0], idx: number) => {
      const strategy = newMap[section.title];
      const sectionId = `section-${idx}`;
      const isOpen = isSectionOpen(sectionId);

      return (
        <ReportAccordionItem
          key={`${section.title}-${idx}`}
          section={section}
          idx={idx}
          isTrashMode={isTrashMode}
          isTrashEnabled={isTrashEnabled}
          isSaving={isSaving}
          strategy={strategy}
          isOpen={isOpen}
          onToggle={toggleSection}
          sectionRefs={sectionRefs}
          visibleButtons={visibleButtons}
          onScrollToTop={handleScrollToTop}
          triggerClassName={triggerClassName}
        />
      );
    };
  }, [
    newMap,
    isTrashMode,
    isTrashEnabled,
    isSaving,
    isSectionOpen,
    toggleSection,
    sectionRefs,
    visibleButtons,
    handleScrollToTop,
    triggerClassName
  ]);

  const handleFilterChange = (filterId: string) => {
    actions.setSelectedCombinedFilter?.(filterId);
  };

  const renderSections = useMemo(() => {
    return (
      <div className="flex flex-col">
        {isCombinado && (
          <div
            className="flex flex-wrap lg:flex-nowrap items-center gap-4 md:gap-8 w-full mb-4"
          >
            <div className="w-full lg:w-1/2 max-w-[500px]">
              <CombinedReportFilter
                reportSourcesUsed={reportSources}
                selectedFilterId={selectedFilterId}
                onFilterChange={handleFilterChange}
                className="mb-0"
              />
            </div>
            <div className="w-full lg:w-1/2 flex items-center">
              <Text variant="body-md" className="w-full text-foreground/60">Use o menu ao lado para filtrar os dados por relatório ou para ver todos juntos.</Text>
            </div>
          </div>
        )}
        {validSections.map((section, index) => renderAccordionItem(section, index))}
      </div>
    );
  }, [
    reportType,
    reportSources,
    selectedFilterId,
    validSections,
    renderAccordionItem
  ]);

  return renderSections;
};

export default NEW_ReportDetailComponent;