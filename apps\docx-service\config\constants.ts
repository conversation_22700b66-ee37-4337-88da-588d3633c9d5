export const REPORT_CONSTANTS = {
  new_report: {
    "report_name": "report_name",
    "report_omitted_nodes": "omitted_notes",
    "report_type": "report_type",
    "report_status": "report_status",
    "report_search_args": "report_search_args",
    "subject_name": "subject_name",
    "subject_mother_name": "subject_mother_name",
    "subject_age": "subject_age",
    "subject_sex": "subject_sex",
    "creation_at": "created_at",
    "modified_at": "modified_at",
    "data": "data",
    "report_id": "user_reports_id",
    "status_report": "status_report",
    "report_input_encrypted": "report_input_encrypted",
    "snap_request_id": "snap_request_id",
    "hmac": "hmac",
    "ngrams": "n-grams",
    "organization_id": "organization_id",
    "user_id": "user_id",
    "subject_person_count": "subject_person_count",
    "subject_company_count": "subject_company_count"
  },
  multiplos_registros_encontrados: "Multiplos registros encontrados",
  types: {
    cpf: "cpf",
    cnpj: "cnpj",
    telefone: "telefone",
    email: "email",
    relacoes: "relacoes"
  }
};

export const REPORT_SECTIONS = {
  dados_pessoais: "Dados Pessoais",
  mandados_de_prisao: "Mandados de Prisão",
  telefones: "Telefones",
  emails: "Emails",
  enderecos: "Endereços",
  parentes: "Parentes",
  sociedades: "Sociedades",
  vinculos_empregaticios: "Vínculos Empregatícios",
  processos: "Processos",
  recursos_publicos_recebidos: "Recursos Públicos Recebidos",
  servico_publico: "Serviço Público",
  diarios_oficiais_cpf: "Diários Oficiais - CPF",
  diarios_oficiais_nome: "Diários Oficiais - Nome",
  filiacao_partidaria: "Filiação Partidária",
  possiveis_contatos: "Possíveis Contatos",
  possiveis_contas_em_sites: "Possíveis Contas em Sites",
  doacoes_enviadas_campanha: "Doações Enviadas Campanha",
  doacoes_recebidas_campanha: "Doações Recebidas Campanha",
  fornecimentos_enviados_campanha: "Fornecimentos Enviados Campanha",
  fornecimentos_recebidos_campanha: "Fornecimentos Recebidos Campanha",
  dados_empresa: "Dados Empresa",
  socios: "Sócios",
  juntas_comerciais: "Juntas Comerciais",
  diarios_oficiais_cnpj: "Diários Oficiais - CNPJ",
  contatos_salvos: "Contatos Salvos",
  imagens: "Imagens",
  empresas_relacionadas: "Empresas Relacionadas",
  nomes_usuario: "Nomes de Usuário",
  outras_urls: "Outras URLs",
  perfis_redes_sociais: "Perfis Redes Sociais",
  possiveis_pessoas_relacionadas: "Possíveis Pessoas Relacionadas",
  vinculos_educacionais: "Vínculos Educacionais"
};

export const TRANSLATED_LABELS: Record<string, string> = {
  "full name": "Nome Completo",
  "nome_da_mae": "Nome da Mãe",
  "idade": "Idade",
  "sexo": "Sexo",
  "nacionalidade": "Nacionalidade",
  "pais do passaporte": "País do Passaporte",
  "data nascimento": "Data de Nascimento",
  "cpf": "CPF",
  "identidade": "Identidade",
  "data de admissao": "Data de Admissão",
  "escolaridade": "Escolaridade",
  "info restricao": "Informação de Restrição",
  "pis/pasep": "PIS/PASEP",
  "procon": "Procon",
  "renda estimada": "Renda Estimada",
  "status receita": "Status na Receita",
  "titulo de eleitor": "Título de Eleitor",
  "signo": "Sígno",
  "instancia": "Instância",
  "movimentacoes": "Movimentações",
  "numero do processo": "Número do Processo",
  "orgao": "Órgão",
  "data da remessa": "Data da Remessa",
  "data da instauracao": "Data da Instauração",
  "advogado": "Advogado",
  "pessoa": "Pessoa",
  "empresa": "Empresa",
  "razao social": "Razão Social",
  "city": "Cidade",
  "bairro": "Bairro",
  "cep ou zipcode": "CEP",
  "logradouro": "Logradouro",
  "complemento": "Complemento",
  "numero": "Número",
  "uf": "UF",
  "pais": "País",
  "estado ou regiao": "Estado ou Região",
  "email": "Email",
  "telefone": "Telefone",
  "area code": "Código de Área",
  "country code": "Código do País",
  "phone number": "Telefone",
  "phonenumber": "Telefone",
  "provedor de aplicacoes de internet": "Provedor de Aplicações de Internet",
  "candidato": "Candidato",
  "alcunha": "Alcunha",
  "naturalidade": "Naturalidade",
  "nome mae": "Nome da Mãe",
  "nome pai": "Nome do Pai",
  "pena imposta": "Pena Imposta",
  "recaptura": "Recaptura",
  "source": "Fonte",
  "area": "Área",
  "doacoes": "Doações",
  "entradasociedade": "Data de Entrada na Sociedade",
  "data de inicio em sociedade": "Data de Início em Sociedade",
  "identificador socio": "Identificador do Sócio",
  "qualificação societária": "Cargo em Sociedade",
  "faixa etaria": "Faixa Etária",
  "cargo em sociedade": "Participação Sociedade",
  "cnpj": "CNPJ",
  "data de fundacao": "Data de Fundação",
  "data_abertura": "Data de Abertura",
  "sequencial": "Sequencial",
  "porte": "Porte",
  "tipo de imposto": "Tipo de Imposto",
  "total de funcionarios": "Total de Funcionários",
  "quantidade de funcionarios acima de 5 salarios": "Funcionários acima de 5 salários",
  "quantidade de funcionarios abaixo de 5 salarios": "Funcionários abaixo de 5 salários",
  "email address": "Email",
  "telefone relacionado": "Telefone Relacionado",
  "local": "Local",
  "dados adicionais": "Dados Adicionais",
  "link": "Link",
  "descricao": "Descrição",
  "texto correspondente": "Descrição",
  "edicao extra?": "Edição Extra?",
  "ocorrencia": "Texto",
  "ano": "Ano",
  "cargo eleitoral": "Cargo Eleitoral",
  "unidade eleitoral": "Unidade Eleitoral",
  "número do candidato": "Número",
  "partido eleitoral": "Partido",
  "cnae": "CNAE",
  "site": "Site",
  "entrada_cpf": "Entrada CPF",
  "found": "Conta encontrada?",
  "alerta": "Alvo alertado?",
  "tipo_alerta": "Tipo de Alerta",
  "mae": "Mãe",
  "pai": "Pai",
  "parentesco": "Parentesco",
  "nome1": "Nome",
  "carreira": "Função",
  "instituicao": "Instituição",
  "quadro funcional": "Quadro Funcional",
  "municipio": "Cidade / PR",
  "servidor": "Nome do Servidor",
  "empresa_pagadora": "Empresa Pagadora",
  "situacao": "Situação",
  "cargo": "Cargo",
  "data demissao": "Data de Demissão",
  "tipo de mandado": "Tipo de Mandado",
  "tipificacoes penais": "Tipificações Penais",
  "data de expedicao": "Data de Expedição",
  "data de validade": "Data de Validade",
  "especie de prisao": "Espécie de Prisão",
  "orgao expedidor": "Órgão Expedidor",
  "magistrado": "Magistrado",
  "nome do orgao": "Nome do Orgão",
  "codigo do orgao": "Código do Orgão",
  "pagamento execicio anterior": "Pagamento Execício Anterior",
  "numero de ordem bancaria": "Nº Ordem Bancária",
  "numero da nota de lancamento": "Nº Nota de Lançamento",
  "numero do empenho": "Nº Empenho",
  "fonte do recurso": "Fonte do Recurso",
  "classificacao": "Classificação",
  "credor": "Credor",
  "unidade gestora": "Unidade Gestora",
  "status pagamento": "Status Pagamento",
  "pagamento": "Pagamento",
  "historico": "Histórico",
  "favorecido": "Favorecido",
  "acrescimo": "Acréscimo (R$)",
  "anulado": "Anulado (R$)",
  "pago": "Valor Pago (R$)",
  "liquidado": "Valor Liquidado (R$)",
  "valor pago": "Valor Pago (R$)",
  "valor empenhado": "Valor Empenhado (R$)",
  "valor liquidado": "Valor Liquidado (R$)",
  "tipo de despesa": "Tipo de Despesa",
  "id": "ID",
  "esfera": "Esfera",
  "programa de trabalho": "Programa de Trabalho",
  "funcao": "Função",
  "subfuncao": "Sub-Função",
  "programa": "Programa",
  "categoria economica": "Categoria Econômica",
  "grupo de natureza da despesa": "Grupo de Despesas",
  "modalidade de aplicacao": "Modalidade de Aplicação",
  "elemento": "Elemento",
  "subelemento": "Sub-Elemento",
  "empenhado": "Valor Empenhado (R$)",
  "pago ex": "Pago Ex (R$)",
  "pago rpp": "Pago RPP (R$)",
  "pago rpnp": "Pago RPNP (R$)",
  "pago ret": "Pago RET (R$)",
  "nire": "NIRE",
  "data": "Data",
  "documento": "Documento",
  "recibo": "Recibo",
  "especie": "Espécie",
  "natureza": "Natureza",
  "natureza estimavel": "Natureza Estimável",
  "origem": "Origem",
  "fonte": "Fonte",
  "tipo": "Tipo",
  "renda presumida": "Renda Presumida",
  "ctps": "CTPS",
  "grauinstrucao": "Grau de Instrução",
  "descricao cbo": "Descrição CBO",
  "cbo": "CBO",
  "estadocivil": "Estado Civil",
  "rotulo": "Vínculo",
  "formacao educacional": "Formação Educacional",
  "data inicio": "Data de Início",
  "data termino": "Data de Término",
  "alias": "Nome de Usuário",
  "nome_completo": "Nome Completo",
  "razao_social": "Razão Social",
  "nome_fantasia": "Nome Fantasia",
  "nome": "Nome",
  "data admissao": "Data de Admissão",
  "carga horaria": "Carga Horária",
  "remuneracao": "Remuneração",
  "nome do servidor": "Nome do Servidor",
  "periodo": "Período",
  "valor": "Valor",
  "bonificacao de ferias": "Bonificação de Férias (R$)",
  "remuneracao liquida": "Remuneração Líquida (R$)",
  "valor bruto": "Valor Bruto (R$)",
  "matricula": "Matrícula",
  "reu": "Réu",
  "correu": "Corréu",
  "juiz": "Juíz"
};

export const SOURCE_LABELS: Record<string, string> = {
  "EscavadorDOCNPJ": "Escavador",
  "EscavadorDONome": "Escavador",
  "QueridoDiarioNome": "Querido Diário",
  "EscavadorDOCPF": "Escavador",
  "QueridoDiarioCPF": "Querido Diário",
  "JUCESPCNPJ": "Jucesp",
  "CadastroNacionalPJ": "Cadastro Nacional",
  "Tribunal Superior Eleitoral Fornecimentos": "TSE",
  "TSEFiliacaoPartidaria": "TSE",
  "TSEDoacoes": "TSE",
  "TSEFornecimento": "TSE",
  "SNAP": "SNAP",
  "SintegraMA": "Sintegra",
  "SintegraMTCPF": "Sintegra",
  "SintegraPBCPF": "Sintegra",
  "SintegraSECPF": "Sintegra",
  "SintegraPRCPF": "Sintegra",
  "SintegraMGCPF": "Sintegra",
  "IRBIS": "IRBIS",
  "IRBISLuna": "IRBIS",
  "TransparenciaPRNome": "Transparência PR",
  "TransparenciaPRCPF": "Transparência PR",
  "TransparenciaPRCNPJ": "Transparência PR",
  "TransparenciaManausCPF": "Transparência Manaus",
  "TransparenciaManausCNPJ": "Transparência Manaus",
  "PortalDaTransparenciaDeMinasGerais": "Transparência MG",
  "PortalDaTransparenciaDeMinasGeraisCNPJ": "Transparência MG",
  "PortalDaTransparenciaDoAmazonas": "Transparência Amazonas",
  "PortalDaTransparenciaDoAmazonasCNPJ": "Transparência Amazonas",
  "TransparenciaSC": "Transparência SC",
  "TransparenciaSCCNPJ": "Transparência SC",
  "TransparenciaDF": "Transparência DF",
  "TransparenciaDFCNPJ": "Transparência DF",
  "ProvedorDeAplicacacaoDaInternetcpf": "Provedor de Aplicação da Internet",
  "ProvedorDeAplicacacaoDaInternettelefone": "Provedor de Aplicação da Internet",
  "PAIscpf": "Provedor de Aplicação da Internet",
  "BancoNacionalDeMonitoramentoDePrisoes": "BNMP",
  "PortalDaTransparenciaDeMinasGeraisCPF": "Transparência MG",
  "PortalDaTransparenciaDoAmazonasCPF": "Transparência Amazonas",
  "Portal da Transparência do Distrito Federal": "Transparência DF",
  // Legacy fallbacks
  "api": "API",
  "database": "Base de Dados",
  "external": "Fonte Externa",
  "manual": "Manual",
  "import": "Importação"
};

export const PARSED_VALUES: Record<string, string> = {
  "parente MAE": "Mãe",
  "parente IRMA(O)": "Irma(o)",
  "parente PAI": "Pai",
  "parente TIO(A)": "Tio(a)",
  "parente AVOS": "Avôs",
  "parente FILHO(A)": "Filho(a)",
  "parente CONJUGE": "Cônjuge",
  "parente SOBRINHO(A)": "Sobrinho(a)",
  "F": "Feminino",
  "M": "Masculino",
  "socio administrador": "Sócio Administrador",
  "socio": "Sócio",
  "nire": "NIRE",
  "uf": "UF",
  "family Niece": "Sobrinha",
  "family Nephew": "Sobrinho",
  "family Brother": "Irmão",
  "family Sister": "Irmã",
  "family Son": "Filho",
  "family Daughter": "Filha",
  "family Mother": "Mãe",
  "family Father": "Pai",
  "family Grandfather": "Avô",
  "family Grandmother": "Avó",
  "false": "Não",
  "true": "Sim",
  "Erro: A aplicação bloqueou a requisicao.": "Não"
};
