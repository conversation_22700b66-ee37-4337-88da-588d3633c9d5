import { Client } from "minio";
import * as logger from "../utils/logger";

// MinIO configuration with validation
const MINIO_ENDPOINT = process.env.MINIO_CONTAINER_NAME;
const MINIO_PORT_STR = process.env.MINIO_S3_INTERNAL_PORT;
const MINIO_ACCESS_KEY = process.env.MINIO_ROOT_USER;
const MINIO_SECRET_KEY = process.env.MINIO_ROOT_PASSWORD;

// Validate required environment variables
if (!MINIO_ENDPOINT) {
  throw new Error("MINIO_CONTAINER_NAME environment variable is required");
}
if (!MINIO_PORT_STR) {
  throw new Error("MINIO_S3_INTERNAL_PORT environment variable is required");
}
if (!MINIO_ACCESS_KEY) {
  throw new Error("MINIO_ROOT_USER environment variable is required");
}
if (!MINIO_SECRET_KEY) {
  throw new Error("MINIO_ROOT_PASSWORD environment variable is required");
}

const MINIO_PORT = parseInt(MINIO_PORT_STR);
if (isNaN(MINIO_PORT)) {
  throw new Error("MINIO_S3_INTERNAL_PORT must be a valid number");
}

// Initialize MinIO client
export const minioClient = new Client({
  endPoint: MINIO_ENDPOINT,
  port: MINIO_PORT,
  useSSL: false,
  accessKey: MINIO_ACCESS_KEY,
  secretKey: MINIO_SECRET_KEY,
});

// Storage constants
export const TEMP_DOCX_BUCKET = "temp-docx-data";
export const GENERATED_DOCX_BUCKET = "generated-docx";

// MinIO service functions for DOCX service
export async function loadTempDocxData(
  dataReference: string
): Promise<any | null> {
  logger.info(
    `[loadTempDocxData] Loading temporary DOCX data with reference: ${dataReference}`
  );

  try {
    // Get the object
    const stream = await minioClient.getObject(TEMP_DOCX_BUCKET, dataReference);

    // Convert stream to string
    const chunks: Buffer[] = [];
    for await (const chunk of stream) {
      chunks.push(chunk);
    }
    const dataString = Buffer.concat(chunks).toString("utf-8");

    // Parse JSON
    const dataWithMetadata = JSON.parse(dataString);
    const docxData = dataWithMetadata.data || dataWithMetadata;

    logger.info(`[loadTempDocxData] Temporary DOCX data loaded successfully`);
    return docxData;
  } catch (error: any) {
    if (error.code === "NoSuchKey") {
      logger.warn(
        `[loadTempDocxData] Temporary DOCX data not found: ${dataReference}`
      );
      return null;
    }
    logger.error(
      `[loadTempDocxData] Failed to load temporary DOCX data ${dataReference}: ${error.message}`
    );
    throw error;
  }
}

export async function deleteTempDocxData(dataReference: string): Promise<void> {
  logger.info(
    `[deleteTempDocxData] Deleting temporary DOCX data with reference: ${dataReference}`
  );

  try {
    await minioClient.removeObject(TEMP_DOCX_BUCKET, dataReference);
    logger.info(
      `[deleteTempDocxData] Temporary DOCX data deleted successfully`
    );
  } catch (error: any) {
    if (error.code === "NoSuchKey") {
      logger.warn(
        `[deleteTempDocxData] Temporary DOCX data not found: ${dataReference}`
      );
      return;
    }
    logger.error(
      `[deleteTempDocxData] Failed to delete temporary DOCX data ${dataReference}: ${error.message}`
    );
    // Don't throw error for cleanup operations
  }
}

export async function storeGeneratedDocx(
  docxReference: string,
  docxBuffer: Buffer,
  filename: string
): Promise<string> {
  logger.info(
    `[storeGeneratedDocx] Storing generated DOCX with reference: ${docxReference}`
  );

  try {
    // Ensure bucket exists
    const bucketExists = await minioClient.bucketExists(GENERATED_DOCX_BUCKET);
    if (!bucketExists) {
      logger.info(
        `[storeGeneratedDocx] Creating generated DOCX bucket: ${GENERATED_DOCX_BUCKET}`
      );
      await minioClient.makeBucket(GENERATED_DOCX_BUCKET);
    }

    const metadata = {
      filename: filename,
      "content-type":
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    };

    await minioClient.putObject(
      GENERATED_DOCX_BUCKET,
      docxReference,
      docxBuffer,
      docxBuffer.length,
      {
        "Content-Type":
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ...metadata,
      }
    );

    logger.info(
      `[storeGeneratedDocx] Generated DOCX stored successfully. Size: ${docxBuffer.length} bytes`
    );
    return docxReference;
  } catch (error: any) {
    logger.error(
      `[storeGeneratedDocx] Failed to store generated DOCX: ${error.message}`
    );
    throw error;
  }
}

// Initialize MinIO connection
export async function initializeMinioConnection(): Promise<void> {
  try {
    // Test connection by checking if buckets exist
    const tempBucketExists = await minioClient.bucketExists(TEMP_DOCX_BUCKET);
    const docxBucketExists = await minioClient.bucketExists(
      GENERATED_DOCX_BUCKET
    );

    if (!tempBucketExists) {
      logger.warn(
        `[initializeMinioConnection] Bucket ${TEMP_DOCX_BUCKET} does not exist`
      );
    }
    if (!docxBucketExists) {
      logger.warn(
        `[initializeMinioConnection] Bucket ${GENERATED_DOCX_BUCKET} does not exist`
      );
    }

    if (tempBucketExists || docxBucketExists) {
      logger.info(
        `[initializeMinioConnection] Successfully connected to MinIO`
      );
    }
  } catch (error: any) {
    logger.error(
      `[initializeMinioConnection] Failed to connect to MinIO: ${error.message}`
    );
    throw error;
  }
}
