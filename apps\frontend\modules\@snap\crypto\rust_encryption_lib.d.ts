/* tslint:disable */
/* eslint-disable */
/**
 * Inicializa o panic hook para que os erros de pânico do Rust sejam enviados para o console do navegador.
 * Esta função deve ser chamada uma vez ao inicializar o módulo WebAssembly.
 */
export function init_panic_hook(): void;
/**
 * Deriva uma chave de 32 bytes a partir de uma senha e um sal usando o Argon2id.
 *
 * # Argumentos
 *
 * * `password` - A senha como um slice de bytes.
 * * `salt` - O sal como um slice de bytes. Deve ter no mínimo 8 bytes.
 * * `time_cost` - O número de iterações a serem usadas pelo Argon2.
 * * `memory_cost` - A quantidade de memória (em KiB) a ser usada pelo Argon2.
 * * `parallelism` - O grau de paralelismo a ser usado pelo Argon2.
 *
 * # Retorna
 *
 * Um `Result` contendo um `Vec<u8>` com a chave de 32 bytes em caso de sucesso, ou um `JsValue` com uma mensagem de erro em caso de falha.
 */
export function derive_key(password: Uint8Array, salt: Uint8Array, time_cost: number, memory_cost: number, parallelism: number): Uint8Array;
/**
 * Criptografa dados usando AES-256-GCM.
 *
 * # Argumentos
 *
 * * `key` - A chave de 32 bytes.
 * * `nonce` - O nonce de 12 bytes. Nunca deve ser reutilizado com a mesma chave.
 * * `plaintext` - Os dados a serem criptografados.
 * * `aad` - Dados adicionais autenticados (opcional).
 *
 * # Retorna
 *
 * Um `Result` contendo um `Vec<u8>` com o texto cifrado (incluindo a tag de autenticação) em caso de sucesso, ou um `JsValue` com uma mensagem de erro em caso de falha.
 */
export function aes_gcm_encrypt(key: Uint8Array, nonce: Uint8Array, plaintext: Uint8Array, aad?: Uint8Array | null): Uint8Array;
/**
 * Descriptografa dados usando AES-256-GCM.
 *
 * # Argumentos
 *
 * * `key` - A chave de 32 bytes.
 * * `nonce` - O nonce de 12 bytes.
 * * `ciphertext` - Os dados a serem descriptografados (incluindo a tag de autenticação).
 * * `aad` - Dados adicionais autenticados (opcional).
 *
 * # Retorna
 *
 * Um `Result` contendo um `Vec<u8>` com o texto plano em caso de sucesso, ou um `JsValue` com uma mensagem de erro em caso de falha (incluindo falha na autenticação).
 */
export function aes_gcm_decrypt(key: Uint8Array, nonce: Uint8Array, ciphertext: Uint8Array, aad?: Uint8Array | null): Uint8Array;
/**
 * Calcula um digest HMAC-SHA256.
 *
 * # Argumentos
 *
 * * `key` - A chave para o HMAC.
 * * `message` - A mensagem a ser autenticada.
 *
 * # Retorna
 *
 * Um `Result` contendo um `Vec<u8>` com o digest de 32 bytes em caso de sucesso, ou um `JsValue` com uma mensagem de erro em caso de falha.
 */
export function hmac_sha256(key: Uint8Array, message: Uint8Array): Uint8Array;

export type InitInput = RequestInfo | URL | Response | BufferSource | WebAssembly.Module;

export interface InitOutput {
  readonly memory: WebAssembly.Memory;
  readonly derive_key: (a: number, b: number, c: number, d: number, e: number, f: number, g: number) => [number, number, number, number];
  readonly aes_gcm_encrypt: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number) => [number, number, number, number];
  readonly aes_gcm_decrypt: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number) => [number, number, number, number];
  readonly hmac_sha256: (a: number, b: number, c: number, d: number) => [number, number, number, number];
  readonly init_panic_hook: () => void;
  readonly __wbindgen_free: (a: number, b: number, c: number) => void;
  readonly __wbindgen_malloc: (a: number, b: number) => number;
  readonly __wbindgen_realloc: (a: number, b: number, c: number, d: number) => number;
  readonly __wbindgen_export_3: WebAssembly.Table;
  readonly __externref_table_dealloc: (a: number) => void;
  readonly __wbindgen_start: () => void;
}

export type SyncInitInput = BufferSource | WebAssembly.Module;
/**
* Instantiates the given `module`, which can either be bytes or
* a precompiled `WebAssembly.Module`.
*
* @param {{ module: SyncInitInput }} module - Passing `SyncInitInput` directly is deprecated.
*
* @returns {InitOutput}
*/
export function initSync(module: { module: SyncInitInput } | SyncInitInput): InitOutput;

/**
* If `module_or_path` is {RequestInfo} or {URL}, makes a request and
* for everything else, calls `WebAssembly.instantiate` directly.
*
* @param {{ module_or_path: InitInput | Promise<InitInput> }} module_or_path - Passing `InitInput` directly is deprecated.
*
* @returns {Promise<InitOutput>}
*/
export default function __wbg_init (module_or_path?: { module_or_path: InitInput | Promise<InitInput> } | InitInput | Promise<InitInput>): Promise<InitOutput>;
