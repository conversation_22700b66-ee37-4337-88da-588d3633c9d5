import { create } from "zustand";
import { HierarchicalFolderItem } from "~/types/global";


interface FolderListStoreActions {
  setSelectedFolder: (folderId: string | null) => void;
  clearSelectedFolder: () => void;
  setFolderList: (list: HierarchicalFolderItem[]) => void;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  setTotalItems: (total: number) => void;
}

interface FolderListFilters {
  column_order: string;
  order: string;
  limit: number;
  page: number;
}

interface FolderListStoreState {
  folderList: HierarchicalFolderItem[];
  selectedFolder: string | null;
  filters: FolderListFilters;
  totalItems: number;
  actions: FolderListStoreActions;
}

const defaultFilters: FolderListFilters = {
  column_order: "created_at",
  order: "desc",
  limit: 10,
  page: 1,
};

const useFolderListStore = create<FolderListStoreState>((set) => ({
  folderList: [],
  selectedFolder: null,
  filters: defaultFilters,
  totalItems: 0,
  actions: {
    setSelectedFolder: (folderId: string | null) => set({ selectedFolder: folderId }),
    clearSelectedFolder: () => set({ selectedFolder: null }),
    setFolderList: (list: HierarchicalFolderItem[]) => set({ folderList: list }),
    setPage: (page: number) => set(state => ({ filters: { ...state.filters, page } })),
    setLimit: (limit: number) => set(state => ({ filters: { ...state.filters, limit } })),
    setTotalItems: (total: number) => set({ totalItems: total }),
  },
}));

export const useSelectedFolder = () =>
  useFolderListStore((state) => state.selectedFolder);
export const useFolderListActions = () =>
  useFolderListStore((state) => state.actions);
export const useFolderList = () =>
  useFolderListStore((state) => state.folderList);
export const useFolderListFilters = () =>
  useFolderListStore((state) => state.filters);
export const useFolderListTotalItems = () =>
  useFolderListStore((state) => state.totalItems);
