import React from 'react';
import { Button } from '@snap/design-system';
import { ChevronUp } from 'lucide-react';

interface ScrollToTopButtonProps {
  isVisible: boolean;
  onScrollToTop: () => void;
}

export const ScrollToTopButton: React.FC<ScrollToTopButtonProps> = ({ isVisible, onScrollToTop }) => {
  return (
    <div className="sticky top-12 z-10 h-0 overflow-visible">
      <div className={`bg-accordion-badge transition-opacity duration-300 ${isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'} py-0.5`}>
        <Button 
          variant="link" 
          className="!text-neutral-100 !text-[16px] w-full justify-start pl-1 hover:underline flex items-center gap-1" 
          onClick={onScrollToTop}
        >
          Voltar ao topo
          <ChevronUp size={16} className="text-neutral-100" />
        </Button>
      </div>
    </div>
  );
};