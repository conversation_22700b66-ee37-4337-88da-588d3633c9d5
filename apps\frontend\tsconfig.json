{"compilerOptions": {"outDir": "dist", "strict": true, "strictNullChecks": true, "skipLibCheck": true, "baseUrl": ".", "forceConsistentCasingInFileNames": true, "paths": {"~/*": ["./src/*"], "~constants/*": ["../../constants/*"], "root/*": ["./*"], "_@snap/*": ["./modules/@snap/*"]}, "types": ["@types/node", "@playwright/test"], "importHelpers": true, "resolveJsonModule": true, "esModuleInterop": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "allowJs": true, "checkJs": false, "jsx": "react-jsx", "jsxImportSource": "react"}, "include": ["globals.d.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "domain/**/*.ts", "domain/**/*.tsx", "./testUtils.jsx", "./setupTests.ts"], "exclude": ["node_modules", "**/node_modules/**", "playwright-report", ".vscode", "dist", "public", ".cache"]}