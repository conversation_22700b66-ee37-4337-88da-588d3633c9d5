import React from 'react';

export interface BaseImageProps {
  id?: string;
  style?: React.CSSProperties | React.CSSProperties[];
  /**
   * Render component in all wrapped pages.
   */
  fixed?: boolean;
  /**
   * Force the wrapping algorithm to start a new page when rendering the
   * element.
   */
  break?: boolean;
  /**
   * Hint that no page wrapping should occur between all sibling elements following the element within n points
   */
  minPresenceAhead?: number;
  /**
   * Enables debug mode on page bounding box.
   */
  debug?: boolean;
  cache?: boolean;
}

export interface ImageWithSrcProp extends BaseImageProps {
  className?: string;
  src: string | { uri: string; method?: string; headers?: Record<string, string>; body?: string };
}

export interface ImageWithSourceProp extends BaseImageProps {
  className?: string;
  source: string | { uri: string; method?: string; headers?: Record<string, string>; body?: string };
}

export type ImageProps = ImageWithSrcProp | ImageWithSourceProp;

/**
 * A React component for displaying network or local (Node only) JPG or
 * PNG images, as well as base64 encoded image strings.
 */
export const Image: React.FC<ImageProps> = (props) => {
  const {
    id,
    style,
    fixed,
    break: pageBreak,
    minPresenceAhead,
    debug,
    cache,
  } = props;

  // Extract src from either src or source prop
  const src = 'src' in props ? props.src : props.source;

  // Convert style array to single style object if needed
  const combinedStyle = Array.isArray(style) 
    ? style.reduce((acc, s) => ({ ...acc, ...s }), {})
    : style;

  // Base image styles that mimic PDF image behavior
  const imageStyle: React.CSSProperties = {
    display: 'block',
    maxWidth: '100%',
    height: 'auto',
    border: debug ? '1px solid orange' : undefined,
    ...combinedStyle,
  };

  // Handle fixed positioning
  if (fixed) {
    imageStyle.position = 'absolute';
  }

  // Handle page break
  if (pageBreak) {
    imageStyle.pageBreakBefore = 'always';
  }

  // Convert source object to string if needed
  const getImageSrc = () => {
    if (typeof src === 'string') {
      return src;
    }
    if (src && typeof src === 'object' && 'uri' in src) {
      return src.uri;
    }
    return '';
  };

  const imageSrc = getImageSrc();

  return (
    <img 
      id={id}
      className="pdf-image" 
      src={imageSrc}
      style={imageStyle}
      alt=""
      data-debug={debug}
      data-fixed={fixed}
      data-cache={cache}
      data-min-presence-ahead={minPresenceAhead}
    />
  );
};

export default Image;
