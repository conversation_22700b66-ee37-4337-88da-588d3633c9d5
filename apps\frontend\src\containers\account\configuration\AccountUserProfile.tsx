import { Avatar, Text } from '@snap/design-system'
import UserMenuContent from '~/components/UserMenuContent'
import { cn } from "~/lib/utils";
import { isValidUrl } from "root/modules/@snap/reports/ui/strategies/helpers.strategy";
import { getInitials } from "~/helpers";
import { useUserData } from '~/store/userStore';
import { useEffect, useState } from 'react';
import { USER_CONSTANTS } from '~/helpers/constants';

const AccountUserProfile = () => {
  const userData = useUserData();
  const initials = getInitials(userData?.name);
  const image = userData?.[USER_CONSTANTS.user_data.image as keyof typeof userData] as string | undefined;
  const organizationLogo = userData?.[USER_CONSTANTS.user_data.organization_logo as keyof typeof userData] as string | null;
  const organizationName = userData?.[USER_CONSTANTS.user_data.organization_name as keyof typeof userData] as string | null;
  const [isAvatarLoading, setIsAvatarLoading] = useState(false);

  useEffect(() => {
    setIsAvatarLoading(true);
    if (!image) {
      setIsAvatarLoading(false);
      return;
    }
    const img = new Image();
    img.src = image;
    img.onload = () => setIsAvatarLoading(false);
    img.onerror = () => setIsAvatarLoading(false);
    const timer = setTimeout(() => setIsAvatarLoading(false), 5000);
    return () => clearTimeout(timer);
  }, [image]);

  return (
    <div className='flex flex-col gap-4'>
      <div className="flex flex-col items-center justify-center gap-3 p-4 rounded-md bg-card">
        <div className={`${isAvatarLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-1000 ease-in-out max-w-[150px]`}>
          <Avatar
            size="18"
            className={cn(
              "border-8 border-border aspect-square",
              "[&_img]:h-full [&_img]:w-auto [&_img]:object-cover",
              "[&_span]:h-46 [&_span]:w-46 [&_span]:text-6xl"
            )}
            src={isValidUrl(image) ? image || '' : undefined}
            fallback={initials}
            textAlign="left"
            fallbackClassName="text-[32px]"
          />
        </div>
        <Text className="text-[24px] font-semibold">{userData?.name}</Text>
      </div>

      {/* logo container */}
      {
        organizationLogo && (
          <div className="flex items-center justify-center max-w-full rounded-md">
            <img
              src={organizationLogo}
              alt="SNAP Reports logo"
              className="object-cover"
            />
          </div>
        )
      }
      {
        organizationName && (
          <Text variant="body-md" className="font-semibold text-center text-ellipsis overflow-hidden bg-background p-1">
            {organizationName}
          </Text>
        )
      }

      <UserMenuContent showRemainingCredits classNameContainer="px-0" />
    </div>
  )
}

export default AccountUserProfile