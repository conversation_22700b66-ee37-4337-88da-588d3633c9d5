import json
from datetime import datetime, timezone
from io import BytesIO

import boto3
import botocore.exceptions
import pytz

import reports_processor.constants
from reports_processor.encoder import SetEncoder
from reports_processor.extractors import extract_entities_from_dataframe
from reports_processor.constants import ReportKeys, MINIO_ENDPOINT, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, \
    logger, Constants, ReportType
# from apps.spark.reports_processor.formatters import organize_main_entity
from reports_processor.utils import normalize_document, get_constant

from reports_executions.ReportsExecutions import insert_report_execution, update_report_execution

def process_report_data(data, report_type, search_value):
    """
    Process report data to transform it into the desired format.
    Returns a dictionary with the processed data.
    """
    # Determine entity type based on report type
    try:
        report_type = ReportType(report_type)
    except ValueError:
        logger.error(f"[process_report_data] Unknown report type: {report_type}")
        return None

    # Extract the data for this report type
    try:
        data_inside = data[ReportKeys.DATA][report_type.value][0]
    except KeyError:
        logger.error(f"[process_report_data] Missing data for report type {report_type}")
        return None

    # Convert to DataFrame
    logger.debug(f'[timne] converting df and extracting entities {datetime.now()}')
    # json_data = json.dumps(data_inside)
    # rdd = reports_processor.constants.spark.sparkContext.parallelize([json_data])
    # df = reports_processor.constants.spark.read.option("multiline", "true").json(rdd)

    # Process entities
    extracted_entities, processor = extract_entities_from_dataframe(data_inside, report_type, normalize_document(search_value))
    logger.debug(f'[timne] back from converting df and extracting entities {datetime.now()}')

    # processed_data = organize_main_entity(
    #     extracted_entities, entity_type, report_type, search_value
    # )

    return extracted_entities, processor


def handle_message(bucket, key):
    """Processes each batch of data from the stream."""

    s3 = boto3.client(
        "s3",
        endpoint_url=MINIO_ENDPOINT,
        aws_access_key_id=MINIO_ACCESS_KEY,
        aws_secret_access_key=MINIO_SECRET_KEY
    )

    # try:
    logger.info(f"[handle_batch] Processing file: bucket={bucket}, key={key}")
    obj = s3.get_object(Bucket=bucket, Key=key)
    logger.info(f"[handle_batch] Got objet from minio {obj}")
    # except botocore.exceptions.ClientError as e:
        # if e.response['Error']['Code'] == 'NoSuchKey':
        #     logger.error(f"[handle_batch] File not found: {bucket}/{key}. Skipping...")
        #     raise

    final_result = process_file(obj['Body'].read().decode('utf-8'))
    logger.info(f"[handle_batch] Got final result")

    if not final_result:
        return

    # Convert to JSON and save
    # with open("output.json", "w", encoding="utf-8") as f:
    #     json.dump(dfinal, f, cls=SetEncoder, indent=4, ensure_ascii=False)

    logger.info(f"[handle_batch] trying json dumps")
    json_result = json.dumps(final_result, cls=SetEncoder)
    logger.info(f"[handle_batch] got json dumps")
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S%f")
    file_tmp_path = f'/tmp/processed-at{timestamp}'

    with open(file_tmp_path, 'w') as f:
        f.write(json_result)

    # Upload to MinIO
    byte_stream = BytesIO(json_result.encode('utf-8'))
    s3.put_object(
        Bucket="processed-reports",
        Key=key,
        Body=byte_stream
    )

    logger.info(f"[handle_batch] Successfully processed and uploaded: {key}")


def process_file(file_content):
    # Read and preprocess file content
    # file_content = obj['Body'].read().decode('utf-8')
    original_data = json.loads(file_content)

    update_report_execution(original_data.get("user_reports_id"), {"dt_proc_spark": datetime.now(pytz.timezone("UTC"))})

    # Apply text substitutions from constants
    substitutions = get_constant(Constants.Generic, "translateWord", {})
    for old_text, new_text in substitutions.items():
        file_content = file_content.replace(old_text, new_text)

    # Parse the transformed content
    transformed_data = json.loads(file_content)
    logger.debug(f"[process_file] Transformed data")

    # Extract metadata from original report
    result = {
        "report_name": original_data.get("report_name"),
        "report_type": original_data.get("report_type"),
        "user_reports_id": original_data.get("user_reports_id"),
        "report_status": original_data.get("report_status"),
        "report_search_args": original_data.get("report_search_args"),
        "subject_name": original_data.get("subject_name"),
        "subject_mother_name": original_data.get("subject_mother_name"),
        "subject_age": original_data.get("subject_age"),
        "subject_sex": original_data.get("subject_sex"),
        "created_at": original_data.get("created_at"),
        "modified_at": original_data.get("modified_at"),
        "omitted_notes": original_data.get("omitted_notes"),
    }

    # Get report type and search parameters
    report_type = transformed_data.get(ReportKeys.REPORT_TYPE)
    search_args = transformed_data.get(ReportKeys.SEARCH_ARGS, {})

    if not search_args:
        logger.error(f"[handle_batch] Missing search arguments in data. Skipping...")
        update_report_execution(original_data.get("user_reports_id"), {"status_tentativa": "error", "detalhe_erro": "Faltando search arguments"})
        return None

    if report_type == ReportType.RELACOES.value:
        value = list(search_args.values())
        search_value = value
    else:
        value = search_args.get(report_type)
        search_value = value if isinstance(value, str) else value[0] if isinstance(value, list) and value else None
    logger.debug(f"[process_file] Searching aargs {value}")

    if not search_value:
        logger.error(f"[handle_batch] Missing search value for report type {report_type}. Skipping...")
        update_report_execution(original_data.get("user_reports_id"), {"status_tentativa": "error", "detalhe_erro": "Faltando search value para tipo do report"})
        return None

    # Process the report data
    logger.debug(f"[timne] process report data {datetime.now()}")

    processed_data, processor = process_report_data(transformed_data, report_type, search_value)
    logger.debug(f"[timne] back from process report data {datetime.now()}")
    if not processed_data:
        logger.error(f"[handle_batch] Missing processed data. Skipping report...")
        update_report_execution(original_data.get("user_reports_id"), {"status_tentativa": "error", "detalhe_erro": "Faltando processed data"})
        return None

    # ajusta metadados
    logger.debug(f"[timne] adjust_metadata {datetime.now()}")
    processor.adjust_metadata(processed_data, result)
    logger.debug(f"[timne] back from adjust_metadata {datetime.now()}")

    # Assemble final result
    result[ReportKeys.DATA] = {report_type: processed_data}
    final_result = {"data": result}

    update_report_execution(original_data.get("user_reports_id"), {"dt_chegada_minio_processed_reports": datetime.now(pytz.timezone("UTC"))})

    return final_result


def debug_processing():

    def debug_one_file(a_file):
        with open(a_file, "r", encoding='utf-8') as f:
            file_content = f.read()  # .decode('utf-8')

        logger.debug(f'[timne] processing file {datetime.now()}')
        processed_data = process_file(file_content)
        logger.debug(f'[timne] back from processing file {datetime.now()}')
        json_result = json.dumps(processed_data, cls=SetEncoder, indent=4)

        breakpoint()

    # Initialize Spark in local mode
    # reports_processor.constants.spark = SparkSession.builder \
    #     .appName("ReportProcessor-Debug") \
    #     .master("local[*]") \
    #     .getOrCreate()

    # Sample test data
    # test_data = '/Users/<USER>/Downloads/reportsEmailMultipleresponse_1749427931872.json'
    # test_data = '/Users/<USER>/Downloads/telefoneguiresponse_1749235056741.json'
    # test_data = '/Users/<USER>/Downloads/cnpjtbiz1response_1749233752058.json'
    # test_data = '/Users/<USER>/Downloads/4bf8a8a2-9748-400d-a411-ee06c2de75d6_794a643f-c18d-4ded-8643-65c40431dcd4 (1).json'
    # test_data = "/Users/<USER>/Downloads/123.json"
    # test_data = "/Users/<USER>/Downloads/cpf_report_pre_processing.json"
    # test_data = "/Users/<USER>/Downloads/2newtest68000367654.json"
    # test_data = "/Users/<USER>/Downloads/07-03-2025-13-50-52_files_list/292f07f5-9eb3-431e-b207-8adf9d7aac1f_a9496dba-79ea-4bdd-be70-16c269d14c51.json"
    # test_data = "/Users/<USER>/Downloads/07-03-2025-13-50-52_files_list/292f07f5-9eb3-431e-b207-8adf9d7aac1f_4837aa8f-4430-423d-ad4c-9db8dadf2e83.json"
    # test_data = "/Users/<USER>/PycharmProjects/Snap_Reports_10/apps/spark/reports_processor/11045231673.json"
    # test_data = "/Users/<USER>/PycharmProjects/Snap_Reports_10/apps/spark/reports_processor/mock_retorno_snap_cpf_bembolado.json"

    test_data = [
        # "/Users/<USER>/Downloads/2newtest68000367654.json",

        # email <EMAIL>
        "/Users/<USER>/Downloads/f64ad28b-6373-4a7c-a6de-310de230efb6_b0f00d36-57c0-46b7-b540-ee8928d852a8.json",
        # cnpj 16670085000155
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_2242e4f4-66e2-4963-adcc-cdc08817b5a3.json",
        # cpf 68000367653
        "/Users/<USER>/Downloads/2newtest68000367654.json",
        # cpf 06587643655
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_2a867890-feed-4f88-b093-6453ada62ae4.json",
        # email <EMAIL>
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_3dab11f0-dc14-436d-bd64-04b81ea65fb6.json",

        # email <EMAIL>
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_21542330-9dc9-4632-8bc8-246641f5db55.json",



        # cnpj 16670085000155
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_2242e4f4-66e2-4963-adcc-cdc08817b5a3.json",
        # cnpj 05757597000218
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_fc376a8c-6e73-47d1-bc75-78a41b371832.json",

        # cpf 11620199645 mariana
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_7f4bc0ad-a3e4-43d6-bd8b-5bee0ee7f73c.json",
        # cpf 51697335420 algum joao
        "/Users/<USER>/Downloads/edaf49bc-df9d-47f2-b654-4b91a61e27f8_882ded8c-4981-46ac-bf8a-6cd913deabe2.json",
        # email <EMAIL>
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_5df5b578-e13d-485d-a027-030944ed1712.json",

        # cpf 11045231673 andreia
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_3a2a9496-2f01-4e3f-b189-45b86c01da31.json",
        # cpf 71108289649 fernando
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_4bb845d0-0f8a-4c6f-b086-8204ba414be8.json",
        # telefone 21988711631
        "/Users/<USER>/Downloads/f2fa18a5-79cb-4659-8abe-472edfcca5f0_224c37ac-cabe-4f26-8f8a-20c44cd0b66e (1).json",
        # email <EMAIL>
        "/Users/<USER>/Downloads/f64ad28b-6373-4a7c-a6de-310de230efb6_78a06849-46cc-4401-a5a6-8543b8b28308.json",
        # relacoes 01629906697 e 56507119687
        "/Users/<USER>/Downloads/d40801d0-4575-44b4-b71f-e1fc6459b14a_c2efdc58-fc02-4f09-9eb4-9739d49d26d8.json",
        # telefone 83999501461
        "/Users/<USER>/Downloads/6aca1626-79e9-4b74-95ca-c45b403ccf4a_a9c7ab3c-d331-4b3e-ba13-4d80926df15e.json",
        # email <EMAIL>
        '/Users/<USER>/Downloads/6aca1626-79e9-4b74-95ca-c45b403ccf4a_7ca15d19-cc44-4646-8093-33de3f4f2b59 (1).json',
        # email <EMAIL>
        "/Users/<USER>/Downloads/6aca1626-79e9-4b74-95ca-c45b403ccf4a_7ca15d19-cc44-4646-8093-33de3f4f2b59.json",
        # telefone 38999547958
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_841c24c7-28a5-4b52-a36e-b7e845e43a38.json",
        # telefone 41997172130
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/d40801d0-4575-44b4-b71f-e1fc6459b14a_7c33cf99-a937-4e55-914a-2edc6e918dde.json",
        # telefone 21988711631
        "/Users/<USER>/Downloads/f2fa18a5-79cb-4659-8abe-472edfcca5f0_224c37ac-cabe-4f26-8f8a-20c44cd0b66e.json",
        # telefone 41997172130
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/d40801d0-4575-44b4-b71f-e1fc6459b14a_7c33cf99-a937-4e55-914a-2edc6e918dde.json",
        # telefone 31999250748
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_08d56394-ee7f-418c-a8c4-2e5ce88ee1fa.json",


        # telefone 41997172130
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_298db704-2f1d-4d12-9330-534f648a05c2.json",

        # telefone 11934438135
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_8a368c83-b7d7-470a-9539-c8e4026a2e3b.json",
        # telefone 31996264775
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_8c54c2a3-e48e-43b6-9021-f7f0c4102246.json",
        # cpf 68000367653
        "/Users/<USER>/Downloads/08-07-2025-08-40-40_files_list/f6557435-61ef-4988-8bd5-64a5013b0ce5_a45f93b5-5744-499e-82b0-3af2cebd961d.json",

    ]

    test_data += [
        "/Users/<USER>/Downloads/testegbr.json",
        "/Users/<USER>/Downloads/2newtest68000367654.json"]

    # test_data = ["/Users/<USER>/Downloads/f6557435-61ef-4988-8bd5-64a5013b0ce5_973ff0ae-0607-4c81-a8bd-ade5867ffd6a.json",
    #              "/Users/<USER>/Downloads/testegbr.json",
    #              "/Users/<USER>/Downloads/2newtest68000367654.json",
    #              "/Users/<USER>/Downloads/07-16-2025-11-58-18_files_list/9ac41628-57fe-46b1-bf32-2f4a428db3b5_2fc952d5-ac54-46ea-abb7-1ed10305647f.json",
    #              "/Users/<USER>/Downloads/07-16-2025-11-58-18_files_list/9ac41628-57fe-46b1-bf32-2f4a428db3b5_27672657-d9b0-4817-9a72-71448cab80b1.json",
    #              "/Users/<USER>/Downloads/07-03-2025-13-50-52_files_list/292f07f5-9eb3-431e-b207-8adf9d7aac1f_a9496dba-79ea-4bdd-be70-16c269d14c51.json",
    #              "/Users/<USER>/Downloads/07-15-2025-08-55-36_files_list/292f07f5-9eb3-431e-b207-8adf9d7aac1f_066dc7f5-efc0-45e9-9d3e-2aa774b1a803.json",
    #              "/Users/<USER>/Downloads/07-15-2025-08-55-36_files_list/292f07f5-9eb3-431e-b207-8adf9d7aac1f_ba2c973c-6e54-49ba-9280-a56ebf9ea24e.json",
    #              "/Users/<USER>/Downloads/07-15-2025-08-55-36_files_list/292f07f5-9eb3-431e-b207-8adf9d7aac1f_80da3252-fa42-490f-9a47-53e2f4a5d11a.json",
    #              "/Users/<USER>/Downloads/07-15-2025-08-55-36_files_list/292f07f5-9eb3-431e-b207-8adf9d7aac1f_484cc057-b40a-4098-afd0-fe3176c88d0a.json"]


    if type(test_data) == list:
        for idx, t_data in enumerate(test_data):
            debug_one_file(t_data)

    else:
        debug_one_file(test_data)

    # with open(test_data, "r", encoding='utf-8') as f:
    #     file_content = f.read() # .decode('utf-8')
    #
    # processed_data = process_file(file_content)
    #
    # json_result = json.dumps(processed_data, cls=SetEncoder)
    #
    breakpoint()

