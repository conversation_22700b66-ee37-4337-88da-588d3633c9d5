export const getAgeFromBirthDate = (birthDate: string): string | number => {
  if (!birthDate) return "";

  const input = birthDate.trim();

  //tenta dd/mm/aaaa, depois fallback para ISO
  type Parser = (dateString: string) => Date | null;

  const parsers: Parser[] = [
    (dateString) => {
      const match = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/.exec(dateString);
      if (!match) return null;
      const day = parseInt(match[1], 10);
      const month = parseInt(match[2], 10);
      const year = parseInt(match[3], 10);
      
      if (isNaN(day) || isNaN(month) || isNaN(year) || 
          day < 1 || day > 31 || month < 1 || month > 12 || year < 1900) {
        return null;
      }
      
      const date = new Date(year, month - 1, day);
      return isNaN(date.getTime()) ? null : date;
    },
    (dateString) => {
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? null : date;
    },
  ];

  const dateBirthday = parsers
    .map((parser) => parser(input))
    .find((date): date is Date => !!date && !isNaN(date.getTime())) ?? null;

  if (!dateBirthday) return "";

  const today = new Date();
  let age = today.getFullYear() - dateBirthday.getFullYear();
  const monthDifference = today.getMonth() - dateBirthday.getMonth();

  if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < dateBirthday.getDate())) {
    age--;
  }
  
  //Não posso permitir retornar NaN de forma alguma
  return isNaN(age) || age < 0 ? "" : age;
}