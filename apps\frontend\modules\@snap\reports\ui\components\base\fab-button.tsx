import React from "react"
import { cn } from "../utils"

interface FabButtonProps {
    onClick?: () => void
    title?: string;
    icon: React.ReactNode
    size?: "sm" | "md" | "lg"
    variant?: "primary" | "secondary"
    disabled?: boolean
    className?: string
    position?: "bottomRight" | "bottomLeft" | "topRight" | "topLeft" | "middleRight" | "none"
    ariaLabel?: string
}

const FabButton = React.forwardRef<HTMLButtonElement, FabButtonProps>(
    (
        {
            onClick,
            icon,
            size = "md",
            variant = "primary",
            disabled = false,
            className,
            position = "bottomRight",
            ariaLabel,
            ...props
        },
        ref,
    ) => {
        const sizeClasses = {
            sm: "h-12 w-12",
            md: "h-14 w-14",
            lg: "h-16 w-16",
        }

        const variantClasses = {
            primary: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl",
            secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-md hover:shadow-lg",
        }

        const positionClasses = {
            bottomRight: "fixed bottom-6 right-6",
            bottomLeft: "fixed bottom-6 left-6",
            topRight: "fixed top-6 right-6",
            topLeft: "fixed top-6 left-6",
            middleRight: "fixed top-1/2 right-2",
            none: "",
        }

        return (
            <button
                ref={ref}
                onClick={onClick}
                disabled={disabled}
                aria-label={ariaLabel}
                className={cn(
                    // Base styles
                    "inline-flex items-center justify-center rounded-full",
                    "transition-all duration-200 ease-in-out",
                    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                    "active:scale-95",
                    // Size
                    sizeClasses[size],
                    // Variant
                    variantClasses[variant],
                    // Position
                    positionClasses[position],
                    // Disabled state
                    disabled && "opacity-50 cursor-not-allowed hover:bg-primary hover:shadow-lg",
                    // Z-index for floating
                    position !== "none" && "z-50",
                    className,
                )}
                {...props}
            >
                {icon}
            </button>
        )
    },
)

FabButton.displayName = "FabButton"

export { FabButton }