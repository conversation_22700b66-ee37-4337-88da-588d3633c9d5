import { Skeleton } from "~/components/ui/skeleton";
import { cn } from "~/lib/utils";

interface FolderTreeSkeletonProps {
  className?: string;
  showRootOption?: boolean;
}

export function FolderTreeSkeleton({ className }: FolderTreeSkeletonProps) {
  return (
    <div className={cn("rounded-md bg-neutral-600 py-4", className)}>
      <div className="py-2">
        <div className="max-h-96 overflow-y-auto">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="flex items-center gap-2 py-2 px-3 justify-between" style={{ paddingLeft: `${12 + (index % 3) * 20}px` }}>
              <div className="flex items-center gap-2">
                <div className="w-5 h-5" />
                <Skeleton className="w-7 h-7" />
                <Skeleton className="w-30 h-7 " />
              </div>
              <Skeleton className="h-7 w-30" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}