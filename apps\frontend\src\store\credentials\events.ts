import { CredentialsState } from "./interface";
import { useCredentialsStoreActions, useIsPasswordExpired } from "./selectors";

export const useCredentialStoreEvents = () => {
  const {
    updatePassword,
    clearCredentials,
    expirePassword: _expirePassword,
    isPasswordSafe,
    setCrypto,
    hasValidVerifiers,
  } = useCredentialsStoreActions();
  const useUpdatePassword = (password: string) => {
    updatePassword(password);
  };

  const afterAuthorizedQuery = () => {
    const isValidPass = useIsPasswordExpired();
    if (isValidPass) {
      return;
    } else {
      expirePassword();
    }
  };

  const persistPassword = (
    password: CredentialsState["password"]["validThrough"]
  ) => {
    persistPassword(password);
  };

  const expirePassword = () => {
    _expirePassword();
  };

  const resetCredentialsStore = () => {
    clearCredentials();
  };

  const useHasValidVerifiers = () => {
    return hasValidVerifiers();
  };

  const setVerifiers = ({
    iv,
    salt,
    encrypted,
  }: Partial<CredentialsState["crypto"]>) => {
    setCrypto({ iv, salt, encrypted });
  };

  return {
    useUpdatePassword,
    afterAuthorizedQuery,
    persistPassword,
    expirePassword,
    resetCredentialsStore,
    useHasValidVerifiers,
    setVerifiers,
  };
};
