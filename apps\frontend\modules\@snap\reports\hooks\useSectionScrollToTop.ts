import { useState, useEffect, useRef, useCallback } from 'react';

interface UseSectionScrollToTopReturn {
  visibleButtons: Record<string | number, boolean>;
  sectionRefs: React.RefObject<Record<string | number, HTMLDivElement | null>>;
  handleScrollToTop: (sectionIndex: string | number) => void;
}

export const useSectionScrollToTop = (scrollContainerId: string = 'report-content'): UseSectionScrollToTopReturn => {
  const [visibleButtons, setVisibleButtons] = useState<Record<string | number, boolean>>({});
  const sectionRefs = useRef<Record<string | number, HTMLDivElement | null>>({});

  const handleScrollToTop = useCallback((sectionIndex: string | number) => {
    const sectionElement = sectionRefs.current[sectionIndex];
    const scrollContainer = document.getElementById(scrollContainerId);

    if (sectionElement && scrollContainer) {
      const containerRect = scrollContainer.getBoundingClientRect();
      const sectionRect = sectionElement.getBoundingClientRect();
      const scrollTop = scrollContainer.scrollTop + sectionRect.top - containerRect.top;
      scrollContainer.scrollTo({ top: scrollTop, behavior: 'smooth' });
    }
  }, [scrollContainerId]);

  useEffect(() => {
    const handleScroll = () => {
      Object.entries(sectionRefs.current).forEach(([idx, ref]) => {
        if (ref) {
          const rect = ref.getBoundingClientRect();
          const isScrolled = rect.top < 100 && rect.bottom > 200;
          setVisibleButtons(prev => ({ ...prev, [idx]: isScrolled }));
        }
      });
    };

    const scrollContainer = document.getElementById(scrollContainerId);
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      return () => scrollContainer.removeEventListener('scroll', handleScroll);
    }
  }, [scrollContainerId]);

  return {
    visibleButtons,
    sectionRefs,
    handleScrollToTop
  };
};