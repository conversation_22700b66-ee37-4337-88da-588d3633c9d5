import { ReportMetadata, ReportSection } from "~/types/global";

export type SectionListRecordsItem = {
    title: string;
    data_count: number;
};

export type RelacoesSectionListItem = {
    title: string;
    data_count: number;
    index: number;
};

type UpdateFnPromise = (entry: any, index?: number) => void;

interface ReportDataState {
    sections: ReportSection[];
    deletedSections: ReportSection[];
    metadata: ReportMetadata | null;
    reportType: string;
    profileImage: string | null;
    selectedCombinedFilter: string;
    combinedReportSources: Array<{
        report_type: string;
        report_input_value: string;
        user_reports_id: string;
        report_name: string;
    }>;
}

interface ReportUIState {
    sectionListRecords: SectionListRecordsItem[];
    relacoesSectionList: RelacoesSectionListItem[];
    totalRecords: number;
    reloadTrigger: number;
    isActionLoading: boolean;
}

interface AutoSaveState {
    timeoutId: NodeJS.Timeout | null;
    isPending: boolean;
    hasPendingChanges: boolean;
    mutation: any;
}

export interface ReportDetailState {
    data: ReportDataState;
    ui: ReportUIState;
    autoSave: AutoSaveState;
}

interface ReportDataActions {
    setReportSections: (sections: ReportSection[]) => void;
    setDeletedSections: (sections: ReportSection[]) => void;
    setReportType: (type: string) => void;
    setMetadata: (metadata: ReportMetadata) => void;
    setProfileImage: (image: string) => void;
    setSelectedCombinedFilter: (filterId: string) => void;
    setCombinedReportSources: (sources: Array<{
        report_type: string;
        report_input_value: string;
        user_reports_id: string;
        report_name: string;
    }>) => void;
}

interface ReportUIActions {
    setSectionListRecords: (records: SectionListRecordsItem[]) => void;
    setRelacoesSectionList: (list: RelacoesSectionListItem[]) => void;
    setTotalRecords: (total: number) => void;
    forceReload: () => void;
    setActionLoading: (loading: boolean) => void;
}

interface ReportUpdateActions {
    updateSectionEntries: (
        sectionTitle: string,
        updaterFn: UpdateFnPromise,
        testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
        testSectionDeletedFn: (section: ReportSection) => boolean,
        calculateDataCountFn?: (section: ReportSection) => number,
        includeSubsections?: boolean,
        crossSectionUpdate?: { matchingProp: string; updaterFn: UpdateFnPromise }
    ) => void;
    updateSubsectionWithMainSection: (
        sectionTitle: string,
        subsectionName: string,
        matchingProp: string,
        updaterFn: UpdateFnPromise,
        testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
        testSectionDeletedFn: (section: ReportSection) => boolean,
        calculateDataCountFn?: (section: ReportSection) => number
    ) => void;
}

interface AutoSaveActions {
    scheduleAutoSave: () => void;
    cancelAutoSave: () => void;
    isPendingSave: () => boolean;
    hasPendingChanges: () => boolean;
    setAutoSaveMutation: (mutation: any) => void;
    setAutoSavePending: (isPending: boolean) => void;
}

interface GeneralActions {
    resetReportDetailStore: () => void;
}

export interface ReportDetailMutations
    extends ReportDataActions,
    ReportUIActions,
    ReportUpdateActions,
    AutoSaveActions,
    GeneralActions { }

export type { UpdateFnPromise };