import React, { memo, useMemo } from 'react';
import { Accordion } from './CustomAccordion';
import { Badge } from './base/badge';
import { ScrollToTopButton } from './ScrollToTopButton';
import { Text } from '@snap/design-system';
import { createSectionKey } from '../../helpers';
import { REPORT_SECTIONS } from '../../config/constants';
import { ActionButton } from './ActionButton';

interface ReportAccordionItemProps {
  section: {
    title: string;
    data: any;
    data_count: number;
    subsection?: any;
  };
  idx: number;
  isTrashMode: boolean;
  isTrashEnabled: boolean;
  isSaving: boolean;
  strategy: {
    render: (data: any) => React.ReactNode[];
    deleteSectionEntries?: () => void;
  };
  isOpen: boolean;
  onToggle: (sectionId: string) => void;
  sectionRefs: React.RefObject<Record<string | number, HTMLDivElement | null>>;
  visibleButtons: Record<string | number, boolean>;
  onScrollToTop: (sectionKey: string) => void;
  triggerClassName: string;
}

const ReportAccordionItem: React.FC<ReportAccordionItemProps> = ({
  section,
  idx,
  isTrashMode,
  isTrashEnabled,
  isSaving,
  strategy,
  isOpen,
  onToggle,
  sectionRefs,
  visibleButtons,
  onScrollToTop,
  triggerClassName
}) => {
  const sectionId = `section-${idx}`;
  const sectionKey = createSectionKey(section.title);
  const isMultiplosResultados = section.title === REPORT_SECTIONS.multiplos_registros_encontrados;

  const sectionContainerClass = useMemo(() => {
    return isMultiplosResultados ? '!pb-0 !px-0 bg-transparent' : '';
  }, [section.title]);

  const renderedContent = useMemo(() => {
    return strategy.render(section.data);
  }, [section.data, section.title]);

  const handleDeleteSection = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (strategy.deleteSectionEntries) {
      strategy.deleteSectionEntries();
    }
  };

  const handleToggle = () => {
    onToggle(sectionId);
  };

  const handleScrollToTop = () => {
    onScrollToTop(sectionKey);
  };

  return (
    <div
      ref={(el) => {
        sectionRefs.current[sectionKey] = el;
      }}
      className="relative w-full"
    >
      <Accordion type="multiple" value={isOpen ? [sectionId] : []} >
        <Accordion.Item key={sectionId} value={sectionId} className='border-b-0'>
          <div className="group sticky top-0 z-10" onClick={handleToggle}>
            <Accordion.Trigger className={triggerClassName}>
              <div className="flex items-center gap-4 w-full justify-between pr-8">
                <div className="flex items-center gap-4">
                  <Text variant={isTrashMode ? "label-md" : "label-lg"} className="uppercase text-left">
                    {section.title}
                  </Text>
                  {!isTrashMode && (
                    <Badge
                      className="rounded-2xl px-4 py-0.5 bg-accordion-badge hover:bg-accordion-badge border-0"
                    >
                      <Text className="text-foreground">{section.data_count}</Text>
                    </Badge>
                  )}
                </div>
                {strategy.deleteSectionEntries && isTrashEnabled && (
                  <ActionButton
                    onClick={handleDeleteSection}
                    title={isTrashMode ? "Restaurar seção" : "Deletar seção"}
                    isTrashMode={isTrashMode}
                    size="lg"
                  />
                )}
              </div>
            </Accordion.Trigger>
          </div>
          <ScrollToTopButton
            isVisible={visibleButtons[sectionKey]}
            onScrollToTop={handleScrollToTop}
          />
          <Accordion.Content className={`${sectionContainerClass}`}>
            <div className="pt-5">
              {renderedContent.map((el: React.ReactNode, j: number) => (
                <div key={j}>{el}</div>
              ))}
            </div>
          </Accordion.Content>
        </Accordion.Item>
      </Accordion>
    </div>
  );
};

ReportAccordionItem.displayName = 'ReportAccordionItem';

export default ReportAccordionItem;
