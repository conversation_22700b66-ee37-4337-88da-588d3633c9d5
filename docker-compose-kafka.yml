services:
  kafka:
    image: my-kafka-image
    build:
      context: apps/kafka
      dockerfile: Dockerfile
    container_name: ${KAFKA_CONTAINER_NAME:-kafka}
    networks:
      - mystack-net
    ports:
      - "${KAFKA_EXTERNAL_PORT:-9093}:9093"
    environment:
      - KAFKA_PROCESS_ROLES=broker,controller
      - KAFKA_NODE_ID=1
      - KAFKA_CONTROLLER_QUORUM_VOTERS=1@kafka:9094
      - KAFKA_LISTENERS=INTERNAL://:9092,EXTERNAL://:9093,CONTROLLER://:9094
      - <PERSON><PERSON><PERSON>_ADVERTISED_LISTENERS=INTERNAL://kafka:9092,EXTERNAL://$KAFKA_EXTERNAL_URI:$KAFKA_EXTERNAL_PORT
      - KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=INTERNAL:PLAINTEXT,EXTERNAL:PLAINTEXT,CONTROLLER:PLAINTEXT
      - KAFKA_INTER_BROKER_LISTENER_NAME=INTERNAL
      - KAFKA_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_AUTO_CREATE_TOPICS_ENABLE=true
      - KAFKA_KRAFT_CLUSTER_ID=2EC08F13-BCF6-4B02-BC93-7712289B4FA1

    volumes:
      - kafka_data:/var/lib/kafka/data
      - ./apps/kafka/kafka-config/server.properties:/opt/kafka/config/server.properties
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: "2500M"
        reservations:
          cpus: "0.3"
          memory: "700M"
    restart: unless-stopped

volumes:
  kafka_data:
