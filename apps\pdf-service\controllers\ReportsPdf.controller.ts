import { Request, Response, NextFunction } from 'express';
import { GenerateReportsHtml, GenerateReportsPdf } from '../service';
import { GenerateCoverPdf } from '../service/generateCoverPdf';
import { mergePdfs } from '../utils/pdfMerger';
import { AppError } from '../middlewares/error.middleware';
import { IReportsPDFControllerBody } from "../global";
import { filterValidSections } from '../helpers';
import * as logger from '../utils/logger';

export const ReportsPDFController = async (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();

  try {
    if (!req.body || !req.body.sections || !req.body.metadata || !req.body.browserRef) {
      const error = new Error('Missing required fields: sections, metadata and browserRef') as AppError;
      error.statusCode = 400;
      error.isOperational = true;
      throw error;
    }

    if (!Array.isArray(req.body.sections)) {
      const error = new Error('Sections must be an array') as AppError;
      error.statusCode = 400;
      error.isOperational = true;
      throw error;
    }

    const { sections, metadata, browserRef, profile_image, should_print_snap_logo, organization_logo }: IReportsPDFControllerBody = req.body;

    // Filtrar apenas seções com conteúdo válido
    const validSections = filterValidSections(sections);

    if (sections.length === 0) {
      const error = new Error('Nenhuma seção com dados válidos encontrada para gerar PDF') as AppError;
      error.statusCode = 400;
      error.isOperational = true;
      throw error;
    }

    if (validSections.length === 0) {
      const error = new Error('Nenhuma seção com dados válidos encontrada para gerar PDF') as AppError;
      error.statusCode = 400;
      error.isOperational = true;
      throw error;
    }

    logger.info('=== PDF Generation Request ===');
    logger.info('Report details', {
      reportName: metadata.report_name || 'Unnamed Report',
      reportType: metadata.report_type || 'Unknown',
      sectionsCount: sections.length,
      validSectionsCount: validSections.length,
      requestSize: JSON.stringify(req.body).length
    });

    const html = await GenerateReportsHtml({ sections: validSections, metadata, profile_image, should_print_snap_logo, organization_logo });
    logger.info('HTML generation completed', {
      duration: Date.now() - startTime
    });

    logger.info('Starting PDF generation...');
    const pdfGenerationStart = Date.now();

    logger.info('Generating cover PDF');
    const coverPdfBuffer = await GenerateCoverPdf({
      browserRef,
      metadata,
      organization_logo,
      should_print_snap_logo
    });

    logger.info('Generating main report PDF');
    const reportPdfBuffer = await GenerateReportsPdf({
      ...html,
      browserRef
    });

    logger.info('Merging cover and report PDFs');
    const finalPdfBytes = await mergePdfs(coverPdfBuffer, reportPdfBuffer);
    const pdfBuffer = Buffer.from(finalPdfBytes);

    logger.info('PDF generation completed', {
      duration: Date.now() - pdfGenerationStart,
      pdfSize: pdfBuffer.length
    });

    const filename = `${metadata.report_name || 'report'}.pdf`.replace(/[^a-zA-Z0-9.-]/g, '_');
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');

    res.send(pdfBuffer);

    const totalTime = Date.now() - startTime;
    logger.info('=== PDF Generation Complete ===');
    logger.info('Total processing time', { duration: totalTime });
    logger.info('================================');

  } catch (error) {
    const totalTime = Date.now() - startTime;
    logger.error('=== PDF Generation Failed ===');
    logger.error('PDF generation error', {
      duration: totalTime,
      error: error instanceof Error ? error : new Error(String(error))
    });
    logger.error('==============================');
    if (
      error instanceof Error &&
      /allocation failed|heap out of memory/i.test(error.message)
    ) {
      logger.error('Heap error detected - exiting to restart container');

      setTimeout(() => process.exit(1), 100);
      return;
    }

    const appError = (error as AppError) || new Error('Unknown PDF error');
    appError.statusCode = appError.statusCode || 500;
    appError.isOperational = true;
    return next(appError);
  }
};
