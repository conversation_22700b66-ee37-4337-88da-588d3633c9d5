import React from 'react';
import { PrintStyles } from './PrintStyles';

export interface DocumentProps {
  style?: React.CSSProperties | React.CSSProperties[];
  title?: string;
  author?: string;
  subject?: string;
  creator?: string;
  keywords?: string;
  producer?: string;
  language?: string;
  creationDate?: Date;
  modificationDate?: Date;
  pdfVersion?: string;
  pageMode?: string;
  pageLayout?: string;
  onRender?: (props: { blob?: Blob }) => any;
  children?: React.ReactNode;
}

/**
 * This component represents the PDF document itself. It must be the root
 * of your tree element structure, and under no circumstances should it be
 * used as children of another react-pdf component. In addition, it should
 * only have children of type <Page />.
 */
export const Document: React.FC<DocumentProps> = ({
  style,
  title,
  author,
  subject,
  creator,
  keywords,
  producer,
  language,
  creationDate,
  modificationDate,
  pdfVersion,
  pageMode,
  pageLayout,
  onRender,
  children,
}) => {
  // Convert style array to single style object if needed
  const combinedStyle = Array.isArray(style) 
    ? style.reduce((acc, s) => ({ ...acc, ...s }), {})
    : style;

  // Base document styles that mimic PDF behavior
  const documentStyle: React.CSSProperties = {
    fontFamily: 'Helvetica, Arial, sans-serif',
    fontSize: '12px',
    margin: 0,
    padding: 0,
    color: '#333',
    width: '100%',
    ...combinedStyle,
  };

  // Set document metadata if provided (only in browser environment)
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      if (title) {
        window.document.title = title;
      }

      // Set meta tags for document metadata
      const setMetaTag = (name: string, content: string) => {
        let meta = window.document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
        if (!meta) {
          meta = window.document.createElement('meta');
          meta.setAttribute('name', name);
          window.document.head.appendChild(meta);
        }
        meta.setAttribute('content', content);
      };

      if (author) setMetaTag('author', author);
      if (subject) setMetaTag('subject', subject);
      if (creator) setMetaTag('creator', creator);
      if (keywords) setMetaTag('keywords', keywords);
      if (producer) setMetaTag('producer', producer);
      if (language) setMetaTag('language', language);
    }
  }, [title, author, subject, creator, keywords, producer, language]);

  return (
    <>
      <PrintStyles />
      <div 
        className="pdf-document" 
        style={documentStyle}
        data-pdf-version={pdfVersion}
        data-page-mode={pageMode}
        data-page-layout={pageLayout}
      >
        {children}
      </div>
    </>
  );
};

export default Document;
