import { ISectionOptions, Paragraph, TextRun, Table, TableRow, TableCell, ShadingType, WidthType } from "docx";
import { ReportSection } from "../../../global";
import { translatePropToLabel, getSingular, translateSource } from "../../../helpers";
import { Socio } from "../../../dtos/socios/SocioValidator";
import { SocioDTO } from "../../../dtos/socios/SocioDTO";
import { createSectionTitle } from "./utils";

interface RenderPrintSociosProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<Socio>
  };
}

export const renderSocios = ({ section }: RenderPrintSociosProps): ISectionOptions => {
  const children: (Paragraph | Table)[] = [createSectionTitle(section.title)];
  const validData = SocioDTO.processData(section.data);

  if (!validData.length) {
    return { children };
  }

  validData.forEach((socio) => {
    if (socio.nome_completo && !socio.nome_completo.is_deleted) {
      children.push(new Paragraph({ children: [new TextRun({ text: `${(socio.nome_completo.label || "Nome").toUpperCase()}: ${socio.nome_completo.value}`, bold: true, color: "FE473C" })] }));
    }
    if (socio.razao_social && !socio.razao_social.is_deleted) {
      children.push(new Paragraph({ children: [new TextRun({ text: `${(socio.razao_social.label || "Razão Social").toUpperCase()}: ${socio.razao_social.value}`, bold: true, color: "FE473C" })] }));
    }

    if (socio.detalhes) {
      const tableRows = Object.entries(socio.detalhes)
        .filter(([_, field]) => !field.is_deleted)
        .map(([key, field]) => new TableRow({ children: [new TableCell({ children: [new Paragraph(translatePropToLabel(field.label || key).toUpperCase())] }), new TableCell({ children: [new Paragraph(String(field.value))] })] }));
      if (tableRows.length > 0) {
        children.push(new Table({ rows: tableRows, width: { size: 100, type: WidthType.PERCENTAGE } }));
      }
    }

    if (socio.empresa && socio.empresa.length > 0) {
        children.push(new Paragraph({text: "EMPRESAS", style: "subtitle"}));
        socio.empresa.filter(e => !e.is_deleted).forEach((empresa, index) => {
            children.push(new Paragraph({text: `${getSingular(empresa.label || "EMPRESA").toUpperCase()} ${index + 1}`}));
            const empRows = Object.entries(empresa.value).filter(([_,field]) => !field.is_deleted).map(([key, field]) => new TableRow({children: [new TableCell({children: [new Paragraph(translatePropToLabel(field.label || key))]}), new TableCell({children: [new Paragraph(String(field.value))]})]}))
            if(empRows.length > 0) {
                children.push(new Table({rows: empRows, width: {size: 100, type: WidthType.PERCENTAGE}}));
            }
        });
    }

  });

  return { children };
};
