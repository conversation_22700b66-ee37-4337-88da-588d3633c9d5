import { useState, useCallback } from 'react';

export const useAccordionState = () => {
  const [openSections, setOpenSections] = useState<string[]>([]);

  const toggleSection = useCallback((sectionId: string) => {
    setOpenSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  }, []);

  const isSectionOpen = useCallback((sectionId: string) => {
    return openSections.includes(sectionId);
  }, [openSections]);

  return {
    openSections,
    toggleSection,
    isSectionOpen
  };
};
