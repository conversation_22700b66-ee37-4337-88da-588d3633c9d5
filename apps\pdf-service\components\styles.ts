import { StyleSheet } from "./pdf-components/StyleSheet";

export const PdfStyles = StyleSheet.create({
  page: {
    fontFamily: 'Helvetica',
    fontSize: 12,
    paddingTop: 80,
    paddingBottom: 90,
    paddingHorizontal: 20,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 100,
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  logoContainerHeader: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 16,
    width: '100%',
    height: '100%',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  organizationLogo: {
    maxWidth: 150,
    maxHeight: 70,
    objectFit: 'contain',
  },
  reportsLogo: {
    maxWidth: 120,
    objectFit: 'contain',
  },
  headerContent: {
    width: '100%',
    paddingHorizontal: 20,
    paddingVertical: 4,
    backgroundColor: '#DFE1EA',
    '-webkit-print-color-adjust': 'exact',
  },
  reportNameTitle: {
    fontFamily: "Arial, sans-serif",
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'uppercase',
    color: '#242424',
  },
  reportTypeContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  searchIcon: {
    width: 10,
    height: 10,
    marginRight: 4
  },
  searchValue: {
    fontSize: 10
  },
  customHeader: {
    paddingVertical: 10,
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 50,
    backgroundColor: "#DFE1EA",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    overflow: "hidden",
    '-webkit-print-color-adjust': 'exact',
  },
  footerContent: {
    flex: 1,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: "space-between",
    alignItems: "center",
    gap: 32,
  },
  footerCopyright: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#000',
  },
  footerPageNumberContent: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  pageNumberText: {
    fontSize: 12,
    color: '#000',
  },
  pageNumber: {
    fontSize: 12,
    color: '#000',
    fontWeight: 'bold',
  },
});
