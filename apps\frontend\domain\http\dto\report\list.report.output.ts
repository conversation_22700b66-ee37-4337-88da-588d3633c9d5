import { ReportData, EncryptedData } from "~/types/global";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { FolderData } from "root/domain/entities/folder.model";
import { ReportModel } from "root/domain/entities/report.model";
import { FolderModel } from "root/domain/entities/folder.model";
import { DisplayableItem } from "root/domain/entities/base.model";

export type ListItem = ReportData | FolderData;

export class ListReportOutputDTO {
  private readonly items: ListItem[];
  private readonly reportModels: ReportModel[];
  private readonly folderModels: FolderModel[];

  constructor(data: unknown) {
    this.items = this.validateAndTransform(data);
    const reports = this.items.filter(item => this.isValidReportData(item));
    const folders = this.items.filter(item => this.isValidFolderData(item));
    this.reportModels = reports.map(r => new ReportModel(r));
    this.folderModels = folders.map(f => new FolderModel(f));
  }

  private validateAndTransform(data: unknown): ListItem[] {
    if (!Array.isArray(data)) {
      console.error("Invalid data format for list: expected an array");
      throw new Error("Erro ao buscar lista de itens.");
    }

    return data?.map((item, index) => {
      if (this.isValidReportData(item)) {
        return item;
      }
      if (this.isValidFolderData(item)) {
        return item;
      }
      console.error(`Invalid data at index ${index} on the list`, item);
      throw new Error("Erro ao buscar lista de itens.");
    });
  }

  private isValidReportData(item: any): item is ReportData {
    const { new_report } = REPORT_CONSTANTS;
    const hasKeys = [
      new_report.report_id,
      new_report.report_status,
      new_report.report_type,
      new_report.report_search_args,
    ].every(k => k in item);
    if (!hasKeys) return false;

    if (typeof item[new_report.report_id] !== "string") return false;
    if (!this.isValidEncryptedData(item[new_report.report_search_args])) return false;

    const rt = item[new_report.report_type];
    if (typeof rt !== "string" && !this.isValidEncryptedData(rt)) return false;

    return true;
  }

  private isValidFolderData(item: any): item is FolderData {
    const required = [
      "folder_id",
      "folder_name",
      "folder_path",
      "depth_level",
      "created_at",
      "modified_at",
    ] as const;

    if (!required.every(k => k in item)) return false;
    if (typeof item.folder_id !== "string") return false;
    if (!this.isValidEncryptedData(item.folder_name)) return false;
    if (typeof item.folder_path !== "string") return false;
    if (typeof item.depth_level !== "number") return false;

    return true;
  }

  private isValidEncryptedData(data: any): data is EncryptedData {
    return (
      data != null &&
      typeof data.encrypted === "string" &&
      typeof data.iv === "string"
    );
  }

  public getReports(): ReadonlyArray<ReportData> {
    return this.items.filter(item => this.isValidReportData(item));
  }

  public getFolders(): ReadonlyArray<FolderData> {
    return this.items.filter(item => this.isValidFolderData(item));
  }

  public getAllItems(): ReadonlyArray<ListItem> {
    return this.items;
  }

  public getReportModels(): ReadonlyArray<ReportModel> {
    return this.reportModels;
  }

  public getFolderModels(): ReadonlyArray<FolderModel> {
    return this.folderModels;
  }

  public getAllModels(): ReadonlyArray<DisplayableItem> {
    return [...this.reportModels, ...this.folderModels];
  }

  public countReports(): number {
    return this.getReports().length;
  }

  public countFolders(): number {
    return this.getFolders().length;
  }

  public count(): number {
    return this.items.length;
  }
}