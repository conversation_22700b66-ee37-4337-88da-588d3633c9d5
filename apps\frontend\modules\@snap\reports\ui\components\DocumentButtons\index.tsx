import React from 'react';
import { toast } from 'sonner';
import PrintButton from '../PrintButton';
import { ReportSection, ReportMetadata } from '../../../global';
import { useVisibleReportSections, useReportMetadata, useProfileImage, useOrganizationLogo, useShouldPrintSnapLogo } from '../../../context/ReportContext';
import { useReportPDF } from '../../../hooks/useReportPDF';
import { useReportDOCX } from '../../../hooks/useReportDOCX';
import { REPORT_CONSTANTS } from '../../../config/constants';

export interface DocumentButtonsProps {
  showPdfButton: boolean;
  showDocxButton?: boolean;
}

const DocumentButtons: React.FC<DocumentButtonsProps> = ({ showPdfButton, showDocxButton }) => {
  const sections = useVisibleReportSections();
  const metadata = useReportMetadata();
  const profileImage = useProfileImage();
  const organizationLogo = useOrganizationLogo();
  const shouldPrintSnapLogo = useShouldPrintSnapLogo();

  const { generatePDF, loading: loadingPdf } = useReportPDF();
  const { generateDOCX, loading: loadingDocx } = useReportDOCX();

  const handleGenerate = async (
    type: 'pdf' | 'docx',
    generator: (props: {
      sections: ReportSection[];
      metadata: ReportMetadata;
      profile_image?: string;
      organization_logo?: string;
      should_print_snap_logo?: boolean;
    }) => Promise<string>
  ) => {
    let toastId: string | number | null = null;
    try {
      toastId = toast.loading(type === 'pdf' ? 'Gerando PDF...' : 'Gerando DOCX...', {
        description: 'Isso pode levar alguns minutos. Por favor, não feche esta aba até o download terminar.',
      });

      const blobUrl = await generator({
        sections,
        metadata,
        profile_image: profileImage,
        should_print_snap_logo: shouldPrintSnapLogo,
        organization_logo: organizationLogo || undefined,
      });

      const link = document.createElement('a');
      link.href = blobUrl;
      const ext = type === 'pdf' ? 'pdf' : 'docx';
      link.download = `${metadata[REPORT_CONSTANTS.new_report.report_name]}.${ext}`;
      link.click();
      URL.revokeObjectURL(blobUrl);

      if (toastId) toast.dismiss(toastId);
      toast.success(type === 'pdf' ? 'PDF gerado com sucesso!' : 'DOCX gerado com sucesso!');
    } catch (err) {
      console.error('Error in download process:', err);
      if (toastId) toast.dismiss(toastId);
      toast.error(type === 'pdf' ? 'Erro ao gerar o PDF. Favor tentar novamente.' : 'Erro ao gerar o DOCX. Favor tentar novamente.');
    }
  };

  return (
    <div className="fixed top-1/2 right-3 z-50 flex flex-col gap-2 -translate-y-1/2">
      {showPdfButton && (
        <PrintButton
          type="pdf"
          onClick={() => handleGenerate('pdf', generatePDF)}
          loading={loadingPdf}
          position="none"
          title="Download PDF"
          variant="primary"
          size='sm'
        />
      )}
      {showDocxButton && (
        <PrintButton
          type="docx"
          onClick={() => handleGenerate('docx', generateDOCX)}
          loading={loadingDocx}
          position="none"
          title="Download DOCX"
          variant="primary"
          size='sm'
        />
      )}
    </div>
  );
};

export default DocumentButtons;