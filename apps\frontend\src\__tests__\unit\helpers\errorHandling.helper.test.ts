import { describe, it, expect, vi, beforeEach } from 'vitest';
import { toast } from 'sonner';
import { 
  processError, 
  handleQueryError, 
  create<PERSON>rrorHandler,
  createSilentErrorHandler,
  commonErrorHandlers 
} from '../../../helpers/errorHandling.helper';

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
  },
}));

describe('errorHandling.helper', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('processError', () => {
    it('should handle 500 errors with custom message', () => {
      const error = {
        response: { status: 500, data: { message: 'Internal server error' } }
      };
      
      const result = processError(error, { 
        serverErrorMessage: 'Custom 500 message' 
      });

      expect(result.isServerError).toBe(true);
      expect(result.statusCode).toBe(500);
      expect(result.message).toBe('Custom 500 message');
    });

    it('should extract error message from error.response.data.error.message', () => {
      const error = {
        response: { 
          status: 400, 
          data: { error: { message: 'Validation failed' } } 
        }
      };
      
      const result = processError(error);

      expect(result.isServerError).toBe(false);
      expect(result.statusCode).toBe(400);
      expect(result.message).toBe('Validation failed');
    });

    it('should extract error message from error.response.data.detail.message', () => {
      const error = {
        response: {
          status: 400,
          data: { detail: { message: 'Detail error message' } }
        }
      };

      const result = processError(error);

      expect(result.message).toBe('Detail error message');
    });

    it('should handle 422 Unprocessable Entity with nested detail structure', () => {
      const error = {
        response: {
          status: 422,
          data: {
            detail: {
              error: 'Unprocessable Entity',
              message: 'Dados inseridos inválidos.'
            }
          }
        }
      };

      const result = processError(error);

      expect(result.statusCode).toBe(422);
      expect(result.isServerError).toBe(false);
      expect(result.message).toBe('Dados inseridos inválidos.');
    });

    it('should extract error message from error.response.data.detail as string', () => {
      const error = {
        response: {
          status: 400,
          data: { detail: 'Failed to set API key' }
        }
      };

      const result = processError(error);

      expect(result.statusCode).toBe(400);
      expect(result.isServerError).toBe(false);
      expect(result.message).toBe('Failed to set API key');
    });

    it('should extract error message from error.response.data.message', () => {
      const error = {
        response: { 
          status: 400, 
          data: { message: 'Direct message' } 
        }
      };
      
      const result = processError(error);

      expect(result.message).toBe('Direct message');
    });

    it('should extract error message from error.message', () => {
      const error = {
        message: 'Network error',
        response: { status: 0 }
      };
      
      const result = processError(error);

      expect(result.message).toBe('Network error');
    });

    it('should handle string errors', () => {
      const error = 'String error message';
      
      const result = processError(error);

      expect(result.message).toBe('String error message');
    });

    it('should return fallback message for unknown error structure', () => {
      const error = { unknownProperty: 'value' };
      
      const result = processError(error);

      expect(result.message).toBe('Ocorreu um erro inesperado');
    });
  });

  describe('handleQueryError', () => {
    it('should show toast by default', () => {
      const error = { message: 'Test error' };
      
      handleQueryError(error, { 
        serverErrorMessage: 'Server error message' 
      });

      expect(toast.error).toHaveBeenCalledWith('Erro', {
        description: 'Test error'
      });
    });

    it('should not show toast when showToast is false', () => {
      const error = { message: 'Test error' };
      
      handleQueryError(error, { 
        showToast: false 
      });

      expect(toast.error).not.toHaveBeenCalled();
    });

    it('should call onError callback when provided', () => {
      const onErrorCallback = vi.fn();
      const error = { message: 'Test error' };
      
      handleQueryError(error, { 
        onError: onErrorCallback 
      });

      expect(onErrorCallback).toHaveBeenCalledWith(error, 'Test error');
    });

    it('should use custom title', () => {
      const error = { message: 'Test error' };
      
      handleQueryError(error, { 
        title: 'Custom Title' 
      });

      expect(toast.error).toHaveBeenCalledWith('Custom Title', {
        description: 'Test error'
      });
    });
  });

  describe('createErrorHandler', () => {
    it('should prioritize API message over fallback for non-500 errors', () => {
      const handler = createErrorHandler('Fallback message', 'Custom Title');
      const error = {
        response: {
          status: 400,
          data: { detail: 'API error message' }
        }
      };

      handler(error);

      expect(toast.error).toHaveBeenCalledWith('Custom Title', {
        description: 'API error message'
      });
    });

    it('should prioritize API message even for 500 errors', () => {
      const handler = createErrorHandler('Custom server error', 'Custom Title');
      const error = {
        response: {
          status: 500,
          data: { detail: 'Failed to set API key' }
        }
      };

      handler(error);

      expect(toast.error).toHaveBeenCalledWith('Custom Title', {
        description: 'Failed to set API key'
      });
    });

    it('should use fallback message for 500 errors when no API message', () => {
      const handler = createErrorHandler('Custom server error', 'Custom Title');
      const error = { response: { status: 500 } };

      handler(error);

      expect(toast.error).toHaveBeenCalledWith('Custom Title', {
        description: 'Custom server error'
      });
    });

    it('should use fallback when no API message can be extracted', () => {
      const handler = createErrorHandler('Fallback message', 'Custom Title');
      const error = { unknownProperty: 'value' };

      handler(error);

      expect(toast.error).toHaveBeenCalledWith('Custom Title', {
        description: 'Fallback message'
      });
    });
  });

  describe('createSilentErrorHandler', () => {
    it('should create a function that handles errors without showing toast', () => {
      const onErrorCallback = vi.fn();
      const handler = createSilentErrorHandler('Server error', onErrorCallback);
      const error = { response: { status: 500 } };
      
      handler(error);

      expect(toast.error).not.toHaveBeenCalled();
      expect(onErrorCallback).toHaveBeenCalledWith(error, 'Server error');
    });
  });

  describe('commonErrorHandlers', () => {
    it('should have predefined error handlers', () => {
      expect(commonErrorHandlers.auth).toBeDefined();
      expect(commonErrorHandlers.validation).toBeDefined();
      expect(commonErrorHandlers.network).toBeDefined();
      expect(commonErrorHandlers.generic).toBeDefined();
    });

    it('should handle auth errors correctly', () => {
      const error = { response: { status: 500 } };
      
      commonErrorHandlers.auth(error);

      expect(toast.error).toHaveBeenCalledWith('Erro de Autenticação', {
        description: 'Erro de autenticação. Tente fazer login novamente.'
      });
    });
  });
});
