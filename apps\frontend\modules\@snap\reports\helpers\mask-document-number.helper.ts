enum DocumentType {
  CPF = 'CPF',
  CNPJ = 'CNPJ',
  TELEFONE = 'TELEFONE',
  UNKNOWN = 'UNKNOWN'
}

export const maskDocumentNumber = (documentNumber: string, documentTypeHint?: string): string => {
  if (!documentNumber || typeof documentNumber !== 'string') {
    return '';
  }

  const cleanDocument = sanitizeDocumentNumber(documentNumber);

  if (isAlreadyMasked(documentNumber)) {
    return documentNumber;
  }

  const documentType = getDocumentType(cleanDocument, documentTypeHint);

  switch (documentType) {
    case DocumentType.CPF:
      return maskCPF(cleanDocument);
    case DocumentType.CNPJ:
      return maskCNPJ(cleanDocument);
    case DocumentType.TELEFONE:
      return maskTelefone(cleanDocument);
    default:
      return documentNumber;
  }
};

/**
 * Removes all non-numeric characters from a string
 * @param document - The document string to sanitize
 * @returns Clean numeric string
 */
const sanitizeDocumentNumber = (document: string): string => {
  return document.replace(/\D/g, '');
};

/**
 * Checks if a document number is already masked
 * @param document - The document string to check
 * @returns True if already masked, false otherwise
 */
const isAlreadyMasked = (document: string): boolean => {
  const cpfMaskPattern = /^\d{3}\.\d{3}\.\d{3}-\d{2}$/;
  const cnpjMaskPattern = /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/;
  const telefoneMaskPattern = /^\(\d{2}\) \d{4,5}-\d{4}$/; // (XX) XXXX-XXXX or (XX) XXXXX-XXXX

  return cpfMaskPattern.test(document) || cnpjMaskPattern.test(document) || telefoneMaskPattern.test(document);
};

/**
 * Determines the type of document based on its length and optional hint
 * @param cleanDocument - The sanitized document number
 * @param documentTypeHint - Optional hint about the document type from context
 * @returns The document type enum
 */
const getDocumentType = (cleanDocument: string, documentTypeHint?: string): DocumentType => {
  // If we have a hint, try to use it
  if (documentTypeHint) {
    const hint = documentTypeHint.toLowerCase();
    if (hint === 'telefone' && (cleanDocument.length === 10 || cleanDocument.length === 11)) {
      return DocumentType.TELEFONE;
    }
    if (hint === 'cpf' && cleanDocument.length === 11) {
      return DocumentType.CPF;
    }
    if (hint === 'cnpj' && cleanDocument.length === 14) {
      return DocumentType.CNPJ;
    }
  }

  // Fallback to length-based detection
  if (cleanDocument.length === 10 || cleanDocument.length === 11) {
    // Without a hint, prefer phone number for 10 digits, ambiguous for 11
    if (cleanDocument.length === 10) {
      return DocumentType.TELEFONE;
    }
    // For 11 digits without hint, default to CPF (existing behavior)
    return DocumentType.CPF;
  }

  if (cleanDocument.length === 14) {
    return DocumentType.CNPJ;
  }

  return DocumentType.UNKNOWN;
};

/**
 * Applies CPF mask formatting (XXX.XXX.XXX-XX)
 * @param cpf - The clean CPF string (11 digits)
 * @returns Formatted CPF string
 */
const maskCPF = (cpf: string): string => {
  return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
};

/**
 * Applies CNPJ mask formatting (XX.XXX.XXX/XXXX-XX)
 * @param cnpj - The clean CNPJ string (14 digits)
 * @returns Formatted CNPJ string
 */
const maskCNPJ = (cnpj: string): string => {
  return cnpj.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
};

const maskTelefone = (telefone: string): string => {
  if (telefone.length === 10) {
    // Format: (XX) XXXX-XXXX for landline numbers
    return telefone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  }
  if (telefone.length === 11) {
    // Format: (XX) XXXXX-XXXX for mobile numbers
    return telefone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  }
  return telefone;
}