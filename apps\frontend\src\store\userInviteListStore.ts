import { create } from "zustand";
import { UserInviteResponse } from "~/types/global";

interface UserInviteListStoreActions {
  setUserInviteList: (list: UserInviteResponse[]) => void;
  clearUserInviteList: () => void;
}

interface UserInviteListStoreState {
  userInviteList: UserInviteResponse[];
  actions: UserInviteListStoreActions;
}

const useUserInviteListStore = create<UserInviteListStoreState>((set) => ({
  userInviteList: [],
  actions: {
    setUserInviteList: (list: UserInviteResponse[]) => set({ userInviteList: list }),
    clearUserInviteList: () => set({ userInviteList: [] }),
  },
}));

export const useUserInviteList = () => useUserInviteListStore(state => state.userInviteList);
export const useUserInviteListActions = () => useUserInviteListStore(state => state.actions);

export default useUserInviteListStore;
