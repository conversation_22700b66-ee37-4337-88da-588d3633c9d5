# Development Workflow for Database Changes

## Overview

This document outlines the complete workflow for making database changes in the reports schema, ensuring consistency, safety, and maintainability.

## 🚨 Critical Understanding

**Database changes are NOT automatic!** You must follow the migration process for all schema changes:

- ✅ **New tables**: Require Alembic migrations
- ✅ **New columns**: Require Alembic migrations
- ✅ **Index changes**: Require Alembic migrations
- ✅ **Constraint changes**: Require Alembic migrations

**Current Setup:**
- Development uses `Base.metadata.create_all()` for initial setup
- Production MUST use Alembic migrations only
- All tables MUST be in `reports` schema

## 🔄 Complete Development Workflow

### 1. Planning Phase

**Before making any changes:**

```bash
# Check current migration status
alembic current
alembic history --verbose

# Validate existing migrations
python database/migration_validator.py --all
```

**Questions to ask:**
- What is the purpose of this change?
- Will this affect existing data?
- What are the performance implications?
- Is this a breaking change for the API?

### 2. Making Changes

#### For New Tables:

1. **Create the SQLAlchemy model**:
   ```bash
   # Use the interactive helper
   python database/create_table_cli.py

   # Or create manually in models/{table_name}_model.py
   ```

2. **Add to model imports**:
   ```python
   # Edit models/__init__.py
   from models.new_table_model import NewTable
   # Add to __all__ list
   ```

3. **Generate migration**:
   ```bash
   alembic revision --autogenerate -m "Create new_table"
   ```

#### For New Columns:

1. **Use the column addition helper**:
   ```bash
   python database/add_column_helper.py
   ```

2. **Or manually modify model**:
   ```python
   # Add column to existing model
   new_column = Column(Text, nullable=True)
   ```

3. **Generate migration**:
   ```bash
   alembic revision --autogenerate -m "Add new_column to existing_table"
   ```

### 3. Migration Review and Validation

**Always review generated migrations:**

```bash
# Validate the migration
python database/migration_validator.py --file alembic/versions/xxx_migration.py

# Review the generated SQL
cat alembic/versions/xxx_migration.py
```

**Key checks:**
- ✅ Schema specified: `schema='reports'`
- ✅ Proper column types and constraints
- ✅ Appropriate indexes created
- ✅ Safe for existing data
- ✅ Rollback function implemented

### 4. Testing Migrations

**Test in development environment:**

```bash
# Check current state
alembic current

# Apply migration
alembic upgrade head

# Verify in database
# Connect to your DB and check the changes

# Test rollback
alembic downgrade -1

# Reapply
alembic upgrade head
```

**Validate changes:**

```bash
# Use schema validator
python -c "
from database.schema_validator import SchemaValidator
from database.db import engine
validator = SchemaValidator(engine)
result = validator.validate_table('your_table_name')
print('✅ Valid' if result.is_valid else '❌ Issues found')
for error in result.errors:
    print(f'  - {error}')
"
```

### 5. Update Application Code

**After successful migration:**

1. **Update Pydantic schemas** (if exposing via API):
   ```python
   # Create/update schemas/{table_name}_schema.py
   ```

2. **Update service layers** to use new columns/tables

3. **Update API endpoints** if needed

4. **Update tests** to cover new functionality

### 6. Pre-Production Checklist

Before deploying to production:

- [ ] All migrations tested locally
- [ ] Migration validation passes
- [ ] No breaking changes to existing APIs
- [ ] Database backup plan ready
- [ ] Rollback plan documented
- [ ] Performance impact assessed
- [ ] Team notified of changes

## 📋 Common Workflows

### Adding a Simple Nullable Column

```bash
# 1. Add column to model
# models/user_model.py
last_seen = Column(DateTime(timezone=True), nullable=True)

# 2. Generate migration
alembic revision --autogenerate -m "Add last_seen to users"

# 3. Review and apply
python database/migration_validator.py --file alembic/versions/xxx_add_last_seen.py
alembic upgrade head
```

### Adding a Non-Nullable Column with Default

```bash
# 1. Add column to model with server_default
status = Column(Text, nullable=False, server_default='active')

# 2. Generate migration
alembic revision --autogenerate -m "Add status to users"

# 3. Review generated migration should include:
# server_default='active' for existing rows
```

### Adding a Foreign Key Column

```bash
# 1. Add column to model
organization_id = Column(UUID, ForeignKey("reports.organizations.organization_id"), nullable=True)

# 2. Add index to __table_args__
Index("idx_users_organization_id", "organization_id"),

# 3. Generate migration (will include FK constraint and index)
alembic revision --autogenerate -m "Add organization_id to users"
```

### Creating a Junction Table

```bash
# 1. Use the table creation helper
python database/create_table_cli.py
# Select "Junction table" option

# 2. Or create manually with composite primary key
class UserRole(Base):
    __tablename__ = 'user_roles'
    __table_args__ = ({"schema": "reports"})

    user_id = Column(UUID, ForeignKey("reports.users.user_id"), primary_key=True)
    role_id = Column(UUID, ForeignKey("reports.roles.role_id"), primary_key=True)
    created_at = Column(DateTime(timezone=True), server_default=func.timezone('UTC', func.now()))
```

## 🚨 Emergency Procedures

### Rolling Back a Migration

```bash
# Check current revision
alembic current

# Rollback one migration
alembic downgrade -1

# Rollback to specific revision
alembic downgrade abc123

# Check what was rolled back
alembic history --verbose
```

### Fixing a Failed Migration

1. **If migration failed partway through:**
   ```bash
   # Check current state
   alembic current

   # Manually fix the database if needed
   # Then mark migration as applied
   alembic stamp head
   ```

2. **If migration has errors:**
   ```bash
   # Edit the migration file
   nano alembic/versions/xxx_problematic_migration.py

   # Test the fix
   alembic upgrade head
   ```

### Data Recovery

1. **Always have database backups**
2. **Test migrations on copy of production data**
3. **Keep rollback scripts ready**

## 🔧 Available Tools and Helpers

### Interactive Tools:
- `python database/create_table_cli.py` - Create new tables
- `python database/add_column_helper.py` - Add columns to existing tables

### Validation Tools:
- `python database/migration_validator.py --all` - Validate all migrations
- `python database/schema_validator.py` - Validate schema compliance

### Templates:
- `database/migration_templates.py` - Migration templates
- `database/column_templates.py` - Column-specific templates
- `database/table_creation_guide.py` - Utilities and patterns

### Documentation:
- `DATABASE_TABLE_CREATION_GUIDE.md` - Complete table creation guide
- `COLUMN_ADDITION_GUIDE.md` - Detailed column addition workflow

## 📊 Migration Status Dashboard

Create a simple status check:

```bash
# Check overall migration health
python -c "
from database.migration_validator import get_migration_status
import json
status = get_migration_status()
print(json.dumps(status, indent=2))
"
```

## 🎯 Best Practices Summary

### DO:
- ✅ Always use Alembic migrations for schema changes
- ✅ Test migrations both up and down
- ✅ Validate migrations before applying
- ✅ Use `reports` schema for all tables
- ✅ Follow naming conventions
- ✅ Add appropriate indexes
- ✅ Provide defaults for non-nullable columns
- ✅ Document complex migrations

### DON'T:
- ❌ Make direct database changes
- ❌ Skip migration testing
- ❌ Apply untested migrations to production
- ❌ Add non-nullable columns without defaults
- ❌ Ignore foreign key constraints
- ❌ Skip rollback function implementation

### PRODUCTION DEPLOYMENT:
1. **Backup database** before applying migrations
2. **Apply migrations** during maintenance window
3. **Verify application** starts correctly
4. **Monitor** for any issues
5. **Have rollback plan** ready

## 🔍 Troubleshooting

### Common Issues:

1. **"Table already exists" error:**
   - Check if migration was partially applied
   - Use `alembic stamp head` to mark as applied

2. **"Column cannot be null" error:**
   - Non-nullable column added without default
   - Add default value or make nullable

3. **Foreign key constraint fails:**
   - Referenced table doesn't exist
   - Check migration order

4. **Index already exists:**
   - Migration applied partially
   - Check database state vs migration state

### Getting Help:

1. **Check migration history:** `alembic history --verbose`
2. **Validate migrations:** `python database/migration_validator.py --all`
3. **Check schema compliance:** Use schema validator tools
4. **Review documentation:** This guide and related files

Remember: **When in doubt, test on a copy of production data first!**