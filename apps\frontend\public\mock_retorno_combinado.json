{"report_name": "combinado 1", "report_type": "combinado", "user_reports_id": "7a8359d2-eebb-41b5-8982-0e159757934d", "report_status": "pending", "report_search_args": {"cpf": "71108289649", "telefone": "31996264775"}, "report_sources_used": [{"report_type": "cpf", "report_input_value": "71108289649", "user_reports_id": "6f6e06e9-af51-4067-a53f-03c8c17c4da8", "report_name": "Relatório CPF fulaninho de tal"}, {"report_type": "telefone", "report_input_value": "31996264775", "user_reports_id": "fea0a198-6110-4621-9ef0-16b667317492", "report_name": "Relatório Telefone fulaninho de tal"}], "subject_name": "", "subject_mother_name": "", "subject_age": "", "subject_sex": "", "created_at": "2025-09-10T14:36:21.490454+00:00", "modified_at": "2025-09-10T14:36:21.490454+00:00", "omitted_notes": null, "subject_person_count": 0, "subject_company_count": 0, "data": {"combinado": [{"combined_data": [{"title": "<PERSON><PERSON>", "subtitle": "Dados consultados na API SNAP.", "subsection": "", "is_deleted": false, "sources": ["SNAP", "PIPLPhone"], "data_count": 7, "data": [{"detalhes": {"full_name": {"value": "FULANO DA SILVA", "label": "Nome", "source": ["SNAP"], "is_deleted": false}, "nome_da_mae": {"value": "SICLANA DA SILVA", "label": "<PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "idade": {"value": "52 ANOS", "label": "<PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "sexo": {"value": "MASCULINO", "label": "Sexo", "source": ["SNAP"], "is_deleted": false}, "data_nascimento": {"value": "1970-06-09", "label": "Data de Nascimento", "source": ["SNAP"], "is_deleted": false}, "status receita": {"value": "REGULAR", "label": "Status na Receita", "source": ["SNAP"], "is_deleted": false}, "país do passaporte": {"value": "BRASIL", "label": "país do passaporte", "source": ["SNAP"], "is_deleted": false}}}]}], "report_sources_data": [{"title": "<PERSON><PERSON>", "subtitle": "Dados consultados na API SNAP.", "subsection": "", "is_deleted": false, "sources": ["SNAP"], "user_reports_id": "6f6e06e9-af51-4067-a53f-03c8c17c4da8", "data_count": 5, "data": [{"detalhes": {"full_name": {"value": "FULANO DA SILVA", "label": "Nome", "source": ["SNAP"], "is_deleted": false}, "nome_da_mae": {"value": "SICLANA DA SILVA", "label": "<PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "idade": {"value": "52 ANOS", "label": "<PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "sexo": {"value": "MASCULINO", "label": "Sexo", "source": ["SNAP"], "is_deleted": false}, "data_nascimento": {"value": "1970-06-09", "label": "Data de Nascimento", "source": ["SNAP"], "is_deleted": false}}}]}, {"title": "<PERSON><PERSON>", "subtitle": "Dados consultados na API SNAP.", "subsection": "", "is_deleted": false, "sources": ["SNAP"], "user_reports_id": "fea0a198-6110-4621-9ef0-16b667317492", "data_count": 4, "data": [{"detalhes": {"full_name": {"value": "FULANO DA SILVA", "label": "Nome", "source": ["SNAP"], "is_deleted": false}, "nome_da_mae": {"value": "SICLANA DA SILVA", "label": "<PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "status receita": {"value": "REGULAR", "label": "Status na Receita", "source": ["SNAP"], "is_deleted": false}, "país do passaporte": {"value": "BRASIL", "label": "país do passaporte", "source": ["SNAP"], "is_deleted": false}}}]}]}]}}