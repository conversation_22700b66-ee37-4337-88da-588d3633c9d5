// List of valid image MIME types (expandable as needed)
const VALID_IMAGE_MIME_TYPES = new Set([
  'png', 'jpeg', 'jpg', 'gif', 'bmp',
  'svg+xml', 'webp', 'apng', 'tiff', 'x-icon'
]);

// Maximum allowed Base64 string length (10MB)
const MAX_BASE64_LENGTH = 10 * 1024 * 1024;

// Minimum length for a valid base64 image (even tiny images are ~100+ chars)
const MIN_BASE64_IMAGE_LENGTH = 100;

// Common image format signatures for raw base64 data
const IMAGE_SIGNATURES = {
  JPEG: ['/9j/', '/9k/'],
  PNG: ['iVBORw0KGgo'],
  GIF: ['R0lGODlh', 'R0lGODdh'],
  WEBP: ['UklGR']
};

/**
 * Validates if a string is a valid base64 image (with or without data URI prefix)
 */
export const isValidBase64Image = (str: string): boolean => {
  // 1. Basic string and length check
  if (typeof str !== 'string' || str.length > MAX_BASE64_LENGTH) {
    return false;
  }

  // 2. Check for data URI format
  if (str.startsWith('data:image/')) {
    // Verify comma separator exists
    const commaIndex = str.indexOf(',');
    if (commaIndex === -1 || commaIndex === 0 || commaIndex === str.length - 1) {
      return false;
    }

    // Extract and validate header
    const header = str.substring(0, commaIndex).trim();
    const segments = header.split(';').map(seg => seg.trim().toLowerCase());

    // Validate encoding (must end with 'base64')
    if (segments[segments.length - 1] !== 'base64') {
      return false;
    }

    // Extract and validate MIME type
    const mimePart = segments[0].split(':')[1] || '';
    const mimeType = mimePart.split('/')[1];

    if (!mimeType || !VALID_IMAGE_MIME_TYPES.has(mimeType)) {
      return false;
    }

    // Validate Base64 payload format and length
    const payload = str.substring(commaIndex + 1);
    if (payload.length < MIN_BASE64_IMAGE_LENGTH) {
      return false;
    }

    return /^[a-zA-Z0-9+/]+={0,2}$/.test(payload);
  }

  // 3. Check for raw base64 image data (without data URI prefix)
  return isValidRawBase64Image(str);
};

/**
 * Validates if a string is raw base64 image data (without data URI prefix)
 * Checks for minimum length and valid image format signatures
 */
export const isValidRawBase64Image = (str: string): boolean => {
  // 1. Basic checks
  if (typeof str !== 'string' || str.length < MIN_BASE64_IMAGE_LENGTH || str.length > MAX_BASE64_LENGTH) {
    return false;
  }

  // 2. Check base64 format
  if (!str.match(/^[a-zA-Z0-9+/]+={0,2}$/)) {
    return false;
  }

  // 3. Check for valid image format signatures
  const allSignatures = Object.values(IMAGE_SIGNATURES).flat();
  return allSignatures.some(signature => str.startsWith(signature));
};

/**
 * Formats image source by adding appropriate data URI prefix for raw base64 images
 */
export const formatImageSrc = (value: string): string => {
  if (!value) return value;

  // If it's already a valid data URI, return as is
  if (value.startsWith('data:image/')) {
    return value;
  }

  // If it's valid raw base64 image data, add appropriate prefix
  if (isValidRawBase64Image(value)) {
    // Detect image type based on signature and add appropriate prefix
    if (IMAGE_SIGNATURES.JPEG.some(sig => value.startsWith(sig))) {
      return `data:image/jpeg;base64,${value}`;
    } else if (IMAGE_SIGNATURES.PNG.some(sig => value.startsWith(sig))) {
      return `data:image/png;base64,${value}`;
    } else if (IMAGE_SIGNATURES.GIF.some(sig => value.startsWith(sig))) {
      return `data:image/gif;base64,${value}`;
    } else if (IMAGE_SIGNATURES.WEBP.some(sig => value.startsWith(sig))) {
      return `data:image/webp;base64,${value}`;
    } else {
      // Default to JPEG if we can't determine the type
      return `data:image/jpeg;base64,${value}`;
    }
  }

  return value;
};