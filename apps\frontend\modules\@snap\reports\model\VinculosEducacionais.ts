import { ValueWithSource } from "./ValueWithSource";

export type UpdaterFunction = (entry: VinculoEducacional, index?: number) => void;
export type TestFunction = (entry: VinculoEducacional) => boolean;
export type SectionTestFunction = (section: VinculosEducacionaisSection) => boolean;
export type CalculateFunction = (section: VinculosEducacionaisSection) => number;

export interface VinculosEducacionaisSection {
    data: VinculoEducacional[];
    data_count: number;
}

export interface TestFunctions {
    empresaEducadora: TestFunction;
    detalhes: TestFunction;
}

export interface VinculoEducacional {
    empresa_educadora: ValueWithSource<string>;
    detalhes: Record<string, ValueWithSource<string>>;
}
