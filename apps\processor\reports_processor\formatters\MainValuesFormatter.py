from reports_processor.constants import ReportKeys
import re

FORMAT_PATTERNS = {
    ReportKeys.CPF: r"(\d{3})(\d{3})(\d{3})(\d{2})",
    ReportKeys.CNPJ: r"(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})",
    ReportKeys.CEP: r"(\d{2})(\d{3})(\d{3})"
}

FORMAT_REPLACEMENTS = {
    ReportKeys.CPF: r"\1.\2.\3-\4",
    ReportKeys.CNPJ: r"\1.\2.\3/\4-\5",
    ReportKeys.CEP: r"\1.\2-\3"
}

def format_normalized_document(doc: str, doc_key: str) -> str:
    pattern = FORMAT_PATTERNS.get(doc_key)
    repl = FORMAT_REPLACEMENTS.get(doc_key)

    if pattern and repl:
        if isinstance(doc, dict) and 'value' in doc:
            doc = doc['value']
        return re.sub(pattern, repl, doc)

    return doc