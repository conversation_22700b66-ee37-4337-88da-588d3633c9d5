services:
  kafka-autoscaler:
    image: my-autoscaler-image
    environment:
      PROMETHEUS_URL: "http://prometheus:9090"
      TOPIC: "pdf-generation-requests"
      SERVICE_NAME: "mystack_pdf"
      POLL_INTERVAL_MS: ${POLL_INTERVAL_MS:-5000}
      HIGH_LAG_THRESHOLD: ${HIGH_LAG_THRESHOLD:-4}
      LOW_LAG_THRESHOLD: ${LOW_LAG_THRESHOLD:-3}
      DOWNSCALE_STABLE_COUNT: ${DOWNSCALE_STABLE_COUNT:-1}
      MIN_REPLICAS: ${MIN_REPLICAS:-1}
      MAX_REPLICAS: ${MAX_REPLICAS:-2}
      SCALE_UP_REPLICAS: ${SCALE_UP_REPLICAS:-1}
      COOLDOWN_SECONDS: ${COOLDOWN_SECONDS:-10}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
      resources:
        limits:
          cpus: "1"
          memory: "512M"
        reservations:
          cpus: "0.6"
          memory: "256M"
    networks:
      - mystack-net
      - monitoring
