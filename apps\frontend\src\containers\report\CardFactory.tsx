import { DisplayableItem, ItemType } from "root/domain/entities/base.model";
import { ReportModel } from "root/domain/entities/report.model";
import { FolderModel } from "root/domain/entities/folder.model";
import ReportCard from "./ReportCard";
import FolderCard from "./FolderCard";

interface CardFactoryProps {
  item: DisplayableItem;
  onRetry?: (report: ReportModel) => void;
  onDownloadPDF?: (reportId: string) => void;
  isDownloadingPDF?: boolean;
  onOpenFolder?: (folder: FolderModel) => void;
  onMoveFolder?: (folder: FolderModel) => void;
  onMergeFolder?: (folder: FolderModel) => void;
  onRenameFolder?: (folder: FolderModel) => void;
  onDeleteFolder?: (folder: FolderModel) => void;
  onMoveReport?: (report: ReportModel) => void;
  onRenameReport?: (report: ReportModel) => void;
  onDeleteReport?: (report: ReportModel) => void;
  onMergeReports?: (report: ReportModel) => void;
  isCreatingReport?: boolean;
  isDeletingReport?: boolean;
  isRenamingReport?: boolean;
  isGeneratingPDF?: boolean;
  isDeletingFolder?: boolean;
  isRenamingFolder?: boolean;
  isMovingReport?: boolean;
  isMovingFolder?: boolean;
  isMergingFolders?: boolean;
  hasPermissionToCreateCombinedReport: boolean;
}

export function CardFactory({
  item,
  onRetry,
  onMoveReport,
  onRenameReport,
  onDeleteReport,
  onMergeReports,
  onDownloadPDF,
  isDownloadingPDF,
  onOpenFolder,
  onMoveFolder,
  onMergeFolder,
  onRenameFolder,
  onDeleteFolder,
  isCreatingReport,
  isDeletingReport,
  isRenamingReport,
  isDeletingFolder,
  isRenamingFolder,
  isMovingReport,
  isMovingFolder,
  isMergingFolders,
  hasPermissionToCreateCombinedReport,
 }: CardFactoryProps) {
    
  if (item.type === ItemType.REPORT) {
    return <ReportCard
      report={item as ReportModel}
      onDownloadPDF={onDownloadPDF}
      isDownloadingPDF={isDownloadingPDF}
      onRetry={onRetry}
      onMoveReport={onMoveReport}
      onRenameReport={onRenameReport}
      onDeleteReport={onDeleteReport}
      isCreatingReport={isCreatingReport}
      isDeletingReport={isDeletingReport}
      isRenamingReport={isRenamingReport}
      isMovingReport={isMovingReport}
      onMergeReports={onMergeReports}
      hasPermissionToCreateCombinedReport={hasPermissionToCreateCombinedReport}
    />

  }

  if (item.type === ItemType.FOLDER) {
    return <FolderCard
      folder={item as FolderModel}
      onOpen={onOpenFolder}
      onMove={onMoveFolder}
      onMerge={onMergeFolder}
      onRename={onRenameFolder}
      onDelete={onDeleteFolder}
      isDeletingFolder={isDeletingFolder}
      isRenamingFolder={isRenamingFolder}
      isMovingFolder={isMovingFolder}
      isMergingFolders={isMergingFolders} />;
  }

  return null;
}