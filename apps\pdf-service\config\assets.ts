import path from 'path';
import { getImageAsDataUrl, getFontAsDataUrl } from "../helpers";
import { determineAssetsPath } from "../helpers/assetUtils";

const assetsPath = determineAssetsPath();
const CPF_ICON_SRC = getImageAsDataUrl(path.join(assetsPath, 'print-cpf.png'));
const CNPJ_ICON_SRC = getImageAsDataUrl(path.join(assetsPath, 'print-cnpj.png'));
const LOGO_SRC = getImageAsDataUrl(path.join(assetsPath, 'pwa-192x192.png'));
const MULTIPLE_USERS_ICON = getImageAsDataUrl(path.join(assetsPath, 'fa_users.png'));
const MULTIPLE_BUILDINGS_ICON = getImageAsDataUrl(path.join(assetsPath, 'fa_city.png'));
const ESPIRAL_SRC = getImageAsDataUrl(path.join(assetsPath, 'espiral.png'));
const LOGO_COMPLETA_HEADER = getImageAsDataUrl(path.join(assetsPath, 'header_logo_snap_reports.png'));
const CYGNITO_FONT = getFontAsDataUrl(path.join(assetsPath, 'fonts/cygnito.otf'));

export {
  CPF_ICON_SRC,
  CNPJ_ICON_SRC,
  LOGO_SRC,
  MULTIPLE_USERS_ICON,
  MULTIPLE_BUILDINGS_ICON,
  ESPIRAL_SRC,
  LOGO_COMPLETA_HEADER,
  CYGNITO_FONT,
}
