import { useReportDetailStore } from "./store";

export const useReportSections = () =>
  useReportDetailStore((state) => state.data.sections);

export const useReportDeletedSections = () =>
  useReportDetailStore((state) => state.data.deletedSections);

export const useReportMetadata = () =>
  useReportDetailStore((state) => state.data.metadata);

export const useReportType = () =>
  useReportDetailStore((state) => state.data.reportType);

export const useReportProfileImage = () =>
  useReportDetailStore((state) => state.data.profileImage);

export const useSelectedCombinedFilter = () =>
  useReportDetailStore((state) => state.data.selectedCombinedFilter);

export const useCombinedReportSources = () =>
  useReportDetailStore((state) => state.data.combinedReportSources);

export const useReportSectionListRecords = () =>
  useReportDetailStore((state) => state.ui.sectionListRecords);

export const useRelacoesSectionList = () =>
  useReportDetailStore((state) => state.ui.relacoesSectionList);

export const useTotalRecords = () =>
  useReportDetailStore((state) => state.ui.totalRecords);

export const useReportReloadTrigger = () =>
  useReportDetailStore((state) => state.ui.reloadTrigger);

export const useIsActionLoading = () =>
  useReportDetailStore((state) => state.ui.isActionLoading);

export const useIsPendingSave = () =>
  useReportDetailStore((state) => state.autoSave.isPending);

export const useHasPendingChanges = () =>
  useReportDetailStore((state) => state.autoSave.hasPendingChanges);

export const useReportDetailActions = () =>
  useReportDetailStore((state) => state.actions);