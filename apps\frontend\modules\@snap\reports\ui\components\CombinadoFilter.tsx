import React, { useMemo } from 'react';
import { Select } from "@snap/design-system";
import { CombinadoFilterOption } from "../../global"
import { maskDocumentNumber } from '../../helpers';

interface CombinadoFilterProps {
  reportSourcesUsed: Array<CombinadoFilterOption>;
  selectedFilterId: string;
  onFilterChange: (filterId: string) => void;
  className?: string;
}

const CombinadoFilter: React.FC<CombinadoFilterProps> = ({
  reportSourcesUsed,
  selectedFilterId,
  onFilterChange,
  className = ""
}) => {
  const filterOptions = useMemo(() => {
    const allReportsOption = {
      value: 'combined_data',
      label: 'TODOS OS RELATÓRIOS'
    };

    const sourceOptions = reportSourcesUsed.map(source => ({
      value: source.user_reports_id,
      label: `${source.report_type} ${maskDocumentNumber(source.report_input_value, source.report_type )} - ${source.report_name}`.toUpperCase()
    }));

    return [allReportsOption, ...sourceOptions];
  }, [reportSourcesUsed]);

  return (
    <div className={`w-full mb-4 ${className}`}>
      <div className="mb-2">
        <label className="text-sm font-medium text-gray-700 block">
          Filtrar por fonte do relatório:
        </label>
      </div>
      <Select
        options={filterOptions}
        value={selectedFilterId}
        onChange={onFilterChange}
        placeholder="Selecionar filtro..."
        className="w-full"
      />
    </div>
  );
};

export default CombinadoFilter;