import { Button, Separator, Text, ModalClose } from "@snap/design-system";

export interface ConsultasInfoDialogContentProps {
}

export function ConsultasInfoDialogContent({ }: ConsultasInfoDialogContentProps) {
  return (
    <div className="space-y-4 text-neutral-200">
      <p className="text-left font-bold">
        As consultas mensais são compartilhadas entre todos os usuários da{" "}
        <span className="font-bold text-accent">organização</span>.
      </p>
      <p className="font-bold">
        O administrador pode definir um limite individual para cada usuário, mas o saldo real
        disponível também depende do total restante da organização. Ou seja, mesmo que você tenha
        uma cota individual de 100 consultas, se a organização tiver apenas 10 consultas restantes
        no mês, você verá 10 como seu saldo disponível.
      </p>
      <Separator className="my-2 border-border" />
      <Text variant="body-md" className="text-left mt-1 font-bold">
        Exemplo:
      </Text>
      <ul className="list-disc list-inside ml-4">
        <li>
          <strong>Organização possui um total de 100 consultas/mês.</strong>
        </li>
        <li>
          <strong>Cenário A - Cotas não definidas:</strong> Todos os usuários compartilham o mesmo
          saldo. Se outro usuário realizar uma consulta, seu saldo também será reduzido — de 100
          para 99, por exemplo.
        </li>
        <li>
          <strong>Cenário B - Cotas definidas por usuário:</strong> Cada usuário tem uma cota fixa
          (ex: 10 consultas para cada um dos 10 usuários). No entanto, seu saldo individual nunca
          será maior do que o total restante da organização. Se sua cota for 10, mas a organização
          tiver apenas 3 consultas restantes, seu saldo será 3.
        </li>
      </ul>
    </div>
  );
}

export function ConsultasInfoDialogFooter() {
  return (
    <ModalClose>
      <Button>Fechar</Button>
    </ModalClose>
  );
}

export const ConsultasInfoDialog = {
  Content: ConsultasInfoDialogContent,
  Footer: ConsultasInfoDialogFooter,
};
