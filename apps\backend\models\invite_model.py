from sqlalchemy import Column, ForeignKey, DateTime, Integer, Text, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB, ENUM as PGEnum

from models.base import Base
from models.enums import invite_status, invite_type


class Invite(Base):
    __tablename__ = 'invite'
    __table_args__ = (
        Index("idx_invite_type_invite", "type_invite"),
        Index("idx_invite_report_types", "report_types", postgresql_using='gin'),
        {"schema": "reports"}
    )

    invite_id = Column(PostgresUUID, primary_key=True, server_default=func.gen_random_uuid())
    user_sender_id = Column(PostgresUUID, ForeignKey("reports.users.user_id"), nullable=False, index=True)
    organization_id = Column(PostgresUUID, ForeignKey("reports.organizations.organization_id"), nullable=False, index=True)

    status_invite = Column(
        PGEnum(invite_status, name="invite_status", schema="reports", create_type=True),
        nullable=False,
        server_default='enviado',
        index=True
    )

    type_invite = Column(
        PGEnum(invite_type, name="invite_type", schema="reports", create_type=True),
        nullable=False,
        server_default='investigador',
        index=True
    )

    report_types = Column(JSONB, nullable=False)
    credits_sent = Column(Integer, nullable=False, server_default='0')
    sent_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now(), index=True)
    email_invited = Column(Text, nullable=False, index=True)

    # Relationships
    user_sender = relationship("Users", back_populates="sent_invites")
    organization = relationship("Organizations", back_populates="invites")

    def __repr__(self):
        return f"<Invite(invite_id={self.invite_id}, email_invited={self.email_invited}, status_invite={self.status_invite})>"
