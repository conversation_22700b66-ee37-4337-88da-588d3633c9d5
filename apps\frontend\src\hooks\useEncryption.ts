import { useCallback } from "react";
import { CRYPTO_SERVICE, CryptoResult } from "~/workers/crypto";
import { EncryptedPayload } from "~/types/global";
import { encryptNgramsDeterministic } from "~/helpers/encryption.helper";
import { usePassword, useVerifiers } from "~/store/credentials";
import { encryptionWorker } from "~/services/workers/encryptionWorker.service";

export function base64ToUint8Array(base64: string) {
  const binary = atob(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes;
}

export const useEncryption = () => {
  const secretKey = usePassword();
  const verifiers = useVerifiers();

  const deriveKey = useCallback(async (password?: string): Promise<Uint8Array> => {
    // Caso seja a senha inserida pelo usuário, derivar a chave a partir dela
    if (password) {
      if (!verifiers.salt) {
        throw new Error("Salt is missing");
      }
      const passwordBytes = new TextEncoder().encode(password);
      const salt = new TextEncoder().encode(verifiers.salt);
      const { key } = await CRYPTO_SERVICE.deriveKey(passwordBytes, salt);
      return key;
    }

    // Caso já esteja usando uma chave persistida
    const secretKeyBytes = base64ToUint8Array(secretKey || "");
    if (!secretKeyBytes || secretKeyBytes.length === 0) {
      throw new Error("Secret key is missing");
    }

    return secretKeyBytes;
  }, [secretKey, verifiers.salt]);

  // const encryptData = useCallback(
  //   async (data: any, password?: string | null): Promise<CryptoResult<EncryptedPayload>> => {
  //     try {
  //       const derivedKey = base64ToUint8Array(password || "");
  //       // const key = await deriveKey(password);
  //       const plaintext = new TextEncoder().encode(JSON.stringify(data));
  //       const { encrypted, iv } = await CRYPTO_SERVICE.encrypt(derivedKey, plaintext);

  //       return {
  //         success: true,
  //         data: {
  //           encrypted,
  //           iv,
  //         },
  //       };
  //     } catch (error) {
  //       console.error("Encryption error:", error);
  //       return {
  //         success: false,
  //         error:
  //           error instanceof Error ? error.message : "Unknown encryption error",
  //       };
  //     }
  //   },
  //   [deriveKey]
  // );

  const _encryptData = useCallback(
    async (data: any, rawKey: ArrayBuffer): Promise<CryptoResult<EncryptedPayload>> => {
      try {
        const encryptedData = await encryptionWorker.encrypt(data, rawKey);
        return {
          success: true,
          data: encryptedData as EncryptedPayload,
        };
      } catch (error) {
        console.error("Encryption error:", error);
        return {
          success: false,
          error:
            error instanceof Error ? error.message : "Unknown encryption error",
        };
      }
    },
    [deriveKey]
  );

  const decryptData = useCallback(
    async (encryptedData: EncryptedPayload, password?: string): Promise<CryptoResult<any>> => {
      try {
        // 1. Base64-decode ciphertext and IV to bytes.
        const ciphertext = base64ToUint8Array(encryptedData.encrypted);
        const iv = base64ToUint8Array(encryptedData.iv);

        // 2. Decrypt with the same 32-byte key and 12-byte IV.
        const key = await deriveKey(password);
        const { decrypted } = await CRYPTO_SERVICE.decrypt(key, iv, ciphertext);
        // console log decrypted type and content

        return {
          success: true,
          data: decrypted,
        };
      } catch (error) {
        console.error("Decryption error:", error);
        return {
          success: false,
          error:
            error instanceof Error ? error.message : "Unknown decryption error",
        };
      }
    },
    [deriveKey]
  );

  const encryptNgrams = useCallback(
    async (
      reportObj: Record<string, any>,
      password?: string
    ): Promise<Record<string, string[]>> => {
      const rawKey = await deriveKey(password);
      const rawArrayBuffer = rawKey.buffer.slice(
        rawKey.byteOffset,
        rawKey.byteOffset + rawKey.byteLength
      ) as ArrayBuffer;
      return encryptNgramsDeterministic(rawArrayBuffer, reportObj);
    },
    [deriveKey]
  );

  return {
    // encryptData,
    _encryptData,
    decryptData,
    encryptNgrams,
    deriveKey,
  };
};
