import { <PERSON><PERSON><PERSON> } from 'puppeteer';
import * as logger from '../utils/logger';
import { GenerateCoverHtml } from './generateCoverHtml';
import { IGenerateCoverPDFInput, ReportMetadata } from '../global';

interface IGenerateCoverPDFWithBrowserInput {
  browserRef: Browser;
  metadata: ReportMetadata;
  should_print_snap_logo?: boolean;
  organization_logo?: string;
}

export const CreateGenerateCoverPDF = (browserRef: Browser) => {
  const _state = Object.freeze({
    browser: browserRef
  });
  const { browser } = _state

  if (!browser) {
    logger.error('Browser not initialized, cannot generate PDF');
    throw new Error('Browser not initialized');
  }

  return async ({
    metadata,
    should_print_snap_logo,
    organization_logo
  }: IGenerateCoverPDFInput): Promise<Uint8Array<ArrayBufferLike>> => {
    try {
      const page = await browser.newPage();
      logger.info('Starting cover PDF generation process');
      const startTime = Date.now();

      const fullHtml = await GenerateCoverHtml({
        metadata,
        organization_logo,
        should_print_snap_logo
      });
      logger.debug('Html created for cover');

      await page.setViewport({ width: 794, height: 1123 });
      logger.debug('Viewport set to A4 dimensions for cover');

      await page.setContent(fullHtml, {
        waitUntil: ['domcontentloaded'],
      });
      logger.debug('content set for cover');

      const pdfBuffer = await page.pdf({
        format: 'A4',
        displayHeaderFooter: false,
        printBackground: true,
        margin: { top: '0', right: '0', bottom: '0', left: '0' },
        preferCSSPageSize: true,
        timeout: 30000,
      });
      await page.close();
      const generationTime = Date.now() - startTime;
      logger.info('Cover PDF generation completed', {
        duration: generationTime,
        size: pdfBuffer.length
      });

      return pdfBuffer;

    } catch (err) {
      logger.error('Cover PDF generation error', {
        error: err as Error
      });
      throw new Error((err as Error).message);
    }
  }
}

export async function GenerateCoverPdf({
  browserRef: browser,
  metadata,
  should_print_snap_logo,
  organization_logo
}: IGenerateCoverPDFWithBrowserInput): Promise<Uint8Array<ArrayBufferLike>> {
  if (!browser) {
    logger.error('Browser not initialized, cannot generate cover PDF');
    throw new Error('Browser not initialized');
  }

  try {
    logger.info('Starting cover PDF generation process');
    const startTime = Date.now();

    const fullHtml = await GenerateCoverHtml({
      metadata,
      organization_logo,
      should_print_snap_logo
    });

    const page = await browser.newPage();
    logger.debug('New browser page created for cover');

    await page.setViewport({ width: 794, height: 1123 });
    logger.debug('Viewport set to A4 dimensions for cover');

    await page.setContent(fullHtml, {
      waitUntil: ['domcontentloaded', 'networkidle0'],
      timeout: 300000,
    });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      displayHeaderFooter: false,
      printBackground: true,
      margin: { top: '0', right: '0', bottom: '0', left: '0' },
      preferCSSPageSize: true,
      timeout: 30000,
    });

    await page.close();

    const generationTime = Date.now() - startTime;
    logger.info('Cover PDF generation completed', {
      duration: generationTime,
      size: pdfBuffer.length
    });

    return pdfBuffer;

  } catch (err) {
    logger.error('Cover PDF generation error', {
      error: err as Error
    });
    throw new Error((err as Error).message);
  }
}
