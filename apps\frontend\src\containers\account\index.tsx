import { Settings } from "lucide-react";
import { useEffect } from "react";
import { Navigate, useOutletContext, useLocation } from "react-router";
import { OutletContextType } from "~/types/global";
import { useUserData } from "~/store/userStore";
import { useUserInvitesActions, useUserInvitesFilters } from "~/store/userInvitesStore";
import { useUserLogsActions, useUserLogsFilters } from "~/store/userLogsStore";
import { useUserCRUD } from "~/hooks/useUserCRUD";
import UserAccountContainer from "./UserAccountContainer";

const AccountContainer = () => {
  const location = useLocation();
  const { setBreadcrumbs } = useOutletContext<OutletContextType>();
  const userData = useUserData();
  const { setInvitesList, setTotalItems } = useUserInvitesActions();
  const filters = useUserInvitesFilters();
  const { setLogsList: setUserLogsList, setTotalItems: setUserLogsTotalItems } = useUserLogsActions();
  const userLogsFilters = useUserLogsFilters();
  const { invitesQuery, userLogsQuery } = useUserCRUD();
  const { data: invitesData, isFetched } = invitesQuery(filters);
  const { data: logsData, isFetched: isLogsFetched } = userLogsQuery(userLogsFilters);
  const isSupport = location.pathname === '/conta/suporte';

  useEffect(() => {
    setBreadcrumbs([
      {
        title: isSupport ? "Suporte" : "Configurações",
        icon: <Settings size={16} className="text-primary" />,
      },
    ]);

    return () => {
      setBreadcrumbs([]);
    };
  }, [location.pathname, isSupport, setBreadcrumbs]);

  useEffect(() => {
    if (isFetched && invitesData) {
      setInvitesList(invitesData.data);
      setTotalItems(invitesData.pagination?.total_items || invitesData.data.length);
    }
  }, [isFetched, invitesData, setInvitesList, setTotalItems]);

  useEffect(() => {
    if (isLogsFetched && logsData) {
      setUserLogsList(logsData.data);
      setUserLogsTotalItems(logsData?.pagination?.total_items || logsData.data.length);
    }
  }, [isLogsFetched, logsData, setUserLogsList, setUserLogsTotalItems]);



  if (!userData) {
    return <Navigate to="/login" replace />;
  }

  return (
    <UserAccountContainer />
  )
}

export default AccountContainer