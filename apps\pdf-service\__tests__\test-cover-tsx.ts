import React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { Cover } from '../components/Cover';
import { Document } from '../components/pdf-components';
import { ReportMetadata } from '../global';

// Mock data for testing the cover
const mockMetadata: ReportMetadata = {
  report_id: "test-123",
  report_status: "completed",
  report_type: "cpf",
  report_search_args: {
    cpf: "123.456.789-00",
    telefone: "(11) 98765-4321"
  },
  report_name: "Investigação caso 'perigoso'",
  creation_at: "2025-01-24T10:00:00Z",
  modified_at: "2025-01-24T10:30:00Z",
  subject_name: "<PERSON>",
  subject_mother_name: "<PERSON>",
  subject_age: 35,
  subject_sex: "M"
};

async function testCoverTSX() {
  console.log('🚀 Starting Cover.tsx test...');

  let browser;
  try {
    // Launch browser
    browser = await puppeteer.launch({
      headless: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    console.log('✅ Browser launched');

    // Generate cover HTML using the actual Cover component wrapped in Document
    const coverComponent = React.createElement(
      Document,
      {},
      React.createElement(Cover, { metadata: mockMetadata })
    );
    const coverHtml = renderToStaticMarkup(coverComponent);

    const fullHtml = `
      <!DOCTYPE html>
      <html lang="pt-br">
      <head>
        <meta charset="UTF-8">
        <title>Cover TSX Test</title>
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          body {
            font-family: Arial, sans-serif;
            width: 100%;
            height: 100vh;
          }
        </style>
      </head>
      <body>
        ${coverHtml}
      </body>
      </html>
    `.trim();

    console.log('✅ Cover HTML generated using Cover.tsx');

    // Create new page
    const page = await browser.newPage();
    await page.setViewport({ width: 794, height: 1123 });

    // Set content
    await page.setContent(fullHtml, {
      waitUntil: ['domcontentloaded', 'networkidle0'],
      timeout: 30000,
    });

    console.log('✅ Page content loaded');

    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      displayHeaderFooter: false,
      printBackground: true,
      margin: { top: '0', right: '0', bottom: '0', left: '0' },
      preferCSSPageSize: true,
    });

    console.log('✅ PDF generated');

    // Save PDF to file
    const outputPath = path.join(__dirname, 'test-cover-tsx-output.pdf');
    fs.writeFileSync(outputPath, pdfBuffer);

    console.log(`✅ Cover PDF saved to: ${outputPath}`);
    console.log(`📄 File size: ${(pdfBuffer.length / 1024).toFixed(2)} KB`);

    await page.close();

  } catch (error) {
    console.error('❌ Error:', error instanceof Error ? error.message : String(error));
    throw error;
  } finally {
    if (browser) {
      await browser.close();
      console.log('✅ Browser closed');
    }
  }
}

// Run the test
if (require.main === module) {
  testCoverTSX()
    .then(() => {
      console.log('🎉 Cover.tsx test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Cover.tsx test failed:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    });
}

export { testCoverTSX };