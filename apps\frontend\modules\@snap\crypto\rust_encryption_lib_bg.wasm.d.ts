/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export const derive_key: (a: number, b: number, c: number, d: number, e: number, f: number, g: number) => [number, number, number, number];
export const aes_gcm_encrypt: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number) => [number, number, number, number];
export const aes_gcm_decrypt: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number) => [number, number, number, number];
export const hmac_sha256: (a: number, b: number, c: number, d: number) => [number, number, number, number];
export const init_panic_hook: () => void;
export const __wbindgen_free: (a: number, b: number, c: number) => void;
export const __wbindgen_malloc: (a: number, b: number) => number;
export const __wbindgen_realloc: (a: number, b: number, c: number, d: number) => number;
export const __wbindgen_export_3: WebAssembly.Table;
export const __externref_table_dealloc: (a: number) => void;
export const __wbindgen_start: () => void;
