# REGRAS RELATÓRIO RELAÇÕES

## Seções existentes para o tipo de relatório "CNPJ" suas respectivas fontes de dados:

- Relações
  - Fontes de dados: "SNAP"

## SEÇÃO: "Relações"

**Regras para processamento de dados:**

- Buscar no resultado da primeira chave **"empresa"** ou **"pessoa"** o primeiro objeto da lista, e ir criando os objetos  subsequentes de acordo com as outras lista de objetos aninhadas.

**Regras para a formatação da seção:**

- Cada objeto da seção precisa conter a chave **"tipo"** com o valor **"empresa"** ou **"pessoa"**.

- Caso a propriedade **"tipo"** tenha o valor **"empresa"**, deve ser construído um objeto com as seguintes propriedades:
  - **"tipo":** "empresa"
  - **"razao social":** {...} - objeto com as propriedades do campo "razao social"
  - **"label default key":** {...} - objeto com as propriedades do campo "label default key"
  - **"detalhes":** {...} - objeto com as propriedades do campo "detalhes"

- Caso a propriedade **"tipo"** tenha o valor **"pessoa"**, deve ser construído um objeto com as seguintes propriedades:
  - **"tipo":** "pessoa"
  - **"full name":** {...} - objeto com as propriedades do campo "full name"
  - **"label default key":** {...} - objeto com as propriedades do campo "label default key"
  - **"detalhes":** {...} - objeto com as propriedades do campo "detalhes"

- O objeto que constitui a seção **"Relações"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Relações"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["SNAP", "CadastroNacionalPJ"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- tipo
- razao social
- full name
- label default key
- detalhes

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
[
  {
    "empresa": [
      {
        "razao social": "TECHBIZ FORENSE DIGITAL LTDA",
        "cnpj": "05757597000137",
        "pessoa": [
          {
            "full name": "GIOVANI THIBAU CHRISTOFARO",
            "cpf": "68000367653",
            "label default key": "socio",
            "pessoa": [
              {
                "full name": "VALDENIZ DOS SANTOS COSTA",
                "cpf": "05129421612",
                "label default key": "outros contatos",
                "pessoa": [
                  {
                    "full name": "RUBIA MARA ALVES",
                    "cpf": "09585332612",
                    "label default key": "outros contatos",
                    "empresa": [
                      {
                        "razao social": "ADM TELEMIG CELULAR DEMAIS",
                        "cnpj": "02320739000106",
                        "label default key": "vinculo empregaticio",
                        "pessoa": [
                          {
                            "full name": "JUSSARA MARQUEZINI FRANCA SPATARA",
                            "cpf": "04794491638",
                            "label default key": "vinculo empregaticio",
                            "pessoa": [
                              {
                                "full name": "BRUNO MARQUEZINI FRANCA",
                                "cpf": "00533714613",
                                "label default key": "parente IRMA(O)",
                                "empresa": [
                                  {
                                    "razao social": "BRUNO MARQUEZINI FRANCA 00533714613",
                                    "cnpj": "40240344000192",
                                    "label default key": "socio"
                                  }
                                ]
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
]
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Relações",
        "subtitle": "Dados consultados na API SNAP.",
        "subsection": "",
        "is_deleted": false,
        "sources": [
            "SNAP"
        ],
        "data_count": 8,
        "data": [
            {
                "tipo": "empresa",
                "razao social": {
                    "value": "TECHBIZ FORENSE DIGITAL LTDA",
                    "label": "Razão Social",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "label default key": {
                    "value": "",
                    "label": "Vínculo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "detalhes": {
                    "cnpj": {
                        "value": "05.757.597/0001-37",
                        "label": "CNPJ",
                        "source": [
                            "SNAP"
                        ],
                        "is_deleted": false
                    }
                }
            },
            {
                "tipo": "pessoa",
                "full name": {
                    "value": "GIOVANI THIBAU CHRISTOFARO",
                    "label": "Nome Completo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "label default key": {
                    "value": "socio",
                    "label": "Vínculo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "detalhes": {
                    "cpf": {
                        "value": "680.000.367-65",
                        "label": "CPF",
                        "source": [
                            "SNAP"
                        ],
                        "is_deleted": false
                    }
                }
            },
            {
                "tipo": "pessoa",
                "full name": {
                    "value": "VALDENIZ DOS SANTOS COSTA",
                    "label": "Nome Completo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "label default key": {
                    "value": "outros contatos",
                    "label": "Vínculo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "detalhes": {
                    "cpf": {
                        "value": "051.294.216-12",
                        "label": "CPF",
                        "source": [
                            "SNAP"
                        ],
                        "is_deleted": false
                    }
                }
            },
            {
                "tipo": "pessoa",
                "full name": {
                    "value": "RUBIA MARA ALVES",
                    "label": "Nome Completo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "label default key": {
                    "value": "outros contatos",
                    "label": "Vínculo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "detalhes": {
                    "cpf": {
                        "value": "095.853.326-12",
                        "label": "CPF",
                        "source": [
                            "SNAP"
                        ],
                        "is_deleted": false
                    }
                }
            },
            {
                "tipo": "empresa",
                "razao social": {
                    "value": "ADM TELEMIG CELULAR DEMAIS",
                    "label": "Razão Social",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "label default key": {
                    "value": "vinculo empregaticio",
                    "label": "Vínculo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "detalhes": {
                    "cnpj": {
                        "value": "02.320.739/0001-06",
                        "label": "CNPJ",
                        "source": [
                            "SNAP"
                        ],
                        "is_deleted": false
                    }
                }
            },
            {
                "tipo": "pessoa",
                "full name": {
                    "value": "JUSSARA MARQUEZINI FRANCA SPATARA",
                    "label": "Nome Completo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "label default key": {
                    "value": "vinculo empregaticio",
                    "label": "Vínculo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "detalhes": {
                    "cpf": {
                        "value": "047.944.916-38",
                        "label": "CPF",
                        "source": [
                            "SNAP"
                        ],
                        "is_deleted": false
                    }
                }
            },
            {
                "tipo": "pessoa",
                "full name": {
                    "value": "BRUNO MARQUEZINI FRANCA",
                    "label": "Nome Completo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "label default key": {
                    "value": "parente IRMA(O)",
                    "label": "Vínculo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "detalhes": {
                    "cpf": {
                        "value": "005.337.146-13",
                        "label": "CPF",
                        "source": [
                            "SNAP"
                        ],
                        "is_deleted": false
                    }
                }
            },
            {
                "tipo": "empresa",
                "razao social": {
                    "value": "BRUNO MARQUEZINI FRANCA 00533714613",
                    "label": "Razão Social",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "label default key": {
                    "value": "socio",
                    "label": "Vínculo",
                    "source": [
                        "SNAP"
                    ],
                    "is_deleted": false
                },
                "detalhes": {
                    "cnpj": {
                        "value": "40.240.344/0001-92",
                        "label": "CNPJ",
                        "source": [
                            "SNAP"
                        ],
                        "is_deleted": false
                    }
                }
            }
        ]
    }
]
```
</div> 
</div>