import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { VinculoEmpregaticio } from "../../model/VinculosEmpregaticios";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { formatFieldValue, parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { ItemSeparator } from "../components/ItemSeparator";

export function useRenderVinculosEmpregaticios(
  sectionTitle: string
): ArrayRenderStrategy<VinculoEmpregaticio> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  // pull our section once, then memoize an entry→index map:
  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<VinculoEmpregaticio, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  // include‐flag helper
  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  // Test functions for individual keys
  const testEmpresaPagadoraDeleted = (e: any) => e.empresa_pagadora?.is_deleted === true;
  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted === true)
      : false;

  const testEntryDeleted = (entry: any): boolean => {
    const isEmpresaPagadoraDeleted = testEmpresaPagadoraDeleted(entry);
    const areDetalhesDeleted = testDetalhesDeleted(entry);

    return isEmpresaPagadoraDeleted && areDetalhesDeleted;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  // Data count calculation function for VinculosEmpregaticios section
  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      // Count non-deleted entries (whole objects)
      // An entry is considered deleted only when ALL its parts are deleted
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  };

  const formatByKey: Record<
    string,
    (entry?: VinculoEmpregaticio) => React.ReactElement | null
  > = {
    empresa_pagadora: (entry) => {
      if (!entry?.empresa_pagadora || !includeKey(entry.empresa_pagadora.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer cols={1} key={`empresa_pagadora-${idx}`}>
          <CustomGridItem
            fullWidth
            className="mb-6"
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === idx && e.empresa_pagadora) {
                    e.empresa_pagadora.is_deleted = !e.empresa_pagadora.is_deleted;

                    // Cascading deletion logic: when empresa_pagadora is deleted, delete all other fields
                    // When empresa_pagadora is restored, restore all other fields
                    const targetDeletedState = e.empresa_pagadora.is_deleted;

                    // Apply to all detalhes
                    if (e.detalhes) {
                      Object.values(e.detalhes).forEach((detalhe: any) => {
                        if (detalhe) detalhe.is_deleted = targetDeletedState;
                      });
                    }
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={entry.empresa_pagadora.label.toUpperCase()}
              colorClass="bg-primary"
              labelTextClass="text-accent"
              value={parseValue(formatFieldValue(entry.empresa_pagadora.value))}
              tooltip={renderSourceTooltip(entry.empresa_pagadora.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    detalhes: (entry) => {
      if (!entry?.detalhes) return null;
      const subs = Object.entries(entry.detalhes).filter(([, v]) =>
        includeKey((v as any).is_deleted)
      );
      if (!subs.length) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer
          cols={2}
          columnFirst
          key={`detalhes-${idx}`}
          className="mb-6"
        >
          {subs.map(([fieldKey, val]) => (
            <CustomGridItem
              key={fieldKey}
              cols={1}
              onToggleField={() =>
                actions.updateSectionEntries!(
                  sectionTitle,
                  (e: any, i?: number) => {
                    if (i === idx) {
                      const d = e.detalhes?.[fieldKey];
                      if (d) d.is_deleted = !d.is_deleted;
                    }
                  },
                  testEntryDeleted,
                  testSectionDeleted,
                  calculateDataCount
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
                value={parseValue(formatFieldValue((val as any).value))}
                tooltip={renderSourceTooltip((val as any).source)}
              />
            </CustomGridItem>
          ))}
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof VinculoEmpregaticio>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: VinculoEmpregaticio): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof VinculoEmpregaticio>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Vínculos Empregatícios] Chaves inválidas:", keys);
    }

    const orderedKeys: Array<keyof VinculoEmpregaticio> = [
      'empresa_pagadora',
      'detalhes'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    return filteredKeys
      .map((chave) => formatByKey[chave]?.(entry))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: VinculoEmpregaticio[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Vínculos Empregatícios] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (isTrash) {
        // In trash mode, show entries that have ANY deleted field
        return testEmpresaPagadoraDeleted(entry) ||
          testDetalhesDeleted(entry) ||
          (entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true));
      } else {
        // In normal mode, show entries that are NOT completely deleted
        const isCompletelyDeleted = testEntryDeleted(entry);
        return !isCompletelyDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (filteredData.length > 1) {
        const isLastItem = index === filteredData.length - 1;
        allElements.push(
          <div
            key={`vinculo-${index}`}
            className={`relative mb-6 pb-1 ${isLastItem ? '!mb-0 !pb-0' : ''}`}
          >
            {elements}
            {!isLastItem && <ItemSeparator />}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Marca todos os campos como deletado/restaurado
        if (entry.empresa_pagadora) {
          entry.empresa_pagadora.is_deleted = targetDeletedState;
        }
        if (entry.detalhes) {
          Object.values(entry.detalhes).forEach((detalhe: any) => {
            if (detalhe) detalhe.is_deleted = targetDeletedState;
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
