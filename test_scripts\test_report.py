from seleniumbase import SB

GOOGLE_ACCOUNT = "guibrabelo"
GOOGLE_PASSWORD = "PREENCHER"
REPORTS_PASSWORD = 'treta@Treta123'

url = "https://localhost"

telefones = [
'***********',
'***********',
'***********',
'***********',
'***********'
]

emails = [
'<EMAIL>',
'<EMAIL>',
'<EMAIL>',
'<EMAIL>'
]

cpfs = [
'***********',
'***********',
'***********',
'***********',
'***********'
]

cnpjs = [
'**************',
'**************'
]

class ReportTypes:
    EMAIL = 'EMAIL'
    CPF = 'CPF'
    TELEFONE = 'TELEFONE'
    CNPJ = 'CNPJ'

def generate_reports(report_type, sb):
    cicle_through = []

    if report_type == ReportTypes.EMAIL:
        cicle_through = emails
    elif report_type == ReportTypes.CNPJ:
        cicle_through = cnpjs
    elif report_type == ReportTypes.CPF:
        cicle_through = cpfs
    elif report_type == ReportTypes.TELEFONE:
        cicle_through = telefones

    for el in cicle_through:
        sb.wait_for_element('button.btn-add')
        sb.click('button.btn-add')

        # Wait for and click the combobox to open dropdown
        sb.wait_for_element('button[role="combobox"]')
        sb.click('button[role="combobox"]')

        # Wait for dropdown options to appear and select one
        sb.wait_for_element(f'div[role="option"]:contains("{report_type}")')
        sb.click(f'div[role="option"]:contains("{report_type}")')

        sb.wait_for_element('input[placeholder*="Digite"]')
        sb.type('input[placeholder*="Digite"]', f"{el}")
        sb.click('button[data-testid="button-confirm-create-report"]')


def generate_all_reports(sb):
    generate_reports(ReportTypes.TELEFONE, sb)
    generate_reports(ReportTypes.EMAIL, sb)
    generate_reports(ReportTypes.CNPJ, sb)
    generate_reports(ReportTypes.CPF, sb)

    return

def export_all_pdf(sb):
    sb.wait_for_element_present("css selector", "div.relative.group\\2f card", timeout=15)

    card_count = len(sb.find_elements("css selector", "div.relative.group\\2f card"))
    for i in range(card_count):
        # Re-find the card fresh each time to avoid stale references
        cards = sb.find_elements("css selector", "div.relative.group\\2f card")
        card = cards[i]

        if card.find_elements("css selector", "div.bg-neutral-700"):
            icon = card.find_element("css selector", "div.icon.size-12.px-2")

            sb.execute_script("arguments[0].scrollIntoView({block: 'center'});", icon)

            try:
                icon.click()
            except Exception as e:
                print(f"Click failed: {e}, retrying via JS")
                sb.execute_script("""
                                            var ev = new MouseEvent('click', {bubbles: true, cancelable: true, view: window});
                                            arguments[0].dispatchEvent(ev);
                                        """, icon)

            # Wait for the export option to be clickable
            sb.wait_for_element_clickable("xpath", "//p[normalize-space()='exportar pdf']", timeout=10)
            export = card.find_element("xpath", "//p[normalize-space()='exportar pdf']")
            sb.execute_script("arguments[0].scrollIntoView({block: 'center'});", export)

            try:
                export.click()
            except Exception as e:
                print(f"Click failed: {e}, retrying via JS")
                sb.execute_script("""
                                            var ev = new MouseEvent('click', {bubbles: true, cancelable: true, view: window});
                                            arguments[0].dispatchEvent(ev);
                                        """, export)

try:

    with SB(
            uc=True,
            test=True,
            chromium_arg=(
                    "--ignore-certificate-errors,"
                    "--ignore-ssl-errors,"
                    "--allow-running-insecure-content,"
                    "--no-first-run,"
                    "--disable-infobars,"
                    "--disable-notifications,"
                    "--disable-popup-blocking,"
                    "--disable-save-password-bubble,"
                    "--no-default-browser-check,"
                    "--disable-features=ChromeWhatsNewUI,ChromeTips,PromoBrowserSigninIntercept,"
                    "--disable-search-engine-choice-screen,"
                #
                    "--disable-features=VizDisplayCompositor,MediaRouter,"
                    "--disable-background-networking,"
                    "--disable-sync,"
                    "--disable-default-apps,"
                    "--no-service-autorun,"
                    "--disable-client-side-phishing-detection,"
                    "--disable-component-update,"
                    "--disable-background-timer-throttling,"
                    "--disable-renderer-backgrounding,"
                    "--disable-backgrounding-occluded-windows,"
                    "--disable-features=TranslateUI,"
                    "--disable-features=Translate,"
                    "--disable-features=ChromeSigninIntercept,"
                    "--disable-signin-promo,"
                    "--disable-features=SigninInterception,"
                    "--disable-features=SigninInterceptionV2,"
                    "--disable-features=DiceWebSigninInterception,"
                    "--guest,"
                    "--disable-ipc-flooding-protection"
            ),
        timeout_multiplier=3
    ) as sb:

        # login

        sb.uc_open_with_reconnect(url)

        sb.click('button[data-testid="button-login-google"] div')
        sb.click('button:contains("Visit Site")')
        sb.wait_for_element("#identifierId")
        sb.type("#identifierId", GOOGLE_ACCOUNT)
        sb.press_keys("#identifierId", "\n")  # Press Enter on the email field
        sb.wait_for_element('input[name="Passwd"]')
        sb.type('input[name="Passwd"]', GOOGLE_PASSWORD)
        sb.press_keys('input[name="Passwd"]', "\n")  # Press Enter to submit
        sb.press_keys("input#user-password", REPORTS_PASSWORD + "\n")



        # export_all_pdf(sb)
        generate_all_reports(sb)

        breakpoint()
except Exception as e:
    breakpoint()
