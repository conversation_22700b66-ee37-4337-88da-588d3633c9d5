import React from 'react';
import { CYGNITO_FONT } from '../../config/assets';

/**
 * Component that adds global print styles to ensure proper page breaks and formatting
 * when generating PDFs with puppeteer.
 */
export const PrintStyles: React.FC = () => {
  return (
    <style>
      {`
        /* Custom font face definition */
        @font-face {
          font-family: 'Cygnito';
          src: url('${CYGNITO_FONT}') format('opentype');
          font-weight: normal;
          font-style: normal;
          font-display: swap;
        }

        /* Define page size and reset counter for pages */
        @page {
          size: A4;
          margin: 0;
          counter-increment: page;
        }

        /* Counter initialization */
        body {
          counter-reset: page 0 total-pages;
        }

        @media print {
          html, body {
            width: 210mm;
            height: 297mm;
            margin: 0;
            padding: 0;
          }

          .pdf-document {
            width: 100%;
            height: 100%;
          }

          .pdf-page {
            page-break-after: always;
            break-after: page;
            page-break-inside: avoid;
            break-inside: avoid;
          }

          .pdf-view {
            page-break-inside: auto;
            break-inside: auto;
          }

          /* Force a page break before the element */
          .page-break-before {
            page-break-before: always;
            break-before: page;
          }

          /* Force a page break after the element */
          .page-break-after {
            page-break-after: always;
            break-after: page;
          }

          /* Avoid page breaks inside the element */
          .no-break-inside {
            page-break-inside: avoid;
            break-inside: avoid;
          }

          /* Keep with next element (no page break between) */
          .keep-with-next {
            page-break-after: avoid;
            break-after: avoid;
          }

          /* Page numbering */
          .pdf-page {
            counter-increment: page;
          }

          /* Style for page numbers in footer */
          .pdf-page-number::before {
            content: "Página " counter(page, decimal-leading-zero);
          }

          /* Add total pages count - this will be replaced by puppeteer during PDF generation */
          .pdf-page-number::after {
            content: " de XX";
          }

          /* Hide the original content of page number elements */
          .pdf-page-number * {
            display: none !important;
          }
        }
      `}
    </style>
  );
};

export default PrintStyles;
