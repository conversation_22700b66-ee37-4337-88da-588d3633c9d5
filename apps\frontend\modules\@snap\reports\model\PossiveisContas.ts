import { ValueWithSource } from "./ValueWithSource";
import { ReportSection } from "../global";

export type UpdaterFunction = (entry: PossivelConta, index?: number) => void;
export type TestFunction = (entry: PossivelConta) => boolean;
export type SectionTestFunction = (section: PossiveisContasSection) => boolean;
export type CalculateFunction = (section: PossiveisContasSection) => number;

export interface PossiveisContasSection extends ReportSection {
    data: PossivelConta[];
    data_count: number;
}

export interface TestFunctions {
    site: (e: PossivelConta) => boolean;
    detalhes: (e: PossivelConta) => boolean;
}

export interface _PossivelConta {
    aplicacao?: string;
    found?: string;
    entrada_cpf?: string;
    alerta?: string;
    tipo_alerta?: string;
    existe?: string;
}

export interface PossivelConta {
    site: ValueWithSource<_PossivelConta["aplicacao"]>;
    detalhes: Record<keyof Omit<_PossivelConta, "aplicacao">, ValueWithSource<_PossivelConta[keyof Omit<_PossivelConta, "aplicacao">]>>;
}
