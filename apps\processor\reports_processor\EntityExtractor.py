from collections import defaultdict
from dataclasses import dataclass, field
from functools import lru_cache
from typing import Union, List, Dict, Any, Callable, Optional

from unidecode import unidecode
from itertools import chain
import copy

from reports_processor.constants import ReportKeys, empregos_vinculo, logger, OtherResultDataTypes
from reports_processor.FormatFrontFormat import FinalFrontFormatBasic
from reports_processor.formatters.formatters import map_to_front_format, merge_entity_lists
from reports_processor.utils import compare_and_update_vinculo, is_front_formatted, collect_sources


@dataclass
class ExtractionResult:
    """Data class for extraction results"""
    data: Union[List[Dict], Dict[str, List[Dict]]]
    sources: List[str]

@dataclass
class VinculoConfig:
    """Configuration for vinculo extraction"""
    front_format: Optional[FinalFrontFormatBasic] = None
    extract_func: Optional[Callable] = None
    extract_type: Optional[str] = None
    filter_source_name: Optional[str] = None
    try_merge: bool = True
    try_merging_lists: bool = False
    skip_lists: bool = True
    item_callback: Optional[Callable] = None
    extra_data_callback: Optional[Callable] = None
    filter_base_data_callback: Optional[Callable] = None
    replace_data_processor: Optional[Callable] = None
    reverse: bool = False
    extract_type_starts_with: Optional[str] = None
    other_result_keys: List[OtherResultDataTypes] = field(default_factory=list)


class EntityExtractor:
    """Main class for entity extraction operations"""

    def __init__(self):
        self._extraction_cache = {}

    @staticmethod
    def filter_by_non_empty_indexes(source: List, *other_lists: List[List]) -> List:
        """Filter source list by non-empty indexes in other lists"""
        return [
            source[i]
            for i in range(len(source))
            if any(other_list[i] for other_list in other_lists)
        ]

    @lru_cache(maxsize=128)
    def _get_normalized_vinculo(self, vinculo: str) -> str:
        """Cache normalized vinculo strings for performance"""
        return unidecode(vinculo).lower() if vinculo else ""

    def extract_vinculos_campanha(
            self,
            data_dict: Dict[str, Dict],
            entity_type,
            search_value: str,
            config_obj: VinculoConfig
    ) -> ExtractionResult:
        """Extract campaign-related relationships"""

        if is_front_formatted(data_dict):
            return ExtractionResult([], [])
        reverse = config_obj.reverse
        filter_source_name = config_obj.filter_source_name

        # Configuration based on reverse flag
        config = self._get_campanha_config(reverse)

        base_data_enviada, base_data_recebida, sources = [], [], []

        # Collect data from all sources
        for source, base_dict in data_dict.items():
            sources.append(source)
            if filter_source_name and filter_source_name not in source:
                base_data_enviada.append([])
                base_data_recebida.append([])
                continue

            base_data_recebida.append(base_dict.get(config['extract_type_recebida'], []))
            base_data_enviada.append(base_dict.get(config['extract_type_enviada'], []))

        if not any(base_data_enviada + base_data_recebida):
            return ExtractionResult([], [])

        used_sources = self.filter_by_non_empty_indexes(sources, base_data_enviada, base_data_recebida)
        formatted_data = {}

        # Process enviada data
        self._process_campanha_data(
            base_data_enviada, sources, config['enviada_subtype'],
            config['enviadas_result'], formatted_data
        )

        # Process recebida data
        self._process_campanha_recebida_data(
            base_data_recebida, sources, config['recebida_subtypes'],
            config['recebidas_result'], formatted_data
        )

        return ExtractionResult(formatted_data, used_sources)

    def _get_campanha_config(self, reverse: bool) -> Dict[str, Any]:
        """Get configuration for campanha extraction"""
        base_config = {
            'enviada_subtype': ReportKeys.CANDIDATO,
            'recebida_subtypes': [ReportKeys.PESSOA, ReportKeys.EMPRESA],
            'extract_type_enviada': ReportKeys.PESSOA,
            'extract_type_recebida': ReportKeys.CANDIDATO,
        }

        if reverse:
            base_config.update({
                'enviadas_result': 'recebida',
                'recebidas_result': 'enviada'
            })
        else:
            base_config.update({
                'enviadas_result': 'enviada',
                'recebidas_result': 'recebida'
            })

        return base_config

    def _process_campanha_data(
            self,
            base_data: List[List],
            sources: List[str],
            subtype: str,
            result_key: str,
            formatted_data: Dict
    ):
        """Process campanha data for a specific type"""
        for idx, data in enumerate(base_data):
            base_front_data = [
                map_to_front_format(x, sources[idx], skip_lists=False)
                for item in data
                if subtype in item
                for x in item[subtype]
            ]
            formatted_data[result_key] = merge_entity_lists(
                formatted_data.get(result_key, []), base_front_data
            )

    def _process_campanha_recebida_data(
            self,
            base_data_recebida: List[List],
            sources: List[str],
            recebida_subtypes: List[str],
            result_key: str,
            formatted_data: Dict
    ):
        """Process recebida data for campanha"""
        for idx, data in enumerate(base_data_recebida):
            base_front_data = [
                map_to_front_format(x, sources[idx], skip_lists=False)
                for cur_entity in data
                for y in recebida_subtypes if y in cur_entity
                for x in cur_entity[y]
            ]
            formatted_data[result_key] = merge_entity_lists(
                formatted_data.get(result_key, []), base_front_data
            )

    def extract_vinculos_empregaticios(
            self,
            data_dict: Dict[str, Dict],
            entity_type,
            search_value: str,
            config_obj: VinculoConfig
    ) -> ExtractionResult:
        """Extract employment relationships"""

        sources, empresas, remuneracoes = [], [], []

        for source, base_dict in data_dict.items():
            empresas.append([
                x for x in base_dict.get(ReportKeys.EMPRESA, [])
                if compare_and_update_vinculo(x, empregos_vinculo)
            ])
            remuneracoes.append(base_dict.get(ReportKeys.REMUNERACAO, []))
            sources.append(source)

        if not any(empresas) and not any(remuneracoes):
            return ExtractionResult([], [])

        used_sources = self.filter_by_non_empty_indexes(sources, empresas, remuneracoes)
        formatted_remuneracoes, formatted_empresas = [], []

        for idx, source_data in enumerate(remuneracoes):
            processed_remuneracoes = self._process_remuneracoes_data(source_data, sources[idx])
            formatted_remuneracoes = merge_entity_lists(formatted_remuneracoes, processed_remuneracoes)

            base_empresa = []

            for em in empresas[idx]:
                vinculos = em.get(ReportKeys.VINCULO)

                if isinstance(vinculos, list) and all(isinstance(v, dict) for v in vinculos):
                    for vinculo in vinculos:
                        merged_em = {**em, **vinculo}  # shallow merge
                        base_empresa.append(
                            map_to_front_format(merged_em, sources[idx], skip_lists=True)
                        )
                else:
                    base_empresa.append(
                        map_to_front_format(em, sources[idx], skip_lists=True)
                    )

            formatted_empresas = merge_entity_lists(formatted_empresas, base_empresa)

        final_data = merge_entity_lists(formatted_empresas, formatted_remuneracoes)
        return ExtractionResult(final_data, used_sources)

    def _process_remuneracoes_data(self, source_data: List[Dict], source: str) -> List[Dict]:
        """Process remuneration data"""
        base_remuneracao = []
        for rm in source_data:
            if ReportKeys.EMPRESA not in rm or len(rm[ReportKeys.EMPRESA]) != 1:
                logger.warning(
                    f"[extract_vinculos_empregaticios] Invalid company data in remuneration: {rm}, skipping"
                )
                continue

            company_data = rm.pop(ReportKeys.EMPRESA)[0]
            rm.update(company_data)
            base_remuneracao.append(map_to_front_format(rm, source, skip_lists=True))

        return base_remuneracao

    def __original_extract_generic_get_data(self,
                                            original_data_dict: Dict[str, Dict],
                                            entity_type,
                                            search_value: str,
                                            config_obj: VinculoConfig
                                            ):
        base_data, sources = [], []
        use_keys = config_obj.other_result_keys if config_obj.other_result_keys else original_data_dict.keys()
        if len(use_keys) == 1:
            data_dict = original_data_dict[use_keys[0]]

        else:

            data_dict = defaultdict(lambda: defaultdict(list))

            for outer_key, d in original_data_dict.items():
                if outer_key not in use_keys:
                    continue
                for source, base_dict in d.items():
                    for k, v in base_dict.items():
                        data_dict[source][k].extend(copy.deepcopy(v))

            data_dict = dict(data_dict)

        # Collect data from sources
        for source, base_dict in data_dict.items():
            if config_obj.filter_source_name and config_obj.filter_source_name not in source:
                base_data.append([])
            elif config_obj.filter_base_data_callback and callable(config_obj.filter_base_data_callback):
                if config_obj.extract_type:
                    base_data.append(
                        config_obj.filter_base_data_callback(base_dict.get(config_obj.extract_type, []), entity_type,
                                                             search_value))
                if config_obj.extract_type_starts_with:
                    base_data.append([
                        config_obj.filter_base_data_callback({k: v}, entity_type, search_value) for k, v in
                        base_dict.items()
                        if k.startswith(config_obj.extract_type_starts_with)])
            else:
                if config_obj.extract_type:
                    extract_type_list = config_obj.extract_type if isinstance(config_obj.extract_type, list) else [
                        config_obj.extract_type]

                    cur_data = list(chain.from_iterable(base_dict.get(t, []) for t in extract_type_list))
                    base_data.append(cur_data)

                if config_obj.extract_type_starts_with:
                    base_data.append([
                        v for k, v in base_dict.items()
                        if k.startswith(config_obj.extract_type_starts_with)
                    ])
            sources.append(source)

        return base_data, sources

    def _extract_vinculos_genericos_get_data(self,
            original_data_dict: Dict[str, Dict],
            entity_type,
            search_value: str,
            config_obj: VinculoConfig):

        base_data, sources = [], []

        if OtherResultDataTypes.other in original_data_dict and OtherResultDataTypes.main in original_data_dict:
            return self.__original_extract_generic_get_data(original_data_dict, entity_type, search_value, config_obj)

        else:

            data_dict = original_data_dict

            # todo: filter source? filter base data?
            # Collect data from sources
            # for source, base_dict in data_dict.items():
            #     if config_obj.filter_source_name and config_obj.filter_source_name not in source:
            #         base_data.append([])
            #     elif config_obj.filter_base_data_callback and callable(config_obj.filter_base_data_callback):
            #         if config_obj.extract_type:
            #             base_data.append(
            #                 config_obj.filter_base_data_callback(base_dict.get(config_obj.extract_type, []), entity_type,
            #                                                      search_value))
            #         if config_obj.extract_type_starts_with:
            #             base_data.append([
            #                 config_obj.filter_base_data_callback({k: v}, entity_type, search_value) for k, v in
            #                 base_dict.items()
            #                 if k.startswith(config_obj.extract_type_starts_with)])
            #     else:

            if config_obj.extract_type:
                extract_type_list = config_obj.extract_type if isinstance(config_obj.extract_type, list) else [
                    config_obj.extract_type]

                cur_data = list(chain.from_iterable(data_dict.get(t, []) for t in extract_type_list))
                if cur_data:
                    base_data.append(cur_data)


            if config_obj.extract_type_starts_with:

                if config_obj.filter_base_data_callback and callable(config_obj.filter_base_data_callback):

                    base_data.append([
                                    config_obj.filter_base_data_callback({k: v}, entity_type, search_value) for k, v in
                                    data_dict.items()
                                    if k.startswith(config_obj.extract_type_starts_with)])

                else:
                    base_data.extend([
                        {k: v} for k, v in data_dict.items()
                        if k.startswith(config_obj.extract_type_starts_with)
                    ])

            elif base_data and config_obj.filter_base_data_callback and callable(config_obj.filter_base_data_callback):
                base_data = [config_obj.filter_base_data_callback(x, entity_type, search_value) for x in base_data]



            return base_data, sources

    def __original_extract_generic_format_data(self,
                                               base_data,
                                               sources,
                                               entity_type,
                                               search_value: str,
                                               config_obj: VinculoConfig):

        used_sources = self.filter_by_non_empty_indexes(sources, base_data)
        formatted_data = []
        extra_formatted_data = []

        if config_obj.replace_data_processor is not None and callable(config_obj.replace_data_processor):
            formatted_data = config_obj.replace_data_processor(base_data, sources)

        else:
            for idx, data in enumerate(base_data):
                # Process extra data if callback provided
                if config_obj.extra_data_callback and callable(config_obj.extra_data_callback):
                    extra_data = defaultdict(list)
                    for d in data:
                        for vinculo, items in config_obj.extra_data_callback(d, entity_type, search_value).items():
                            for item in items:
                                if item not in extra_data[vinculo]:
                                    extra_data[vinculo].append(item)

                    if extra_data:
                        extra_data_front = map_to_front_format(extra_data, sources[idx],
                                                               skip_lists=config_obj.skip_lists)
                        extra_formatted_data = merge_entity_lists(extra_formatted_data, extra_data_front)

                # Process main data
                base_front_data = []
                for d in data:
                    processed_item = config_obj.item_callback(d, entity_type,
                                                              search_value) if config_obj.item_callback else d
                    if type(processed_item) is list:
                        for item in processed_item:
                            mapped = map_to_front_format(item, sources[idx], skip_lists=config_obj.skip_lists)
                            if mapped:
                                base_front_data.append(mapped)
                    else:
                        mapped = map_to_front_format(processed_item, sources[idx], skip_lists=config_obj.skip_lists)
                        if mapped:
                            base_front_data.append(mapped)

                    if config_obj.try_merge:
                        formatted_data = merge_entity_lists(formatted_data, base_front_data)
                    else:
                        formatted_data.extend(base_front_data)

                    base_front_data = []

            if config_obj.try_merging_lists:
                if len(formatted_data) > 1:
                    base_merge = formatted_data[0]

                    for idx, item in enumerate(formatted_data[1:]):
                        for k, v in item.items():
                            if k in base_merge:
                                base_merge[k] = merge_entity_lists(base_merge[k], v)
                            else:
                                base_merge[k] = v
                    formatted_data = [base_merge]


            if extra_formatted_data:
                formatted_data = {'base': formatted_data, 'extra': extra_formatted_data}

        return ExtractionResult(formatted_data, used_sources)

    def extract_vinculos_genericos_format_data(self,
            base_data,
           sources,
            entity_type,
            search_value: str,
            config_obj: VinculoConfig):

        if not is_front_formatted(base_data):
            return self.__original_extract_generic_format_data(base_data, sources, entity_type, search_value,
                                                               config_obj)
        else:

            formatted_data = []
            extra_formatted_data = []
            used_sources = []

            # TODO?:
            if config_obj.replace_data_processor is not None and callable(config_obj.replace_data_processor):
                 formatted_data = []

            else:
                for idx, data in enumerate(base_data):
                    # Process extra data if callback provided
                    if config_obj.extra_data_callback and callable(config_obj.extra_data_callback):
                        breakpoint()
                        extra_data = defaultdict(list)
                        for d in data:
                            for vinculo, items in config_obj.extra_data_callback(d, entity_type, search_value).items():
                                for item in items:
                                    if item not in extra_data[vinculo]:
                                        extra_data[vinculo].append(item)

                        if extra_data:
                            extra_data_front = map_to_front_format(extra_data, sources[idx],
                                                                   skip_lists=config_obj.skip_lists)
                            extra_formatted_data = merge_entity_lists(extra_formatted_data, extra_data_front)

                    # Process main data
                    base_front_data = []
                    for d in data:
                        processed_item = config_obj.item_callback(d, entity_type, search_value) if config_obj.item_callback else d
                        # base_front_data.append(processed_item)

                        if type(processed_item) is list:
                            for item in processed_item:
                                # TODO: arrumar None como fonte
                                mapped = map_to_front_format(item, None, skip_lists=config_obj.skip_lists)
                                if mapped:
                                    base_front_data.append(mapped)
                        else:
                            # TODO: arrumar None como fonte
                            mapped = map_to_front_format(processed_item, None, skip_lists=config_obj.skip_lists)
                            if mapped:
                                base_front_data.append(mapped)

                        if config_obj.try_merge:
                            formatted_data = merge_entity_lists(formatted_data, base_front_data)
                        else:
                            formatted_data.extend(base_front_data)

                        base_front_data = []

                if config_obj.try_merging_lists:
                    if len(formatted_data) > 1:
                        base_merge = formatted_data[0]

                        for idx, item in enumerate(formatted_data[1:]):
                            for k, v in item.items():
                                if k in base_merge:
                                    base_merge[k] = merge_entity_lists(base_merge[k], v)
                                else:
                                    base_merge[k] = v
                        formatted_data = [base_merge]

                if extra_formatted_data:
                    formatted_data = {'base': formatted_data, 'extra': extra_formatted_data}

            used_sources = list(collect_sources(formatted_data))

            return ExtractionResult(formatted_data, used_sources)

    def extract_vinculos_genericos(
            self,
            original_data_dict: Dict[str, Dict],
            entity_type,
            search_value: str,
            config_obj: VinculoConfig

    ) -> ExtractionResult:
        """Generic extraction function with improved type safety and performance"""


        base_data, sources = self._extract_vinculos_genericos_get_data(original_data_dict, entity_type, search_value, config_obj)

        if not any(base_data):
            return ExtractionResult([], [])

        return self.extract_vinculos_genericos_format_data(base_data, sources, entity_type, search_value, config_obj)

