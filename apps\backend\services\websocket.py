import logging
logger = logging.getLogger(__name__)
from fastapi.encoders import jsonable_encoder

async def try_send_websocket(connection_manager, user_id: str, reports_id: str, payload: dict) -> bool:
    logger.info(f"[try_send_websocket][user{user_id}] Try sending websocket requestsnapid = {reports_id}.")
    websocket = connection_manager.get_connection(user_id)
    if websocket:
        try:
            await websocket.send_json(jsonable_encoder(payload))
            logger.info("[try_send_websocket][user(%s)] Sent websocket to user_id = %s and requestsnapid = %s.", user_id, user_id, reports_id)
            return True
        except Exception as e:
            logger.error("[try_send_websocket][user(%s)] Error sending websocket to user_id = %s and requestsnapid = %s: %s", user_id, user_id, reports_id, str(e))
            await connection_manager.disconnect(user_id)

    return False