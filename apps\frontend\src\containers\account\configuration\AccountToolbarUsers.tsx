import { Button, Input, Select } from '@snap/design-system'
import { Calendar, Search, SortAsc, X } from 'lucide-react';
import { useState } from 'react'

const AccountToolbarUsers = () => {
  const [selectedReportType, setSelectedReportType] = useState<string>("");
  const [selectedProfileType, setSelectedProfileType] = useState<string>("");

  // TODO - adicionar filtros de data
  // TODO - user store de filtros
  // TODO - adicionar botão de limpar filtros

  return (
    <div className="py-4 flex items-center gap-4 justify-between">
      <div className='flex flex-1 items-center gap-4'>

            <div className="relative w-full max-w-lg">
              <Input
                //ref={inputRef}
                //value={inputValue}
                placeholder="Buscas usuário"
                //onChange={handleChange}
                className="pr-10"
              //onKeyDown={handleKeyDown}
              />
              <button
                type="button"
                //onClick={inputValue ? clearInput : undefined}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground focus:outline-none"
              //aria-label={inputValue ? "Limpar busca" : "Buscar"}
              >
                {/* inputValue */ false ? (
                  <X size={18} />
                ) : (
                  <Search size={18} />
                )}
              </button>
            </div>
    
        <div className='flex gap-4'>
          <Select
            options={
              [
                { label: "TODOS OS TIPOS", value: "" },
                { label: "CPF", value: "cpf" },
                { label: "CNPJ", value: "cnpj" },
                { label: "EMAIL", value: "email" },
                { label: "TELEFONE", value: "telefone" }
              ]
            }
            value={selectedReportType}
            onChange={setSelectedReportType}
            placeholder="Selecionar tipo"
            data-testid="select-report-type"
            className="w-full"
          />
       
            <Select
              options={
                [
                  { label: "TODOS OS PERFIS", value: "" },
                  { label: "ADMINISTRADOR", value: "administrador" },
                  { label: "INVESTIGADOR", value: "investigador" }
                ]
              }
              value={selectedProfileType}
              onChange={setSelectedProfileType}
              placeholder="Selecionar perfil"
              data-testid="select-user-profile"
              className="w-full"
            />
         
        </div>
      </div>
      <div className="flex gap-1 items-center">
        <Button variant="ghost">
          <SortAsc size={32} />
        </Button>
      </div>
    </div>
  )
}

export default AccountToolbarUsers