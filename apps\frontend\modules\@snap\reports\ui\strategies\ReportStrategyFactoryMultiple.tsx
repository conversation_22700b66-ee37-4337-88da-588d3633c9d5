import { REPORT_SECTIONS } from "../../config/constants";
import { ReportType } from "../../global";
import { useLocation } from "react-router";
import { useRenderEmails } from "./NEW_renderEmails.strategy";
import { useRenderEnderecos } from "./NEW_renderEnderecos.strategy";
import { useRenderTelefonesArray } from "./NEW_renderTelefones.strategy";
import { useRenderPossiveisContas } from "./NEW_renderPossiveisContas.strategy";
import { useRenderParentes } from "./NEW_renderParentes.strategy";
import { useRenderRedesSociais } from "./NEW_renderRedesSociais.strategy";
import { useRenderImagens } from "./NEW_renderImagens.strategy";
import { useRenderNomeUsuarios } from "./NEW_renderNomeUsuarios.strategy";
import { useRenderOutrasUrls } from "./NEW_renderOutrasUrls.strategy";
import { useRenderVinculosEducacionais } from "./NEW_renderVinculosEducacionais.strategy";
import { useRenderSociedades } from "./NEW_renderSociedades.strategy";
import { useRenderPossiveisContatos } from "./NEW_renderPossiveisContatos.strategy";
import { useRenderContatosSalvos } from "./NEW_renderContatosSalvos.strategy";
import { useRenderDadosPessoais } from "./NEW_renderDadosPessoais.strategy";

export const useStrategyMapMultipleResults = () => {
  const location = useLocation();
  const reportType: ReportType | string = location.pathname.split("/")[2];

  // COMUM PARA TODOS OS TIPOS
  const common_base_map: Record<string, any> = {
    [REPORT_SECTIONS.dados_pessoais]: useRenderDadosPessoais(REPORT_SECTIONS.dados_pessoais),
    [REPORT_SECTIONS.telefones]: useRenderTelefonesArray(REPORT_SECTIONS.telefones),
    [REPORT_SECTIONS.emails]: useRenderEmails(REPORT_SECTIONS.emails),
    [REPORT_SECTIONS.enderecos]: useRenderEnderecos(REPORT_SECTIONS.enderecos),
  };

  // CPF, TELEFONE e EMAIL
  const cpf_telefone_email_map: Record<string, any> = {
    [REPORT_SECTIONS.parentes]: useRenderParentes(REPORT_SECTIONS.parentes),
    [REPORT_SECTIONS.imagens]: useRenderImagens(REPORT_SECTIONS.imagens),
    [REPORT_SECTIONS.nomes_usuario]: useRenderNomeUsuarios(REPORT_SECTIONS.nomes_usuario),
    [REPORT_SECTIONS.outras_urls]: useRenderOutrasUrls(REPORT_SECTIONS.outras_urls),
    [REPORT_SECTIONS.vinculos_educacionais]: useRenderVinculosEducacionais(REPORT_SECTIONS.vinculos_educacionais),
    [REPORT_SECTIONS.possiveis_contatos]: useRenderPossiveisContatos(REPORT_SECTIONS.possiveis_contatos),
    [REPORT_SECTIONS.possiveis_contas_em_sites]: useRenderPossiveisContas(REPORT_SECTIONS.possiveis_contas_em_sites),
    [REPORT_SECTIONS.perfis_redes_sociais]: useRenderRedesSociais(REPORT_SECTIONS.perfis_redes_sociais),
  };

  // TELEFONE e EMAIL
  const telefone_email_map: Record<string, any> = {
    [REPORT_SECTIONS.contatos_salvos]: useRenderContatosSalvos(REPORT_SECTIONS.contatos_salvos),
    [REPORT_SECTIONS.empresas_relacionadas]: useRenderSociedades(REPORT_SECTIONS.empresas_relacionadas),
  };

  switch (reportType) {
    case "telefone":
      return {
        ...common_base_map,
        ...cpf_telefone_email_map,
        ...telefone_email_map,
      };
    case "email":
      return {
        ...common_base_map,
        ...cpf_telefone_email_map,
        ...telefone_email_map,
      };
    default:
      return common_base_map;
  }
};