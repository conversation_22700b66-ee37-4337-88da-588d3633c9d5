"""
Automatic Database Initialization System

This module automatically detects if the database needs initialization (first run)
or migration (subsequent runs) and handles the appropriate database setup.
"""

import os
import logging
import asyncio
import subprocess
from pathlib import Path
from typing import Tuple, Optional, Dict, Any
from sqlalchemy import text, inspect
from sqlalchemy.exc import SQLAlchemyError, ProgrammingError
from database.db import engine, Base
from core.config import settings

logger = logging.getLogger(__name__)

class DatabaseInitializer:
    """Handles automatic database initialization and migration detection"""

    def __init__(self):
        self.engine = engine
        # Don't create inspector here - AsyncEngine doesn't support direct inspection
        # We'll use async connections with run_sync when we need to inspect

    async def auto_initialize_database(self, generate_migrations: bool = None) -> Dict[str, Any]:
        """
        Automatically initialize or migrate the database based on current state.

        Args:
            generate_migrations: Override for migration generation (None = use env var)

        Returns:
            Dictionary with initialization results and actions taken
        """
        logger.info("[DatabaseInitializer] Starting automatic database initialization...")

        result = {
            'is_first_run': False,
            'actions_taken': [],
            'migration_applied': False,
            'migration_generated': False,
            'tables_created': 0,
            'errors': [],
            'warnings': []
        }

        try:
            # Step 1: Check database connectivity
            logger.info("[DatabaseInitializer] Checking database connectivity...")
            if not await self._check_database_connectivity():
                result['errors'].append("Database connectivity failed")
                return result

            result['actions_taken'].append("Database connectivity verified")

            # Step 2: Check if this is first run or needs migration
            is_first_run, migration_needed = await self._analyze_database_state()
            result['is_first_run'] = is_first_run

            if is_first_run:
                logger.info("[DatabaseInitializer] 🚀 First run detected - creating initial schema...")
                await self._handle_first_run(result)

            elif migration_needed:
                logger.info("[DatabaseInitializer] 🔄 Migration needed - applying pending migrations...")
                await self._handle_migrations(result)

            else:
                logger.info("[DatabaseInitializer] ✅ Database is up to date - no action needed")
                result['actions_taken'].append("Database schema is current")

            # Step 3: Validate final state
            await self._validate_final_state(result)

            # Step 4: Run smart migration if enabled and not first run
            if not result['is_first_run']:
                await self._run_smart_migration(result, generate_migrations)

        except Exception as e:
            logger.error(f"[DatabaseInitializer] Critical error during initialization: {e}")
            result['errors'].append(f"Initialization failed: {str(e)}")

        # Log final results
        self._log_initialization_summary(result)
        return result

    async def _check_database_connectivity(self) -> bool:
        """Test database connectivity"""
        try:
            async with self.engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            logger.info("[DatabaseInitializer] ✅ Database connectivity confirmed")
            return True
        except Exception as e:
            logger.error(f"[DatabaseInitializer] ❌ Database connectivity failed: {e}")
            return False

    async def _analyze_database_state(self) -> Tuple[bool, bool]:
        """
        Analyze current database state to determine what action is needed.

        Returns:
            Tuple of (is_first_run, migration_needed)
        """
        try:
            async with self.engine.begin() as conn:
                # Check if reports schema exists
                schema_exists = await self._check_schema_exists(conn)

                if not schema_exists:
                    logger.info("[DatabaseInitializer] Reports schema not found - first run")
                    return True, False

                # Check if alembic version table exists
                alembic_version_exists = await self._check_alembic_version_table(conn)

                if not alembic_version_exists:
                    # Schema exists but no alembic - check if tables exist
                    tables_exist = await self._check_application_tables_exist(conn)

                    if tables_exist:
                        # Tables exist but no alembic tracking - mark initial migration as applied
                        logger.info("[DatabaseInitializer] Existing tables found without migration tracking")
                        await self._mark_initial_migration_applied()
                        return False, False
                    else:
                        # Empty schema - first run
                        return True, False

                # Check if migrations are needed
                migration_needed = await self._check_migrations_needed()
                return False, migration_needed

        except Exception as e:
            logger.error(f"[DatabaseInitializer] Error analyzing database state: {e}")
            # Default to first run for safety
            return True, False

    async def _check_schema_exists(self, conn) -> bool:
        """Check if reports schema exists"""
        try:
            result = await conn.execute(text(
                "SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'reports'"
            ))
            return result.fetchone() is not None
        except Exception as e:
            logger.warning(f"[DatabaseInitializer] Could not check schema existence: {e}")
            return False

    async def _check_alembic_version_table(self, conn) -> bool:
        """Check if alembic_version table exists"""
        try:
            result = await conn.execute(text(
                "SELECT table_name FROM information_schema.tables "
                "WHERE table_schema = 'reports' AND table_name = 'alembic_version'"
            ))
            return result.fetchone() is not None
        except Exception as e:
            logger.warning(f"[DatabaseInitializer] Could not check alembic_version table: {e}")
            return False

    async def _check_application_tables_exist(self, conn) -> bool:
        """Check if any application tables exist in reports schema"""
        try:
            result = await conn.execute(text(
                "SELECT COUNT(*) FROM information_schema.tables "
                "WHERE table_schema = 'reports' AND table_name NOT LIKE 'alembic%'"
            ))
            count = result.scalar()
            logger.info(f"[DatabaseInitializer] Found {count} application tables in reports schema")
            return count > 0
        except Exception as e:
            logger.warning(f"[DatabaseInitializer] Could not check application tables: {e}")
            return False

    async def _handle_first_run(self, result: Dict[str, Any]) -> None:
        """Handle first run initialization"""
        try:
            # Create reports schema if it doesn't exist
            await self._ensure_reports_schema()
            result['actions_taken'].append("Ensured reports schema exists")

            # Apply initial migration to establish baseline
            if await self._apply_initial_migration():
                result['migration_applied'] = True
                result['actions_taken'].append("Applied initial migration (001_initial_tables)")

                # Count created tables
                table_count = await self._count_application_tables()
                result['tables_created'] = table_count
                result['actions_taken'].append(f"Created {table_count} database tables")
            else:
                # Fallback to create_all if migration fails
                logger.warning("[DatabaseInitializer] Migration failed, using fallback create_all method")
                await self._fallback_create_tables()
                result['actions_taken'].append("Used fallback table creation method")
                result['warnings'].append("Migration system not available, used direct table creation")

        except Exception as e:
            logger.error(f"[DatabaseInitializer] Error in first run setup: {e}")
            result['errors'].append(f"First run setup failed: {str(e)}")

    async def _handle_migrations(self, result: Dict[str, Any]) -> None:
        """Handle migration application"""
        try:
            if await self._apply_pending_migrations():
                result['migration_applied'] = True
                result['actions_taken'].append("Applied pending migrations")
            else:
                result['warnings'].append("No migrations were applied")

        except Exception as e:
            logger.error(f"[DatabaseInitializer] Error applying migrations: {e}")
            result['errors'].append(f"Migration failed: {str(e)}")

    async def _ensure_reports_schema(self) -> None:
        """Ensure the reports schema exists"""
        try:
            async with self.engine.begin() as conn:
                await conn.execute(text("CREATE SCHEMA IF NOT EXISTS reports"))
                logger.info("[DatabaseInitializer] Reports schema ensured")
        except Exception as e:
            logger.error(f"[DatabaseInitializer] Failed to create reports schema: {e}")
            raise

    async def _apply_initial_migration(self) -> bool:
        """Apply the initial migration (001_initial_tables.py)"""
        try:
            # Check if alembic is available and configured
            if not self._is_alembic_available():
                return False

            # Run alembic upgrade to head (should apply 001_initial_tables)
            result = await asyncio.create_subprocess_exec(
                'alembic', 'upgrade', 'head',
                cwd=Path(__file__).parent.parent,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                logger.info("[DatabaseInitializer] ✅ Alembic migration applied successfully")
                if stdout:
                    logger.debug(f"[DatabaseInitializer] Alembic stdout: {stdout.decode()}")
                return True
            else:
                logger.error(f"[DatabaseInitializer] ❌ Alembic migration failed: {stderr.decode()}")
                return False

        except FileNotFoundError:
            logger.warning("[DatabaseInitializer] Alembic command not found")
            return False
        except Exception as e:
            logger.error(f"[DatabaseInitializer] Error running alembic migration: {e}")
            return False

    async def _apply_pending_migrations(self) -> bool:
        """Apply any pending migrations"""
        try:
            if not self._is_alembic_available():
                logger.warning("[DatabaseInitializer] Alembic not available for migrations")
                return False

            # Check for pending migrations first
            result = await asyncio.create_subprocess_exec(
                'alembic', 'current',
                cwd=Path(__file__).parent.parent,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await result.communicate()

            if result.returncode != 0:
                logger.error(f"[DatabaseInitializer] Failed to check current migration: {stderr.decode()}")
                return False

            # Apply migrations
            result = await asyncio.create_subprocess_exec(
                'alembic', 'upgrade', 'head',
                cwd=Path(__file__).parent.parent,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                output = stdout.decode()
                if "Running upgrade" in output:
                    logger.info("[DatabaseInitializer] ✅ Migrations applied successfully")
                    return True
                else:
                    logger.info("[DatabaseInitializer] No pending migrations found")
                    return False
            else:
                logger.error(f"[DatabaseInitializer] Migration failed: {stderr.decode()}")
                return False

        except Exception as e:
            logger.error(f"[DatabaseInitializer] Error applying migrations: {e}")
            return False

    async def _fallback_create_tables(self) -> None:
        """Fallback method to create tables using SQLAlchemy metadata"""
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
                logger.info("[DatabaseInitializer] ✅ Tables created using fallback method")
        except Exception as e:
            logger.error(f"[DatabaseInitializer] Fallback table creation failed: {e}")
            raise

    async def _check_migrations_needed(self) -> bool:
        """Check if there are pending migrations"""
        try:
            # This is a simplified check - in practice you might want to compare
            # the current alembic version with available migrations
            result = await asyncio.create_subprocess_exec(
                'alembic', 'current',
                cwd=Path(__file__).parent.parent,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                # For now, assume no migrations needed if alembic current runs
                # You could enhance this to actually compare versions
                return False
            else:
                logger.warning("[DatabaseInitializer] Could not check migration status")
                return False

        except Exception as e:
            logger.warning(f"[DatabaseInitializer] Error checking migration status: {e}")
            return False

    async def _mark_initial_migration_applied(self) -> None:
        """Mark the initial migration as applied for existing databases"""
        try:
            # This would typically involve inserting the initial migration version
            # into the alembic_version table, but since alembic handles this,
            # we'll let the migration system handle it
            logger.info("[DatabaseInitializer] Marking initial migration as applied...")

            # Run alembic stamp to mark current state
            result = await asyncio.create_subprocess_exec(
                'alembic', 'stamp', 'head',
                cwd=Path(__file__).parent.parent,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                logger.info("[DatabaseInitializer] ✅ Initial migration marked as applied")
            else:
                logger.warning(f"[DatabaseInitializer] Could not mark migration: {stderr.decode()}")

        except Exception as e:
            logger.warning(f"[DatabaseInitializer] Error marking initial migration: {e}")

    async def _count_application_tables(self) -> int:
        """Count application tables in reports schema"""
        try:
            async with self.engine.begin() as conn:
                result = await conn.execute(text(
                    "SELECT COUNT(*) FROM information_schema.tables "
                    "WHERE table_schema = 'reports' AND table_name NOT LIKE 'alembic%'"
                ))
                return result.scalar() or 0
        except Exception as e:
            logger.warning(f"[DatabaseInitializer] Could not count tables: {e}")
            return 0

    async def _validate_final_state(self, result: Dict[str, Any]) -> None:
        """Validate the final database state"""
        try:
            table_count = await self._count_application_tables()

            if table_count > 0:
                logger.info(f"[DatabaseInitializer] ✅ Database validation passed: {table_count} tables found")
                result['actions_taken'].append(f"Validated {table_count} tables exist")
            else:
                logger.error("[DatabaseInitializer] ❌ Database validation failed: no tables found")
                result['errors'].append("No application tables found after initialization")

        except Exception as e:
            logger.error(f"[DatabaseInitializer] Database validation failed: {e}")
            result['errors'].append(f"Validation error: {str(e)}")

    def _is_alembic_available(self) -> bool:
        """Check if Alembic is available and configured"""
        try:
            # Check if alembic.ini exists
            alembic_ini = Path(__file__).parent.parent / "alembic.ini"
            if not alembic_ini.exists():
                logger.warning("[DatabaseInitializer] alembic.ini not found")
                return False

            # Check if alembic directory exists
            alembic_dir = Path(__file__).parent.parent / "alembic"
            if not alembic_dir.exists():
                logger.warning("[DatabaseInitializer] alembic directory not found")
                return False

            return True

        except Exception as e:
            logger.warning(f"[DatabaseInitializer] Error checking alembic availability: {e}")
            return False

    async def _run_smart_migration(self, result: Dict[str, Any], generate_migrations: bool = None) -> None:
        """Run smart migration generation if enabled"""
        try:
            # Determine if migrations should be generated
            should_generate = generate_migrations
            if should_generate is None:
                # Use environment variable
                should_generate = should_auto_generate_migrations()

            if not should_generate:
                logger.info("[DatabaseInitializer] Smart migration disabled")
                return

            logger.info("[DatabaseInitializer] 🔄 Running smart migration check...")

            # Import and run smart migration
            try:
                # Run the smart migration script
                smart_migrate_path = Path(__file__).parent / "smart_migrate.py"
                if not smart_migrate_path.exists():
                    logger.warning("[DatabaseInitializer] Smart migration script not found")
                    result['warnings'].append("Smart migration script not available")
                    return

                process = await asyncio.create_subprocess_exec(
                    'python', str(smart_migrate_path),
                    cwd=Path(__file__).parent.parent,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                stdout, stderr = await process.communicate()

                if process.returncode == 0:
                    logger.info("[DatabaseInitializer] ✅ Smart migration completed successfully")
                    result['migration_generated'] = True
                    result['actions_taken'].append("Smart migration check completed")

                    # Check if a migration was actually generated
                    stdout_text = stdout.decode()
                    if "Migration file created" in stdout_text:
                        result['actions_taken'].append("New migration file generated")
                    else:
                        result['actions_taken'].append("No migration needed - database up to date")
                else:
                    logger.warning(f"[DatabaseInitializer] Smart migration had issues: {stderr.decode()}")
                    result['warnings'].append(f"Smart migration issues: {stderr.decode()}")

            except Exception as e:
                logger.warning(f"[DatabaseInitializer] Error running smart migration: {e}")
                result['warnings'].append(f"Smart migration error: {str(e)}")

        except Exception as e:
            logger.error(f"[DatabaseInitializer] Error in smart migration setup: {e}")
            result['errors'].append(f"Smart migration setup failed: {str(e)}")

    def _log_initialization_summary(self, result: Dict[str, Any]) -> None:
        """Log a summary of the initialization process"""
        logger.info("=" * 60)
        logger.info("[DatabaseInitializer] INITIALIZATION SUMMARY")
        logger.info("=" * 60)

        logger.info(f"First Run: {'Yes' if result['is_first_run'] else 'No'}")
        logger.info(f"Migration Applied: {'Yes' if result['migration_applied'] else 'No'}")
        logger.info(f"Tables Created/Validated: {result.get('tables_created', 0)}")

        if result['actions_taken']:
            logger.info("Actions Taken:")
            for action in result['actions_taken']:
                logger.info(f"  ✅ {action}")

        if result['warnings']:
            logger.warning("Warnings:")
            for warning in result['warnings']:
                logger.warning(f"  ⚠️  {warning}")

        if result['errors']:
            logger.error("Errors:")
            for error in result['errors']:
                logger.error(f"  ❌ {error}")

        status = "✅ SUCCESS" if not result['errors'] else "❌ FAILED"
        logger.info(f"Initialization Status: {status}")
        logger.info("=" * 60)

# Singleton instance
_db_initializer = None

def get_database_initializer() -> DatabaseInitializer:
    """Get the singleton database initializer instance"""
    global _db_initializer
    if _db_initializer is None:
        _db_initializer = DatabaseInitializer()
    return _db_initializer

async def auto_initialize_database() -> Dict[str, Any]:
    """
    Convenience function for automatic database initialization.

    This function should be called during application startup.
    """
    initializer = get_database_initializer()
    return await initializer.auto_initialize_database()

# Environment-based configuration
def should_auto_initialize() -> bool:
    """
    Determine if auto-initialization should run based on environment.

    Returns True if:
    - Environment variable AUTO_INIT_DB is set to 'true'
    - Or if no environment variable is set (default behavior)
    """
    auto_init = os.getenv('AUTO_INIT_DB', 'true').lower()
    return auto_init in ('true', '1', 'yes', 'on')

def should_auto_generate_migrations() -> bool:
    """
    Determine if migration auto-generation should run based on environment.

    Returns True if:
    - Environment variable AUTO_GENERATE_MIGRATIONS is set to 'true'
    """
    auto_generate = os.getenv('AUTO_GENERATE_MIGRATIONS', 'false').lower()
    return auto_generate in ('true', '1', 'yes', 'on')

def is_production_environment() -> bool:
    """Check if running in production environment"""
    env = os.getenv('ENVIRONMENT', '').lower()
    profile = os.getenv('PROFILE', '').lower()
    return env == 'production' or profile == 'prod'